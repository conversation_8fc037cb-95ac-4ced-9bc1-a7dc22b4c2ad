// Content script for Gladiatus Helper Bot UI injection

import { ExtensionMessage, MessageType } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import '../styles/content.css';

class ContentUIManager {
  private urlHelper: GladiatorUrlHelper;
  private isInjected: boolean = false;
  private extensionId: string;
  private botStatus: any = null;

  constructor() {
    this.urlHelper = new GladiatorUrlHelper();
    this.extensionId = chrome.runtime.id;
    this.initialize();
  }

  private initialize(): void {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
    } else {
      this.onDOMReady();
    }

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message: ExtensionMessage, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });
  }

  private onDOMReady(): void {
    const currentUrl = window.location.href;

    if (this.urlHelper.isLobby(currentUrl)) {
      this.handleLobbyPage();
    } else if (this.urlHelper.isPlaying(currentUrl)) {
      this.handleGamePage(currentUrl);
    }
  }

  private handleLobbyPage(): void {
    console.log('Gladiatus Helper Bot: Lobby page detected');

    // Send login request to background script
    const message: ExtensionMessage = {
      type: MessageType.LOGIN_REQUEST,
      isLobby: true,
      server: this.extractServerFromUrl(),
      playerId: this.extractPlayerIdFromUrl(),
      language: this.extractLanguageFromUrl()
    };

    chrome.runtime.sendMessage(message, (response) => {
      if (response?.shouldLogin && response.loginDelay) {
        this.scheduleAutoLogin(response.loginDelay);
      }
    });
  }

  private handleGamePage(url: string): void {
    console.log('Gladiatus Helper Bot: Game page detected');

    const urlInfo = this.urlHelper.resolve(url);
    if (!urlInfo) {
      console.error('Failed to resolve game URL');
      return;
    }

    // Set global extension ID for main script
    (window as any).ghExtensionId = this.extensionId;

    // Inject UI elements first
    this.injectUI();

    // Inject main bot script after UI is ready
    setTimeout(() => {
      this.injectMainScript();
    }, 100);

    // Load and restore bot status
    this.loadBotStatus();
  }

  private injectMainScript(): void {
    if (this.isInjected) return;

    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('main/index.iife.js');
    script.onload = () => {
      console.log('Gladiatus Helper Bot: Main script injected');
      this.isInjected = true;
    };
    script.onerror = () => {
      console.error('Gladiatus Helper Bot: Failed to inject main script');
    };

    (document.head || document.documentElement).appendChild(script);
  }

  private injectUI(): void {
    // Create bot control panel
    this.createControlPanel();

    // Create notification system
    this.createNotificationSystem();

    // Highlight underworld items if enabled
    this.highlightUnderworldItems();
  }

  private createControlPanel(): void {
    // Check if panel already exists
    if (document.getElementById('gh-control-panel')) return;

    const panel = document.createElement('div');
    panel.id = 'gh-control-panel';
    panel.className = 'gh-panel';

    panel.innerHTML = `
      <div class="gh-panel-header">
        <h3>Gladiatus Helper Bot</h3>
        <button id="gh-toggle-panel" class="gh-btn gh-btn-sm">−</button>
      </div>
      <div class="gh-panel-content">
        <div class="gh-status-section">
          <div class="gh-status-indicator" id="gh-status-indicator">
            <span class="gh-status-dot"></span>
            <span id="gh-status-text">Inactive</span>
          </div>
          <button id="gh-toggle-bot" class="gh-btn gh-btn-primary">Start Bot</button>
        </div>
        <div class="gh-stats-section">
          <div class="gh-stat-item">
            <span class="gh-stat-label">Runtime:</span>
            <span id="gh-runtime" class="gh-stat-value">00:00:00</span>
          </div>
          <div class="gh-stat-item">
            <span class="gh-stat-label">Actions:</span>
            <span id="gh-actions" class="gh-stat-value">0</span>
          </div>
        </div>
        <div class="gh-actions-section">
          <button id="gh-settings" class="gh-btn gh-btn-secondary">Settings</button>
          <button id="gh-statistics" class="gh-btn gh-btn-secondary">Statistics</button>
        </div>
      </div>
    `;

    // Position panel
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      width: 280px;
      background: #2c3e50;
      border: 1px solid #34495e;
      border-radius: 8px;
      color: white;
      font-family: Arial, sans-serif;
      font-size: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    document.body.appendChild(panel);

    // Add event listeners
    this.attachPanelEventListeners();
  }

  private attachPanelEventListeners(): void {
    const togglePanel = document.getElementById('gh-toggle-panel');
    const toggleBot = document.getElementById('gh-toggle-bot');
    const settingsBtn = document.getElementById('gh-settings');
    const statisticsBtn = document.getElementById('gh-statistics');

    togglePanel?.addEventListener('click', () => {
      const content = document.querySelector('.gh-panel-content') as HTMLElement;
      const isCollapsed = content.style.display === 'none';
      content.style.display = isCollapsed ? 'block' : 'none';
      togglePanel.textContent = isCollapsed ? '−' : '+';
    });

    toggleBot?.addEventListener('click', () => {
      this.toggleBot();
    });

    settingsBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('Settings button clicked');
      this.openSettings();
    });

    statisticsBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('Statistics button clicked');
      this.openStatistics();
    });
  }

  private createNotificationSystem(): void {
    if (document.getElementById('gh-notifications')) return;

    const container = document.createElement('div');
    container.id = 'gh-notifications';
    container.className = 'gh-notifications';
    container.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 9999;
      max-width: 400px;
    `;

    document.body.appendChild(container);
  }

  private highlightUnderworldItems(): void {
    // This would be implemented based on game-specific selectors
    const items = document.querySelectorAll('[data-item-type="underworld"]');
    items.forEach(item => {
      (item as HTMLElement).style.border = '2px solid #e74c3c';
      (item as HTMLElement).style.boxShadow = '0 0 10px rgba(231, 76, 60, 0.5)';
    });
  }

  private scheduleAutoLogin(delay: number): void {
    const remainingTime = delay - Date.now();
    if (remainingTime > 0) {
      setTimeout(() => {
        this.performAutoLogin();
      }, remainingTime);
    }
  }

  private performAutoLogin(): void {
    // Look for login button and click it
    const loginButton = document.querySelector('input[type="submit"][value*="Login"], button[type="submit"]') as HTMLElement;
    if (loginButton) {
      loginButton.click();
    }
  }

  private async loadBotStatus(): Promise<void> {
    try {
      // Get bot status from storage
      const result = await chrome.storage.local.get('gh_bot_status');
      this.botStatus = result.gh_bot_status || null;

      console.log('Content script loaded bot status:', this.botStatus);

      if (this.botStatus) {
        // Update UI with stored status
        this.updateStatusUI(this.botStatus);
      }

      // Set up periodic status sync
      this.setupStatusSync();
    } catch (error) {
      console.error('Failed to load bot status:', error);
    }
  }

  private setupStatusSync(): void {
    // Listen for storage changes
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.gh_bot_status) {
        console.log('Bot status changed in storage:', changes.gh_bot_status.newValue);
        this.botStatus = changes.gh_bot_status.newValue;
        if (this.botStatus) {
          this.updateStatusUI(this.botStatus);
        }
      }
    });

    // Periodic sync every 5 seconds
    setInterval(async () => {
      try {
        const result = await chrome.storage.local.get('gh_bot_status');
        const newStatus = result.gh_bot_status;
        if (newStatus && JSON.stringify(newStatus) !== JSON.stringify(this.botStatus)) {
          console.log('Syncing bot status from storage');
          this.botStatus = newStatus;
          this.updateStatusUI(this.botStatus);
        }
      } catch (error) {
        // Ignore sync errors
      }
    }, 5000);
  }

  private updateStatusUI(status: any): void {
    const statusText = document.getElementById('gh-status-text');
    const toggleButton = document.getElementById('gh-toggle-bot') as HTMLButtonElement;
    const statusDot = document.querySelector('.gh-status-dot') as HTMLElement;

    if (statusText && toggleButton && statusDot) {
      if (status.isActive) {
        if (status.isPaused) {
          statusDot.style.backgroundColor = '#f39c12'; // Orange for paused
          statusText.textContent = 'Paused';
          toggleButton.textContent = 'Resume Bot';
        } else {
          statusDot.style.backgroundColor = '#27ae60'; // Green for active
          statusText.textContent = 'Active';
          toggleButton.textContent = 'Stop Bot';
        }
      } else {
        statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive
        statusText.textContent = status.error ? 'Error' : 'Inactive';
        toggleButton.textContent = 'Start Bot';
      }
    }
  }

  private toggleBot(): void {
    // Send message to main script to toggle bot
    const event = new CustomEvent('ghToggleBot');
    window.dispatchEvent(event);
  }

  private openSettings(): void {
    console.log('Opening settings modal...');
    // Prevent any immediate event propagation
    setTimeout(() => {
      this.createSettingsModal();
    }, 10);
  }

  private createSettingsModal(): void {
    console.log('Creating settings modal...');

    // Check if modal already exists
    const existingModal = document.getElementById('gh-settings-modal');
    if (existingModal) {
      console.log('Modal already exists, removing...');
      existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'gh-settings-modal';
    modal.className = 'gh-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'gh-modal-content';
    modalContent.style.cssText = `
      background: #2c3e50;
      color: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    `;

    modalContent.innerHTML = `
      <div class="gh-modal-header">
        <h2>Bot Settings</h2>
        <button class="gh-modal-close" style="float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
      </div>
      <div class="gh-modal-body">
        <div class="gh-settings-section">
          <h3>General Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-auto-start"> Auto-start bot on page load
          </label>
          <label>
            <input type="number" id="gh-setting-login-delay" min="1" max="60" value="5"> Login delay (seconds)
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Combat Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-arena-enabled"> Enable Arena Combat
          </label>
          <label>
            <input type="checkbox" id="gh-setting-turma-enabled"> Enable Turma Combat
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Healing Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-heal-enabled"> Enable Auto-Healing
          </label>
          <label>
            <input type="number" id="gh-setting-min-health" min="1" max="100" value="50"> Minimum Health %
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Quest & Market Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-quest-enabled"> Enable Quest Management
          </label>
          <label>
            <input type="checkbox" id="gh-setting-market-enabled"> Enable Market Operations
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Forge & Repair Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-forge-enabled"> Enable Auto-Forging
          </label>
          <label>
            <input type="checkbox" id="gh-setting-repair-enabled"> Enable Auto-Repair
          </label>
        </div>
      </div>
      <div class="gh-modal-footer" style="margin-top: 20px; text-align: right;">
        <button id="gh-settings-save" class="gh-btn gh-btn-primary">Save Settings</button>
        <button id="gh-settings-cancel" class="gh-btn gh-btn-secondary" style="margin-left: 10px;">Cancel</button>
      </div>
    `;

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    console.log('Modal added to DOM, modal element:', modal);
    console.log('Modal display style:', modal.style.display);

    // Load current settings
    this.loadSettingsIntoModal();

    // Add event listeners
    this.attachSettingsEventListeners(modal);

    console.log('Settings modal fully initialized');
  }

  private async loadSettingsIntoModal(): Promise<void> {
    try {
      const result = await chrome.storage.local.get('gh_settings');
      const settings = result.gh_settings || {};

      // Populate form fields with current settings
      const autoStart = document.getElementById('gh-setting-auto-start') as HTMLInputElement;
      const loginDelay = document.getElementById('gh-setting-login-delay') as HTMLInputElement;
      const arenaEnabled = document.getElementById('gh-setting-arena-enabled') as HTMLInputElement;
      const turmaEnabled = document.getElementById('gh-setting-turma-enabled') as HTMLInputElement;
      const healEnabled = document.getElementById('gh-setting-heal-enabled') as HTMLInputElement;
      const minHealth = document.getElementById('gh-setting-min-health') as HTMLInputElement;
      const questEnabled = document.getElementById('gh-setting-quest-enabled') as HTMLInputElement;
      const marketEnabled = document.getElementById('gh-setting-market-enabled') as HTMLInputElement;
      const forgeEnabled = document.getElementById('gh-setting-forge-enabled') as HTMLInputElement;
      const repairEnabled = document.getElementById('gh-setting-repair-enabled') as HTMLInputElement;

      if (autoStart) autoStart.checked = settings.general?.autoStart || false;
      if (loginDelay) loginDelay.value = settings.general?.loginDelay || 5;
      if (arenaEnabled) arenaEnabled.checked = settings.arena?.enabled || false;
      if (turmaEnabled) turmaEnabled.checked = settings.turma?.enabled || false;
      if (healEnabled) healEnabled.checked = settings.heal?.enabled || false;
      if (minHealth) minHealth.value = settings.heal?.minHealth || 50;
      if (questEnabled) questEnabled.checked = settings.quest?.enabled || false;
      if (marketEnabled) marketEnabled.checked = settings.market?.enabled || false;
      if (forgeEnabled) forgeEnabled.checked = settings.forge?.enabled || false;
      if (repairEnabled) repairEnabled.checked = settings.repair?.enabled || false;

    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  private attachSettingsEventListeners(modal: HTMLElement): void {
    console.log('Attaching settings event listeners...');

    const closeBtn = modal.querySelector('.gh-modal-close');
    const saveBtn = document.getElementById('gh-settings-save');
    const cancelBtn = document.getElementById('gh-settings-cancel');
    const modalContent = modal.querySelector('.gh-modal-content');

    console.log('Found elements:', { closeBtn, saveBtn, cancelBtn, modalContent });

    closeBtn?.addEventListener('click', (e) => {
      console.log('Close button clicked');
      e.preventDefault();
      e.stopPropagation();
      modal.remove();
    });

    cancelBtn?.addEventListener('click', (e) => {
      console.log('Cancel button clicked');
      e.preventDefault();
      e.stopPropagation();
      modal.remove();
    });

    saveBtn?.addEventListener('click', (e) => {
      console.log('Save button clicked');
      e.preventDefault();
      e.stopPropagation();
      this.saveSettings();
      modal.remove();
    });

    // Prevent modal content clicks from closing modal
    modalContent?.addEventListener('click', (e) => {
      console.log('Modal content clicked');
      e.stopPropagation();
    });

    // Close on background click only - with delay to prevent immediate closure
    setTimeout(() => {
      modal.addEventListener('click', (e) => {
        console.log('Modal background clicked, target:', e.target, 'modal:', modal);
        if (e.target === modal) {
          console.log('Closing modal via background click');
          e.preventDefault();
          e.stopPropagation();
          modal.remove();
        }
      });
    }, 100);

    // Close on Escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        console.log('Escape key pressed, closing modal');
        modal.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    console.log('Event listeners attached successfully');
  }

  private async saveSettings(): Promise<void> {
    try {
      // Get current settings
      const result = await chrome.storage.local.get('gh_settings');
      const settings = result.gh_settings || {};

      // Update settings from form
      const autoStart = (document.getElementById('gh-setting-auto-start') as HTMLInputElement)?.checked;
      const loginDelay = parseInt((document.getElementById('gh-setting-login-delay') as HTMLInputElement)?.value) || 5;
      const arenaEnabled = (document.getElementById('gh-setting-arena-enabled') as HTMLInputElement)?.checked;
      const turmaEnabled = (document.getElementById('gh-setting-turma-enabled') as HTMLInputElement)?.checked;
      const healEnabled = (document.getElementById('gh-setting-heal-enabled') as HTMLInputElement)?.checked;
      const minHealth = parseInt((document.getElementById('gh-setting-min-health') as HTMLInputElement)?.value) || 50;
      const questEnabled = (document.getElementById('gh-setting-quest-enabled') as HTMLInputElement)?.checked;
      const marketEnabled = (document.getElementById('gh-setting-market-enabled') as HTMLInputElement)?.checked;
      const forgeEnabled = (document.getElementById('gh-setting-forge-enabled') as HTMLInputElement)?.checked;
      const repairEnabled = (document.getElementById('gh-setting-repair-enabled') as HTMLInputElement)?.checked;

      // Update settings object
      settings.general = settings.general || {};
      settings.general.autoStart = autoStart;
      settings.general.loginDelay = loginDelay;

      settings.arena = settings.arena || {};
      settings.arena.enabled = arenaEnabled;

      settings.turma = settings.turma || {};
      settings.turma.enabled = turmaEnabled;

      settings.heal = settings.heal || {};
      settings.heal.enabled = healEnabled;
      settings.heal.minHealth = minHealth;

      settings.quest = settings.quest || {};
      settings.quest.enabled = questEnabled;

      settings.market = settings.market || {};
      settings.market.enabled = marketEnabled;

      settings.forge = settings.forge || {};
      settings.forge.enabled = forgeEnabled;

      settings.repair = settings.repair || {};
      settings.repair.enabled = repairEnabled;

      // Save to storage
      await chrome.storage.local.set({ 'gh_settings': settings });

      // Notify main script of settings update
      const event = new CustomEvent('ghSettingsUpdated', { detail: settings });
      window.dispatchEvent(event);

      this.showNotification({
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully.'
      });

    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showNotification({
        title: 'Error',
        message: 'Failed to save settings. Please try again.'
      });
    }
  }

  private openStatistics(): void {
    // Open statistics modal
    const event = new CustomEvent('ghOpenStatistics');
    window.dispatchEvent(event);
  }

  private handleMessage(message: ExtensionMessage, _sender: any, _sendResponse: (response?: any) => void): void {
    switch (message.type) {
      case MessageType.NOTIFICATION:
        this.showNotification(message.data);
        break;
      case MessageType.ERROR:
        this.showError(message.data);
        break;
      default:
        break;
    }
  }

  private showNotification(data: any): void {
    const container = document.getElementById('gh-notifications');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = 'gh-notification gh-notification-info';
    notification.innerHTML = `
      <div class="gh-notification-content">
        <strong>${data.title || 'Notification'}</strong>
        <p>${data.message}</p>
      </div>
      <button class="gh-notification-close">×</button>
    `;

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);

    // Add close button functionality
    notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {
      notification.remove();
    });
  }

  private showError(data: any): void {
    console.error('Gladiatus Helper Bot Error:', data);
    this.showNotification({
      title: 'Error',
      message: data.message || 'An error occurred'
    });
  }

  private extractServerFromUrl(): string {
    const match = window.location.href.match(/s(\d+)-(\w+)/);
    return match ? `${match[1]}-${match[2]}` : '';
  }

  private extractPlayerIdFromUrl(): string {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('playerId') || '';
  }

  private extractLanguageFromUrl(): string {
    const match = window.location.href.match(/s\d+-(\w+)/);
    return match ? match[1] : 'en';
  }
}

// Initialize content UI manager
new ContentUIManager();