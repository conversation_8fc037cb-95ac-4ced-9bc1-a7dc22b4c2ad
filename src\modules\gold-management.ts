// Gold Management Module - Handles gold hiding, donations, and market operations

import { HideGoldSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface GoldHidingLocation {
  type: 'guild' | 'shop' | 'market' | 'training';
  name: string;
  isAvailable: boolean;
  capacity: number;
  currentAmount: number;
}

export interface MarketItem {
  id: string;
  name: string;
  price: number;
  quality: string;
  seller: string;
  canBuy: boolean;
}

export class GoldManagementModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isProcessing: boolean = false;
  private currentGold: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async manageGold(settings: HideGoldSettings): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('Gold Management: Starting gold management');

    try {
      this.currentGold = await this.getCurrentGold();
      
      if (this.shouldHideGold(settings)) {
        await this.hideGold(settings);
      }
      
      console.log('Gold Management: Gold management completed');

    } catch (error) {
      console.error('Gold Management: Error managing gold:', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  private async getCurrentGold(): Promise<number> {
    const goldElements = [
      '#header_values_gold',
      '.player_gold',
      '.gold-amount',
      '#gold_display'
    ];

    for (const selector of goldElements) {
      const element = document.querySelector(selector);
      if (element) {
        const goldText = element.textContent?.replace(/\D/g, '') || '0';
        return parseInt(goldText);
      }
    }

    return 0;
  }

  private shouldHideGold(settings: HideGoldSettings): boolean {
    const excessGold = this.currentGold - settings.pocketMoney;
    return excessGold > settings.minGoldBackup;
  }

  private async hideGold(settings: HideGoldSettings): Promise<void> {
    const excessGold = this.currentGold - settings.pocketMoney;
    
    console.log(`Gold Management: Hiding ${excessGold} gold (keeping ${settings.pocketMoney} pocket money)`);

    // Try primary location first
    if (settings.primary.enabled) {
      const success = await this.hideGoldInLocation(settings.primary, excessGold, settings);
      if (success) return;
    }

    // Try backup location if primary failed
    if (settings.backup.enabled) {
      await this.hideGoldInLocation(settings.backup, excessGold, settings);
    }
  }

  private async hideGoldInLocation(locationConfig: any, amount: number, settings: HideGoldSettings): Promise<boolean> {
    switch (locationConfig.location) {
      case 'guild':
        return await this.hideGoldInGuild(amount, settings);
      case 'shop':
        return await this.hideGoldInShop(amount, settings);
      case 'market':
        return await this.hideGoldInMarket(amount, settings);
      case 'training':
        return await this.hideGoldInTraining(amount, settings);
      default:
        console.log(`Gold Management: Unknown location type: ${locationConfig.location}`);
        return false;
    }
  }

  private async hideGoldInGuild(amount: number, settings: HideGoldSettings): Promise<boolean> {
    console.log('Gold Management: Hiding gold in guild');
    
    await this.navigateToGuild();
    
    // Look for donation interface
    const donationInput = document.querySelector('#donation_amount, .donation-input') as HTMLInputElement;
    const donateButton = document.querySelector('.donate_button, [data-action="donate"]');
    
    if (!donationInput || !donateButton) {
      console.log('Gold Management: Guild donation interface not found');
      return false;
    }

    // Set donation amount
    donationInput.value = amount.toString();
    donationInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Click donate button
    (donateButton as HTMLElement).click();
    
    await this.delay(2000);
    console.log(`Gold Management: Donated ${amount} gold to guild`);
    return true;
  }

  private async hideGoldInShop(amount: number, settings: HideGoldSettings): Promise<boolean> {
    console.log('Gold Management: Hiding gold by buying shop items');
    
    await this.navigateToShop();
    
    let remainingAmount = amount;
    const items = await this.getShopItems(settings);
    
    for (const item of items) {
      if (remainingAmount <= 0) break;
      
      if (item.price <= remainingAmount && item.canBuy) {
        await this.buyShopItem(item);
        remainingAmount -= item.price;
        await this.delay(1000);
      }
    }
    
    const spentAmount = amount - remainingAmount;
    console.log(`Gold Management: Spent ${spentAmount} gold on shop items`);
    return spentAmount > 0;
  }

  private async hideGoldInMarket(amount: number, settings: HideGoldSettings): Promise<boolean> {
    console.log('Gold Management: Hiding gold by buying market items');
    
    await this.navigateToMarket();
    
    let remainingAmount = amount;
    const items = await this.getMarketItems(settings);
    
    for (const item of items) {
      if (remainingAmount <= 0) break;
      
      if (item.price <= remainingAmount && item.canBuy) {
        await this.buyMarketItem(item);
        remainingAmount -= item.price;
        await this.delay(1500);
      }
    }
    
    const spentAmount = amount - remainingAmount;
    console.log(`Gold Management: Spent ${spentAmount} gold on market items`);
    return spentAmount > 0;
  }

  private async hideGoldInTraining(amount: number, settings: HideGoldSettings): Promise<boolean> {
    console.log('Gold Management: Hiding gold in skill training');
    
    await this.navigateToTraining();
    
    let remainingAmount = amount;
    
    for (const skill of settings.skillsToTrain) {
      if (remainingAmount <= 0) break;
      
      const trained = await this.trainSkill(skill, remainingAmount);
      remainingAmount -= trained;
    }
    
    const spentAmount = amount - remainingAmount;
    console.log(`Gold Management: Spent ${spentAmount} gold on skill training`);
    return spentAmount > 0;
  }

  private async getShopItems(settings: HideGoldSettings): Promise<MarketItem[]> {
    const items: MarketItem[] = [];
    const itemElements = document.querySelectorAll('.shop_item, .market-item');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      const item = await this.parseShopItem(element, i);
      
      if (item && this.isValidPurchase(item, settings)) {
        items.push(item);
      }
    }
    
    // Sort by price (ascending) for efficient spending
    items.sort((a, b) => a.price - b.price);
    
    return items;
  }

  private async getMarketItems(settings: HideGoldSettings): Promise<MarketItem[]> {
    const items: MarketItem[] = [];
    const itemElements = document.querySelectorAll('.market_item, .auction-item');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      const item = await this.parseMarketItem(element, i);
      
      if (item && this.isValidPurchase(item, settings)) {
        items.push(item);
      }
    }
    
    // Sort by price (ascending)
    items.sort((a, b) => a.price - b.price);
    
    return items;
  }

  private async parseShopItem(element: HTMLElement, index: number): Promise<MarketItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const priceElement = element.querySelector('.item_price, .price');
    const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
    
    if (!nameElement || !priceElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
    
    return {
      id: index.toString(),
      name,
      price,
      quality: 'unknown',
      seller: 'shop',
      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))
    };
  }

  private async parseMarketItem(element: HTMLElement, index: number): Promise<MarketItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const priceElement = element.querySelector('.item_price, .price');
    const sellerElement = element.querySelector('.seller_name, .seller');
    const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
    
    if (!nameElement || !priceElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
    const seller = sellerElement?.textContent?.trim() || 'unknown';
    
    return {
      id: index.toString(),
      name,
      price,
      quality: 'unknown',
      seller,
      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))
    };
  }

  private isValidPurchase(item: MarketItem, settings: HideGoldSettings): boolean {
    // Check minimum price
    if (item.price < settings.minItemPrice) return false;
    
    // Check if buying from specific players
    if (settings.buyFromPlayers.length > 0) {
      return settings.buyFromPlayers.includes(item.seller);
    }
    
    // Check if item can be bought
    return item.canBuy;
  }

  private async buyShopItem(item: MarketItem): Promise<void> {
    console.log(`Gold Management: Buying ${item.name} for ${item.price} gold`);
    
    const itemElement = document.querySelector(`.shop_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
    if (buyButton && !buyButton.hasAttribute('disabled')) {
      (buyButton as HTMLElement).click();
    }
  }

  private async buyMarketItem(item: MarketItem): Promise<void> {
    console.log(`Gold Management: Buying ${item.name} from ${item.seller} for ${item.price} gold`);
    
    const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
    if (buyButton && !buyButton.hasAttribute('disabled')) {
      (buyButton as HTMLElement).click();
      
      // Handle confirmation if present
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_buy, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private async trainSkill(skillName: string, maxAmount: number): Promise<number> {
    console.log(`Gold Management: Training ${skillName} with up to ${maxAmount} gold`);
    
    const skillElements = document.querySelectorAll('.skill_item, .training-skill');
    
    for (const element of skillElements) {
      const nameElement = element.querySelector('.skill_name, .name');
      if (nameElement?.textContent?.toLowerCase().includes(skillName.toLowerCase())) {
        const trainButton = element.querySelector('.train_button, [data-action="train"]');
        const costElement = element.querySelector('.training_cost, .cost');
        
        if (trainButton && !trainButton.hasAttribute('disabled')) {
          const cost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
          if (cost <= maxAmount) {
            (trainButton as HTMLElement).click();
            await this.delay(2000);
            return cost;
          }
        }
      }
    }
    
    return 0;
  }

  private async navigateToGuild(): Promise<void> {
    if (!window.location.href.includes('mod=guild')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=guild`;
      await this.waitForPageLoad();
    }
  }

  private async navigateToShop(): Promise<void> {
    if (!window.location.href.includes('mod=location&loc=26')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=location&loc=26`;
      await this.waitForPageLoad();
    }
  }

  private async navigateToMarket(): Promise<void> {
    if (!window.location.href.includes('mod=market')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=market`;
      await this.waitForPageLoad();
    }
  }

  private async navigateToTraining(): Promise<void> {
    if (!window.location.href.includes('mod=training')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=training`;
      await this.waitForPageLoad();
    }
  }

  async getGoldStatus(): Promise<any> {
    return {
      currentGold: await this.getCurrentGold(),
      isProcessing: this.isProcessing,
      lastProcessed: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
