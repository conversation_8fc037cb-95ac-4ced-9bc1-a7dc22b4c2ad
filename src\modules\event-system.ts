// Event System Module - Handles special events and bonus collection

import { EventSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface GameEvent {
  id: string;
  name: string;
  type: 'combat' | 'collection' | 'special';
  isActive: boolean;
  timeRemaining: number;
  hasRewards: boolean;
  canParticipate: boolean;
}

export interface EventReward {
  type: 'gold' | 'xp' | 'item' | 'points';
  amount: number;
  description: string;
  isCollected: boolean;
}

export class EventSystemModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private eventCount: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: EventSettings): Promise<void> {
    if (this.isRunning || !settings.enabled) return;
    
    this.isRunning = true;
    this.eventCount = 0;
    
    console.log('Event System: Starting event automation');
    
    try {
      await this.performEventLoop(settings);
    } catch (error) {
      console.error('Event System: Error in event automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Event System: Stopped event automation');
  }

  private async performEventLoop(settings: EventSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueEvents(settings)) {
      try {
        // Check for active events
        const activeEvents = await this.getActiveEvents();
        
        if (activeEvents.length === 0) {
          console.log('Event System: No active events found');
          await this.delay(30000); // Check again in 30 seconds
          continue;
        }

        // Process each active event
        for (const event of activeEvents) {
          if (!this.isRunning) break;
          
          await this.processEvent(event, settings);
          await this.delay(5000);
        }

        // Collect bonuses if enabled
        if (settings.autoCollectBonuses) {
          await this.collectEventBonuses();
        }

        // Auto renew event if enabled
        if (settings.autoRenewEvent) {
          await this.renewEvents();
        }

        await this.delay(60000); // Check events every minute
        
      } catch (error) {
        console.error('Event System: Error in event loop:', error);
        await this.delay(30000);
      }
    }
  }

  private async getActiveEvents(): Promise<GameEvent[]> {
    const events: GameEvent[] = [];
    
    // Check main event page
    await this.navigateToEvents();
    
    const eventElements = document.querySelectorAll('.event_item, .active-event');
    
    for (let i = 0; i < eventElements.length; i++) {
      const element = eventElements[i] as HTMLElement;
      
      try {
        const event = await this.parseEventElement(element, i);
        if (event && event.isActive) {
          events.push(event);
        }
      } catch (error) {
        console.error('Event System: Error parsing event:', error);
      }
    }
    
    return events;
  }

  private async parseEventElement(element: HTMLElement, index: number): Promise<GameEvent | null> {
    const nameElement = element.querySelector('.event_name, .name');
    const timerElement = element.querySelector('.event_timer, .timer');
    const participateButton = element.querySelector('.participate_button, [data-action="participate"]');
    const rewardsElement = element.querySelector('.event_rewards, .rewards');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const timerText = timerElement?.textContent?.trim() || '0:00:00';
    const timeMatch = timerText.match(/(\d+):(\d+):(\d+)/);
    const timeRemaining = timeMatch ? 
      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
    
    // Determine event type based on name or class
    let type: 'combat' | 'collection' | 'special' = 'special';
    if (name.toLowerCase().includes('combat') || name.toLowerCase().includes('fight')) {
      type = 'combat';
    } else if (name.toLowerCase().includes('collect') || name.toLowerCase().includes('gather')) {
      type = 'collection';
    }
    
    return {
      id: index.toString(),
      name,
      type,
      isActive: timeRemaining > 0,
      timeRemaining,
      hasRewards: !!rewardsElement,
      canParticipate: !!(participateButton && !participateButton.hasAttribute('disabled'))
    };
  }

  private async processEvent(event: GameEvent, settings: EventSettings): Promise<void> {
    console.log(`Event System: Processing event ${event.name}`);
    
    switch (event.type) {
      case 'combat':
        await this.processCombatEvent(event, settings);
        break;
      case 'collection':
        await this.processCollectionEvent(event, settings);
        break;
      case 'special':
        await this.processSpecialEvent(event, settings);
        break;
    }
    
    this.eventCount++;
  }

  private async processCombatEvent(event: GameEvent, settings: EventSettings): Promise<void> {
    if (!event.canParticipate) return;
    
    console.log(`Event System: Participating in combat event ${event.name}`);
    
    // Navigate to event combat page
    await this.navigateToEventCombat(event);
    
    // Look for combat targets
    const targets = await this.getEventTargets(settings);
    
    for (const target of targets) {
      if (!this.isRunning) break;
      
      await this.attackEventTarget(target);
      await this.delay(5000);
    }
  }

  private async processCollectionEvent(event: GameEvent, settings: EventSettings): Promise<void> {
    console.log(`Event System: Processing collection event ${event.name}`);
    
    // Navigate to event collection page
    await this.navigateToEventCollection(event);
    
    // Collect available items
    await this.collectEventItems();
  }

  private async processSpecialEvent(event: GameEvent, settings: EventSettings): Promise<void> {
    console.log(`Event System: Processing special event ${event.name}`);
    
    if (event.canParticipate) {
      const eventElement = document.querySelector(`.event_item:nth-child(${parseInt(event.id) + 1})`);
      if (eventElement) {
        const participateButton = eventElement.querySelector('.participate_button, [data-action="participate"]');
        if (participateButton && !participateButton.hasAttribute('disabled')) {
          (participateButton as HTMLElement).click();
          await this.delay(3000);
        }
      }
    }
  }

  private async getEventTargets(settings: EventSettings): Promise<any[]> {
    const targets: any[] = [];
    const targetElements = document.querySelectorAll('.event_target, .event-enemy');
    
    for (const element of targetElements) {
      const nameElement = element.querySelector('.target_name, .name');
      const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
      
      if (nameElement && attackButton && !attackButton.hasAttribute('disabled')) {
        const name = nameElement.textContent?.trim() || '';
        
        // Check if this matches the configured mob
        if (!settings.mob || name.toLowerCase().includes(settings.mob.toLowerCase())) {
          targets.push({
            name,
            element,
            button: attackButton
          });
        }
      }
    }
    
    return targets;
  }

  private async attackEventTarget(target: any): Promise<void> {
    console.log(`Event System: Attacking ${target.name}`);
    
    (target.button as HTMLElement).click();
    
    // Wait for combat result
    await this.delay(3000);
    
    // Check for combat result
    const resultElement = document.querySelector('.combat_result, .event_result');
    if (resultElement) {
      const won = resultElement.textContent?.includes('Victory') || false;
      console.log(`Event System: Combat result - ${won ? 'Victory' : 'Defeat'}`);
    }
  }

  private async collectEventItems(): Promise<void> {
    const collectButtons = document.querySelectorAll('.collect_button, [data-action="collect"]');
    
    for (const button of collectButtons) {
      if (!button.hasAttribute('disabled')) {
        console.log('Event System: Collecting event item');
        (button as HTMLElement).click();
        await this.delay(1500);
      }
    }
  }

  private async collectEventBonuses(): Promise<void> {
    console.log('Event System: Collecting event bonuses');
    
    // Look for bonus collection buttons
    const bonusButtons = document.querySelectorAll('.collect_bonus, .bonus_button, [data-action="collect-bonus"]');
    
    for (const button of bonusButtons) {
      if (!button.hasAttribute('disabled')) {
        console.log('Event System: Collecting bonus');
        (button as HTMLElement).click();
        await this.delay(2000);
      }
    }
  }

  private async renewEvents(): Promise<void> {
    console.log('Event System: Auto-renewing events');
    
    const renewButtons = document.querySelectorAll('.renew_event, [data-action="renew"]');
    
    for (const button of renewButtons) {
      if (!button.hasAttribute('disabled')) {
        console.log('Event System: Renewing event');
        (button as HTMLElement).click();
        await this.delay(2000);
      }
    }
  }

  private async navigateToEvents(): Promise<void> {
    if (!window.location.href.includes('mod=events')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=events`;
      await this.waitForPageLoad();
    }
  }

  private async navigateToEventCombat(event: GameEvent): Promise<void> {
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('?')[0];
    window.location.href = `${baseUrl}?mod=events&action=combat&event=${event.id}`;
    await this.waitForPageLoad();
  }

  private async navigateToEventCollection(event: GameEvent): Promise<void> {
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('?')[0];
    window.location.href = `${baseUrl}?mod=events&action=collect&event=${event.id}`;
    await this.waitForPageLoad();
  }

  private shouldContinueEvents(settings: EventSettings): boolean {
    // Check auto stop condition
    if (settings.autoStop && this.eventCount >= 50) { // Arbitrary limit
      console.log('Event System: Event limit reached');
      return false;
    }
    
    // Check if following a leader
    if (settings.followLeader) {
      // This would need leader detection logic
      return true; // Simplified
    }
    
    return true;
  }

  async getEventStatus(): Promise<any> {
    const activeEvents = await this.getActiveEvents();
    
    return {
      isRunning: this.isRunning,
      eventCount: this.eventCount,
      activeEvents: activeEvents.length,
      lastUpdate: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
