(function () {
	"use strict";
	const gh_UrlInfo = {
		resolveQueries: (n) => {
			const l = {},
				c = n.split("&");
			for (let d = c.length - 1; d >= 0; d--) {
				const [h, g] = c[d].split("=");
				l[h] = g;
			}
			return l;
		},
		isGladiatus: (n) => /gladiatus\.gameforge\.com/.test(n),
		ajaxLink: (n) => gh_UrlInfo.link(n, "ajax.php"),
		link: (n, l) => {
			var c;
			const d = [];
			for (const h in n) d.push(h + "=" + n[h]);
			return d.push("sh=" + ((c = gh_UrlInfo == null ? void 0 : gh_UrlInfo.queries) != null && c.sh ? gh_UrlInfo.queries.sh : secureHash)), (l || "index.php") + "?" + d.join("&");
		},
		isPlaying: (n) => /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(n),
		isLobby: (n) => /lobby\.gladiatus\.gameforge\.com/.test(n) && n.indexOf("loading") < 0,
		resolve: function (n, l = !1) {
			if (this.resolved && !l) return this;
			this.resolved = !0;
			const c = n.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main).php\?(.*)/i);
			return !!c && ((this.server = c[1]), (this.country = c[2]), (this.domain = `s${this.server}-${this.country}.gladiatus.gameforge.com`), (this.queries = this.resolveQueries(c[3])), this);
		},
		server: void 0,
		country: void 0,
		queries: {},
	},
		gh_lang = {
			global: {
				all: "All",
				weapons: "Weapons",
				shields: "Shields",
				chestArmour: "Chest Armour",
				helmets: "Helmets",
				gloves: "Gloves",
				shoes: "Shoes",
				rings: "Rings",
				amulets: "Amulets",
				usable: "Usable",
				upgrades: "Upgrades",
				recipes: "Recipes",
				mercenary: "Mercenary",
				tools: "Tools",
				scroll: "Scroll",
				eventItems: "Event Items",
				reinforcements: "Reinforcements",
				forgingGoods: "Forging goods",
				gold: "Gold",
				grindstone: "Grindstone",
				smallGrindstone: "Small Grindstone",
				shredLeather: "Shred of leather",
				protectiveGear: "Protective Gear",
				godOils: "God Oils",
				qualities: { white: "White", green: "Green", blue: "Blue", purple: "Purple", orange: "Orange", red: "Red" },
				stats: { random: "Min cost", strength: "Strength", dexterity: "Dexterity", agility: "Agility", constitution: "Constitution", charisma: "Charisma", intelligence: "Intelligence" },
				settings: {
					title: "GH",
					expedition: {
						title: "Expedition",
						description: "Description",
						descriptionContent: "Configuration for automating Expedition",
						location: "Location",
						opponent: "Opponent",
						notAvailableSetting: "Configuration is not available when you are in underworld or travelling",
						autoCollectAllBonuses: "Auto collect all bonuses",
						useHolyHourGlass: "Use Holy Hourglass",
					},
					underworld: {
						title: "Underworld",
						descriptionContent: "Configuration for automating Underworld",
						additionalDescription: "You are in underworld. No need configuration for this section.",
						additionalDescription1: "Configuration for automating Underworld.",
						attackDisPaterAsap: "Attack Dis Pater asap",
						allowUseRuby: "Allow use ruby",
						allowUseMobilisation: "Allow use Mobilisation",
						farmingMode: "Farming purpose",
						farmingCostume: "To get costume",
						farmingItem: "To farm items",
						location: "Location",
						opponent: "Opponent",
						autoEnterUnderworld: "Auto enter underworld",
						normal: "Normal",
						medium: "Medium",
						hard: "Hard",
						currentCostume: "Current costume",
						pauseArena: "Pause arena while in underworld",
						autoWearUWCostume: "Auto wear Underworld costume",
					},
					dungeon: {
						title: "Dungeon",
						description: "Description",
						descriptionContent: "Configuration for automating Dungeon",
						location: "Location",
						isAdvanced: "Is advanced",
						skipBoss: "Skip boss",
						skipBossDescription: " (If you can not defeat boss, turn on this option to skip boss and restart dungeon)",
						bossText: "'Boss' in your language",
						notAvailableSetting: "Configuration is not available when you are in underworld or travelling or in Britannia",
						restartAfterXTimesLost: "Restart after x times lost",
					},
					arena: {
						title: "Arena",
						provinceTitle: "Provinciarum Arena",
						description: "Description",
						descriptionContent: "Configuration for automating Provinciarum Arena",
						preferPlayers: "Attack players cross server",
						sameServerPlayers: "Same server attack list",
						addToAttachList: "Attack if raid over",
						ignorePlayers: "Ignore players",
						preferServers: "Attack servers",
						ignoreServers: "Ignore servers",
						copyToClipboard: "copy to clipboard",
						autoIgnorePlayer: "Auto ignore player if lose",
						attackFiveTimesEnabled: "Limit 5 attacks per day",
						donotRunIfNoQuest: "Pause if no suiable quest",
						addToSameServerAttackList: "Add this player to the same server attack list in Arena",
						removeFromSameServerAttackList: "Remove this player from the same server attack list in Arena",
						attackSameServerEnabled: "Enable attacking same server",
					},
					circusTuma: {
						title: "Circus",
						provinceTitle: "Circus Provinciarum",
						description: "Description",
						descriptionContent: "Configuration for automating Circus Provinciarum",
						preferPlayers: "Attack players cross server",
						sameServerPlayers: "Same server attack list",
						addToAttachList: "Attack if raid over",
						ignorePlayers: "Ignore players",
						preferServers: "Attack servers",
						ignoreServers: "Ignore servers",
						copyToClipboard: "copy to clipboard",
						autoIgnorePlayer: "Auto ignore player if lose",
						donotRunIfNoQuest: "Pause if no suiable quest",
						runUntilGetChest: "Pause after get treasure box",
						addToSameServerAttackList: "Add this player to the same server attack list in Circus Tuma",
						removeFromSameServerAttackList: "Remove this player from the same server attack list in Circus Tuma",
						attackSameServerEnabled: "Enable attacking same server",
					},
					quest: {
						title: "Pantheon",
						description: "Description",
						descriptionContent: "Configuration for picking quest automatically.",
						ruleForPickingQuest: "Picking quest rules",
						descriptionForStandardLicense: "Buy premium license to make picking quests configuable.",
					},
					healing: {
						title: "Heal",
						description: "Description",
						descriptionContent: "Configuration for automating heal life point and some other features",
						healPercent: "Heal once % life below",
						bagOfFoods: "Bag of foods",
						autoHideGold: "Auto hide gold",
						bidOnceGoldOver: "Hide gold once over",
						attackEventMobs: "Auto attack event mobs",
						attackEventBoss: "Attack event boss",
						autoPickQuest: "Auto pick quests",
						guildMedic: "Guild medic",
						healingPotion: "Healing potion",
						underworlHealFunction: "Underworld healing",
						hideGoldIn: "Hide gold in",
						guildMarket: "Guild market",
						guildBank: "Guild bank",
						training: "Training",
						shop: "Shop",
						auctionHouse: "Auction house",
						stopAllIfHealingFail: "Stop Bot if cant heal",
						autoPickQuestInstruction:
							"Note: Currently this checkbox is turned off automatically because you have full of quests or no possible completed quest to pick. It will be turned on automatically when there is any completed or failed quests or after 3 minutes.",
						questOfYourChoice: "of your choice",
						questSuccession: "succession",
						pickingFoodMessage: "Bot is picking food from packages",
						autoBuyFood: "Auto buy food in Auction House",
						buyFoodDescription: "Buy any food which has ratio of life points per gold greater than ",
						allowBotEatCervisia: "Allow Bot eats Cervisia",
						buyFoodFromShop: "Buy food from shop",
						renewShopForFood: "Renew shop for food",
						onlyBuyFoodIfNeed: "Only buy food if need",
					},
					premium: {
						title: "Premium",
						description: "Description",
						descriptionContent: "All features are listed in this section is Premium Feature.",
						allowRequestPack: "Allow request pack",
						messageFolder: "Message folder",
						autoGetFoodFromPackages: "Auto get food from packages",
						packBoughtFromGuildMarket: "Packs bought from Guild Market",
						autoPutPackToGuildMarketDescription: "Enable this option for auto put all gold packs in queue below to Guild Market with selected duration ",
						hours: "hours",
						days: "day(s)",
						noBoughtPack: "There is no pack need to put back in Guild Market",
						autoHideGoldInstruction: "Note: Currently this checkbox is turned off automatically because no item to buy. It will be turned on automatically after 5 minutes.",
						boughtPackHistorySentence: "Bot bought {itemName} from {playerName}",
						errorOnPuttingPack: "Bot can not find the item for putting this pack to guild market. Please do it manualy and remove this pack",
						puttingPackMessage: "Bot is putting pack to Guild Market",
						failedMessage: "Failed",
						minGoldPackCanBuy: "Gold packing range",
						goldPackingRangeHelpText: "Bot will buy any items which has amount of gold in the configuration range",
						autoRepackExpiringItem: "Auto reset timer of expiring items",
						autoRepackExpiringItemDescription:
							"By enable this option, the Bot will sell upcoming expired items from Packages to Guild Market with 2 gold and cancel to reset expiration time. It requires player must in a Guild and the items cooldown less than ",
						selectedItemTypeForRepack: "Item types should repack",
						autoRenewShopOption: "Auto renew shop once full",
						resetEventPoint: "Auto reset event points",
						onlyForPremium: "Available only for Premium License",
						statToTrain: "Training attribute",
						publicMarket: "Public market",
						buyPackFromPlayer: "Only buy from player",
						profileUrlText: "Copy & Paste seller profile's url here and press Enter",
						enableHighlightUnderworldItem: "Highlight underworld items",
						amountOfGoldForPicking: "Max gold amount can pick from packages",
						sellingGoldTarget: "Selling gold target from packages",
						amountOfGoldForPickingDescription: "The bot will ignore any gold item that has amount over setting while picking items from package to inventory",
						autoThrowDice: "Auto throw free dice",
						autoRepairItem: "Auto repair item",
						repairConditioningThrehold: "Repair when conditioning below x%",
						ignoreMaterials: "Repair item without using listed materials",
						itemNameContains: "Name contains",
						auctionHouseItemFilter: "Auction house item filters",
						autoBidMatchedItem: "Auto bid matched items",
						autoCollectGodOils: "Auto collect god oils",
						filterFullWord: "Filter full word",
						itemTypesForFiltering: "Item types for filtering",
						nameContainsWord: "Name contains word",
						repairBeforeSmelt: "Repair before smelt item",
						keepAmountOfGoldOnHand: "Keep amount of gold on hand",
						damage: "Min damage",
						storeResourcesAfterSmelted: "Store smelted resources",
						storeResourcesEveryHour: "Store resources every hour",
						scheduleStartStop: "Schedule start/stop",
						enabled: "Enabled",
						startAt: "Start at",
						stopAt: "Stop at",
						donateGuildBankBeforeStop: "Donate guild bank before stop",
						autoSmeltSettings: "Auto smelting item filters",
						itemColorIsGreen: 'Item color is <b style="color:green">green</b>',
						itemColorIsBlue: 'Item color is <b style="color:blue">blue</b>',
						itemColorIsPurple: 'Item color is <b style="color:purple">purple</b>',
						itemColorIsOrange: 'Item color is <b style="color:orange">orange</b>',
						itemColorIsRed: 'Item color is <b style="color:red">red</b>',
						isUnderworldItem: 'Item has underworld <b style="color:red">prefix/suffix</b>',
					},
					auction: { outbidDuration: "Auction bidding duration", maxOutbidItemPrice: "Max bidding item price" },
					general: {
						title: "General",
						description: "Description",
						descriptionContent: "General settings",
						language: "Language",
						randomDelay: "Random delay",
						randomDelayDescription: "Auto will be delayed a random second from 0 to ",
						second: " second(s)",
						allowRelogin: "Allow relogin",
						licenseKey: "License key",
						validUntil: "Valid until",
						invalidKey: "Invalid key",
						expiredKey: "Key Expired",
						serverTime: "Server time",
						buyLicenseKey: "Upgrade Premium",
						day: "Days",
						year: "Year",
						lifetime: "Lifetime",
						contactDescription: "Your payment account code is ",
						contact: "Contact",
						resetButtonText: "Reset default settings",
						releaseNotes: "Release Notes",
						individualTabRunning: "Individual tab running",
						menuPosition: "Menu position",
						animateItemColor: "Animate item color",
						debugMode: "Debug mode",
						debugModeDescription: "This option is used only for debugging purpose, please don't turn it on.",
					},
					event: { title: "Event", description: "Description", descriptionContent: "Configuration for automating attack Event mobs", stopPoint: "Event points stop at", shouldFollowLeaderScore: "Follow leader score" },
				},
				messages: {
					checkPlayerBelongToGuild: "Checking is player belong to a Guild or not",
					findEmptySlot: "Finding empty space ({w} x {h} cells) in inventory",
					findUpcomingExpiredItem: "Finding upcoming expired item in page ",
					moveItemToInventory: "Moving item to inventory",
					sellItemToGuildMarket: "Selling item to Guild Market",
					cancelItemInGuildMarket: "Cancelling item in Guild Market",
					sellItemToShop: "Selling item to shop",
					gettingShopInformation: "Checking empty space in shop",
					gettingPackagePage: "Getting items information from Package page ",
					shopIsFull: "Shop is full",
					inventoryIsFull: "No space (2x3) in inventory for picking up item from Package",
					renewShop: "Renewing shop",
					checkingSmelterState: "Checking smelter state",
					runningAutoSmelt: "Auto smelt is running",
					buyingItemFromShop: "Buying back item from shop",
					checkingDollItemForRepair: "Checking items need to repair on doll ",
					searchingItemsInAuctionHouse: "Searching items in auction house",
					rentingSmelterySlot: "renting smeltery slot",
					isHealing: "Bot is healing",
					buyingFoodFromShop: "Buying food from shop",
					noEmptySpaceInInventory: "No space {w} x {h} in inventory for buying food for buying food",
				},
			},
			packages: {
				pickingSectionTitle: "Select items type for picking",
				pickAll: "Pick all",
				pickAllConfirmation: "Are you sure you want to pick all items in this page (except gold)?",
				pickGoldConfirmation: "Are you sure you want to pick selected items including gold pack (over {maxAmount} gold pack will be ignored)?",
				pickSelectedType: "Pick selected types",
				sendAllResourceToHorreum: "Store resources",
				itemType: "Item types",
				itemQuality: "Item qualities",
				stop: "Stop",
				sellSelectedTypeToMerchants: "Sell to shop",
				selectItemForAutoSmelt: "Select item for auto smelt",
			},
			merchants: { sellAll: "Sell all", sellSelectedType: "Sell selected types", stop: "Stop", sellSectionTitle: "Select item types for sale" },
			guildMarket: { quickAction: "Quick actions", offerAs1Gold: "Offer item as 1 gold", stop: "Stop" },
			publicMarket: { quickAction: "Quick actions", quickOffer: "Quick offer", stop: "Stop" },
			smeltery: {
				action: "Actions",
				noSlot: "There is no slot to melt.",
				startMelt: "Start melt",
				stopMelt: "Stop melt",
				sendAllToHorreum: "Send all to Horreum",
				noFinishedItem: "There is no finished item.",
				notEnoughGold: "You've not enough gold.",
				itemTypesForSmelt: "Item types for auto smelt",
				itemQualitiesForSmelt: "Item qualities for auto smelt",
			},
			workbench: {
				action: "Actions",
				startFix: "Full fix",
				partialFix: "Partial fix",
				stopFix: "Stop fix",
				noSlot: "There is no slot to fixing item.",
				notEnoughGold: "You've not enough gold.",
				sendToPackage: "Send all to package",
				noFinishedItem: "There is no finished item.",
				maxMaterialQuality: "Max used material quality",
				notHaveMaterial: "Dont have any required material",
			},
			location: {
				level1: ["Grimwood", "Rat", "Lynx", "Wolf", "Bear"],
				level5: ["Pirate Harbour", "Fled Slave", "Corrupt Soldier", "Assasin", "Captain"],
				level10: ["Misty Mountains", "Elusive Recruit", "Harpy", "Cerberus", "Medusa"],
				level15: ["Wolf Cave", "Wild Boar", "Wolf Pack", "Alphawolf", "Werewolf"],
				level60: ["Ancient Temple", "Cultist Guard", "Wererat", "Minotaur", "Minotaur Chief"],
				level65: ["Barbarian Village", "Barbarian", "Barbarian Warrior", "Berserker", "Barbarian Chief"],
				level70: ["Bandit Camp", "Renegade Soldier", "Renegade Mercenary", "Assassinator", "Bandit Chief"],
				level40: ["Cave Temple", "Legionnaire", "Myrmidon", "Centurion", "Soulless"],
				level45: ["The Green Forest", "Giant Wild Boar", "Swamp Lord", "Swamp Spirit", "Werebear"],
				level50: ["Cursed Village", "Hun", "Ancient", "Nachzehrer", "Abomination"],
				level55: ["Death Hill", "Skeleton Warrior", "Skeleton Berserker", "Lich", "Necromancer Prince"],
				level95: ["Vandal Village", "Vandal Warrior", "Jarl", "Dark Fighter", "Death Knight"],
				level100: ["Mine", "Guard", "Draug", "Stone Golem", "Tatzelwurm"],
				level104: ["Teuton Camp", "Barbarian", "Teuton Hero", "Teuton Lord", "Seidr"],
				level108: ["Koman Mountain", "Infernal Springbok", "Sabre-Tooth Tiger", "Dragon Whelp", "Dragon"],
				level112: ["Dragon Remains", "Bone Golem", "Lemures", "Ritualist", "Dracolich"],
				level20: ["Voodoo Temple", "Cobra", "Giant Scorpion", "Awakened Mummy", "Seth Priest"],
				level25: ["Bridge", "Tax Collector", "Man Eater", "Tribal Warrior", "Bone Shaman"],
				level30: ["Blood Cave", "Blood Wolf", "Giant Beetle", "Fire Dancer", "Fire Demon"],
				level35: ["Lost Harbour", "Crocodile", "Undead Holder", "Giant Water Snake", "Mokele Mbembe"],
				level75: ["Umpokta Tribe", "Tribal Warrior", "Tribal Magician", "Spirit Warrior", "Seth High Priest"],
				level80: ["Caravan", "Spy", "Caravan Guard", "Elite Guard", "Slave Merchant"],
				level85: ["Mesoai Oasis", "Elephant", "Cheetah", "Demon Lion", "Demon Elephant"],
				level90: ["Cliff Jumper", "Cursed Antelope", "Giant Spider", "Shaman", "High Shaman"],
				level120: ["Bank of Thames", "Bibroci", "Ancalite", "Cenimagni", "Cassi"],
				level130: ["Forest Fortress", "Wood Elf", "Dwarf", "British Chariot", "Callirius"],
				level140: ["The Moor", "Тhe man from...", "Тhe woman from...", "Bandit", "Nodens"],
				level150: ["Camp Cassivellaunus", "Chariot Rider", "Mercenary", "Fluror", "Cassivellaunus"],
				level160: ["Kent", "Cingetorix", "Segovax", "Carvilius", "Taximagulus"],
				level170: ["The Ford", "Leech", "Water Spider", "Caratacus", "Opponent_boss"],
				level180: ["Camulodunum", "City Guard", "Settler Trinovantes", "Warrior Trinovantes", "Caratacus"],
				level190: ["Cambria", "Opponent_1", "Opponent_2", "Opponent_3", "Opponent_4"],
				level200: ["Mona Isle", "Opponent_1", "Opponent_2", "Opponent_3", "Opponent_4"],
			},
			dungeon: {
				level1: ["Gustavos Country House"],
				level5: ["On the Run"],
				level10: ["The Dragon Stronghold"],
				level15: ["The Cave of Dark intrigue"],
				level20: ["Temple of Perdition"],
				level25: ["Abducted"],
				level30: ["Chamber of Pyro"],
				level35: ["Poisoned Country"],
				level40: ["Dark Catacombs"],
				level45: ["With all its might"],
				level50: ["Viking Camp"],
				level60: ["Hidden Grave"],
				level65: ["In enemy hands"],
				level68: ["The Last Resort"],
				level70: ["The True Owner"],
				level73: ["Gioll Passage"],
				level78: ["Zagrash's Fort"],
				level80: ["Mysterious Laboratory", "Fairground"],
				level86: ["Under a Blood-red Sky"],
				level90: ["In the Heart of Decay"],
				level92: ["Sasama's last journey"],
				level102: ["Externsteine"],
				level112: ["Late Revenge"],
				level120: ["Alpha & Omega"],
			},
		},
		bagpack = { 512: 0, 513: 1, 514: 2, 515: 3 },
		gh_locationData = {
			level1: ["Grimwood", "Rat", "Lynx", "Wolf", "Bear"],
			level5: ["Pirate Harbour", "Fled Slave", "Corrupt Soldier", "Assasin", "Captain"],
			level10: ["Misty Mountains", "Elusive Recruit", "Harpy", "Cerberus", "Medusa"],
			level15: ["Wolf Cave", "Wild Boar", "Wolf Pack", "Alphawolf", "Werewolf"],
			level60: ["Ancient Temple", "Cultist Guard", "Wererat", "Minotaur", "Minotaur Chief"],
			level65: ["Barbarian Village", "Barbarian", "Barbarian Warrior", "Berserker", "Barbarian Chief"],
			level70: ["Bandit Camp", "Renegade Soldier", "Renegade Mercenary", "Assassinator", "Bandit Chief"],
			level40: ["Cave Temple", "Legionnaire", "Myrmidon", "Centurion", "Soulless"],
			level45: ["The Green Forest", "Giant Wild Boar", "Swamp Lord", "Swamp Spirit", "Werebear"],
			level50: ["Cursed Village", "Hun", "Ancient", "Nachzehrer", "Abomination"],
			level55: ["Death Hill", "Skeleton Warrior", "Skeleton Berserker", "Lich", "Necromancer Prince"],
			level95: ["Vandal Village", "Vandal Warrior", "Jarl", "Dark Fighter", "Death Knight"],
			level100: ["Mine", "Guard", "Draug", "Stone Golem", "Tatzelwurm"],
			level104: ["Teuton Camp", "Barbarian", "Teuton Hero", "Teuton Lord", "Seidr"],
			level108: ["Koman Mountain", "Infernal Springbok", "Sabre-Tooth Tiger", "Dragon Whelp", "Dragon"],
			level112: ["Dragon Remains", "Bone Golem", "Lemures", "Ritualist", "Dracolich"],
			level20: ["Voodoo Temple", "Cobra", "Giant Scorpion", "Awakened Mummy", "Seth Priest"],
			level25: ["Bridge", "Tax Collector", "Man Eater", "Tribal Warrior", "Bone Shaman"],
			level30: ["Blood Cave", "Blood Wolf", "Giant Beetle", "Fire Dancer", "Fire Demon"],
			level35: ["Lost Harbour", "Crocodile", "Undead Holder", "Giant Water Snake", "Mokele Mbembe"],
			level75: ["Umpokta Tribe", "Tribal Warrior", "Tribal Magician", "Spirit Warrior", "Seth High Priest"],
			level80: ["Caravan", "Spy", "Caravan Guard", "Elite Guard", "Slave Merchant"],
			level85: ["Mesoai Oasis", "Elephant", "Cheetah", "Demon Lion", "Demon Elephant"],
			level90: ["Cliff Jumper", "Cursed Antelope", "Giant Spider", "Shaman", "High Shaman"],
			level120: ["Bank of Thames", "Bibroci", "Ancalite", "Cenimagni", "Cassi"],
			level130: ["Forest Fortress", "Wood Elf", "Dwarf", "British Chariot", "Callirius"],
			level140: ["The Moor", "Тhe man from...", "Тhe woman from...", "Bandit", "Nodens"],
			level150: ["Camp Cassivellaunus", "Chariot Rider", "Mercenary", "Fluror", "Cassivellaunus"],
			level160: ["Kent", "Cingetorix", "Segovax", "Carvilius", "Taximagulus"],
			level170: ["The Ford", "Leech", "Water Spider", "Caratacus", "Togodumnus"],
			level180: ["Camulodunum", "City Guard", "Settler Trinovantes", "Warrior Trinovantes", "Caratacus"],
			level190: ["Cambria", "Opponent_1", "Opponent_2", "Opponent_3", "Opponent_4"],
			level200: ["Mona Isle", "Opponent_1", "Opponent_2", "Opponent_3", "Opponent_4"],
		},
		gh_dungeon = {
			level1: ["Gustavos Country House"],
			level5: ["On the Run"],
			level10: ["The Dragon Stronghold"],
			level15: ["The Cave of Dark intrigue"],
			level20: ["Temple of Perdition"],
			level25: ["Abducted"],
			level30: ["Chamber of Pyro"],
			level35: ["Poisoned Country"],
			level40: ["Dark Catacombs"],
			level45: ["With all its might"],
			level50: ["Viking Camp"],
			level60: ["Hidden Grave"],
			level65: ["In enemy hands"],
			level68: ["The Last Resort"],
			level70: ["The True Owner"],
			level73: ["Gioll Passage"],
			level78: ["Zagrash's Fort"],
			level80: ["Mysterious Laboratory", "Fairground"],
			level86: ["Under a Blood-red Sky"],
			level90: ["In the Heart of Decay"],
			level92: ["Sasama's last journey"],
			level102: ["Externsteine"],
			level112: ["Late Revenge"],
			level120: ["Alpha & Omega"],
		},
		gh_mobData = {
			britannia: {
				locations: [
					{ id: 0, name: gh_locationData.level120[0], level: 120, opponents: [gh_locationData.level120[1], gh_locationData.level120[2], gh_locationData.level120[3], gh_locationData.level120[4]], dungeonLevels: [] },
					{ id: 1, name: gh_locationData.level130[0], level: 130, opponents: [gh_locationData.level130[1], gh_locationData.level130[2], gh_locationData.level130[3], gh_locationData.level130[4]], dungeonLevels: [] },
					{ id: 2, name: gh_locationData.level140[0], level: 140, opponents: [gh_locationData.level140[1], gh_locationData.level140[2], gh_locationData.level140[3], gh_locationData.level140[4]], dungeonLevels: [] },
					{ id: 3, name: gh_locationData.level150[0], level: 150, opponents: [gh_locationData.level150[1], gh_locationData.level150[2], gh_locationData.level150[3], gh_locationData.level150[4]], dungeonLevels: [] },
					{ id: 4, name: gh_locationData.level160[0], level: 160, opponents: [gh_locationData.level160[1], gh_locationData.level160[2], gh_locationData.level160[3], gh_locationData.level160[4]], dungeonLevels: [] },
					{ id: 5, name: gh_locationData.level170[0], level: 170, opponents: [gh_locationData.level170[1], gh_locationData.level170[2], gh_locationData.level170[3], gh_locationData.level170[4]], dungeonLevels: [] },
					{ id: 6, name: gh_locationData.level180[0], level: 180, opponents: [gh_locationData.level180[1], gh_locationData.level180[2], gh_locationData.level180[3], gh_locationData.level180[4]], dungeonLevels: [] },
					{ id: 7, name: gh_locationData.level190[0], level: 190, opponents: [gh_locationData.level190[1], gh_locationData.level190[2], gh_locationData.level190[3], gh_locationData.level190[4]], dungeonLevels: [] },
					{ id: 8, name: gh_locationData.level200[0], level: 200, opponents: [gh_locationData.level200[1], gh_locationData.level200[2], gh_locationData.level200[3], gh_locationData.level200[4]], dungeonLevels: [] },
				],
			},
			italy: {
				locations: [
					{
						id: 0,
						name: gh_locationData.level1[0],
						level: 1,
						opponents: [gh_locationData.level1[1], gh_locationData.level1[2], gh_locationData.level1[3], gh_locationData.level1[4]],
						dungeonLevels: [10],
						dungeonNames: [gh_dungeon.level1],
					},
					{
						id: 1,
						name: gh_locationData.level5[0],
						level: 5,
						opponents: [gh_locationData.level5[1], gh_locationData.level5[2], gh_locationData.level5[3], gh_locationData.level5[4]],
						dungeonLevels: [10, 68],
						dungeonNames: [gh_dungeon.level5],
					},
					{
						id: 2,
						name: gh_locationData.level10[0],
						level: 10,
						opponents: [gh_locationData.level10[1], gh_locationData.level10[2], gh_locationData.level10[3], gh_locationData.level10[4]],
						dungeonLevels: [10, 70],
						dungeonNames: [gh_dungeon.level10],
					},
					{
						id: 3,
						name: gh_locationData.level15[0],
						level: 15,
						opponents: [gh_locationData.level15[1], gh_locationData.level15[2], gh_locationData.level15[3], gh_locationData.level15[4]],
						dungeonLevels: [15, 73],
						dungeonNames: [gh_dungeon.level15],
					},
					{
						id: 4,
						name: gh_locationData.level60[0],
						level: 60,
						opponents: [gh_locationData.level60[1], gh_locationData.level60[2], gh_locationData.level60[3], gh_locationData.level60[4]],
						dungeonLevels: [60],
						dungeonNames: [gh_dungeon.level60],
					},
					{
						id: 5,
						name: gh_locationData.level65[0],
						level: 65,
						opponents: [gh_locationData.level65[1], gh_locationData.level65[2], gh_locationData.level65[3], gh_locationData.level65[4]],
						dungeonLevels: [65, 78],
						dungeonNames: [gh_dungeon.level65, gh_dungeon.level78],
					},
					{ id: 6, name: gh_locationData.level70[0], level: 70, opponents: [gh_locationData.level70[1], gh_locationData.level70[2], gh_locationData.level70[3], gh_locationData.level70[4]], dungeonLevels: [] },
				],
			},
			africa: {
				locations: [
					{
						id: 0,
						name: gh_locationData.level20[0],
						level: 20,
						opponents: [gh_locationData.level20[1], gh_locationData.level20[2], gh_locationData.level20[3], gh_locationData.level20[4]],
						dungeonLevels: [20],
						dungeonNames: [gh_dungeon.level20],
					},
					{
						id: 1,
						name: gh_locationData.level25[0],
						level: 25,
						opponents: [gh_locationData.level25[1], gh_locationData.level25[2], gh_locationData.level25[3], gh_locationData.level25[4]],
						dungeonLevels: [25, 86],
						dungeonNames: [gh_dungeon.level25, gh_dungeon.level86],
					},
					{
						id: 2,
						name: gh_locationData.level30[0],
						level: 30,
						opponents: [gh_locationData.level30[1], gh_locationData.level30[2], gh_locationData.level30[3], gh_locationData.level30[4]],
						dungeonLevels: [30],
						dungeonNames: [gh_dungeon.level30],
					},
					{
						id: 3,
						name: gh_locationData.level35[0],
						level: 35,
						opponents: [gh_locationData.level35[1], gh_locationData.level35[2], gh_locationData.level35[3], gh_locationData.level35[4]],
						dungeonLevels: [35, 90],
						dungeonNames: [gh_dungeon.level35, gh_dungeon.level90],
					},
					{
						id: 4,
						name: gh_locationData.level75[0],
						level: 75,
						opponents: [gh_locationData.level75[1], gh_locationData.level75[2], gh_locationData.level75[3], gh_locationData.level75[4]],
						dungeonLevels: [80, 92],
						dungeonNames: [gh_dungeon.level80, gh_dungeon.level92],
					},
					{ id: 5, name: gh_locationData.level80[0], level: 80, opponents: [gh_locationData.level80[1], gh_locationData.level80[2], gh_locationData.level80[3], gh_locationData.level80[4]], dungeonLevels: [] },
					{ id: 6, name: gh_locationData.level85[0], level: 85, opponents: [gh_locationData.level85[1], gh_locationData.level85[2], gh_locationData.level85[3], gh_locationData.level85[4]], dungeonLevels: [] },
					{ id: 7, name: gh_locationData.level90[0], level: 90, opponents: [gh_locationData.level90[1], gh_locationData.level90[2], gh_locationData.level90[3], gh_locationData.level90[4]], dungeonLevels: [] },
				],
			},
			germany: {
				locations: [
					{
						id: 0,
						name: gh_locationData.level40[0],
						level: 40,
						opponents: [gh_locationData.level40[1], gh_locationData.level40[2], gh_locationData.level40[3], gh_locationData.level40[4]],
						dungeonLevels: [40],
						dungeonNames: [gh_dungeon.level40],
					},
					{
						id: 1,
						name: gh_locationData.level45[0],
						level: 45,
						opponents: [gh_locationData.level45[1], gh_locationData.level45[2], gh_locationData.level45[3], gh_locationData.level45[4]],
						dungeonLevels: [45, 102],
						dungeonNames: [gh_dungeon.level45, gh_dungeon.level102],
					},
					{
						id: 2,
						name: gh_locationData.level50[0],
						level: 50,
						opponents: [gh_locationData.level50[1], gh_locationData.level50[2], gh_locationData.level50[3], gh_locationData.level50[4]],
						dungeonLevels: [50],
						dungeonNames: [gh_dungeon.level50],
					},
					{
						id: 3,
						name: gh_locationData.level55[0],
						level: 55,
						opponents: [gh_locationData.level55[1], gh_locationData.level55[2], gh_locationData.level55[3], gh_locationData.level55[4]],
						dungeonLevels: [80],
						dungeonNames: [gh_dungeon.level80],
					},
					{ id: 4, name: gh_locationData.level95[0], level: 95, opponents: [gh_locationData.level95[1], gh_locationData.level95[2], gh_locationData.level95[3], gh_locationData.level95[4]], dungeonLevels: [] },
					{ id: 5, name: gh_locationData.level100[0], level: 100, opponents: [gh_locationData.level100[1], gh_locationData.level100[2], gh_locationData.level100[3], gh_locationData.level100[4]], dungeonLevels: [] },
					{ id: 6, name: gh_locationData.level104[0], level: 104, opponents: [gh_locationData.level104[1], gh_locationData.level104[2], gh_locationData.level104[3], gh_locationData.level104[4]], dungeonLevels: [] },
					{ id: 7, name: gh_locationData.level108[0], level: 108, opponents: [gh_locationData.level108[1], gh_locationData.level108[2], gh_locationData.level108[3], gh_locationData.level108[4]], dungeonLevels: [] },
					{
						id: 9,
						name: gh_locationData.level112[0],
						level: 112,
						opponents: [gh_locationData.level112[1], gh_locationData.level112[2], gh_locationData.level112[3], gh_locationData.level112[4]],
						dungeonLevels: [112, 120],
						dungeonNames: [gh_dungeon.level112, gh_dungeon.level120],
					},
				],
			},
		},
		gh_questType = { combat: 1, item: 2, groupArena: 3, arena: 4, expedition: 5, dungeon: 6, work: 7, any: 8 },
		gh_filterOperators = {
			contains: 1,
			notContains: 2,
			numberAttackLessThan: 3,
			hasNoTimer: 4,
			itemContains: 5,
			rewardIsFood: 6,
			hasExperience: 7,
			itemNotContains: 8,
			hasApollo: 9,
			hasVulcan: 10,
			hasMars: 11,
			hasMercury: 12,
			hasDiana: 13,
			hasMinerva: 14,
			rewardIsNotFood: 15,
			rewardContainsAny: 16,
			minGold: 17,
			minHonor: 18,
		},
		gh_godImageMaps = { apollo_s16: "apollo", merkur_s16: "mercury", vulcanus_s16: "vulcan", mars_s16: "mars", diana_s16: "diana", minerva_s16: "minerva" },
		gh_qualityMap = ["white", "green", "blue", "purple", "orange", "red"],
		gh_defaultIgnoreUWMaterials = [54, 55, 56, 57, 60, 64],
		gh_forgeMode = { forge: "forge", smeltery: "smeltery", workbench: "workbench" },
		gh_auctionState = { verylong: 0, long: 1, middle: 2, short: 3, veryshort: 4 },
		gh_auctionStateMap = {
			br: { veryshort: "bastante curto", short: "curto", middle: "médio", long: "longo", verylong: "muito tempo" },
			cz: { veryshort: "velmi krátká", short: "krátká", middle: "střední", long: "dlouhá", verylong: "velmi dlouhá" },
			dk: { veryshort: "meget kort tid", short: "kort tid", middle: "halv tid", long: "lang tid", verylong: "meget lang tid" },
			de: { veryshort: "sehr kurz", short: "kurz", middle: "mittel", long: "lange", verylong: "sehr lange" },
			ee: { veryshort: "väga lühike", short: "lühike", middle: "keskmine", long: "pikk", verylong: "väga pikk" },
			en: { veryshort: "very short", short: "short", middle: "middle", long: "long", verylong: "very long" },
			us: { veryshort: "very short", short: "short", middle: "middle", long: "long", verylong: "very long" },
			ar: { veryshort: "muy corto", short: "corto", middle: "medio", long: "largo", verylong: "muy largo" },
			es: { veryshort: "muy corto", short: "corto", middle: "medio", long: "largo", verylong: "muy largo" },
			mx: { veryshort: "muy corto", short: "corto", middle: "medio", long: "largo", verylong: "muy largo" },
			fr: { veryshort: "très court", short: "court", middle: "moyen", long: "longtemps", verylong: "très longtemps" },
			it: { veryshort: "brevissima", short: "breve", middle: "media", long: "lunga", verylong: "lunghissima" },
			lv: { veryshort: "ļoti īss", short: "īss", middle: "vidējs", long: "garš", verylong: "ļoti garš" },
			lt: { veryshort: "labai trumpai", short: "trumpai", middle: "vidutiniškai", long: "ilgai", verylong: "labai ilgai" },
			hu: { veryshort: "nagyon rövid", short: "rövid", middle: "közepes", long: "hosszú", verylong: "nagyon hosszú" },
			nl: { veryshort: "zeer kort", short: "kort", middle: "gemiddeld", long: "lang", verylong: "heel lang" },
			no: { veryshort: "veldig kort", short: "kortvarig", middle: "medium", long: "lenge", verylong: "veldig lenge" },
			pl: { veryshort: "bardzo krótki", short: "krótki", middle: "średni", long: "długi", verylong: "bardzo długi" },
			pt: { veryshort: "muito curto", short: "curto", middle: "médio", long: "longo", verylong: "muito longo" },
			ro: { veryshort: "foarte scurt", short: "scurt", middle: "mijlociu", long: "lung", verylong: "foarte lung" },
			sk: { veryshort: "veľmi krátko", short: "krátko", middle: "stredne", long: "dlho", verylong: "veľmi dlho" },
			yu: { veryshort: "jako kratko", short: "kratko", middle: "srednje", long: "dugo", verylong: "jako dugo" },
			fi: { veryshort: "hyvin lyhyt", short: "lyhyt", middle: "keskimääräinen", long: "pitkä", verylong: "hyvin pitkä" },
			se: { veryshort: "mycket kort", short: "kort", middle: "medel", long: "lång", verylong: "mycket lång" },
			tr: { veryshort: "çok kısa", short: "kısa", middle: "orta", long: "uzun", verylong: "çok uzun" },
			ae: { veryshort: "قصيرة جداً", short: "قصير", middle: "منتصف", long: "طويل", verylong: "طويل جداً" },
			il: { veryshort: "קצר מאוד", short: "קצר", middle: "בינוני", long: "ארוך", verylong: "ארוך מאוד" },
			gr: { veryshort: "πολύ μικρή", short: "μικρή", middle: "mέση", long: "μεγάλη", verylong: "πολύ μεγάλη" },
			bg: { veryshort: "много къс", short: "къс", middle: "среден", long: "дълъг", verylong: "много дълъг" },
			ru: { veryshort: "очень мало", short: "мало", middle: "средне", long: "много", verylong: "очень много" },
			tw: { veryshort: "超短", short: "較短", middle: "常規時間", long: "較長", verylong: "超長" },
		},
		gh_itemTypeValues = {
			all: 0,
			weapons: 1,
			shields: 2,
			chestArmour: 3,
			helmets: 4,
			gloves: 5,
			rings: 6,
			usable: 7,
			shoes: 8,
			amulets: 9,
			reinforcements: 11,
			upgrades: 12,
			recipes: 13,
			gold: 14,
			mercenary: 15,
			forgingGoods: 18,
			tools: 19,
			scroll: 20,
			eventItems: 21,
		},
		gh_operators = { isWD: "wd", isNWD: "nwd", containsR: "conr", nContainsN: "nocon", containsN: "con", contains: "co", containsWord: "cow", greaterThan: "gt", lessThan: "lt", startWith: "sw", endWith: "ew", isTrue: "is" },
		gh_itemQuality = { white: -1, green: 0, blue: 1, purple: 2, orange: 3, red: 4 },
		Nh = [173, 156, 162, 182, 175, 183, 180, 178, 177, 158, 179, 174, 172, 155, 171, 168, 167, 159, 166, 176, 164, 157, 181, 169, 161, 163, 165],
		Oh = [
			258,
			284,
			275,
			269,
			283,
			282,
			287,
			272,
			194,
			259,
			261,
			241,
			166,
			266,
			246,
			248,
			277,
			247,
			270,
			202,
			243,
			242,
			279,
			254,
			274,
			256,
			245,
			250,
			268,
			244,
			281,
			257,
			263,
			278,
			276,
			289,
			262,
			280,
			286,
			267,
			271,
			252,
			255,
			288,
			260,
			264,
			265,
		],
		gh_dollItemContentType = [1, 2, 4, 48, 8, 256, 512, 1024],
		gh_defaultSelectedQualityForPicking = ["-1", "0", "1", "2", "3", "4"],
		gh_itemSubTypes = [
			{ name: gh_lang.global.grindstone, value: "12-2" },
			{ name: gh_lang.global.smallGrindstone, value: "12-1" },
			{ name: gh_lang.global.shredLeather, value: "12-3" },
			{ name: gh_lang.global.protectiveGear, value: "12-4" },
			{ name: gh_lang.global.godOils, value: "12-18" },
			{ name: gh_lang.global.godOils, value: "12-19" },
			{ name: gh_lang.global.godOils, value: "12-20" },
			{ name: gh_lang.global.godOils, value: "12-21" },
			{ name: gh_lang.global.godOils, value: "12-22" },
			{ name: gh_lang.global.godOils, value: "12-23" },
		],
		gh_itemQualities = [
			{ name: gh_lang.global.qualities.white, value: "-1" },
			{ name: gh_lang.global.qualities.green, value: "0" },
			{ name: gh_lang.global.qualities.blue, value: "1" },
			{ name: gh_lang.global.qualities.purple, value: "2" },
			{ name: gh_lang.global.qualities.orange, value: "3" },
			{ name: gh_lang.global.qualities.red, value: "4" },
		],
		gh_itemTypes = [
			{ name: gh_lang.global.weapons, value: gh_itemTypeValues.weapons + "" },
			{ name: gh_lang.global.shields, value: gh_itemTypeValues.shields + "" },
			{ name: gh_lang.global.chestArmour, value: gh_itemTypeValues.chestArmour + "" },
			{ name: gh_lang.global.helmets, value: gh_itemTypeValues.helmets + "" },
			{ name: gh_lang.global.gloves, value: gh_itemTypeValues.gloves + "" },
			{ name: gh_lang.global.shoes, value: gh_itemTypeValues.shoes + "" },
			{ name: gh_lang.global.rings, value: gh_itemTypeValues.rings + "" },
			{ name: gh_lang.global.amulets, value: gh_itemTypeValues.amulets + "" },
			{ name: gh_lang.global.usable, value: gh_itemTypeValues.usable + "" },
			{ name: gh_lang.global.upgrades, value: gh_itemTypeValues.upgrades + "" },
			{ name: gh_lang.global.recipes, value: gh_itemTypeValues.recipes + "" },
			{ name: gh_lang.global.mercenary, value: gh_itemTypeValues.mercenary + "" },
			{ name: gh_lang.global.tools, value: gh_itemTypeValues.tools + "" },
			{ name: gh_lang.global.scroll, value: gh_itemTypeValues.scroll + "" },
			{ name: gh_lang.global.reinforcements, value: gh_itemTypeValues.reinforcements + "" },
			{ name: gh_lang.global.eventItems, value: gh_itemTypeValues.eventItems + "" },
			{ name: gh_lang.global.forgingGoods, value: gh_itemTypeValues.forgingGoods + "" },
			{ name: gh_lang.global.gold, value: gh_itemTypeValues.gold + "" },
		],
		gh_itemSubTypesIgnoreSell = ["12-1", "12-2", "12-3", "12-4", "12-18", "12-19", "12-20", "12-21", "12-22", "12-23"],
		gh_renewShopOptions = [
			{ name: "Not allow", value: 0 },
			{ name: "Only use Working Clothe", value: 1 },
			{ name: "Allow use Ruby or Working Clothe", value: 2 },
		],
		packHideTypes = { guild: "guild", shop: "shop", auction: "auction", training: "training", market: "market", donate: "donate" },
		gh_characterStats = [
			{ name: gh_lang.global.stats.random, value: 0 },
			{ name: gh_lang.global.stats.strength, value: 1 },
			{ name: gh_lang.global.stats.dexterity, value: 2 },
			{ name: gh_lang.global.stats.agility, value: 3 },
			{ name: gh_lang.global.stats.constitution, value: 4 },
			{ name: gh_lang.global.stats.charisma, value: 5 },
			{ name: gh_lang.global.stats.intelligence, value: 6 },
		],
		gh_underworldFarmingMode = { costume: "costume", item: "item" };
	var __async = (n, l, c) =>
		new Promise((d, h) => {
			var g = (m) => {
				try {
					p(c.next(m));
				} catch (y) {
					h(y);
				}
			},
				f = (m) => {
					try {
						p(c.throw(m));
					} catch (y) {
						h(y);
					}
				},
				p = (m) => (m.done ? d(m.value) : Promise.resolve(m.value).then(g, f));
			p((c = c.apply(n, l)).next());
		});
	class ExtensionStore {
		constructor() {
			(this.i = {}), this.l();
		}
		u(l) {
			return __async(this, null, function* () {
				try {
					this.i = yield this.m(l);
				} catch { }
			});
		}
		m(l) {
			return new Promise((c) => {
				chrome.runtime.sendMessage(ghExtensionId, { type: "GET_STORAGE_DATA", playerId: l }, (d) => {
					chrome.runtime.lastError ? c({}) : c(d.data);
				});
			});
		}
		_(l) {
			return new Promise((c, d) => {
				chrome.runtime.sendMessage(ghExtensionId, { type: "SET_STORAGE_DATA", data: l }, (h) => {
					chrome.runtime.lastError ? d(chrome.runtime.lastError) : c(h.success);
				});
			});
		}
		l() {
			typeof window < "u"
				? window.addEventListener("storageChange", (l) => {
					this.v(l.detail);
				})
				: typeof self < "u" &&
				self.addEventListener("message", (l) => {
					l.data && l.data.type === "storageChange" && this.v(l.data.detail);
				});
		}
		v(l) {
			for (const [c, { newValue: d }] of Object.entries(l)) this.i[c] = d;
		}
		k(l, c) {
			var d;
			return (d = this.i[l]) != null ? d : c;
		}
	}
	const extensionStore = new ExtensionStore(),
		_initExtensionStore = (n) =>
			__async(void 0, null, function* () {
				return yield extensionStore.u(n), extensionStore;
			}),
		ro = {
			I: !1,
			S: !1,
			D: !1,
			q: !1,
			A: !1,
			C: !1,
			P: !1,
			T: !1,
			j: !1,
			M: !1,
			R: !1,
			U: !1,
			B: !1,
			F: !1,
			L: !1,
			N: !1,
			O: !1,
			G: !1,
			H: !1,
			W: !1,
			X: !1,
			J: !1,
			V: !1,
			Y: !1,
			K: null,
			Z: null,
			get tt() {
				return this.V;
			},
			set tt(n) {
				(this.V = n), this.et();
			},
			get nt() {
				return this.Y;
			},
			set nt(n) {
				(this.Y = n), this.et();
			},
			get rt() {
				return this.L;
			},
			set rt(n) {
				(this.L = n), this.et();
			},
			get st() {
				return this.O;
			},
			set st(n) {
				(this.O = n), this.et();
			},
			get ot() {
				return this.N;
			},
			set ot(n) {
				(this.N = n), this.et();
			},
			get ct() {
				return this.F;
			},
			set ct(n) {
				(this.F = n), this.et();
			},
			get ut() {
				return this.B;
			},
			set ut(n) {
				(this.B = n), this.et();
			},
			get dt() {
				return this.j;
			},
			set dt(n) {
				(this.j = n), this.et();
			},
			get ht() {
				return this.R;
			},
			set ht(n) {
				(this.R = n), this.et();
			},
			get ft() {
				return this.U;
			},
			set ft(n) {
				(this.U = n), this.et();
			},
			get gt() {
				return this.M;
			},
			set gt(n) {
				(this.M = n), this.et();
			},
			get yt() {
				return this.S;
			},
			set yt(n) {
				(this.S = n), this.et();
			},
			get wt() {
				return this.I;
			},
			set wt(n) {
				(this.I = n), this.et();
			},
			get _t() {
				return this.T;
			},
			set _t(n) {
				(this.T = n), this.et();
			},
			get vt() {
				return this.P;
			},
			set vt(n) {
				(this.P = n), this.et();
			},
			get kt() {
				return this.D;
			},
			set kt(n) {
				(this.D = n), this.et();
			},
			get It() {
				return this.q;
			},
			set It(n) {
				(this.q = n), this.et();
			},
			get St() {
				return this.A;
			},
			set St(n) {
				(this.A = n), this.et();
			},
			get Dt() {
				return this.C;
			},
			set Dt(n) {
				(this.C = n), this.et();
			},
			get qt() {
				return this.G;
			},
			set qt(n) {
				(this.G = n), this.et();
			},
			get At() {
				return this.H;
			},
			set At(n) {
				(this.H = n), this.et();
			},
			get Ct() {
				return this.W;
			},
			set Ct(n) {
				(this.W = n), this.et();
			},
			get Pt() {
				return this.X;
			},
			set Pt(n) {
				(this.X = n), this.et();
			},
			get xt() {
				return this.J;
			},
			set xt(n) {
				(this.J = n), this.et();
			},
			et(n = !1) {
				st.Tt("413fb5101d10271de1a323b", {
					isAttacking: this.wt,
					isCheckingQuests: this.yt,
					isBuyingPack: this.kt,
					isSellingPack: this.It,
					isBuyingFood: this.St,
					isHealing: this.Dt,
					isTraveling: this.vt,
					isRotatingItems: this._t,
					isBuffing: this.dt,
					isRepairing: this.gt,
					isSmelting: this.ht,
					isPickingItems: this.ut,
					isSellingItems: this.ct,
					isEquippingCostumes: this.rt,
					isAuction: this.ot,
					isCheckingSmelting: this.st,
					isCheckingMarket: this.qt,
					isSellingMarket: this.At,
					isActivatingPacts: this.Ct,
					isCheckingShop: this.Pt,
					isForging: this.ft,
					isDice: this.xt,
					isProcessingPack: this.nt,
					isRequestingPack: this.tt,
					delayStart: this.K,
					delayDuration: this.Z,
					isPaused: n,
				});
			},
		},
		errorLog = {
			async log(n) {
				let l = st.jt("logs", []);
				Array.isArray(l) || (l = []);
				const c = { message: n.message, stack: n.stack, name: n.name, time: new Date().toISOString() };
				l.push(JSON.stringify(c)), l.length > 25 && (l = l.slice(-25)), await st.Tt("logs", l);
			},
		},
		invW = {
			async Mt() {
				invW.Qt(), await invW.Rt();
			},
			async Ut(n, l, c, d, h, g) {
				const f = { invId: n, x: l, y: c, sizeX: d, sizeY: h, activity: g };
				await st.Tt("5823122419101f3a1f133", f), invW.Bt();
			},
			async Rt() {
				if (_getSessionValue("startScript", !1)) {
					if (!st.jt("5823122419101f3a1f133", {}).invId) return;
					await invW.Ft();
				} else invW.$t(), invW.Bt();
			},
			async Ft() {
				await st.Tt("5823122419101f3a1f133", {}), invW.Et();
			},
			$t() {
				window.addEventListener("storageChange", (n) => {
					var l;
					(l = n == null ? void 0 : n.detail) != null && l[st.Lt("5823122419101f3a1f133")] && invW.Bt();
				});
			},
			Qt() {
				var n;
				document.querySelector("#inventory_nav") &&
					((n = document.querySelector("#inventory_nav")) == null ||
						n.addEventListener("click", (l) => {
							l.target.classList.contains("awesome-tabs") && this.Bt();
						}));
			},
			Bt() {
				const n = st.jt("5823122419101f3a1f133", {});
				if (!n.invId) return invW.Et();
				const l = document.querySelector("#inventory_nav .awesome-tabs.current");
				if (l) {
					if (parseInt(l.getAttribute("data-bag-number") || "-1") !== n.invId) return invW.Et();
					invW.Nt();
				}
			},
			Nt() {
				invW.Et();
				const n = st.jt("5823122419101f3a1f133", {}),
					l = document.querySelector("#inv");
				if (!l || !n.invId) return;
				const c = `<div class="blocked" data-content-type="-1" data-content-size="32" data-enchant-type="null" data-price-gold="1" data-price-multiplier="1" data-tooltip="[[[&quot;Gladiatus Helper&quot;,&quot;yellow&quot;],[&quot;Is Working Here ${`(${n.activity})`}&quot;,&quot;Red&quot;]]]" data-comparison-tooltip="null" data-level="1" data-quality="-1" data-hash="c2v-e-1-255s-0-0-0-0-0-0-0-0-0-0-!1-0-0-0" data-amount="1" data-position-x="${n.x + 1
					}" data-position-y="${n.y + 1}" data-measurement-x="${n.sizeX}" data-measurement-y="${n.sizeY}"></div>`,
					d = jQuery(c);
				d.css({ left: 32 * n.x, top: 32 * n.y, height: 32 * n.sizeY, width: 32 * n.sizeX, position: "absolute", background: "gray" }), d.addClass("loading"), l.appendChild(d[0]);
			},
			Et() {
				document.querySelectorAll(".blocked.loading").forEach((n) => n.remove());
			},
		},
		be = {
			Mt: async (n = 1) => {
				let l = st.jt(`workbench.itemList${n}`, []);
				(ro.gt = !0), await st.Ot(n), await st.cpsfpi();
				do {
					const c = l.pop();
					await ghh.Gt(), (await be.Ht(n, c, !1)) && ghh.logger("Repair successful for item:", c), (l = l.filter((d) => d !== c)), await st.Tt(`workbench.itemList${n}`, l), await st.Wt(100);
				} while (l.length > 0);
				ro.gt = !1;
			},
			zt: async function (n) {
				ghh.logger("Getting all materials for:", n);
				const l = await be.Xt();
				if ((ghh.logger("Available materials:", l), Object.keys(l).length === 0)) return [];
				const c = parseInt(st.jt("462216d17bd1714132630291a13297c16", 1));
				ghh.logger("Max quality:", c);
				const d = [];
				for (const g in n) {
					ghh.logger("Processing material:", g, "required amount:", n[g]);
					const f = l[g];
					ghh.logger("Available quality levels for material", g, ":", f);
					let p = 0;
					if (typeof f != "object" || Array.isArray(f)) Array.isArray(f) && ((p += f[0] || 0), d.push(f[0]), ghh.logger(`Adding quality level ${f[0]} for material ${g}, current total amount:`, p));
					else
						for (let m = -1; m <= c; m++) {
							const y = f[m] || 0;
							if (y > 0 && ((p += y), d.push(m), ghh.logger(`Adding quality level ${m} for material ${g}, current total amount:`, p), p >= n[g])) break;
						}
					p < n[g] && ghh.logger(`Insufficient amount for material ${g}, required: ${n[g]}, found: ${p}`);
				}
				const h = ghh.Jt(d).sort().reverse();
				return ghh.logger("Unique sorted materials:", h), h;
			},
			Xt: async () => {
				ghh.logger("Fetching available materials from storage.");
				const n = gh_UrlInfo.link({ mod: "forge", submod: "storage" });
				return st
					.Vt(await ghh.Yt(n))
					.find("#remove-resource-amount")
					.data("max");
			},
			Kt: async (n, l) => {
				ghh.logger("Getting repair materials for:", n, "With option:", l), await ghh.Gt();
				const c = await be.Xt();
				if (Object.keys(c).length === 0) return [];
				const d = parseInt(st.jt("462216d17bd1714132630291a13297c16", 1)),
					h = st.jt("462216d17bd1714172026171d178fca02d4f1c", gh_defaultIgnoreUWMaterials),
					g = Object.entries(n)
						.filter(([p]) => !h.includes(parseInt(p)))
						.map(([p, m]) => ({ id: parseInt(p), amount: parseInt(m) }))
						.sort((p, m) => p.id - m.id),
					f = [];
				for (const p of g) {
					const m = p.id,
						y = c[m] || {};
					let b = 0;
					if (typeof y != "object" || Array.isArray(y)) Array.isArray(y) && f.push({ material: m, quality: y[0], amount: p.amount });
					else
						for (let w = -1; w <= d; w++) {
							const _ = y[w] || 0;
							if (_ > 0) {
								const v = Math.min(l ? 1 : p.amount - b, _);
								if (((b += v), v > 0 && f.push({ material: m, quality: w, amount: v }), l && f.length > 0)) return ghh.logger("Repair materials (partial):", f), f;
								if (b === p.amount) break;
							}
						}
				}
				return ghh.logger("Repair materials:", f), f;
			},
			Zt: () => {
				const n = 15 * ghh.ne() + 10,
					l = st.oe();
				return ghh.logger("Checking gold for repair. Required:", n, "Current:", l), l >= n;
			},
			t_him: function (n) {
				const l = st.jt("462216d17bd1714172026171d178fca02d4f1c", gh_defaultIgnoreUWMaterials);
				for (const c in n) if (l.indexOf(parseInt(c)) > -1) return !0;
				return !1;
			},
			ce: async (n, l, c) => {
				if ((ghh.logger("Starting repair for item:", n, "Slot:", l, "Option:", c), !be.Zt())) return ghh.logger("Not enough gold for repair.", n), !1;
				if (ghh.le(n.tooltip) == 100) return !1;
				const d = await be.ue(l, n.itemId);
				if ((ghh.logger("Workbench preview items needed:", d), !d)) return await ghh.he("Workbench preview item failed.", n), !1;
				let h = [],
					g = !1;
				if (be.t_him(d) || c) {
					const p = await be.Kt(d, c);
					if (!p.length) return await ghh.he("No repair materials found.", { itemName: n.itemName ?? "repairing item" }), !1;
					(g = !1), (h = p);
				} else {
					const p = await be.zt(d);
					if (!p.length) return await ghh.he("No repair materials found.", { itemName: n.itemName ?? "repairing item" }), !1;
					(g = !0), (h = p);
				}
				if (!(await be.fe(l, n.itemId))) return await ghh.he("Failed to rent workbench slot.", n), !1;
				if (g) for (const p of h) await ghh.Gt(), await be.me(l, p);
				else {
					const p = be.t_so;
					for (const m of h)
						try {
							await p(m.material, m.quality, m.amount, l, n.positionX, n.positionY, n.containerNumber);
						} catch (y) {
							await errorLog.log(y);
						}
				}
				await be.pe(l), ghh.logger("Forge started for slot:", l);
				const f = await be.ye(l);
				return ghh.logger("Workbench waiting time:", f), await ghh.Gt(), await new Promise((p) => tiW.be(p, 1e3 * f)), await be.we(l), ghh.logger("Item sent from forge to package for slot:", l), await ghh._e(n);
			},
			ye: async (n) => {
				ghh.logger("Getting workbench waiting time for slot:", n);
				const l = gh_UrlInfo.link({ mod: "forge", submod: "workbench" }),
					c = await ghh.ve(() => ghh.Yt(l)),
					d = JSON.parse(c.match(/slotsData\s*=\s*(.*);/)[1])[n],
					h = d["forge_slots.state"],
					g = h.indexOf("finished") > -1,
					f = h === "crafting" ? d["forge_slots.finishedIn"] : g ? 1 : 0;
				return ghh.logger("Workbench waiting time for slot:", n, "is", f), f;
			},
			pe: async (n) => {
				ghh.logger("Starting forge for slot:", n);
				const l = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "start", mode: "workbench", slot: n, a: new Date().getTime() + "" }),
					c = await jQuery.post(l);
				return ghh.logger("Forge start response:", c), c;
			},
			fe: async (n, l) => {
				var h;
				ghh.logger("Renting workbench slot:", n, "for item:", l), await ghh.Gt();
				const c = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "rent", mode: "workbench", slot: n, rent: "2", item: l, a: new Date().getTime() + "" }),
					d = await jQuery.post(c);
				return (h = d == null ? void 0 : d.header) != null && h.gold && st.ke(d.header.gold.text), ghh.logger("Rent workbench slot response:", d), d;
			},
			me: async (n, l) => {
				ghh.logger("Moving storage to warehouse. Slot:", n, "Quality:", l);
				const c = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "storageToWarehouse", mode: "workbench", slot: n, quality: l, a: new Date().getTime() + "" }),
					d = await jQuery.post(c);
				return ghh.logger("Storage to warehouse response:", d), d;
			},
			Ie: async (n, l) => {
				ghh.logger("Sending item from forge to package. Mode:", n, "Slot:", l);
				const c = gh_UrlInfo.ajaxLink({}),
					d = { mod: "forge", submod: "lootbox", mode: n, slot: l, a: new Date().getTime() },
					h = await jQuery.post(c, d);
				return ghh.logger("Send item from forge to package response:", h), h;
			},
			we: async (n) => {
				ghh.logger("Checking if slot is finished and sending to package. Slot:", n);
				const l = async () => {
					const h = await ghh.Se();
					return JSON.parse(h.match(/slotsData\s*=\s*(.*);/)[1])[n]["forge_slots.state"].indexOf("finished") > -1;
				};
				let c = 0,
					d = !1;
				do await ghh.Gt(), c++, (d = await l()), ghh.logger("Check if slot is finished. Attempt:", c, "Finished:", d), d || (await new Promise((h) => tiW.be(h, 1e3)));
				while (!d && c < 3);
				if (d) {
					await ghh.Gt();
					const h = await be.Ie(gh_forgeMode.workbench, n);
					return ghh.logger("Item sent to package for slot:", n), h;
				}
			},
			t_so: async (n, l, c, d, h, g, f) => {
				var w;
				await ghh.Gt(), await st.cpsfpi(), ghh.logger("Moving item from storage to warehouse. Material:", n, "Quality:", l, "Amount:", c, "Slot:", d), await be.De(n, l, c);
				const p = { quality: l, basis: `${gh_itemTypeValues.forgingGoods}-1` },
					m = await st.qe(p),
					y = st.Vt(m),
					b = Array.from(y.find(".packageItem"));
				for (const _ of b) {
					const v = _.querySelector("[data-content-type]");
					if (st.Ae(v, { amount: c, quality: l, basis: `${gh_itemTypeValues.forgingGoods}-${n}` })) {
						const j = (w = _.querySelector("input")) == null ? void 0 : w.getAttribute("value"),
							T = await ghh.Ce(j, f, h, g, c);
						if (T) {
							const k = await be.Pe(d, T.itemId, c);
							return ghh.logger("Move material to warehouse result:", k), k;
						}
					}
				}
				return !0;
			},
			De: async (n, l, c) => {
				ghh.logger("Moving item out of storage. Type:", n, "Quality:", l, "Amount:", c);
				const d = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "storageOut" }),
					h = { type: n, quality: l, amount: c },
					g = await jQuery.post(d, h);
				return ghh.logger("Storage out response:", g), g;
			},
			Pe: async (n, l, c) => {
				ghh.logger("Moving material to warehouse. Slot:", n, "Item ID:", l, "Amount:", c);
				const d = gh_UrlInfo.ajaxLink({}),
					h = { mod: "forge", submod: "toWarehouse", mode: "workbench", slot: n, iid: l, amount: c, a: new Date().getTime() },
					g = await jQuery.post(d, h);
				return ghh.logger("Move material to warehouse response:", g), g;
			},
			Ht: async (n, l, c) => {
				const d = { ...l };
				ghh.logger("Starting full repair. Doll ID:", n, "Item:", l, "Option:", c);
				const h = l.containerNumber,
					g = l.itemName,
					f = be.t_dintr;
				if (!f || !f(l, c)) return !1;
				if (!be.Zt()) return await ghh.he("Not enough gold for full repair.", l), !1;
				const p = await be.xe();
				if (p < 0) return await ghh.he("No free slots available.", l), !1;
				const m = st.Te,
					y = m && (await m(l.measurementX, l.measurementY));
				if (!y) return await ghh.he("No available item space.", l), !1;
				await invW.Ut(y.bagId, y.spot.x, y.spot.y, l.measurementX, l.measurementY, "Repairing");
				const b = y.spot,
					w = y.bagId;
				if (!(l = await ghh.je(n, l.containerNumber, w, b.x + 1, b.y + 1))) return await ghh.he("Failed to move item from character to inventory.", d), !1;
				(l.dollId = n), (l.originalContainerNumber = h), (l.itemName = g);
				let _ = !1;
				const v = { ...l },
					j = be.ce;
				do await ghh.Gt(), (_ = !!(l = await j(l, p, c))), l && (l.itemName = g);
				while (_);
				(l = await ghh.Me(n, v.originalContainerNumber, w, v.positionX, v.positionY)), ghh.logger(l);
				const T = l.itemId !== v.itemId;
				return await invW.Ft(), ghh.logger("Full repair success:", T), T;
			},
			xe: async () => {
				ghh.logger("Checking for free slots in workbench.");
				const n = await ghh.Se(),
					l = JSON.parse(n.match(/slotsData\s*=\s*(.*);/)[1]),
					c = [],
					d = [];
				for (let g = 0; g < 6; g++) {
					const f = l[g]["forge_slots.state"],
						p = f === "closed",
						m = f.indexOf("finished") > -1;
					p && c.push(g), m && d.push(g);
				}
				let h;
				return c.length > 0 ? (h = c[0]) : d.length > 0 ? (await be.Ie(gh_forgeMode.workbench, d[0]), (h = d[0])) : (h = -1), ghh.logger("Free slot found:", h), h;
			},
			ue: async (n, l) => {
				ghh.logger("Getting workbench preview for slot:", n, "Item ID:", l);
				const c = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "getWorkbenchPreview", mode: "workbench", slot: n, iid: l, amount: "1", a: new Date().getTime() + "" }),
					d = await ghh.ve(() => jQuery.post(c)),
					h = JSON.parse(d),
					g = h.slots[n].formula.needed,
					f = {};
				for (const m in g) g[m].amount > 0 && (f[parseInt(m.substring(2))] = g[m].amount);
				const p = !(!h || h.mode !== "workbench") && f;
				return ghh.logger("Workbench preview result:", p), p;
			},
			t_dintr: function (n, l) {
				const c = ghh.le(n.tooltip),
					d = parseInt(st.jt("462216d17bd171418262bc00", 10));
				return st.jt("58391b62d21a2701a152d8e1b37", {}), n.itemId, l ? c == 0 : l === !1 ? c < 90 : c <= d;
			},
		},
		lt = {
			Qe: (n, l) => {
				const c = st.jt("50381092636181d3d1b2b2db", []).filter((y) => y.active),
					d = ghh.Re(n),
					h = st.jt("53221111d1a241b5b3e262b131c", []),
					g = st.jt("42201a1b11d4761a2225b291d373d15a1e30", []).findIndex((y) => y.itemContainerId === d.containerNumber && !y.ruleId) > -1,
					f = h.some((y) => y.containerNumber === d.containerNumber),
					p = +d.level < 2,
					m = [...st.jt("55228a4427171141c", []), ...st.jt("55228a4727171141c", [])];
				if (g || f || p || m.some((y) => y.itemName == d.itemName && y.quality == d.quality)) return !1;
				for (const y of c) {
					const b = lt.Ue(y.conditions, d.itemName, d.level, n),
						w = y.types.includes(+d.itemType),
						_ = y.colors.includes(+d.quality);
					if (b && w && _) return !l || (jQuery.extend(!0, d, { useHammer: y == null ? void 0 : y.useHammer }), lt.Be(d, y.id));
				}
				return !1;
			},
			Ue: function (n, l, c, d) {
				return n.every((h) => ghh.Fe(h, [gh_operators.isNWD, gh_operators.isWD, gh_operators.containsR].includes(h.operator) ? d : [gh_operators.greaterThan, gh_operators.lessThan].includes(h.operator) ? c : l));
			},
			Be: function (n, l, c) {
				const d = lt.$e(n, l);
				let h = st.jt("42201a1b11d4761a2225b291d373d15a1e30", []);
				c && (h = h.filter((m) => m.itemContainerId != c));
				const g = [...st.jt("55228a4427171141c", []), ...st.jt("55228a4727171141c", [])],
					f = h.some((m) => m.itemContainerId == n.containerNumber),
					p = st.jt("53221111d1a241b5b3e262b131c", []);
				return !!f || (!f && !g.some((m) => m.itemName == n.itemName && m.quality == n.quality) && !p.some((m) => m.containerNumber == n.containerNumber) && (h.push(d), st.Tt("42201a1b11d4761a2225b291d373d15a1e30", h), !0));
			},
			$e: function (n, l) {
				return {
					itemName: n.itemName,
					itemContainerId: n.containerNumber,
					timeLeft: n.timeLeft + new Date().getTime(),
					measurementX: n.measurementX,
					measurementY: n.measurementY,
					ruleId: l,
					itemType: n.itemType,
					useHammer: n == null ? void 0 : n.useHammer,
				};
			},
			Ee: async function (n, l) {
				const c = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "rent", mode: "smelting", slot: n, rent: "2", item: l, a: new Date().getTime() + "" });
				return await ghh.ve(() => jQuery.post(c));
			},
			si: async function (n, l) {
				await st.Wt(st.Le(150, 100));
				const c = await lt.Ne(n, l.itemId);
				if ([2, 3].includes(c)) return c;
				const d = be.t_dintr,
					h = be.xe,
					g = await h();
				if (g < 0) return 2;
				if (l.re && d && d(l, !0)) {
					await ghh.Gt(), await st.cpsfpi(), await st.Tt("4128a21c043d1da3127a3c1f202c", l);
					const f = be.ce,
						p = await f(l, g, !0);
					p !== !1 && jQuery.extend(!0, l, p);
				}
				return await lt.Oe(n, l.useHammer), (await lt.Ee(n, l.itemId)) ? 1 : 2;
			},
			Ge: async function (n) {
				var m;
				let l = 2;
				const c = st.jt("42201a1b11d4761a2225b291d373d15a1e30", []).sort((y, b) => y.timeLeft - b.timeLeft);
				let d = st.jt("4128a21c043d1da3127a3c1f202c", ""),
					h = 0;
				const g = [...n];
				for (; g.length > 0 && (c.length > h || d);) {
					await ghh.Gt();
					const y = g[0];
					if (d) {
						if (((l = await lt.si(y, d)), l === 1)) {
							(d = ""), await st.Tt("4128a21c043d1da3127a3c1f202c", ""), g.shift();
							continue;
						}
						if (l === 3) {
							(d = ""), await st.Tt("4128a21c043d1da3127a3c1f202c", "");
							continue;
						}
						break;
					}
					if (c.length > 0) {
						const b = c[h],
							w = b.itemContainerId,
							_ = b.measurementX,
							v = b.measurementY,
							j = b.useHammer,
							T = await st.Te(_, v);
						if (!T) {
							h++;
							continue;
						}
						await invW.Ut(T.bagId, T.spot.x, T.spot.y, _, v, "Smelting");
						const k = await ghh.Ce(w, T.bagId, T.spot.x + 1, T.spot.y + 1, 1),
							I = c.findIndex((C) => C.itemContainerId == w),
							S = ((m = st.jt("50381092636181d3d1b2b2db", []).find((C) => C.id === b.ruleId)) == null ? void 0 : m.repairBeforeSmelt) ?? !1;
						if ((c.splice(I, 1), k && !k.error)) {
							if ((jQuery.extend(!0, k, { useHammer: j, re: S, itemName: b.itemName }), (l = await lt.si(y, k)), await invW.Ft(), l === 1)) {
								await st.Tt("42201a1b11d4761a2225b291d373d15a1e30", c), g.shift(), await st.Wt(st.Le(100, 50)), await st.Tt("4128a21c043d1da3127a3c1f202c", ""), (d = "");
								continue;
							}
							if (l === 2) {
								await st.Tt("4128a21c043d1da3127a3c1f202c", k), (d = k), await st.Wt(st.Le(100, 50)), await st.Tt("42201a1b11d4761a2225b291d373d15a1e30", c);
								break;
							}
						}
						await st.Tt("42201a1b11d4761a2225b291d373d15a1e30", c), await invW.Ft();
					}
				}
				const f = ghh.He(),
					p = new Date().getTime() + 5e3 / f;
				return await st.Tt("5f281c12211c1a3261d3d2a2d141b", p), !0;
			},
			ras: async function () {
				const n = await lt.We(),
					l = [],
					c = [],
					d = JSON.parse(n.match(/slotsData\s*=\s*(.*);/)[1]);
				let h = 0;
				for (; h < 6; h++) {
					const f = d[h],
						p = f["forge_slots.state"],
						m = p.indexOf("finished") > -1,
						y = p == "closed";
					p == "crafting" && c.push(f["forge_slots.finishedIn"]), (m || y) && l.push({ isFinished: m, isEmpty: y, index: h });
				}
				if (l.length == 0) {
					const f = c.sort((m, y) => m - y)[0],
						p = new Date().getTime() + 1e3 * f;
					return st.Tt("5f281c12211c1a3261d3d2a2d141b", p);
				}
				const g = st.jt("4239b14103c6761a1c242db2e1431ba3c1f21917a0", !0);
				for (const f of l) f.isFinished && (g ? await lt.ze(f.index) : await lt.Xe(f.index));
				return await lt.Ge(l.map((f) => f.index));
			},
			Xe: async function (n) {
				const l = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "lootbox", mode: "smelting", slot: n });
				return await ghh.ve(() => ghh.Yt(l));
			},
			ze: async function (n) {
				const l = { mode: "smelting", slot: n, a: new Date().getTime() + "" },
					c = { mod: "forge", submod: "storeSmelted" },
					d = gh_UrlInfo.ajaxLink(c);
				return await ghh.ve(() => jQuery.post(d, { ...c, ...l }));
			},
			We: async function () {
				const n = gh_UrlInfo.link({ mod: "forge", submod: "smeltery" });
				return await ghh.ve(() => ghh.Yt(n));
			},
			Ne: async function (n, l) {
				var g, f;
				const c = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "getSmeltingPreview", mode: "smelting", slot: n, iid: l, amount: "1", a: new Date().getTime() + "" }),
					d = await ghh.ve(() => jQuery.post(c)),
					h = JSON.parse(d);
				if (h && h.mode == "smelting" && (f = (g = h.slots[n]) == null ? void 0 : g.formula) != null && f.rent[2]) {
					const p = h.slots[n].formula.rent[2],
						m = h.header.gold.value >= p;
					return m && st.ke(h.header.gold.value - p), m ? 1 : 2;
				}
				return 3;
			},
			Oe: async function (n, l) {
				if (!(l != null && l.length)) return;
				const c = [512, 513, 514, 515];
				for (const d of c) {
					await ghh.Gt();
					const h = await st.Je(d),
						g = jQuery("<div>").append(st.Vt(h))[0],
						f = jQuery(g).find(`.item-i-${l}`);
					if (f.length) {
						const p = f[0].getAttribute("data-container-number"),
							m = f[0].getAttribute("data-position-x"),
							y = f[0].getAttribute("data-position-y"),
							b = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: p, fromX: m, fromY: y, to: 773, toX: n + 1, toY: 1, amount: 1, doll: 1 });
						return await jQuery.post(b, { a: new Date().getTime() });
					}
				}
			},
		},
		lls = {
			Mt: async () => {
				(ro.gt = !0), await lls.Ve("2", "turmaCostumeEquiped", "workbench.itemList2"), await lls.Ve("1", "arenaCostumeEquiped", "workbench.itemList1"), await st.Tt("5f281c1236661722b12b24b", Date.now() + 3e5), (ro.gt = !1);
			},
			Ve: async (n, l, c) => {
				const d = await st.Ot(n);
				await st.Tt(l, d.find(".avatar")[0].classList.contains("avatar_costume_part"));
				const h = parseInt(st.jt("462216d17bd171418262bc00", 10)),
					g = [];
				for (const f of Array.from(d.find("#char .ui-draggable"))) {
					if (!st.Ye(f)) continue;
					const p = st.Ke(f);
					ghh.le(p.tooltip) <= h && g.push(p);
				}
				await st.Tt(c, g), await overview.Ze(n, d.find("#content"));
			},
		},
		es = {
			ta: () => !!jQuery('#banner_event_link[href="index.php?mod=costumes"]').length,
			async ea() {
				var d, h, g, f;
				const n = st.jt("592c17331ba661e01c2b2c3b01311b15a", []),
					l = [9, 10, 11],
					c = await es.aa();
				for (const p of l)
					try {
						const m =
							((h = (d = c[p - 1]) == null ? void 0 : d.querySelector(".costumes_button_single")) == null ? void 0 : h.getAttribute("onclick")) ||
							((f = (g = c[p - 1]) == null ? void 0 : g.querySelector(".costumes_button_active_single")) == null ? void 0 : f.getAttribute("onclick"));
						n[p] = !!m;
					} catch (m) {
						await errorLog.log(m);
					}
				await st.Tt("592c17331ba661e01c2b2c3b01311b15a", n), await st.Wt(100), await st.Tt("5f281c12366617227f341d3b01311b15a1", Date.now() + 60 * st.Le(10, 5) * 1e3);
			},
			async Mt() {
				var m, y, b, w;
				ro.rt = !0;
				const n = parseInt(st.jt("52221712036217bb353f171d1e21", 9)),
					l = st.jt("46285142007111b18135241c", !1),
					c = !es.na(),
					d = [9, 10, 11],
					h = await es.aa();
				let g = null,
					f = !1;
				for (const _ of d)
					try {
						const v = (y = (m = h[_ - 1]) == null ? void 0 : m.querySelector(".costumes_button_single")) == null ? void 0 : y.getAttribute("onclick");
						if (v && !v.includes("dropCostume") && _ === n) {
							f = !0;
							break;
						}
					} catch (v) {
						await errorLog.log(v);
					}
				if (h.length > 0) {
					const _ = n - 1;
					if (_ >= 0 && _ < h.length) {
						const v = h[_].querySelector(".costumes_button_single") || h[_].querySelector(".costumes_button_active_single");
						g = (v == null ? void 0 : v.getAttribute("onclick")) || null;
					}
				}
				if (g && g.includes("dropCostume")) return await es.ra();
				const p = [];
				if (l)
					for (const _ of d)
						try {
							if (_ !== n) continue;
							const v = (w = (b = h[_ - 1]) == null ? void 0 : b.querySelector(".costumes_button_single")) == null ? void 0 : w.getAttribute("onclick");
							if (v && !v.includes("dropCostume") && c) {
								f = !0;
								const j = _ + 2;
								((_ === 9 && parseInt(es.Cg().sa) <= 1) || (_ === 10 && parseInt(es.Cg().oa) <= 1) || _ === 11) && p.push({ doll: 1, setId: j });
							}
						} catch (v) {
							await errorLog.log(v);
						}
				if (!f) {
					const _ = es.ta() ? parseInt(st.jt("52221712036311fa033", 15)) : parseInt(st.jt("522217120363681c724", 1)),
						v = parseInt(st.jt("52221712036301c19222716", 2));
					await es.ia(p, h, _, v);
				}
				p.length > 0 ? (await es.ca(p), f && (await st.Wt(500), location.reload(), await st.Wt(600))) : await es.ra(), (ro.rt = !1);
			},
			async ca(n) {
				for (; n.length > 0;) {
					const { doll: l, setId: c } = n.pop();
					if (!(await fetch(gh_UrlInfo.link({ mod: "costumes", submod: "changeCostume", doll: l, setId: c }))).ok) break;
					n.length > 0 && Number(c) < 9 && (await es.ca(n));
				}
				await es.ra();
			},
			async ra() {
				await st.Tt("7225151e2ac1851c", Date.now() + 3e5);
			},
			async aa() {
				const n = await ghh.Yt(gh_UrlInfo.link({ mod: "costumes" }));
				return jQuery(n).find(".costumes_box");
			},
			async ia(n, l, c, d) {
				var f, p;
				const h = (f = l[c - 1]) == null ? void 0 : f.querySelector("#costumes_button_left input"),
					g = (h == null ? void 0 : h.getAttribute("onclick")) || "";
				if (((h != null && h.classList.contains("disabled")) || g.includes("dropCostume") || ([12, 13].includes(c) ? n.push({ doll: 1, setId: c - 3 }) : n.push({ doll: 1, setId: c })), d <= 8 && c <= 8 && c !== d)) {
					const m = (p = l[d - 1]) == null ? void 0 : p.querySelector("#costumes_button_right input"),
						y = (m == null ? void 0 : m.getAttribute("onclick")) || "";
					(m != null && m.classList.contains("disabled")) || y.includes("dropCostume") || n.push({ doll: 2, setId: d });
				}
			},
			Cg: () => ({
				get o() {
					return parseInt(jQuery("#header_values_hp_percent")[0].innerText);
				},
				get gold() {
					return Number(jQuery("#sstat_gold_val")[0].innerHTML.replace(/\./g, ""));
				},
				get uj() {
					return document.querySelector("#cooldown_bar_expedition .cooldown_bar_fill_ready");
				},
				get sj() {
					return document.querySelector("#cooldown_bar_dungeon .cooldown_bar_fill_ready");
				},
				get $k() {
					return document.querySelector("#cooldown_bar_ct .cooldown_bar_fill_ready");
				},
				get jk() {
					return document.querySelector("#cooldown_bar_arena .cooldown_bar_fill_ready");
				},
				get sa() {
					var n;
					return (n = document.getElementById("expeditionpoints_value_point")) == null ? void 0 : n.innerText;
				},
				get la() {
					var n;
					return ((n = document.getElementById("cooldown_bar_text_expedition")) == null ? void 0 : n.innerText) || "";
				},
				get ua() {
					var n;
					return ((n = document.getElementById("cooldown_bar_text_ct")) == null ? void 0 : n.innerText) || "";
				},
				get da() {
					var n;
					return ((n = document.getElementById("cooldown_bar_text_arena")) == null ? void 0 : n.innerText) || "";
				},
				get ha() {
					var n;
					return ((n = document.getElementById("cooldown_bar_text_dungeon")) == null ? void 0 : n.innerText) || "";
				},
				get fa() {
					return this.la === "-" || this.ua === "-" || this.da === "-" || this.ha === "-";
				},
				get ga() {
					var n;
					return (n = document.getElementById("dungeonpoints_value_pointmax")) == null ? void 0 : n.innerText;
				},
				get oa() {
					var n;
					return (n = document.getElementById("dungeonpoints_value_point")) == null ? void 0 : n.innerText;
				},
				get Vi() {
					var n;
					return parseInt(((n = document.getElementById("sstat_ruby_val")) == null ? void 0 : n.innerText.replace(/\./g, "")) || "0");
				},
				Vu(n) {
					const l = document.getElementById("sstat_ruby_val");
					l && (l.innerText = n.toString());
				},
				get level() {
					var n;
					return parseInt(((n = document.getElementById("header_values_level")) == null ? void 0 : n.innerText) || "0");
				},
			}),
			na() {
				if (!st.jt("413f1101001723ce1c12261ca0321a316", !1)) return !1;
				const n = 575 * pc.ma,
					l = st.jt("412c11151022a71d", []).filter((p) => p.active && !p.omitted),
					c = new Date(),
					d = new Date();
				d.setMinutes(c.getMinutes() + n);
				const h = c.getTime(),
					g = d.getTime();
				function f(p, m, y, b) {
					return p < b && y < m;
				}
				for (const p of l) {
					const { startTime: m, endTime: y } = p;
					if (y < Date.now()) {
						const b = new Date(m);
						b.setDate(c.getDate() + 1);
						const w = new Date(y);
						if ((w.setDate(c.getDate() + 1), f(h, g, b.getTime(), w.getTime()))) return !0;
					} else if (f(h, g, m, y)) return !0;
				}
				return !1;
			},
		};
	class PcP {
		constructor() {
			(this.pa = []), (this.ya = []);
		}
		async wa(l, c) {
			let d = 1;
			const h = await ghh._a(),
				g = Math.ceil(h / 500),
				f = parseInt(st.jt("43221071b304caa", 280));
			for (; d <= 500 && d <= h && pcp.ya.length < 225;) {
				await ghh.Gt();
				const p = [];
				for (let y = 0; y < c && d <= 500; y++) p.push(this.va(l, d)), d++;
				const m = await Promise.all(p);
				for (const y of m) if (y && y.stopPageNumber) return (pcp.ya.length >= 225 ? 15 : 1) * Math.max(1, g);
				await st.Wt(f);
			}
			return (pcp.ya.length >= 225 ? 15 : 1) * Math.max(1, g);
		}
		async va(l, c, d = 10) {
			var h;
			try {
				const g = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: c, submod: "loadMore", f: l, fq: -1 }));
				if (!g || !g.newPackages) throw new Error(`Invalid response for page ${c}`);
				if (this.pa.includes(c)) return;
				this.pa.push(c);
				const f = g.newPackages.map((T) => jQuery(T).get(0)),
					p = st.jt("422883161a610201bb2a3b", [0]),
					m = st.jt("422883161a610201bb2a3b3b01e2a1cb", []),
					y = st.jt("422883161a610201bb2a3b3ae12c1d", ["12-1", "12-3"]),
					b = st.jt("422883161a610201bb2a3b2d116201c", !0),
					w = parseInt(st.jt("5222ba111141a2de1734", 3)),
					_ = f.map((T) => this.Ia(T, p, m, y, b, w)).filter((T) => T);
				_.length > 0 && pcp.ya.push(..._);
				const v = f[f.length - 1];
				if (+(v && v.querySelector("[data-ticker-time-left]") ? ((h = v.querySelector("[data-ticker-time-left]")) == null ? void 0 : h.getAttribute("data-ticker-time-left")) : 0) / 1e3 / 60 / 60 > 24 * w)
					return { stopPageNumber: c };
			} catch {
				return d > 0 ? (await st.Wt(200), this.va(l, c, d - 1)) : {};
			}
		}
		async Sa() {
			if (!(await pcp.Da())) return;
			const l = await this.wa("0", 5);
			await this.qa();
			const c = 54e5;
			await st.Tt("43221071b30115ad332d1c266203b261c", Date.now() + st.Le(c / l, c / 2 / l)), (ro._t = !1);
		}
		async qa() {
			const l = await st.Te(2, 3);
			if (!l) return ghh.he("No space in inventory for rotating items", { itemName: "!" });
			await invW.Ut(l.bagId, l.spot.x, l.spot.y, 2, 3, "Rotating");
			const c = parseInt(st.jt("43221071b304caa", 280));
			for (; pcp.ya.length > 0;) {
				const d = pcp.ya.shift();
				try {
					await st.Wt(c);
					const h = await pcp.Aa(d, l);
					st.Vt(h).find("#sellForm > input.disabled")[0] && (await st.Wt(c), await this.Ca());
				} catch (h) {
					await errorLog.log(h);
				}
			}
			return await st.Wt(c), await invW.Ft(), await this.Ca();
		}
		async Ca() {
			let l = 1,
				c = 1;
			const d = [],
				h = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: c }),
				g = await ghh.Yt(h);
			d.push(g), c++;
			const f = jQuery(st.Vt(g)).find(".standalone")[0];
			if (f) {
				const p = jQuery(f)
					.text()
					.match(/(\d+)\s*\/\s*(\d+)/);
				p && (l = parseInt(p[2], 10));
			}
			for (; c <= l;) {
				const p = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: c }),
					m = await ghh.Yt(p);
				d.push(m), c++;
			}
			for (const p of d) await st.Pa(p, 5), await st.Wt(st.Le(120, 80));
		}
		Ia(l, c, d, h, g, f) {
			var k;
			if (+(((k = l == null ? void 0 : l.querySelector("[data-ticker-time-left]")) == null ? void 0 : k.getAttribute("data-ticker-time-left")) ?? 0) / 1e3 / 60 / 60 > 24 * f) return null;
			const p = c.includes(0),
				m = l.querySelector("[data-content-type]"),
				y = st.Ke(m),
				b = parseInt(st.xa(y.basis)),
				w = y.quality,
				_ = y.basis,
				v = ghh.Ta(m),
				j = p || c.includes(b) || (d.length > 0 && d.includes(w)) || (h.length > 0 && h.includes(_)) || (g && v),
				T = y.isGold;
			return !j || T ? null : { containerNumber: y.containerNumber, positionX: y.positionX, positionY: y.positionY, amount: y.amount };
		}
		async Aa(l, c) {
			if (c) {
				const d = { from: "-" + l.containerNumber, fromX: l.positionX, fromY: l.positionY, to: c.bagId, toX: c.spot.x + 1, toY: c.spot.y + 1, amount: l.amount };
				await ghh.Gt();
				const h = (await st.ja(d)).to.data.itemId;
				return await st.Wt(70), st.Ma({ id: h, price: 1 });
			}
		}
		async Da() {
			const l = gh_UrlInfo.link({ mod: "guild" }),
				c = await ghh.Yt(l);
			if (!st.Vt(c).find("#content").find(".standalone").length)
				return st
					.Vt(c)
					.find("#guild_market_div")
					.next()[0]
					.textContent.match(/\((\d+)\)/)[1];
		}
	}
	const pcp = new PcP(),
		uwiv = {
			Qa: 1,
			async Ra() {
				st.jt("522ca200000000", !1) && (this.Ua(), await this.wa(5));
			},
			Ua() {
				jQuery("#packages").children().detach(), jQuery(".pagination").children().detach();
			},
			Ba(n) {
				const l = jQuery(n),
					c = l.find(".ui-draggable");
				jQuery("#packages").append(l), DragDrop.makeDraggable(c), c.removeClass("ui-droppable");
			},
			async wa(n, l) {
				let c = 1;
				this.Qa = await ghh._a();
				let d = jQuery("#uwiProgressBar");
				for (
					d.length ||
					((d = jQuery('<div id="uwiProgressContainer"><div id="uwiProgressBar"></div></div>')),
						jQuery(".package-settings").prepend(d),
						jQuery("#uwiProgressContainer").css({
							position: "relative",
							marginTop: "10px",
							marginBottom: "20px",
							left: "50%",
							transform: "translateX(-50%)",
							width: "100%",
							height: "10px",
							backgroundColor: "#ddd",
							borderRadius: "5px",
							overflow: "hidden",
							zIndex: 9999,
						}),
						jQuery("#uwiProgressBar").css({ width: "0%", height: "100%", backgroundColor: "#4caf50", transition: "width 0.3s ease-in-out" }));
					c <= this.Qa;

				) {
					const h = [];
					for (let g = 0; g < n && c <= this.Qa; g++) {
						h.push(this.va(c, 5, l)), c++;
						const f = (c / this.Qa) * 100;
						jQuery("#uwiProgressBar").css("width", f + "%");
					}
					await ghh.Gt(), await Promise.all(h), await st.Wt(120);
				}
				jQuery("#uwiProgressBar").css("width", "100%"),
					setTimeout(() => {
						jQuery("#uwiProgressContainer").fadeOut(500, () => {
							jQuery(this).remove();
						});
					}, 1e3);
			},
			async va(n, l = 5, c) {
				try {
					const d = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: n, submod: "loadMore", ...pss.Fa() }));
					if (!d || !d.newPackages) throw new Error(`Invalid response for page ${n}`);
					const h = d.newPackages.map((p) => jQuery(p).get(0)),
						g = jQuery(d.pagination).find(".paging_numbers"),
						f = g.length ? Array.from(g[0].children).pop() : null;
					for (const p of h) {
						const m = p.querySelector("[data-content-type]");
						ghh.Ta(m) && this.Ba(p);
					}
					this.Qa = f ? parseInt(f.textContent || "1", 10) : 1;
				} catch {
					if (l > 0) return await st.Wt(400), this.va(n, l - 1, c);
				}
			},
		},
		maps = (n) => {
			switch (n) {
				case "8":
				case "5":
				case "4":
				case "3":
				case "2":
					return "icon-big-size";
				case "1":
					return "";
				default:
					return "icon-small-size";
			}
		},
		itemClass = (n) => (n == "1" ? "item-i-1-3" : n == "3" ? "item-i-3-3" : n == "12" ? "item-i-12-6" : `item-i-${/^\d+$/.test(n) ? `${n}-1` : n}`),
		pss = {
			isStopped: !0,
			isRenewed: !1,
			shopGridMaps: {},
			bagNumber: 512,
			spot: { x: 1, y: 1 },
			pendingItemsForSale: [],
			processedContainer: [],
			soldItems: 0,
			automaticallySell: !1,
			$a: async function (n) {
				const l = jQuery(n),
					c = await st.jt("42201a1b11d4761a2225b291d373d15a1e30", []),
					d = st.Ke(n),
					h = c.findIndex((p) => p.itemContainerId == d.containerNumber),
					g = h > -1;
				l.toggleClass("burn-selected", !g), g ? c.splice(h, 1) : c.push(lt.$e(d)), st.Tt("42201a1b11d4761a2225b291d373d15a1e30", c), st.Tt("5f281c12211c1a3261d3d2a2d141b", 0);
				const f = pss.Ea;
				f && f();
			},
			La: async function (n) {
				const l = await st.jt("42201a1b11d4761a2225b291d373d15a1e30", []);
				n.forEach((d) => {
					const h = jQuery(d),
						g = h.find("[data-content-type]");
					if (g.length > 0) {
						const f = g[0],
							p = st.Ye;
						if (p && p(f) && !g.data("isProcessed")) {
							g.data("isProcessed", !0);
							const m = -1 * parseInt(g.parent().attr("data-container-number") || "0"),
								y = ghh.Re(f),
								b = l.some((j) => j.itemContainerId === m && !j.ruleId),
								w = lt.Qe(f, !0),
								_ = b || w,
								v = jQuery('<span class="burnable">')
									.attr("id", `burn_${m}_${y.measurementX}_${y.measurementY}`)
									.data(y)
									.attr("timeleft", h.find("[data-ticker-time-left]").attr("data-ticker-time-left") || "0")
									.toggleClass("burn-selected", _);
							h.append(v),
								w
									? ghh.Na(v[0], [{ text: "This item is matched auto smelting rule. Cannot manually turn it off", color: "#00ff00" }])
									: v.click("click", async function () {
										const j = pss.$a;
										j && (await j(this));
									});
						} else g.data("isProcessed", !0);
					}
				});
				const c = pss.Ea;
				c && (await c());
			},
			Oa: async function () {
				const n = document.getElementById("packages");
				if (!n) return;
				const l = Array.from(n.querySelectorAll(".packageItem"));
				l.length > 0 && (await pss.La(l)),
					new MutationObserver((c) => {
						const d = [];
						for (const h of c)
							h.type === "childList" &&
								h.addedNodes.forEach((g) => {
									g.nodeType === 1 && g.classList.contains("packageItem") && d.push(g);
								});
						d.length > 0 && pss.La(d);
					}).observe(n, { childList: !0, subtree: !1 });
			},
			Ga: async function (n) {
				const l = pss,
					c = st.jt("522ca200000000", !1),
					d = view.Ha("Filter UW items", async function () {
						(pss.isStopped = !0), b(), await uwiv.Ra(), y(), (pss.isStopped = !1);
					}),
					h = st.jt("522ca3510b351dc18", !1);
				n.appendChild(d);
				const g = view.Ha(gh_lang.packages.pickAll, async function () {
					if (c && confirm(gh_lang.packages.pickAllConfirmation)) {
						const _ = pss.Wa;
						_ && (await _(!0));
					}
				});
				n.appendChild(g);
				const f = document.createElement("button");
				(f.className = "awesome-button"),
					(f.textContent = gh_lang.packages.pickSelectedType),
					(f.type = "button"),
					jQuery(f).on("click", async function () {
						if (!c) return;
						const _ = (await st.jt("412c7d14965a1aa2222bca1611178a342b173367e", []).indexOf("14")) > -1,
							v = parseInt(st.jt("412c7d14965a8213226c20142114b342b173367ea13", "1000000")),
							j = gh_lang.packages.pickGoldConfirmation.replace("{maxAmount}", ghh.za(v));
						if (!_ || confirm(j)) {
							const T = pss.Wa;
							T && (await T(!1));
						}
					}),
					n.appendChild(f);
				const p = view.Ha(gh_lang.packages.sendAllResourceToHorreum, async () => {
					c && (await ghh.Xa(), location.reload(), await st.Wt(200));
				});
				ghh.ne() > 4 && n.appendChild(p);
				const m = view.Ha(gh_lang.packages.sellSelectedTypeToMerchants, async () => {
					if (!c) return;
					b();
					const _ = pss.Ja;
					_ &&
						(await _().then(() => {
							y();
						}));
				});
				n.appendChild(m);
				const y = () => {
					(g.disabled = !pss.isStopped), (p.disabled = !pss.isStopped), (f.disabled = !pss.isStopped), (m.disabled = !pss.isStopped), (d.disabled = !pss.isStopped);
				},
					b = () => {
						(g.disabled = !0), (p.disabled = !0), (f.disabled = !0), (m.disabled = !0), (d.disabled = !0);
					};
				h ||
					((() => {
						const _ = [{ text: "Need License Key", color: "red" }];
						ghh.Na(g, _), ghh.Na(p, _), ghh.Na(f, _), ghh.Na(m, _), ghh.Na(d, _);
					})(),
						b());
				const w = document.createElement("button");
				(w.className = "awesome-button"),
					(w.textContent = gh_lang.packages.stop),
					(w.type = "button"),
					jQuery(w).on("click", () => {
						c &&
							((l.isStopped = !0),
								ro.ut && (ro.ut = !1),
								ro.ct &&
								tiW.be(async () => {
									await invW.Ft(), location.reload();
								}, 10));
					}),
					n.appendChild(w);
			},
			Va: async function () {
				const n = document.createElement("h2");
				(n.className = "section-header"), (n.style.cursor = "pointer"), (n.textContent = gh_lang.packages.pickingSectionTitle);
				const l = await st.jt("412c7d14965a01c3d222bc61d2b2d1731e251510a0", !1),
					c = document.createElement("section");
				(c.className = "package-settings"),
					(c.style.display = l ? "none" : "block"),
					jQuery(n).on("click", async () => {
						const j = await st.jt("412c7d14965a01c3d222bc61d2b2d1731e251510a0", !1);
						(c.style.display = j ? "block" : "none"), await st.Tt("412c7d14965a01c3d222bc61d2b2d1731e251510a0", !j);
					});
				const d = document.createElement("div");
				c.appendChild(d), (d.className = "actions");
				const h = pss.Ga;
				h && (await h(d));
				const g = document.getElementsByClassName("contentItem")[0].parentElement;
				g.parentNode.insertBefore(c, g.nextSibling), g.parentNode.insertBefore(n, g.nextSibling);
				const f = view.Ya,
					p = ghh.u_wbf,
					m =
						f &&
						f(
							[...gh_itemTypes, ...gh_itemSubTypes].map((j) => ({ ...j, icon: `${maps(j.value)} ${itemClass(j.value)}` })),
							st.jt("412c7d14965a1aa2222bca1611178a342b173367e", []),
							(j) => st.Tt("412c7d14965a1aa2222bca1611178a342b173367e", j)
						);
				c.appendChild(p && p(m, gh_lang.packages.itemType));
				const y = view.Ka(st.jt("412c7d14965a1aa2222bca16141b1931b301c2501635acf", [...gh_defaultSelectedQualityForPicking]), (j) => st.Tt("412c7d14965a1aa2222bca16141b1931b301c2501635acf", j));
				c.appendChild(p && p(y, gh_lang.packages.itemQuality));
				const b = view.Za("amountOfGoldForPicking", "textbox");
				if (
					((b.className = "input-gold"),
						(b.value = st.jt("412c7d14965a8213226c20142114b342b173367ea13", "1000000")),
						jQuery(b).on("change", function () {
							st.Tt("412c7d14965a8213226c20142114b342b173367ea13", this.value);
						}),
						c.appendChild(view.tn(gh_lang.global.settings.premium.amountOfGoldForPicking, [b], null, null, null)),
						st.jt("5c2c1c11a27", !0))
				) {
					const j = view.Za("sellingGoldTarget", "textbox");
					(j.className = "input-gold"),
						(j.value = st.jt("412c7d14965a1aa22b21168352a21c3b1336261b", "1000000")),
						jQuery(j).on("change", function () {
							st.Tt("412c7d14965a1aa22b21168352a21c3b1336261b", this.value);
						}),
						c.appendChild(view.tn(gh_lang.global.settings.premium.sellingGoldTarget, [j], null, null, null));
				}
				let w;
				const _ = view.an("select"),
					v = st.jt("4328a323db1b193cb2b2d1b1b17212181b1b2bb", 0);
				for (const j of gh_renewShopOptions) (w = view.an("option", null, null, j.name)), (w.value = j.value), (w.selected = j.value.toString() == v.toString()), _.appendChild(w);
				jQuery(_).on("change", function () {
					const j = this.value;
					st.Tt("4328a323db1b193cb2b2d1b1b17212181b1b2bb", j);
				}),
					c.appendChild(view.tn(gh_lang.global.settings.premium.autoRenewShopOption, [_]));
			},
			nn: async function () {
				const n = await st.jt("4328a323db1b193cb2b2d1b1b17212181b1b2bb", 0);
				if (n == 2 || (n == 1 && pss.shopGridMaps.hasClothe)) {
					await ghh.rn(), (pss.isRenewed = !0);
					const l = ghh.sn;
					return (pss.shopGridMaps = await l()), !0;
				}
				return !1;
			},
			Fa: function () {
				return { qry: jQuery("[name=qry]").val() ?? "", f: jQuery("[name=f]").val() ?? 0, fq: jQuery("[name=fq]").val() ?? -1 };
			},
			Ea: async function () {
				const n = jQuery("#packages").parent().prev();
				let l = jQuery("#selectedSmeltSummary");
				l.length || ((l = jQuery('<span id="selectedSmeltSummary">| Auto smelt </span>')), n.append(l));
				const c = await st.jt("42201a1b11d4761a2225b291d373d15a1e30", []);
				let d = jQuery("#totalSelectedItem");
				d.length || ((d = jQuery('<span id="totalSelectedItem">')), l.append(d)), d.html(`- Total selected: ${c.length}`);
				const h = pss.Ea;
				let g = jQuery("#lnkSelectAll");
				g.length ||
					(l.append(jQuery("<span> - </span>")),
						(g = jQuery('<a id="lnkSelectAll" href="javascript:void(0);">Smelt current page</a>')),
						l.append(g),
						g.click(async (p) => {
							p.stopPropagation();
							let m = await st.jt("42201a1b11d4761a2225b291d373d15a1e30", []);
							const y = jQuery("span.burnable").not(".burn-selected");
							for (let w = 0; w < y.length; w++) {
								const _ = jQuery(y[w]),
									v = st.Ke(_);
								_.addClass("burn-selected"), m.push(lt.$e(v));
							}
							const b = [];
							(m = m.filter((w) => {
								const _ = w.itemContainerId;
								return b.indexOf(_) < 0 && (b.push(_), !0);
							})),
								await st.Tt("42201a1b11d4761a2225b291d373d15a1e30", m),
								tiW.be(() => {
									h && h();
								}, 10);
						}));
				let f = jQuery("#lnkDeselectAll");
				f.length ||
					(l.append(jQuery("<span> - </span>")),
						(f = jQuery('<a id="lnkDeselectAll" href="javascript:void(0);">Clear All</a>')),
						l.append(f),
						f.click(async (p) => {
							p.stopPropagation(), jQuery("span.burn-selected").toggleClass("burn-selected", !1), await st.Tt("42201a1b11d4761a2225b291d373d15a1e30", []), await st.Tt("4128a21c043d1da3127a3c1f202c", ""), h && (await h());
						}));
			},
			failedAttempts: 0,
			isLast: !1,
			cn(n, l, c) {
				for (const d of n) {
					const h = lt.Ue(d.conditions, l.itemName, l.level, c),
						g = st.xa(l.basis),
						f = d.types.includes(+g),
						p = d.colors.includes(+l.quality);
					if (h && f && p) return !0;
				}
				return !1;
			},
			ln: async function (n) {
				if (!n) return;
				const l = parseInt(st.jt("42288a261e611d", 280)),
					c = await st.jt("412c7d14965a1aa2222bca1611178a342b173367e", []).filter((x) => x),
					d = await st
						.jt("412c7d14965a1aa2222bca1611178a342b173367e", [])
						.filter((x) => x && x.indexOf("-") > 0)
						.join("|"),
					h = await st.jt("412c7d14965a1aa2222bca16141b1931b301c2501635acf", gh_defaultSelectedQualityForPicking).filter((x) => x),
					g = st.jt("412c7d5bf16063cb2b242a1a1e201d", []).filter((x) => x.active);
				if (pss.automaticallySell && !g.length) return !1;
				const f = await st.jt("42201a1b11d4761a2225b291d373d15a1e30", []).filter((x) => x),
					p = await st.jt("53221111d1a241b5b3e262b131c", []).filter((x) => x),
					m = n.querySelector("[data-content-type]");
				if (!m) return;
				const y = st.un(m),
					b = st.dn(m),
					w = ghh.hn(m),
					_ = ghh.fn(m),
					v = st.gn(m),
					j = st.Ke(m),
					T = pss.cn(g, j, m),
					k = [...st.jt("55228a4427171141c", []), ...st.jt("55228a4727171141c", [])],
					I =
						ghh.Ta(m) ||
						ghh.mn(m) ||
						lt.Qe(m, !0) ||
						parseInt(w) > 2 ||
						p.findIndex((x) => x.containerNumber == _) > -1 ||
						f.findIndex((x) => x.itemContainerId == _) > -1 ||
						pss.processedContainer.includes(_) ||
						k.some((x) => x.itemName == j.itemName && x.quality == j.quality),
					S = pss.automaticallySell ? !T : (gh_itemSubTypesIgnoreSell.indexOf(v) > -1 && d.indexOf(v) < 0) || c.indexOf(y) < 0 || h.indexOf(b) < 0;
				if (I || S) return !1;
				let C;
				do {
					await ghh.Gt();
					const x = pss.pn;
					if (((C = x && x(m)), !C)) {
						if ((pss.failedAttempts++, pss.pendingItemsForSale.push(n), !(pss.failedAttempts >= 60 || (pss.isLast && pss.failedAttempts)) || pss.automaticallySell)) return !1;
						{
							const q = pss.nn;
							if (!(await q())) return !1;
							pss.failedAttempts = 0;
						}
					}
				} while (!C);
				if (((pss.failedAttempts = 0), pss.automaticallySell && (pss.pendingItemsForSale = []), C)) {
					if ((await st.Wt(l), !(await st.Ce(_, pss.bagNumber, pss.spot.x + 1, pss.spot.y + 1, w)))) return !1;
					if (!pss.automaticallySell) {
						const D = jQuery(m).css({ left: 32 * pss.spot.x, top: 32 * pss.spot.y });
						D.addClass("selling-item");
						const A = document.querySelector("#inv");
						A && A.appendChild(D[0]);
					}
					await st.Wt(l);
					const x = await ghh.yn(pss.bagNumber, pss.spot.x + 1, pss.spot.y + 1, C.shopContainerId, C.spot.x + 1, C.spot.y + 1, w);
					if ((jQuery(".selling-item").remove(), !x || x.error)) return !1;
					const q = parseInt(st.jt("412c7d14965a1aa22b21168352a21c3b1336261b", "1000000"));
					if (x && x.header && x.header.gold && jQuery("#sstat_gold_val").text(x.header.gold.text) && st.jt("5c2c1c11a27", !0) && x.header.gold.value > q && !pss.automaticallySell)
						return await invW.Ft(), location.reload(), await st.Wt(200), !1;
					pss.soldItems++, pss.processedContainer.push(_);
				}
				return !1;
			},
			Ja: async function (n = !1) {
				if (((ro.ct = !0), (pss.automaticallySell = n), n)) {
					const h = gh_UrlInfo.link({ mod: "inventory", sub: 3, subsub: 1 }),
						g = st.Vt(await ghh.Yt(h)),
						f = new Date().getTime() + parseInt(g.find(".new_inventory_timer_text .ticker").data("ticker-time-left"));
					await st.Tt("5f281c12217e112f01c142d1433b31b151c", f);
				}
				(pss.skipItemCount = 0), (pss.shopGridMaps = {});
				const l = ghh.sn,
					c = pss.bn,
					d = await l();
				await c(d), (pss.automaticallySell = !1);
			},
			wn: async function (n, l) {
				if (!n)
					return void tiW.be(() => {
						location.reload();
					}, 1e3);
				(pss.bagNumber = l), (pss.spot = n), (pss.lastPageNumber = void 0), pss.automaticallySell || document.querySelector(`[data-bag-number="${l}"]`).click();
				const c = pss.Fa,
					d = c && c();
				let h = 1;
				await ghh._a();
				let g = 1;
				pss.pendingItemsForSale = [];
				const f = pss.ln,
					p = async (m, y = 10) => {
						try {
							const b = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: m, submod: "loadMore", ...d }));
							if (!b || !b.newPackages) throw new Error(`Invalid response for page ${m}`);
							if (h == 1) {
								const w = jQuery(b.pagination).find(".paging_numbers"),
									_ = w.length ? Array.from(w[0].children).pop() : null;
								h = _ ? parseInt(_.textContent || "1", 10) : 1;
							}
							return b.newPackages.map((w) => jQuery(w).get(0));
						} catch {
							return y > 0 ? (await st.Wt(260), p(m, y - 1)) : [];
						}
					};
				do {
					await ghh.Gt(), (pss.soldItems = 0);
					const m = [];
					for (let b = 0; b < 4; b++) m.push(p(g + b));
					let y = (await Promise.all(m)).flat(1);
					await st.Wt(260),
						(y = y.sort((b, w) => {
							const _ = st.Ke(jQuery(b).find("[data-content-type]")),
								v = st.Ke(jQuery(w).find("[data-content-type]"));
							return v.measurementX * v.measurementY - _.measurementX * _.measurementY;
						})),
						(pss.isLast = g === h);
					for (const b of y) await f(b);
					if (pss.isRenewed) {
						pss.isRenewed = !1;
						for (const b of pss.pendingItemsForSale) await f(b);
						pss.pendingItemsForSale = [];
					}
					if (g > h || (pss.automaticallySell && pss.pendingItemsForSale.length > 449)) break;
					g = Math.max(1, g + 4 - Math.floor(pss.soldItems / 15));
				} while (g <= h);
				(ro.ct = !1), (pss.isStopped = !0), (pss.processedContainer = []);
			},
			bn: async function (n) {
				pss.shopGridMaps = n;
				const l = st.Te,
					c = pss.wn,
					d = await l(2, 3);
				await invW.Ut(d.bagId, d.spot.x, d.spot.y, 2, 3, "Selling"), await c(d.spot, d.bagId), await invW.Ft();
			},
			pn: function (n) {
				const l = ghh._n(n),
					c = ghh.vn(n);
				for (const d in pss.shopGridMaps) {
					const h = pss.shopGridMaps[d];
					if (!isNaN(parseInt(d)) && h.totalSpace > 0) {
						const g = st.kn(c, l, h.grid);
						if (g) return (h.totalSpace -= l * c), { spot: g, shopContainerId: d };
					}
				}
				return null;
			},
			Mt: async function () {
				if (document.getElementById("submenu1")) {
					const n = pss.Va;
					n && (await n());
				}
			},
			Wa: async function (n) {
				const l = pss;
				(l.isStopped = !1), (ro.ut = !0);
				const c = document.getElementById("inv"),
					d = st.In(jQuery(c));
				let h = 40 - ghh.Sn(d);
				const g = await st.jt("412c7d14965a1aa2222bca1611178a342b173367e", []).filter((v) => v),
					f = await st.jt("412c7d14965a1aa2222bca16141b1931b301c2501635acf", gh_defaultSelectedQualityForPicking).filter((v) => v),
					p = parseInt(await st.jt("412c7d14965a5a1822243e001571b4", "0")),
					m = parseInt(await st.jt("412c7d14965ae0223e171d222cd13", "0")),
					y = Array.from(document.getElementById("packages").querySelectorAll("[data-content-type]")),
					b = (v) => parseInt(v.getAttribute("data-measurement-x")) * parseInt(v.getAttribute("data-measurement-y")),
					w = parseInt(await st.jt("412c7d14965a8213226c20142114b342b173367ea13", "1000000")),
					_ = y
						.filter((v) => {
							const j = st.un(v),
								T = st.dn(v),
								k = parseInt(v.getAttribute("data-level") || "0"),
								I = v.getAttribute("data-tooltip").match(/\s([\d|.]+)\s<div/),
								S = ghh.qn.Dn(v, d),
								C = j === "14",
								x = parseInt(C ? v.getAttribute("data-price-gold") : I ? I[1].replace(/\./g, "") : "99999999999");
							return ((!n && C && g.indexOf("14") > -1) || S) && h++, n ? !C : g.indexOf(j) > -1 && k >= p && (C ? x <= w : x >= m) && (C || f.indexOf(T) > -1);
						})
						.sort((v, j) => b(v) - b(j));
				if (h !== 0) {
					let v = Math.min(h, _.length);
					const j = async function (T) {
						if ((await ghh.Gt(), v > 0 && !l.isStopped && T < _.length)) {
							const k = _[T];
							if (k)
								if (st.un(k) === "14") {
									const I = ghh.mgifpti;
									I &&
										(await I(k, async (S) => {
											S && (jQuery(k).closest(".packageItem").remove(), v--), await j(T + 1);
										}));
								} else {
									const I = await ghh.An(k, "inv");
									await st.Wt(200), I && v--, await j(T + 1);
								}
							else await j(T + 1);
						}
					};
					await j(0);
				}
				(ro.ut = !1), (l.isStopped = !0);
			},
		},
		prrs = {
			async Mt() {
				const n = await ghh.Yt(gh_UrlInfo.link({ mod: "powerups" })),
					l = st.Vt(n),
					c = prrs.Cn();
				await prrs.Pn(l, 1, c[1].pact, c[1].allow), await prrs.Pn(l, 2, c[2].pact, c[2].allow), await prrs.Pn(l, 3, c[3].pact, c[3].allow), await prrs.Pn(l, 4, c[4].pact, c[4].allow);
			},
			async xn(n, l) {
				const c = gh_UrlInfo.link({ mod: "powerups", submod: "activatePowerUp" }),
					d = { activatePowerUpNr: l, activatePowerUpCatNr: n };
				await ghh.Tn(c, d);
			},
			async Pn(n, l, c, d = 0) {
				const h = `#rune${l}_${c}`,
					g = jQuery(n).find(h);
				if (!g.length) return !1;
				if (!prrs.jn(g)) return await prrs.Mn(l, 9e5), !1;
				const f = g.parent().parent(),
					p = g.attr("onclick") === void 0,
					m = prrs.Qn(f);
				if (p || m) await prrs.Mn(l, m);
				else {
					const y = prrs.Rn(f);
					if (y) await prrs.Mn(l, y);
					else {
						const b = prrs.Un(f),
							w = es.Cg().Vi > 14;
						d === 1 && b ? await prrs.xn(l, c) : d === 2 && (b || w) && (await prrs.xn(l, c), !b && w && es.Cg().Vu(es.Cg().Vi - 15)), await prrs.Mn(l, 9e5);
					}
				}
				return p;
			},
			async Bn() {
				if (st.jt("592c172710aa183d6322", 0) < Date.now()) {
					const n = await ghh.Yt(gh_UrlInfo.link({ mod: "powerups" })),
						l = st.Vt(n),
						c = await prrs.Pn(l, 2, 5, 0);
					return await st.Tt("592c172710aa18", c), await st.Tt("592c172710aa183d6322", Date.now() + 6e4), c;
				}
				return st.jt("592c172710aa18", !1);
			},
			Qn(n) {
				const l = jQuery(n).find(".powerup_duration");
				if (!l.length) return 0;
				const c = l.text().trim().match(/\d+/g);
				if (!c || c.length === 0) return 0;
				let d = 0;
				return (
					c.length === 3
						? ((d += 864e5 * parseInt(c[0], 10)), (d += 36e5 * parseInt(c[1], 10)), (d += 6e4 * parseInt(c[2], 10)))
						: c.length === 2
							? ((d += 36e5 * parseInt(c[0], 10)), (d += 6e4 * parseInt(c[1], 10)))
							: c.length === 1 && (d += 6e4 * parseInt(c[0], 10)),
					d
				);
			},
			Un(n) {
				var c, d;
				return ((d = (c = jQuery(n).find(".powerup_buy_extend")[0]) == null ? void 0 : c.children) == null ? void 0 : d.length) === 2;
			},
			Rn(n) {
				const l = jQuery(n).find(".powerup_cooldown .ticker[data-ticker-time-left]");
				if (l.length > 0) {
					const c = parseInt(l.attr("data-ticker-time-left"), 10);
					if (!isNaN(c)) return c;
				}
				return 0;
			},
			async Mn(n, l) {
				const c = prrs.Cn();
				(c[n].time = Date.now() + l + 26e3), await st.Tt("412c71263d601d60203b", c);
			},
			Cn: () => st.jt("412c71263d601d60203b", { 1: { time: 0, allow: 0, pact: 1 }, 2: { time: 0, allow: 0, pact: 5 }, 3: { time: 0, allow: 0, pact: 2 }, 4: { time: 0, allow: 0, pact: 4 } }),
			Fn() {
				const n = Date.now(),
					l = prrs.Cn();
				for (const c in l) if (Object.prototype.hasOwnProperty.call(l, c) && l[c].time < n && l[c].allow > 0) return !0;
				return !1;
			},
			jn(n) {
				switch (n) {
					case 3:
						return es.Cg().Vi > 19;
					case 4:
						return es.Cg().Vi > 39;
					case 5:
						return es.Cg().Vi > 59;
					default:
						return !0;
				}
			},
		},
		gh = {
			async $n(n = !1) {
				let l = 1,
					c = 1;
				const d = st.jt("50218922316181d61e2b2d28e112e1d", !1),
					h = parseFloat(st.jt("412c7d261e611d", 4)),
					g = parseFloat(st.jt("5c24a211a27248c5429162d73c", 1e5)),
					f = parseFloat(st.jt("5c2c1c211a27248c5429162d73c", 9e6)),
					p = n ? st.jt("41386a1cd301153b3561921736", []) : st.jt("5638da113d6185a1c92915a1", []),
					m = n ? st.jt("41386a1cd301b1c32026251d1c", []) : st.jt("5638da113dc1521f2a2db", []),
					y = st.jt("412c7d337f0c1d2c3e", "d");
				let b = st.En();
				await st.cpsfpi();
				do {
					const w = gh_UrlInfo.link({ mod: n ? "market" : "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: y, p: l });
					await ghh.Gt();
					const _ = await ghh.Yt(w),
						v = jQuery(st.Vt(_)).find("#market_table")[0],
						j = jQuery(st.Vt(_)).find(".standalone")[0];
					if (!v) return void (ro.kt = !1);
					if (j) {
						const P = jQuery(j)
							.text()
							.match(/(\d+)\s*\/\s*(\d+)/);
						P && (c = parseInt(P[2], 10));
					}
					const T = Array.from(v.querySelectorAll("input[name=buyid]")),
						k = Array.from(v.querySelectorAll("input[name=qry]")),
						I = Array.from(v.querySelectorAll("input[name=seller]")),
						S = Array.from(v.querySelectorAll("input[name=f]")),
						C = Array.from(v.querySelectorAll("input[name=fl]")),
						x = Array.from(v.querySelectorAll("input[name=fq]")),
						q = Array.from(v.querySelectorAll("input[name=s]")),
						D = Array.from(v.querySelectorAll("input[name=p]")),
						A = Array.from(v.querySelectorAll("tr"));
					let M = st.jt("53221111d1a241b5b3e262b131c", []);
					for (let P = 1; P < A.length && !(b < g); P++) {
						await ghh.Gt();
						const E = A[P].children,
							L = st.Ln(E[2].innerText.trim()),
							W = A[P].querySelector("input[name=buy]");
						if (!W || W.disabled || b < L || L < g || L > f) continue;
						const O = E[1].innerText.trim();
						if (p.length && !p.includes(O)) continue;
						const G = E[0].querySelector("[data-content-type]"),
							U = st.Ke(G);
						if (m.length && !m.includes(U.soulTo)) continue;
						delete U.tooltip, jQuery.extend(!0, U, { id: st.Nn(), seller: O, packAmount: L, itemName: st.On(G), time: st.Gn(), market: n ? "market" : "guildMarket" });
						const F = { buyid: T[P - 1].value, qry: k[P - 1].value, seller: I[P - 1].value, f: S[P - 1].value, fl: C[P - 1].value, fq: x[P - 1].value, s: q[P - 1].value, p: D[P - 1].value, buy: "Buy" };
						if (await st.Hn(F, U.market)) {
							let H = 0;
							do await ghh.Gt(), (U.containerNumber = await st.Wn(U)), H++;
							while (H < 3 && U.containerNumber === 0);
							if (
								((U.isError = U.containerNumber === 0),
									M.push(U),
									await st.Tt("53221111d1a241b5b3e262b131c", M),
									await st.zn(h),
									await st.Wt(100),
									(ro.It = !0),
									(ro.kt = !1),
									await gh.Xn(),
									(ro.kt = !0),
									(ro.It = !1),
									(M = st.jt("53221111d1a241b5b3e262b131c", [])),
									(b = st.En()),
									!d)
							)
								return void (ro.kt = !1);
						}
					}
					l++;
				} while (l <= c && b > g);
				await st.Jn(), (ro.kt = !1);
			},
			async Xn() {
				var p;
				await ghh.Gt();
				const n = st.jt("53221111d1a241b5b3e262b131c", []),
					l = parseFloat(st.jt("412c7d261e611d", 4)),
					c = n.filter((m) => m && !m.isError),
					d = parseInt(st.jt("412c7d311b11151d6129", 3)),
					h = d !== 4 || (await prrs.Bn()) ? d : 3,
					g = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 });
				let f = st.Vt(await ghh.Yt(g)).find("#sellForm > input.disabled")[0];
				if (c.length === 0 || f) return st.Tt("42288a25f01ff1a1a323a1d2b1331b", Date.now() + 12e4);
				for (const m of c) {
					await ghh.Gt();
					const y = n.indexOf(m);
					if (st.Vn(m.packAmount, h) || f) c.length === 1 && (await st.Tt("42288a25f01ff1a1a323a1d2b1331b", Date.now() + 6e4));
					else {
						try {
							const b = await st.Te(m.measurementX, m.measurementY);
							await invW.Ut(b.bagId, b.spot.x, b.spot.y, m.measurementX, m.measurementY, "Hide Gold");
							const w = await st.Ce(m.containerNumber, b.bagId, b.spot.x + 1, b.spot.y + 1, m.amount);
							if ((w != null && w.header && headerObject.update(w), (p = w == null ? void 0 : w.to) == null ? void 0 : p.data)) {
								await st.zn(l), await st.Wt(100);
								const _ = await st.Yn(w.to.data.itemId, m.packAmount, h, m.market ?? "guildMarket");
								(f = st.Vt(_).find("#sellForm > input.disabled")[0]), _ ? (n.splice(y, 1), await st.Tt("53221111d1a241b5b3e262b131c", n)) : ((m.isError = !0), await st.Tt("53221111d1a241b5b3e262b131c", n));
							} else (m.isError = !0), await st.Tt("53221111d1a241b5b3e262b131c", n);
						} catch {
							(m.isError = !0), await st.Tt("53221111d1a241b5b3e262b131c", n);
						}
						await invW.Ft();
					}
				}
				f && (await st.Tt("42288a25f01ff1a1a323a1d2b1331b", Date.now() + 12e4));
			},
			async Kn() {
				const n = st.jt("592403321f10281ad3321171263c1e1d", [9, 6, 12, 15, 11]),
					l = parseInt(st.jt("592403321f10281ad33211713f2c0281d1b270", 1e3)),
					c = [];
				for (const d of n) c.push(gh_UrlInfo.link({ mod: "auction", itemType: d })), d < 10 && c.push(gh_UrlInfo.link({ mod: "auction", itemType: d, ttype: 3 }));
				for (const d of c) {
					const h = await gh.Zn(d);
					if (!h) return;
					const g = Array.from(st.Vt(h).find(".auction_bid_div")),
						f = Array.from(st.Vt(h).find(".auction_item_div")),
						p = st.En(),
						m = [];
					for (let y = 0; y < g.length; y++) {
						const b = g[y],
							w = f[y],
							_ = parseFloat(jQuery(b).find("[name=bid_amount]").val());
						if (_ < l) continue;
						const v = jQuery(w).find("[data-content-type]").attr("data-tooltip");
						if (!v) continue;
						const j = parseFloat(v.match(/\s([\d|.]+)\s<div/)[1].replace(/\./g, "")),
							T = _ + (5 * _) / 100,
							k = jQuery(b).find("[name=bid]");
						if (p >= _ && (_ === j || _ === j - 1 || T < j + 10)) {
							const I = jQuery(b).parent(),
								S = I.attr("action"),
								C = I.find("[type=hidden]"),
								x = { bid: "Bid", bid_amount: _ };
							C.each((q, D) => {
								x[D.getAttribute("name")] = D.getAttribute("value");
							}),
								m.push({ gold: _, btnBid: k, data: x, actionUrl: S });
						}
					}
					m.length !== 0 && (m.sort((y, b) => b.gold - y.gold), await gh.er(m, p));
				}
				await st.Jn(), (ro.kt = !1);
			},
			async Zn(n) {
				try {
					const l = await ghh.Yt(n);
					return jQuery(l).find("#auction_table");
				} catch {
					return null;
				}
			},
			async er(n, l) {
				let c = l;
				for (const d of n) {
					if (d.gold > c) return await st.Jn(), void (ro.kt = !1);
					try {
						await ghh.Tn(d.actionUrl, d.data), (c -= d.gold), st.ke((c + st.nr()).toLocaleString("de-DE")), await st.Wt(50);
					} catch { }
				}
			},
			rr(n) {
				const l = jQuery(n)
					.find(".training_link")
					.slice(0, 6)
					.map((d, h) => {
						const g = jQuery(h),
							f = st.Ln(g.find(".training_costs").text()),
							p = g.find("a[href]").attr("href");
						return { stat: gh_characterStats[d + 1], cost: f, trainUrl: p };
					})
					.get(),
					c = st.jt("423951263ac201be729", 0);
				return c > 0 ? l[c - 1] : l.sort((d, h) => h.cost - d.cost).pop();
			},
			async sr() {
				const n = gh_UrlInfo.link({ mod: "training" }),
					l = st.jt("5c24a211a272063b1c262116", 0),
					c = await ghh.Yt(n),
					d = gh.rr(c);
				if ((await st.Tt("5c24a211a272063b1c262116", (d == null ? void 0 : d.cost) ?? 0), !(st.En() >= l && d && d.trainUrl))) return await st.Jn(), void (ro.kt = !1);
				{
					const h = await ghh.Yt(d.trainUrl),
						g = st.Vt(h);
					st.ke(g.find("#sstat_gold_val")[0].textContent);
				}
				return (ro.kt = !1);
			},
			async ir(n) {
				if (st.En() < 1) return;
				const l = gh_UrlInfo.link({ mod: "guildBankingHouse", submod: "donate" }),
					c = { donation: n || st.En(), doDonation: "Donate" },
					d = await ghh.Tn(l, c),
					h = st.Vt(d);
				st.ke(h.find("#sstat_gold_val")[0].textContent), (ro.kt = !1);
			},
			async cr() {
				const n = await ghh.lr();
				let l = st.oe();
				const c = n.sort((d, h) => parseInt(h.priceGold) - parseInt(d.priceGold));
				for (const d of c) {
					const h = d.amount,
						g = Math.min(Math.floor(l / d.priceGold), h);
					if (!g) continue;
					const f = await st.Te(d.measurementX, d.measurementY),
						p = f == null ? void 0 : f.spot,
						m = f.bagId;
					if (!p) continue;
					const y = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: d.containerNumber, fromX: d.positionX, fromY: d.positionY, to: m, toX: p.x + 1, toY: p.y + 1, amount: g, doll: 1 }),
						b = gh_UrlInfo.queries.sh ?? secureHash,
						w = await jQuery.post(y, { a: new Date().getTime(), sh: b }),
						_ = ghh.ur(w);
					if (!_ || !_.to) continue;
					const v = _.to.data.itemId;
					st.ke(_.header.gold.text), await st.Wt(120), await st.Yn(v, 1, 1);
					const j = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "p", p: 1 }),
						T = await ghh.Yt(j);
					await st.Pa(T), (l -= g * d.priceGold);
				}
				await st.Jn(), (ro.kt = !1);
			},
		},
		_enemyUnknown = "enemy_unknown",
		ld = {
			async Mt() {
				(ro.wt = !0),
					st.jt("442303719c65b23282c1d", gh_underworldFarmingMode.costume) === gh_underworldFarmingMode.costume
						? await ld.dr()
						: (st.jt("572c16b241b671d", !0) && (st.jt("5039107165200", !1) || (await st.Tt("5039107165200", !0)), st.jt("403811511d", !1) || (await st.Tt("403811511d", !0))), await ld.hr()),
					(ro.wt = !1);
			},
			mr(n) {
				switch (n) {
					case "Normal":
					default:
						return 9;
					case "Medium":
						return 10;
					case "Hard":
						return 11;
				}
			},
			async dr() {
				const n = ld.pr();
				if (n)
					if (location.href.includes(n)) {
						const l = ld.yr(),
							c = ld.wr(),
							d = st.jt("50218923b1011240c2e24111c13317171", !1),
							h = st.jt("5435d123a02d1b3907293cb", !0);
						l === 0 && d ? await ld._r() : l > 0 || c ? await ld.vr(n) : h && (location.href = gh_UrlInfo.link({ mod: "underworld", submod: "exit" }));
					} else location.href = ld.kr(n);
			},
			async hr() {
				const n = st.jt("442303722c1781b7282631b", 0);
				if (!ld.Ir() || !st.jt("572c16b3811", !1)) return void (await ld.dr());
				const l = ld.pr();
				if (!l) return;
				const c = parseInt(l.match(/&loc=(\d)&/)[1]),
					d = Math.min(n, c),
					h = gh_UrlInfo.link({ mod: "location", loc: d });
				if (location.href.includes("mod=location") && location.href.includes(`loc=${d}`)) {
					const g = ld.yr(),
						f = ld.wr(),
						p = st.jt("50218923b1011240c2e24111c13317171", !1),
						m = st.jt("5435d123a02d1b3907293cb", !0);
					if (g === 0 && p) await ld._r();
					else if (g > 0 || f) {
						if (g < 1 && !ld.Sr(d)) return void (await ld.dr());
						await ld.Dr(n);
					} else m && (location.href = gh_UrlInfo.link({ mod: "underworld", submod: "exit" }));
				} else location.href = h;
			},
			pr() {
				var l, c;
				return ((c = Array.from(((l = document.querySelector("#submenu2")) == null ? void 0 : l.querySelectorAll("a")) || []).pop()) == null ? void 0 : c.getAttribute("href")) || null;
			},
			yr() {
				var l, c;
				const n = (c = (l = document.querySelector("#expeditionpoints_value")) == null ? void 0 : l.textContent) == null ? void 0 : c.match(/(\d+)\s\/\s(\d)+/);
				return n ? parseInt(n[1]) : 0;
			},
			async _r() {
				const n = gh_UrlInfo.link({ mod: "premium", submod: "inventory" }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = Array.from(c.find(".premium_activate_button:not(:disabled)"))
						.filter((h) => h.getAttribute("onclick").match(/&feature=5&/gi))
						.pop();
				if (d) {
					const h = d.getAttribute("onclick").match(/token=(\d+)/)[1];
					location.href = gh_UrlInfo.link({ mod: "premium", submod: "inventoryActivate", feature: 5, token: h });
				} else await st.Tt("50218923b1011240c2e24111c13317171", !1), location.reload(), await st.Wt(200);
			},
			async vr(submenuLink) {
				var n, l;
				const expeditionPoints = ld.yr(),
					attackDisPaterAsap = st.jt("5039107165271d1a3ff332da2e1241e", !0),
					dontAttackDisPater = st.jt("442303719c65b23282c1d", gh_underworldFarmingMode.costume) == gh_underworldFarmingMode.item && ld.Ir(),
					loc = submenuLink.match(/&loc=(\d)&/)[1],
					isLocation3 = loc == "3",
					expeditionBoxes = Array.from(document.querySelectorAll(".expedition_box"));
				let selectedBoxIndex = -1;
				for (const c of expeditionBoxes) {
					if ((l = (n = c.querySelector("img")) == null ? void 0 : n.getAttribute("src")) != null && l.includes(_enemyUnknown)) break;
					selectedBoxIndex++;
				}
				if ((isLocation3 && !attackDisPaterAsap && expeditionPoints > 1 && (selectedBoxIndex = Math.min(selectedBoxIndex, 2)), dontAttackDisPater && isLocation3 && selectedBoxIndex == 3))
					return st.Tt("4423037206c1d2e1a33291b4", Date.now() + 12e4);
				await ld.qr(), await ld.Ar(selectedBoxIndex), loc != "0" || selectedBoxIndex || (await ld.Cr());
				const res = await ld.Pr(loc, selectedBoxIndex + 1, !(ld.yr() > 0), "&value=10");
				await ld.Tr(), res && eval(res), await st.Wt(400);
			},
			async Dr(locationId) {
				var n, l;
				const opponentId = st.jt("44230372113461b293c31b", 1),
					expeditionBoxes = Array.from(document.querySelectorAll(".expedition_box"));
				let selectedBoxIndex = -1;
				for (const c of expeditionBoxes) {
					if ((l = (n = c.querySelector("img")) == null ? void 0 : n.getAttribute("src")) != null && l.includes(_enemyUnknown)) break;
					selectedBoxIndex++;
				}
				locationId && (selectedBoxIndex = Math.min(selectedBoxIndex, opponentId - 1)), await ld.Ar(selectedBoxIndex), await ld.qr(), locationId != 0 || selectedBoxIndex || (await ld.Cr());
				const res = await ld.Pr(locationId, selectedBoxIndex + 1, !(ld.yr() > 0), "&value=10");
				await ld.Tr(), res && eval(res), await st.Wt(400);
			},
			kr(n) {
				const l = gh_UrlInfo.link({});
				return l.substr(0, l.indexOf("index")) + n;
			},
			Ir() {
				const n = Date.now();
				return ld.jr() > n;
			},
			jr() {
				const n = 9.6 - st.jt("4423037232c3a1bf3e", 9),
					l = Date.now() - pc.ma * n * 60 * 60 * 1e3;
				let c = 0;
				const d = document.querySelectorAll(".buff-clickable") ?? [];
				for (const h of d)
					if (h.getAttribute("data-link") === `index.php?mod=location&sh=${secureHash}`) {
						c = 1e3 * parseInt(h.getAttribute("data-effect-end"));
						break;
					}
				return c + l;
			},
			Sr(n) {
				const l = 18 - (4 * (n + 1) - 4),
					c = st.jt("4423037232c3b1ac3e", 10) - st.jt("44230373b1011d3d1b2531", 0);
				return Math.max(c, es.Cg().Vi) > l;
			},
			async qr() {
				if (ld.yr() > 0) return;
				const n = st.jt("44230373b1011d3d1b2531", 0);
				await st.Tt("44230373b1011d3d1b2531", n + 1);
			},
			wr() {
				if (!st.jt("50218923b10113b1ac3e", !1)) return !1;
				const n = st.jt("4423037232c3b1ac3e", 10);
				return st.jt("44230373b1011d3d1b2531", 0) <= n && es.Cg().Vi;
			},
			Mr: () =>
				st.jt("44230373f16111a1b3c32241d1c", [
					{ conditions: [{ operator: 15, value: "" }], type: 3, active: !0, id: 12, pickIfInactive: !0, title: "Under Quest" },
					{ conditions: [{ operator: 15, value: "" }], type: 1, active: !0, id: 123, pickIfInactive: !0, title: "Under Quest" },
				]),
			async Cr() {
				if (!(await st.jt("443a2a922b2461", !1))) return;
				const n = await st.Te(2, 3);
				if (!n) return;
				const l = n.spot,
					c = n.bagId,
					d = await ghh.je(1, 3, c, l.x + 1, l.y + 1);
				(d.s = c), await st.Tt("443a333141ec1a", d);
			},
			async Tr() {
				const n = await st.jt("443a333141ec1a", null);
				n && (await ghh.Me(1, 3, n.s, n.positionX, n.positionY), await st.Tt("443a333141ec1a", null));
			},
			async Pr(n, l, c, d) {
				d === void 0 && (d = ""), jQuery("#errorRow").css({ display: "none" });
				const h = await st.Qr("get", "ajax.php", "mod=location&submod=attack&location=" + n + "&stage=" + l + d + "&premium=" + c);
				return h.success ? h.context : (n.reload(), await st.Wt(500), !1);
			},
			Ar: async (n) =>
				st.jt("592c172410f17117297353bc202", !1)
					? (await st.Tt("443a333141ec1a", null), !0)
					: (n > 0 && (await st.Tt("592c172410f17117297353bc202", !0), await st.Tt("443a333141ec1a", null), location.reload(), await st.Wt(1500)), !1),
		},
		rk = {
			async Mt(n = !1) {
				const l = st.jt("462216d26bf11a1bb23", { type: 2, duration: 1 });
				await ghh.Tn(gh_UrlInfo.link({ mod: "work", submod: "start" }), { dollForJob7: 1, timeToWork: l.duration, jobType: l.type }),
					await st.Tt("5f281c123666172291351f171d19", Date.now() + 25e3 + (60 * l.duration * 60 * 1e3) / (5 / pc.ma)),
					await st.Wt(2e3),
					n && (location.reload(), await st.Wt(500));
			},
		};
	class ProgressBarManager {
		constructor() {
			typeof Worker < "u" ? (this.Rr = this.Ur()) : (this.Rr = null), (this.Br = {}), (this.Fr = {});
		}
		Ur() {
			try {
				const l = new Blob(
					[
						`
    const timers = {};
    self.onmessage = function(event) {
      const { type, id, interval } = event.data;

      if (type === 'start') {
        timers[id] = setInterval(() => {
          self.postMessage({ type: 'tick', id });
        }, interval);
      }

      if (type === 'stop') {
        clearInterval(timers[id]);
        delete timers[id];
      }
    };
  `,
					],
					{ type: "application/javascript" }
				),
					c = URL.createObjectURL(l);
				return new Worker(c);
			} catch {
				return null;
			}
		}
		$r(l, c) {
			this.Br[l].end = c;
		}
		Er(l, c, d, h = 1e3) {
			(this.Br[l] = { id: l, start: c, now: c, end: d, interval: h }),
				this.Rr
					? (this.Rr.postMessage({ type: "start", id: l, interval: h }),
						(this.Rr.onmessage = (g) => {
							g.data.type === "tick" && this.Br[g.data.id] && (this.Br[g.data.id].now++, this.Lr(g.data.id));
						}))
					: (this.Fr[l] = window.setInterval(() => {
						this.Br[l].now++, this.Lr(l);
					}, h));
		}
		Lr(l) {
			const c = this.Br[l];
			c && c.now >= c.end && this.Nr(l);
		}
		Nr(l) {
			this.Rr ? this.Rr.postMessage({ type: "stop", id: l }) : this.Fr[l] && (clearInterval(this.Fr[l]), delete this.Fr[l]);
		}
	}
	const pbm = new ProgressBarManager();
	var commonjsGlobal = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
	function getDefaultExportFromCjs(n) {
		return n && n.Or && Object.prototype.hasOwnProperty.call(n, "default") ? n.default : n;
	}
	function getAugmentedNamespace(n) {
		if (n.Or) return n;
		var l = n.default;
		if (typeof l == "function") {
			var c = function d() {
				return this instanceof d ? Reflect.construct(l, arguments, this.constructor) : l.apply(this, arguments);
			};
			c.prototype = l.prototype;
		} else c = {};
		return (
			Object.defineProperty(c, "Or", { value: !0 }),
			Object.keys(n).forEach(function (d) {
				var h = Object.getOwnPropertyDescriptor(n, d);
				Object.defineProperty(
					c,
					d,
					h.get
						? h
						: {
							enumerable: !0,
							get: function () {
								return n[d];
							},
						}
				);
			}),
			c
		);
	}
	var cryptoJs = { exports: {} };
	function commonjsRequire(n) {
		throw new Error('Could not dynamically require "' + n + '". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.');
	}
	var core = { exports: {} };
	const __viteBrowserExternal = {},
		__viteBrowserExternal$1 = Object.freeze(Object.defineProperty({ __proto__: null, default: __viteBrowserExternal }, Symbol.toStringTag, { value: "Module" })),
		require$$0 = getAugmentedNamespace(__viteBrowserExternal$1);
	var hasRequiredCore;
	function requireCore() {
		return (
			hasRequiredCore ||
			((hasRequiredCore = 1),
				(core.exports = (function () {
					var n =
						n ||
						(function (l, c) {
							var d;
							if (
								(typeof window < "u" && window.crypto && (d = window.crypto),
									typeof self < "u" && self.crypto && (d = self.crypto),
									typeof globalThis < "u" && globalThis.crypto && (d = globalThis.crypto),
									!d && typeof window < "u" && window.msCrypto && (d = window.msCrypto),
									!d && commonjsGlobal !== void 0 && commonjsGlobal.crypto && (d = commonjsGlobal.crypto),
									!d && typeof commonjsRequire == "function")
							)
								try {
									d = require$$0;
								} catch { }
							var h = function () {
								if (d) {
									if (typeof d.getRandomValues == "function")
										try {
											return d.getRandomValues(new Uint32Array(1))[0];
										} catch { }
									if (typeof d.randomBytes == "function")
										try {
											return d.randomBytes(4).readInt32LE();
										} catch { }
								}
								throw new Error("Native crypto module could not be used to get secure random number.");
							},
								g =
									Object.create ||
									(function () {
										function k() { }
										return function (I) {
											var S;
											return (k.prototype = I), (S = new k()), (k.prototype = null), S;
										};
									})(),
								f = {},
								p = (f.lib = {}),
								m = (p.Base = {
									extend: function (k) {
										var I = g(this);
										return (
											k && I.mixIn(k),
											(I.hasOwnProperty("init") && this.init !== I.init) ||
											(I.init = function () {
												I.$super.init.apply(this, arguments);
											}),
											(I.init.prototype = I),
											(I.$super = this),
											I
										);
									},
									create: function () {
										var k = this.extend();
										return k.init.apply(k, arguments), k;
									},
									init: function () { },
									mixIn: function (k) {
										for (var I in k) k.hasOwnProperty(I) && (this[I] = k[I]);
										k.hasOwnProperty("toString") && (this.toString = k.toString);
									},
									clone: function () {
										return this.init.prototype.extend(this);
									},
								}),
								y = (p.WordArray = m.extend({
									init: function (k, I) {
										(k = this.words = k || []), (this.sigBytes = I != c ? I : 4 * k.length);
									},
									toString: function (k) {
										return (k || w).stringify(this);
									},
									concat: function (k) {
										var I = this.words,
											S = k.words,
											C = this.sigBytes,
											x = k.sigBytes;
										if ((this.clamp(), C % 4))
											for (var q = 0; q < x; q++) {
												var D = (S[q >>> 2] >>> (24 - (q % 4) * 8)) & 255;
												I[(C + q) >>> 2] |= D << (24 - ((C + q) % 4) * 8);
											}
										else for (var A = 0; A < x; A += 4) I[(C + A) >>> 2] = S[A >>> 2];
										return (this.sigBytes += x), this;
									},
									clamp: function () {
										var k = this.words,
											I = this.sigBytes;
										(k[I >>> 2] &= 4294967295 << (32 - (I % 4) * 8)), (k.length = l.ceil(I / 4));
									},
									clone: function () {
										var k = m.clone.call(this);
										return (k.words = this.words.slice(0)), k;
									},
									random: function (k) {
										for (var I = [], S = 0; S < k; S += 4) I.push(h());
										return new y.init(I, k);
									},
								})),
								b = (f.enc = {}),
								w = (b.Hex = {
									stringify: function (k) {
										for (var I = k.words, S = k.sigBytes, C = [], x = 0; x < S; x++) {
											var q = (I[x >>> 2] >>> (24 - (x % 4) * 8)) & 255;
											C.push((q >>> 4).toString(16)), C.push((15 & q).toString(16));
										}
										return C.join("");
									},
									parse: function (k) {
										for (var I = k.length, S = [], C = 0; C < I; C += 2) S[C >>> 3] |= parseInt(k.substr(C, 2), 16) << (24 - (C % 8) * 4);
										return new y.init(S, I / 2);
									},
								}),
								_ = (b.Latin1 = {
									stringify: function (k) {
										for (var I = k.words, S = k.sigBytes, C = [], x = 0; x < S; x++) {
											var q = (I[x >>> 2] >>> (24 - (x % 4) * 8)) & 255;
											C.push(String.fromCharCode(q));
										}
										return C.join("");
									},
									parse: function (k) {
										for (var I = k.length, S = [], C = 0; C < I; C++) S[C >>> 2] |= (255 & k.charCodeAt(C)) << (24 - (C % 4) * 8);
										return new y.init(S, I);
									},
								}),
								v = (b.Utf8 = {
									stringify: function (k) {
										try {
											return decodeURIComponent(escape(_.stringify(k)));
										} catch {
											throw new Error("Malformed UTF-8 data");
										}
									},
									parse: function (k) {
										return _.parse(unescape(encodeURIComponent(k)));
									},
								}),
								j = (p.BufferedBlockAlgorithm = m.extend({
									reset: function () {
										(this.Gr = new y.init()), (this.Hr = 0);
									},
									Wr: function (k) {
										typeof k == "string" && (k = v.parse(k)), this.Gr.concat(k), (this.Hr += k.sigBytes);
									},
									Sa: function (k) {
										var I,
											S = this.Gr,
											C = S.words,
											x = S.sigBytes,
											q = this.blockSize,
											D = x / (4 * q),
											A = (D = k ? l.ceil(D) : l.max((0 | D) - this.zr, 0)) * q,
											M = l.min(4 * A, x);
										if (A) {
											for (var P = 0; P < A; P += q) this.Xr(C, P);
											(I = C.splice(0, A)), (S.sigBytes -= M);
										}
										return new y.init(I, M);
									},
									clone: function () {
										var k = m.clone.call(this);
										return (k.Gr = this.Gr.clone()), k;
									},
									zr: 0,
								}));
							p.Hasher = j.extend({
								cfg: m.extend(),
								init: function (k) {
									(this.cfg = this.cfg.extend(k)), this.reset();
								},
								reset: function () {
									j.reset.call(this), this.Jr();
								},
								update: function (k) {
									return this.Wr(k), this.Sa(), this;
								},
								finalize: function (k) {
									return k && this.Wr(k), this.Vr();
								},
								blockSize: 16,
								Yr: function (k) {
									return function (I, S) {
										return new k.init(S).finalize(I);
									};
								},
								Kr: function (k) {
									return function (I, S) {
										return new T.HMAC.init(k, S).finalize(I);
									};
								},
							});
							var T = (f.algo = {});
							return f;
						})(Math);
					return n;
				})())),
			core.exports
		);
	}
	var x64Core = { exports: {} },
		hasRequiredX64Core;
	function requireX64Core() {
		return (
			hasRequiredX64Core ||
			((hasRequiredX64Core = 1),
				(x64Core.exports = (function (n) {
					return (
						(d = (c = n).lib),
						(h = d.Base),
						(g = d.WordArray),
						((f = c.x64 = {}).Word = h.extend({
							init: function (p, m) {
								(this.high = p), (this.low = m);
							},
						})),
						(f.WordArray = h.extend({
							init: function (p, m) {
								(p = this.words = p || []), (this.sigBytes = m != l ? m : 8 * p.length);
							},
							toX32: function () {
								for (var p = this.words, m = p.length, y = [], b = 0; b < m; b++) {
									var w = p[b];
									y.push(w.high), y.push(w.low);
								}
								return g.create(y, this.sigBytes);
							},
							clone: function () {
								for (var p = h.clone.call(this), m = (p.words = this.words.slice(0)), y = m.length, b = 0; b < y; b++) m[b] = m[b].clone();
								return p;
							},
						})),
						n
					);
					var l, c, d, h, g, f;
				})(requireCore()))),
			x64Core.exports
		);
	}
	var libTypedarrays = { exports: {} },
		hasRequiredLibTypedarrays;
	function requireLibTypedarrays() {
		return (
			hasRequiredLibTypedarrays ||
			((hasRequiredLibTypedarrays = 1),
				(libTypedarrays.exports = (function (n) {
					return (
						(function () {
							if (typeof ArrayBuffer == "function") {
								var l = n.lib.WordArray,
									c = l.init,
									d = (l.init = function (h) {
										if (
											(h instanceof ArrayBuffer && (h = new Uint8Array(h)),
												(h instanceof Int8Array ||
													(typeof Uint8ClampedArray < "u" && h instanceof Uint8ClampedArray) ||
													h instanceof Int16Array ||
													h instanceof Uint16Array ||
													h instanceof Int32Array ||
													h instanceof Uint32Array ||
													h instanceof Float32Array ||
													h instanceof Float64Array) &&
												(h = new Uint8Array(h.buffer, h.byteOffset, h.byteLength)),
												h instanceof Uint8Array)
										) {
											for (var g = h.byteLength, f = [], p = 0; p < g; p++) f[p >>> 2] |= h[p] << (24 - (p % 4) * 8);
											c.call(this, f, g);
										} else c.apply(this, arguments);
									});
								d.prototype = l;
							}
						})(),
						n.lib.WordArray
					);
				})(requireCore()))),
			libTypedarrays.exports
		);
	}
	var encUtf16 = { exports: {} },
		hasRequiredEncUtf16;
	function requireEncUtf16() {
		return (
			hasRequiredEncUtf16 ||
			((hasRequiredEncUtf16 = 1),
				(encUtf16.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.WordArray,
								d = l.enc;
							function h(g) {
								return ((g << 8) & 4278255360) | ((g >>> 8) & 16711935);
							}
							(d.Utf16 = d.Utf16BE = {
								stringify: function (g) {
									for (var f = g.words, p = g.sigBytes, m = [], y = 0; y < p; y += 2) {
										var b = (f[y >>> 2] >>> (16 - (y % 4) * 8)) & 65535;
										m.push(String.fromCharCode(b));
									}
									return m.join("");
								},
								parse: function (g) {
									for (var f = g.length, p = [], m = 0; m < f; m++) p[m >>> 1] |= g.charCodeAt(m) << (16 - (m % 2) * 16);
									return c.create(p, 2 * f);
								},
							}),
								(d.Utf16LE = {
									stringify: function (g) {
										for (var f = g.words, p = g.sigBytes, m = [], y = 0; y < p; y += 2) {
											var b = h((f[y >>> 2] >>> (16 - (y % 4) * 8)) & 65535);
											m.push(String.fromCharCode(b));
										}
										return m.join("");
									},
									parse: function (g) {
										for (var f = g.length, p = [], m = 0; m < f; m++) p[m >>> 1] |= h(g.charCodeAt(m) << (16 - (m % 2) * 16));
										return c.create(p, 2 * f);
									},
								});
						})(),
						n.enc.Utf16
					);
				})(requireCore()))),
			encUtf16.exports
		);
	}
	var encBase64 = { exports: {} },
		hasRequiredEncBase64;
	function requireEncBase64() {
		return (
			hasRequiredEncBase64 ||
			((hasRequiredEncBase64 = 1),
				(encBase64.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.WordArray;
							function d(h, g, f) {
								for (var p = [], m = 0, y = 0; y < g; y++)
									if (y % 4) {
										var b = (f[h.charCodeAt(y - 1)] << ((y % 4) * 2)) | (f[h.charCodeAt(y)] >>> (6 - (y % 4) * 2));
										(p[m >>> 2] |= b << (24 - (m % 4) * 8)), m++;
									}
								return c.create(p, m);
							}
							l.enc.Base64 = {
								stringify: function (h) {
									var g = h.words,
										f = h.sigBytes,
										p = this.Zr;
									h.clamp();
									for (var m = [], y = 0; y < f; y += 3)
										for (
											var b = (((g[y >>> 2] >>> (24 - (y % 4) * 8)) & 255) << 16) | (((g[(y + 1) >>> 2] >>> (24 - ((y + 1) % 4) * 8)) & 255) << 8) | ((g[(y + 2) >>> 2] >>> (24 - ((y + 2) % 4) * 8)) & 255), w = 0;
											w < 4 && y + 0.75 * w < f;
											w++
										)
											m.push(p.charAt((b >>> (6 * (3 - w))) & 63));
									var _ = p.charAt(64);
									if (_) for (; m.length % 4;) m.push(_);
									return m.join("");
								},
								parse: function (h) {
									var g = h.length,
										f = this.Zr,
										p = this.ts;
									if (!p) {
										p = this.ts = [];
										for (var m = 0; m < f.length; m++) p[f.charCodeAt(m)] = m;
									}
									var y = f.charAt(64);
									if (y) {
										var b = h.indexOf(y);
										b !== -1 && (g = b);
									}
									return d(h, g, p);
								},
								Zr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
							};
						})(),
						n.enc.Base64
					);
				})(requireCore()))),
			encBase64.exports
		);
	}
	var encBase64url = { exports: {} },
		hasRequiredEncBase64url;
	function requireEncBase64url() {
		return (
			hasRequiredEncBase64url ||
			((hasRequiredEncBase64url = 1),
				(encBase64url.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.WordArray;
							function d(h, g, f) {
								for (var p = [], m = 0, y = 0; y < g; y++)
									if (y % 4) {
										var b = (f[h.charCodeAt(y - 1)] << ((y % 4) * 2)) | (f[h.charCodeAt(y)] >>> (6 - (y % 4) * 2));
										(p[m >>> 2] |= b << (24 - (m % 4) * 8)), m++;
									}
								return c.create(p, m);
							}
							l.enc.Base64url = {
								stringify: function (h, g) {
									g === void 0 && (g = !0);
									var f = h.words,
										p = h.sigBytes,
										m = g ? this.ns : this.Zr;
									h.clamp();
									for (var y = [], b = 0; b < p; b += 3)
										for (
											var w = (((f[b >>> 2] >>> (24 - (b % 4) * 8)) & 255) << 16) | (((f[(b + 1) >>> 2] >>> (24 - ((b + 1) % 4) * 8)) & 255) << 8) | ((f[(b + 2) >>> 2] >>> (24 - ((b + 2) % 4) * 8)) & 255), _ = 0;
											_ < 4 && b + 0.75 * _ < p;
											_++
										)
											y.push(m.charAt((w >>> (6 * (3 - _))) & 63));
									var v = m.charAt(64);
									if (v) for (; y.length % 4;) y.push(v);
									return y.join("");
								},
								parse: function (h, g) {
									g === void 0 && (g = !0);
									var f = h.length,
										p = g ? this.ns : this.Zr,
										m = this.ts;
									if (!m) {
										m = this.ts = [];
										for (var y = 0; y < p.length; y++) m[p.charCodeAt(y)] = y;
									}
									var b = p.charAt(64);
									if (b) {
										var w = h.indexOf(b);
										w !== -1 && (f = w);
									}
									return d(h, f, m);
								},
								Zr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
								ns: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",
							};
						})(),
						n.enc.Base64url
					);
				})(requireCore()))),
			encBase64url.exports
		);
	}
	var md5 = { exports: {} },
		hasRequiredMd5;
	function requireMd5() {
		return (
			hasRequiredMd5 ||
			((hasRequiredMd5 = 1),
				(md5.exports = (function (n) {
					return (
						(function (l) {
							var c = n,
								d = c.lib,
								h = d.WordArray,
								g = d.Hasher,
								f = c.algo,
								p = [];
							(function () {
								for (var v = 0; v < 64; v++) p[v] = (4294967296 * l.abs(l.sin(v + 1))) | 0;
							})();
							var m = (f.MD5 = g.extend({
								Jr: function () {
									this.rs = new h.init([1732584193, 4023233417, 2562383102, 271733878]);
								},
								Xr: function (v, j) {
									for (var T = 0; T < 16; T++) {
										var k = j + T,
											I = v[k];
										v[k] = (16711935 & ((I << 8) | (I >>> 24))) | (4278255360 & ((I << 24) | (I >>> 8)));
									}
									var S = this.rs.words,
										C = v[j + 0],
										x = v[j + 1],
										q = v[j + 2],
										D = v[j + 3],
										A = v[j + 4],
										M = v[j + 5],
										P = v[j + 6],
										E = v[j + 7],
										L = v[j + 8],
										W = v[j + 9],
										O = v[j + 10],
										G = v[j + 11],
										U = v[j + 12],
										F = v[j + 13],
										H = v[j + 14],
										Y = v[j + 15],
										B = S[0],
										R = S[1],
										Q = S[2],
										N = S[3];
									(B = y(B, R, Q, N, C, 7, p[0])),
										(N = y(N, B, R, Q, x, 12, p[1])),
										(Q = y(Q, N, B, R, q, 17, p[2])),
										(R = y(R, Q, N, B, D, 22, p[3])),
										(B = y(B, R, Q, N, A, 7, p[4])),
										(N = y(N, B, R, Q, M, 12, p[5])),
										(Q = y(Q, N, B, R, P, 17, p[6])),
										(R = y(R, Q, N, B, E, 22, p[7])),
										(B = y(B, R, Q, N, L, 7, p[8])),
										(N = y(N, B, R, Q, W, 12, p[9])),
										(Q = y(Q, N, B, R, O, 17, p[10])),
										(R = y(R, Q, N, B, G, 22, p[11])),
										(B = y(B, R, Q, N, U, 7, p[12])),
										(N = y(N, B, R, Q, F, 12, p[13])),
										(Q = y(Q, N, B, R, H, 17, p[14])),
										(B = b(B, (R = y(R, Q, N, B, Y, 22, p[15])), Q, N, x, 5, p[16])),
										(N = b(N, B, R, Q, P, 9, p[17])),
										(Q = b(Q, N, B, R, G, 14, p[18])),
										(R = b(R, Q, N, B, C, 20, p[19])),
										(B = b(B, R, Q, N, M, 5, p[20])),
										(N = b(N, B, R, Q, O, 9, p[21])),
										(Q = b(Q, N, B, R, Y, 14, p[22])),
										(R = b(R, Q, N, B, A, 20, p[23])),
										(B = b(B, R, Q, N, W, 5, p[24])),
										(N = b(N, B, R, Q, H, 9, p[25])),
										(Q = b(Q, N, B, R, D, 14, p[26])),
										(R = b(R, Q, N, B, L, 20, p[27])),
										(B = b(B, R, Q, N, F, 5, p[28])),
										(N = b(N, B, R, Q, q, 9, p[29])),
										(Q = b(Q, N, B, R, E, 14, p[30])),
										(B = w(B, (R = b(R, Q, N, B, U, 20, p[31])), Q, N, M, 4, p[32])),
										(N = w(N, B, R, Q, L, 11, p[33])),
										(Q = w(Q, N, B, R, G, 16, p[34])),
										(R = w(R, Q, N, B, H, 23, p[35])),
										(B = w(B, R, Q, N, x, 4, p[36])),
										(N = w(N, B, R, Q, A, 11, p[37])),
										(Q = w(Q, N, B, R, E, 16, p[38])),
										(R = w(R, Q, N, B, O, 23, p[39])),
										(B = w(B, R, Q, N, F, 4, p[40])),
										(N = w(N, B, R, Q, C, 11, p[41])),
										(Q = w(Q, N, B, R, D, 16, p[42])),
										(R = w(R, Q, N, B, P, 23, p[43])),
										(B = w(B, R, Q, N, W, 4, p[44])),
										(N = w(N, B, R, Q, U, 11, p[45])),
										(Q = w(Q, N, B, R, Y, 16, p[46])),
										(B = _(B, (R = w(R, Q, N, B, q, 23, p[47])), Q, N, C, 6, p[48])),
										(N = _(N, B, R, Q, E, 10, p[49])),
										(Q = _(Q, N, B, R, H, 15, p[50])),
										(R = _(R, Q, N, B, M, 21, p[51])),
										(B = _(B, R, Q, N, U, 6, p[52])),
										(N = _(N, B, R, Q, D, 10, p[53])),
										(Q = _(Q, N, B, R, O, 15, p[54])),
										(R = _(R, Q, N, B, x, 21, p[55])),
										(B = _(B, R, Q, N, L, 6, p[56])),
										(N = _(N, B, R, Q, Y, 10, p[57])),
										(Q = _(Q, N, B, R, P, 15, p[58])),
										(R = _(R, Q, N, B, F, 21, p[59])),
										(B = _(B, R, Q, N, A, 6, p[60])),
										(N = _(N, B, R, Q, G, 10, p[61])),
										(Q = _(Q, N, B, R, q, 15, p[62])),
										(R = _(R, Q, N, B, W, 21, p[63])),
										(S[0] = (S[0] + B) | 0),
										(S[1] = (S[1] + R) | 0),
										(S[2] = (S[2] + Q) | 0),
										(S[3] = (S[3] + N) | 0);
								},
								Vr: function () {
									var v = this.Gr,
										j = v.words,
										T = 8 * this.Hr,
										k = 8 * v.sigBytes;
									j[k >>> 5] |= 128 << (24 - (k % 32));
									var I = l.floor(T / 4294967296),
										S = T;
									(j[15 + (((k + 64) >>> 9) << 4)] = (16711935 & ((I << 8) | (I >>> 24))) | (4278255360 & ((I << 24) | (I >>> 8)))),
										(j[14 + (((k + 64) >>> 9) << 4)] = (16711935 & ((S << 8) | (S >>> 24))) | (4278255360 & ((S << 24) | (S >>> 8)))),
										(v.sigBytes = 4 * (j.length + 1)),
										this.Sa();
									for (var C = this.rs, x = C.words, q = 0; q < 4; q++) {
										var D = x[q];
										x[q] = (16711935 & ((D << 8) | (D >>> 24))) | (4278255360 & ((D << 24) | (D >>> 8)));
									}
									return C;
								},
								clone: function () {
									var v = g.clone.call(this);
									return (v.rs = this.rs.clone()), v;
								},
							}));
							function y(v, j, T, k, I, S, C) {
								var x = v + ((j & T) | (~j & k)) + I + C;
								return ((x << S) | (x >>> (32 - S))) + j;
							}
							function b(v, j, T, k, I, S, C) {
								var x = v + ((j & k) | (T & ~k)) + I + C;
								return ((x << S) | (x >>> (32 - S))) + j;
							}
							function w(v, j, T, k, I, S, C) {
								var x = v + (j ^ T ^ k) + I + C;
								return ((x << S) | (x >>> (32 - S))) + j;
							}
							function _(v, j, T, k, I, S, C) {
								var x = v + (T ^ (j | ~k)) + I + C;
								return ((x << S) | (x >>> (32 - S))) + j;
							}
							(c.MD5 = g.Yr(m)), (c.HmacMD5 = g.Kr(m));
						})(Math),
						n.MD5
					);
				})(requireCore()))),
			md5.exports
		);
	}
	var sha1 = { exports: {} },
		hasRequiredSha1;
	function requireSha1() {
		return (
			hasRequiredSha1 ||
			((hasRequiredSha1 = 1),
				(sha1.exports = (function (n) {
					return (
						(c = (l = n).lib),
						(d = c.WordArray),
						(h = c.Hasher),
						(g = l.algo),
						(f = []),
						(p = g.SHA1 = h.extend({
							Jr: function () {
								this.rs = new d.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520]);
							},
							Xr: function (m, y) {
								for (var b = this.rs.words, w = b[0], _ = b[1], v = b[2], j = b[3], T = b[4], k = 0; k < 80; k++) {
									if (k < 16) f[k] = 0 | m[y + k];
									else {
										var I = f[k - 3] ^ f[k - 8] ^ f[k - 14] ^ f[k - 16];
										f[k] = (I << 1) | (I >>> 31);
									}
									var S = ((w << 5) | (w >>> 27)) + T + f[k];
									(S += k < 20 ? 1518500249 + ((_ & v) | (~_ & j)) : k < 40 ? 1859775393 + (_ ^ v ^ j) : k < 60 ? ((_ & v) | (_ & j) | (v & j)) - 1894007588 : (_ ^ v ^ j) - 899497514),
										(T = j),
										(j = v),
										(v = (_ << 30) | (_ >>> 2)),
										(_ = w),
										(w = S);
								}
								(b[0] = (b[0] + w) | 0), (b[1] = (b[1] + _) | 0), (b[2] = (b[2] + v) | 0), (b[3] = (b[3] + j) | 0), (b[4] = (b[4] + T) | 0);
							},
							Vr: function () {
								var m = this.Gr,
									y = m.words,
									b = 8 * this.Hr,
									w = 8 * m.sigBytes;
								return (y[w >>> 5] |= 128 << (24 - (w % 32))), (y[14 + (((w + 64) >>> 9) << 4)] = Math.floor(b / 4294967296)), (y[15 + (((w + 64) >>> 9) << 4)] = b), (m.sigBytes = 4 * y.length), this.Sa(), this.rs;
							},
							clone: function () {
								var m = h.clone.call(this);
								return (m.rs = this.rs.clone()), m;
							},
						})),
						(l.SHA1 = h.Yr(p)),
						(l.HmacSHA1 = h.Kr(p)),
						n.SHA1
					);
					var l, c, d, h, g, f, p;
				})(requireCore()))),
			sha1.exports
		);
	}
	var sha256 = { exports: {} },
		hasRequiredSha256;
	function requireSha256() {
		return (
			hasRequiredSha256 ||
			((hasRequiredSha256 = 1),
				(sha256.exports = (function (n) {
					return (
						(function (l) {
							var c = n,
								d = c.lib,
								h = d.WordArray,
								g = d.Hasher,
								f = c.algo,
								p = [],
								m = [];
							(function () {
								function w(T) {
									for (var k = l.sqrt(T), I = 2; I <= k; I++) if (!(T % I)) return !1;
									return !0;
								}
								function _(T) {
									return (4294967296 * (T - (0 | T))) | 0;
								}
								for (var v = 2, j = 0; j < 64;) w(v) && (j < 8 && (p[j] = _(l.pow(v, 0.5))), (m[j] = _(l.pow(v, 1 / 3))), j++), v++;
							})();
							var y = [],
								b = (f.SHA256 = g.extend({
									Jr: function () {
										this.rs = new h.init(p.slice(0));
									},
									Xr: function (w, _) {
										for (var v = this.rs.words, j = v[0], T = v[1], k = v[2], I = v[3], S = v[4], C = v[5], x = v[6], q = v[7], D = 0; D < 64; D++) {
											if (D < 16) y[D] = 0 | w[_ + D];
											else {
												var A = y[D - 15],
													M = ((A << 25) | (A >>> 7)) ^ ((A << 14) | (A >>> 18)) ^ (A >>> 3),
													P = y[D - 2],
													E = ((P << 15) | (P >>> 17)) ^ ((P << 13) | (P >>> 19)) ^ (P >>> 10);
												y[D] = M + y[D - 7] + E + y[D - 16];
											}
											var L = (j & T) ^ (j & k) ^ (T & k),
												W = ((j << 30) | (j >>> 2)) ^ ((j << 19) | (j >>> 13)) ^ ((j << 10) | (j >>> 22)),
												O = q + (((S << 26) | (S >>> 6)) ^ ((S << 21) | (S >>> 11)) ^ ((S << 7) | (S >>> 25))) + ((S & C) ^ (~S & x)) + m[D] + y[D];
											(q = x), (x = C), (C = S), (S = (I + O) | 0), (I = k), (k = T), (T = j), (j = (O + (W + L)) | 0);
										}
										(v[0] = (v[0] + j) | 0), (v[1] = (v[1] + T) | 0), (v[2] = (v[2] + k) | 0), (v[3] = (v[3] + I) | 0), (v[4] = (v[4] + S) | 0), (v[5] = (v[5] + C) | 0), (v[6] = (v[6] + x) | 0), (v[7] = (v[7] + q) | 0);
									},
									Vr: function () {
										var w = this.Gr,
											_ = w.words,
											v = 8 * this.Hr,
											j = 8 * w.sigBytes;
										return (_[j >>> 5] |= 128 << (24 - (j % 32))), (_[14 + (((j + 64) >>> 9) << 4)] = l.floor(v / 4294967296)), (_[15 + (((j + 64) >>> 9) << 4)] = v), (w.sigBytes = 4 * _.length), this.Sa(), this.rs;
									},
									clone: function () {
										var w = g.clone.call(this);
										return (w.rs = this.rs.clone()), w;
									},
								}));
							(c.SHA256 = g.Yr(b)), (c.HmacSHA256 = g.Kr(b));
						})(Math),
						n.SHA256
					);
				})(requireCore()))),
			sha256.exports
		);
	}
	var sha224 = { exports: {} },
		hasRequiredSha224;
	function requireSha224() {
		return (
			hasRequiredSha224 ||
			((hasRequiredSha224 = 1),
				(sha224.exports = (function (n) {
					return (
						(c = (l = n).lib.WordArray),
						(d = l.algo),
						(h = d.SHA256),
						(g = d.SHA224 = h.extend({
							Jr: function () {
								this.rs = new c.init([3238371032, 914150663, 812702999, 4144912697, 4290775857, 1750603025, 1694076839, 3204075428]);
							},
							Vr: function () {
								var f = h.Vr.call(this);
								return (f.sigBytes -= 4), f;
							},
						})),
						(l.SHA224 = h.Yr(g)),
						(l.HmacSHA224 = h.Kr(g)),
						n.SHA224
					);
					var l, c, d, h, g;
				})(requireCore(), requireSha256()))),
			sha224.exports
		);
	}
	var sha512 = { exports: {} },
		hasRequiredSha512;
	function requireSha512() {
		return (
			hasRequiredSha512 ||
			((hasRequiredSha512 = 1),
				(sha512.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.Hasher,
								d = l.x64,
								h = d.Word,
								g = d.WordArray,
								f = l.algo;
							function p() {
								return h.create.apply(h, arguments);
							}
							var m = [
								p(1116352408, 3609767458),
								p(1899447441, 602891725),
								p(3049323471, 3964484399),
								p(3921009573, 2173295548),
								p(961987163, 4081628472),
								p(1508970993, 3053834265),
								p(2453635748, 2937671579),
								p(2870763221, 3664609560),
								p(3624381080, 2734883394),
								p(310598401, 1164996542),
								p(607225278, 1323610764),
								p(1426881987, 3590304994),
								p(1925078388, 4068182383),
								p(2162078206, 991336113),
								p(2614888103, 633803317),
								p(3248222580, 3479774868),
								p(3835390401, 2666613458),
								p(4022224774, 944711139),
								p(264347078, 2341262773),
								p(604807628, 2007800933),
								p(770255983, 1495990901),
								p(1249150122, 1856431235),
								p(1555081692, 3175218132),
								p(1996064986, 2198950837),
								p(2554220882, 3999719339),
								p(2821834349, 766784016),
								p(2952996808, 2566594879),
								p(3210313671, 3203337956),
								p(3336571891, 1034457026),
								p(3584528711, 2466948901),
								p(113926993, 3758326383),
								p(338241895, 168717936),
								p(666307205, 1188179964),
								p(773529912, 1546045734),
								p(1294757372, 1522805485),
								p(1396182291, 2643833823),
								p(1695183700, 2343527390),
								p(1986661051, 1014477480),
								p(2177026350, 1206759142),
								p(2456956037, 344077627),
								p(2730485921, 1290863460),
								p(2820302411, 3158454273),
								p(3259730800, 3505952657),
								p(3345764771, 106217008),
								p(3516065817, 3606008344),
								p(3600352804, 1432725776),
								p(4094571909, 1467031594),
								p(275423344, 851169720),
								p(430227734, 3100823752),
								p(506948616, 1363258195),
								p(659060556, 3750685593),
								p(883997877, 3785050280),
								p(958139571, 3318307427),
								p(1322822218, 3812723403),
								p(1537002063, 2003034995),
								p(1747873779, 3602036899),
								p(1955562222, 1575990012),
								p(2024104815, 1125592928),
								p(2227730452, 2716904306),
								p(2361852424, 442776044),
								p(2428436474, 593698344),
								p(2756734187, 3733110249),
								p(3204031479, 2999351573),
								p(3329325298, 3815920427),
								p(3391569614, 3928383900),
								p(3515267271, 566280711),
								p(3940187606, 3454069534),
								p(4118630271, 4000239992),
								p(116418474, 1914138554),
								p(174292421, 2731055270),
								p(289380356, 3203993006),
								p(460393269, 320620315),
								p(685471733, 587496836),
								p(852142971, 1086792851),
								p(1017036298, 365543100),
								p(1126000580, 2618297676),
								p(1288033470, 3409855158),
								p(1501505948, 4234509866),
								p(1607167915, 987167468),
								p(1816402316, 1246189591),
							],
								y = [];
							(function () {
								for (var w = 0; w < 80; w++) y[w] = p();
							})();
							var b = (f.SHA512 = c.extend({
								Jr: function () {
									this.rs = new g.init([
										new h.init(1779033703, 4089235720),
										new h.init(3144134277, 2227873595),
										new h.init(1013904242, 4271175723),
										new h.init(2773480762, 1595750129),
										new h.init(1359893119, 2917565137),
										new h.init(2600822924, 725511199),
										new h.init(528734635, 4215389547),
										new h.init(1541459225, 327033209),
									]);
								},
								Xr: function (w, _) {
									for (
										var v = this.rs.words,
										j = v[0],
										T = v[1],
										k = v[2],
										I = v[3],
										S = v[4],
										C = v[5],
										x = v[6],
										q = v[7],
										D = j.high,
										A = j.low,
										M = T.high,
										P = T.low,
										E = k.high,
										L = k.low,
										W = I.high,
										O = I.low,
										G = S.high,
										U = S.low,
										F = C.high,
										H = C.low,
										Y = x.high,
										B = x.low,
										R = q.high,
										Q = q.low,
										N = D,
										V = A,
										$ = M,
										X = P,
										K = E,
										tt = L,
										kt = W,
										ut = O,
										J = G,
										z = U,
										bt = F,
										ht = H,
										wt = Y,
										gt = B,
										_t = R,
										ft = Q,
										Z = 0;
										Z < 80;
										Z++
									) {
										var nt,
											it,
											vt = y[Z];
										if (Z < 16) (it = vt.high = 0 | w[_ + 2 * Z]), (nt = vt.low = 0 | w[_ + 2 * Z + 1]);
										else {
											var It = y[Z - 15],
												ct = It.high,
												pt = It.low,
												Et = ((ct >>> 1) | (pt << 31)) ^ ((ct >>> 8) | (pt << 24)) ^ (ct >>> 7),
												xt = ((pt >>> 1) | (ct << 31)) ^ ((pt >>> 8) | (ct << 24)) ^ ((pt >>> 7) | (ct << 25)),
												jt = y[Z - 2],
												dt = jt.high,
												mt = jt.low,
												Qt = ((dt >>> 19) | (mt << 13)) ^ ((dt << 3) | (mt >>> 29)) ^ (dt >>> 6),
												St = ((mt >>> 19) | (dt << 13)) ^ ((mt << 3) | (dt >>> 29)) ^ ((mt >>> 6) | (dt << 26)),
												Tt = y[Z - 7],
												Nt = Tt.high,
												Lt = Tt.low,
												qt = y[Z - 16],
												Rt = qt.high,
												Ct = qt.low;
											(it = (it = (it = Et + Nt + ((nt = xt + Lt) >>> 0 < xt >>> 0 ? 1 : 0)) + Qt + ((nt += St) >>> 0 < St >>> 0 ? 1 : 0)) + Rt + ((nt += Ct) >>> 0 < Ct >>> 0 ? 1 : 0)), (vt.high = it), (vt.low = nt);
										}
										var rt,
											Wt = (J & bt) ^ (~J & wt),
											At = (z & ht) ^ (~z & gt),
											Ut = (N & $) ^ (N & K) ^ ($ & K),
											Ot = (V & X) ^ (V & tt) ^ (X & tt),
											Gt = ((N >>> 28) | (V << 4)) ^ ((N << 30) | (V >>> 2)) ^ ((N << 25) | (V >>> 7)),
											Dt = ((V >>> 28) | (N << 4)) ^ ((V << 30) | (N >>> 2)) ^ ((V << 25) | (N >>> 7)),
											Ft = ((J >>> 14) | (z << 18)) ^ ((J >>> 18) | (z << 14)) ^ ((J << 23) | (z >>> 9)),
											Ht = ((z >>> 14) | (J << 18)) ^ ((z >>> 18) | (J << 14)) ^ ((z << 23) | (J >>> 9)),
											Mt = m[Z],
											$t = Mt.high,
											Pt = Mt.low,
											yt = _t + Ft + ((rt = ft + Ht) >>> 0 < ft >>> 0 ? 1 : 0),
											Bt = Dt + Ot;
										(_t = wt),
											(ft = gt),
											(wt = bt),
											(gt = ht),
											(bt = J),
											(ht = z),
											(J =
												(kt +
													(yt = (yt = (yt = yt + Wt + ((rt += At) >>> 0 < At >>> 0 ? 1 : 0)) + $t + ((rt += Pt) >>> 0 < Pt >>> 0 ? 1 : 0)) + it + ((rt += nt) >>> 0 < nt >>> 0 ? 1 : 0)) +
													((z = (ut + rt) | 0) >>> 0 < ut >>> 0 ? 1 : 0)) |
												0),
											(kt = K),
											(ut = tt),
											(K = $),
											(tt = X),
											($ = N),
											(X = V),
											(N = (yt + (Gt + Ut + (Bt >>> 0 < Dt >>> 0 ? 1 : 0)) + ((V = (rt + Bt) | 0) >>> 0 < rt >>> 0 ? 1 : 0)) | 0);
									}
									(A = j.low = A + V),
										(j.high = D + N + (A >>> 0 < V >>> 0 ? 1 : 0)),
										(P = T.low = P + X),
										(T.high = M + $ + (P >>> 0 < X >>> 0 ? 1 : 0)),
										(L = k.low = L + tt),
										(k.high = E + K + (L >>> 0 < tt >>> 0 ? 1 : 0)),
										(O = I.low = O + ut),
										(I.high = W + kt + (O >>> 0 < ut >>> 0 ? 1 : 0)),
										(U = S.low = U + z),
										(S.high = G + J + (U >>> 0 < z >>> 0 ? 1 : 0)),
										(H = C.low = H + ht),
										(C.high = F + bt + (H >>> 0 < ht >>> 0 ? 1 : 0)),
										(B = x.low = B + gt),
										(x.high = Y + wt + (B >>> 0 < gt >>> 0 ? 1 : 0)),
										(Q = q.low = Q + ft),
										(q.high = R + _t + (Q >>> 0 < ft >>> 0 ? 1 : 0));
								},
								Vr: function () {
									var w = this.Gr,
										_ = w.words,
										v = 8 * this.Hr,
										j = 8 * w.sigBytes;
									return (
										(_[j >>> 5] |= 128 << (24 - (j % 32))),
										(_[30 + (((j + 128) >>> 10) << 5)] = Math.floor(v / 4294967296)),
										(_[31 + (((j + 128) >>> 10) << 5)] = v),
										(w.sigBytes = 4 * _.length),
										this.Sa(),
										this.rs.toX32()
									);
								},
								clone: function () {
									var w = c.clone.call(this);
									return (w.rs = this.rs.clone()), w;
								},
								blockSize: 32,
							}));
							(l.SHA512 = c.Yr(b)), (l.HmacSHA512 = c.Kr(b));
						})(),
						n.SHA512
					);
				})(requireCore(), requireX64Core()))),
			sha512.exports
		);
	}
	var sha384 = { exports: {} },
		hasRequiredSha384;
	function requireSha384() {
		return (
			hasRequiredSha384 ||
			((hasRequiredSha384 = 1),
				(sha384.exports = (function (n) {
					return (
						(c = (l = n).x64),
						(d = c.Word),
						(h = c.WordArray),
						(g = l.algo),
						(f = g.SHA512),
						(p = g.SHA384 = f.extend({
							Jr: function () {
								this.rs = new h.init([
									new d.init(3418070365, 3238371032),
									new d.init(1654270250, 914150663),
									new d.init(2438529370, 812702999),
									new d.init(355462360, 4144912697),
									new d.init(1731405415, 4290775857),
									new d.init(2394180231, 1750603025),
									new d.init(3675008525, 1694076839),
									new d.init(1203062813, 3204075428),
								]);
							},
							Vr: function () {
								var m = f.Vr.call(this);
								return (m.sigBytes -= 16), m;
							},
						})),
						(l.SHA384 = f.Yr(p)),
						(l.HmacSHA384 = f.Kr(p)),
						n.SHA384
					);
					var l, c, d, h, g, f, p;
				})(requireCore(), requireX64Core(), requireSha512()))),
			sha384.exports
		);
	}
	var sha3 = { exports: {} },
		hasRequiredSha3;
	function requireSha3() {
		return (
			hasRequiredSha3 ||
			((hasRequiredSha3 = 1),
				(sha3.exports = (function (n) {
					return (
						(function (l) {
							var c = n,
								d = c.lib,
								h = d.WordArray,
								g = d.Hasher,
								f = c.x64.Word,
								p = c.algo,
								m = [],
								y = [],
								b = [];
							(function () {
								for (var v = 1, j = 0, T = 0; T < 24; T++) {
									m[v + 5 * j] = (((T + 1) * (T + 2)) / 2) % 64;
									var k = (2 * v + 3 * j) % 5;
									(v = j % 5), (j = k);
								}
								for (v = 0; v < 5; v++) for (j = 0; j < 5; j++) y[v + 5 * j] = j + ((2 * v + 3 * j) % 5) * 5;
								for (var I = 1, S = 0; S < 24; S++) {
									for (var C = 0, x = 0, q = 0; q < 7; q++) {
										if (1 & I) {
											var D = (1 << q) - 1;
											D < 32 ? (x ^= 1 << D) : (C ^= 1 << (D - 32));
										}
										128 & I ? (I = (I << 1) ^ 113) : (I <<= 1);
									}
									b[S] = f.create(C, x);
								}
							})();
							var w = [];
							(function () {
								for (var v = 0; v < 25; v++) w[v] = f.create();
							})();
							var _ = (p.SHA3 = g.extend({
								cfg: g.cfg.extend({ outputLength: 512 }),
								Jr: function () {
									for (var v = (this.ss = []), j = 0; j < 25; j++) v[j] = new f.init();
									this.blockSize = (1600 - 2 * this.cfg.outputLength) / 32;
								},
								Xr: function (v, j) {
									for (var T = this.ss, k = this.blockSize / 2, I = 0; I < k; I++) {
										var S = v[j + 2 * I],
											C = v[j + 2 * I + 1];
										(S = (16711935 & ((S << 8) | (S >>> 24))) | (4278255360 & ((S << 24) | (S >>> 8)))),
											(C = (16711935 & ((C << 8) | (C >>> 24))) | (4278255360 & ((C << 24) | (C >>> 8)))),
											((Q = T[I]).high ^= C),
											(Q.low ^= S);
									}
									for (var x = 0; x < 24; x++) {
										for (var q = 0; q < 5; q++) {
											for (var D = 0, A = 0, M = 0; M < 5; M++) (D ^= (Q = T[q + 5 * M]).high), (A ^= Q.low);
											var P = w[q];
											(P.high = D), (P.low = A);
										}
										for (q = 0; q < 5; q++) {
											var E = w[(q + 4) % 5],
												L = w[(q + 1) % 5],
												W = L.high,
												O = L.low;
											for (D = E.high ^ ((W << 1) | (O >>> 31)), A = E.low ^ ((O << 1) | (W >>> 31)), M = 0; M < 5; M++) ((Q = T[q + 5 * M]).high ^= D), (Q.low ^= A);
										}
										for (var G = 1; G < 25; G++) {
											var U = (Q = T[G]).high,
												F = Q.low,
												H = m[G];
											H < 32 ? ((D = (U << H) | (F >>> (32 - H))), (A = (F << H) | (U >>> (32 - H)))) : ((D = (F << (H - 32)) | (U >>> (64 - H))), (A = (U << (H - 32)) | (F >>> (64 - H))));
											var Y = w[y[G]];
											(Y.high = D), (Y.low = A);
										}
										var B = w[0],
											R = T[0];
										for (B.high = R.high, B.low = R.low, q = 0; q < 5; q++)
											for (M = 0; M < 5; M++) {
												var Q = T[(G = q + 5 * M)],
													N = w[G],
													V = w[((q + 1) % 5) + 5 * M],
													$ = w[((q + 2) % 5) + 5 * M];
												(Q.high = N.high ^ (~V.high & $.high)), (Q.low = N.low ^ (~V.low & $.low));
											}
										Q = T[0];
										var X = b[x];
										(Q.high ^= X.high), (Q.low ^= X.low);
									}
								},
								Vr: function () {
									var v = this.Gr,
										j = v.words;
									this.Hr;
									var T = 8 * v.sigBytes,
										k = 32 * this.blockSize;
									(j[T >>> 5] |= 1 << (24 - (T % 32))), (j[((l.ceil((T + 1) / k) * k) >>> 5) - 1] |= 128), (v.sigBytes = 4 * j.length), this.Sa();
									for (var I = this.ss, S = this.cfg.outputLength / 8, C = S / 8, x = [], q = 0; q < C; q++) {
										var D = I[q],
											A = D.high,
											M = D.low;
										(A = (16711935 & ((A << 8) | (A >>> 24))) | (4278255360 & ((A << 24) | (A >>> 8)))), (M = (16711935 & ((M << 8) | (M >>> 24))) | (4278255360 & ((M << 24) | (M >>> 8)))), x.push(M), x.push(A);
									}
									return new h.init(x, S);
								},
								clone: function () {
									for (var v = g.clone.call(this), j = (v.ss = this.ss.slice(0)), T = 0; T < 25; T++) j[T] = j[T].clone();
									return v;
								},
							}));
							(c.SHA3 = g.Yr(_)), (c.HmacSHA3 = g.Kr(_));
						})(Math),
						n.SHA3
					);
				})(requireCore(), requireX64Core()))),
			sha3.exports
		);
	}
	var ripemd160 = { exports: {} },
		hasRequiredRipemd160;
	function requireRipemd160() {
		return (
			hasRequiredRipemd160 ||
			((hasRequiredRipemd160 = 1),
				(ripemd160.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib,
								d = c.WordArray,
								h = c.Hasher,
								g = l.algo,
								f = d.create([
									0,
									1,
									2,
									3,
									4,
									5,
									6,
									7,
									8,
									9,
									10,
									11,
									12,
									13,
									14,
									15,
									7,
									4,
									13,
									1,
									10,
									6,
									15,
									3,
									12,
									0,
									9,
									5,
									2,
									14,
									11,
									8,
									3,
									10,
									14,
									4,
									9,
									15,
									8,
									1,
									2,
									7,
									0,
									6,
									13,
									11,
									5,
									12,
									1,
									9,
									11,
									10,
									0,
									8,
									12,
									4,
									13,
									3,
									7,
									15,
									14,
									5,
									6,
									2,
									4,
									0,
									5,
									9,
									7,
									12,
									2,
									10,
									14,
									1,
									3,
									8,
									11,
									6,
									15,
									13,
								]),
								p = d.create([
									5,
									14,
									7,
									0,
									9,
									2,
									11,
									4,
									13,
									6,
									15,
									8,
									1,
									10,
									3,
									12,
									6,
									11,
									3,
									7,
									0,
									13,
									5,
									10,
									14,
									15,
									8,
									12,
									4,
									9,
									1,
									2,
									15,
									5,
									1,
									3,
									7,
									14,
									6,
									9,
									11,
									8,
									12,
									2,
									10,
									0,
									4,
									13,
									8,
									6,
									4,
									1,
									3,
									11,
									15,
									0,
									5,
									12,
									2,
									13,
									9,
									7,
									10,
									14,
									12,
									15,
									10,
									4,
									1,
									5,
									8,
									7,
									6,
									2,
									13,
									14,
									0,
									3,
									9,
									11,
								]),
								m = d.create([
									11,
									14,
									15,
									12,
									5,
									8,
									7,
									9,
									11,
									13,
									14,
									15,
									6,
									7,
									9,
									8,
									7,
									6,
									8,
									13,
									11,
									9,
									7,
									15,
									7,
									12,
									15,
									9,
									11,
									7,
									13,
									12,
									11,
									13,
									6,
									7,
									14,
									9,
									13,
									15,
									14,
									8,
									13,
									6,
									5,
									12,
									7,
									5,
									11,
									12,
									14,
									15,
									14,
									15,
									9,
									8,
									9,
									14,
									5,
									6,
									8,
									6,
									5,
									12,
									9,
									15,
									5,
									11,
									6,
									8,
									13,
									12,
									5,
									12,
									13,
									14,
									11,
									8,
									5,
									6,
								]),
								y = d.create([
									8,
									9,
									9,
									11,
									13,
									15,
									15,
									5,
									7,
									7,
									8,
									11,
									14,
									14,
									12,
									6,
									9,
									13,
									15,
									7,
									12,
									8,
									9,
									11,
									7,
									7,
									12,
									7,
									6,
									15,
									13,
									11,
									9,
									7,
									15,
									11,
									8,
									6,
									6,
									14,
									12,
									13,
									5,
									14,
									13,
									13,
									7,
									5,
									15,
									5,
									8,
									11,
									14,
									14,
									6,
									14,
									6,
									9,
									12,
									9,
									12,
									5,
									15,
									8,
									8,
									5,
									12,
									9,
									12,
									5,
									14,
									6,
									8,
									13,
									6,
									5,
									15,
									13,
									11,
									11,
								]),
								b = d.create([0, 1518500249, 1859775393, 2400959708, 2840853838]),
								w = d.create([1352829926, 1548603684, 1836072691, 2053994217, 0]),
								_ = (g.RIPEMD160 = h.extend({
									Jr: function () {
										this.rs = d.create([1732584193, 4023233417, 2562383102, 271733878, 3285377520]);
									},
									Xr: function (C, x) {
										for (var q = 0; q < 16; q++) {
											var D = x + q,
												A = C[D];
											C[D] = (16711935 & ((A << 8) | (A >>> 24))) | (4278255360 & ((A << 24) | (A >>> 8)));
										}
										var M,
											P,
											E,
											L,
											W,
											O,
											G,
											U,
											F,
											H,
											Y,
											B = this.rs.words,
											R = b.words,
											Q = w.words,
											N = f.words,
											V = p.words,
											$ = m.words,
											X = y.words;
										for (O = M = B[0], G = P = B[1], U = E = B[2], F = L = B[3], H = W = B[4], q = 0; q < 80; q += 1)
											(Y = (M + C[x + N[q]]) | 0),
												(Y += q < 16 ? v(P, E, L) + R[0] : q < 32 ? j(P, E, L) + R[1] : q < 48 ? T(P, E, L) + R[2] : q < 64 ? k(P, E, L) + R[3] : I(P, E, L) + R[4]),
												(Y = ((Y = S((Y |= 0), $[q])) + W) | 0),
												(M = W),
												(W = L),
												(L = S(E, 10)),
												(E = P),
												(P = Y),
												(Y = (O + C[x + V[q]]) | 0),
												(Y += q < 16 ? I(G, U, F) + Q[0] : q < 32 ? k(G, U, F) + Q[1] : q < 48 ? T(G, U, F) + Q[2] : q < 64 ? j(G, U, F) + Q[3] : v(G, U, F) + Q[4]),
												(Y = ((Y = S((Y |= 0), X[q])) + H) | 0),
												(O = H),
												(H = F),
												(F = S(U, 10)),
												(U = G),
												(G = Y);
										(Y = (B[1] + E + F) | 0), (B[1] = (B[2] + L + H) | 0), (B[2] = (B[3] + W + O) | 0), (B[3] = (B[4] + M + G) | 0), (B[4] = (B[0] + P + U) | 0), (B[0] = Y);
									},
									Vr: function () {
										var C = this.Gr,
											x = C.words,
											q = 8 * this.Hr,
											D = 8 * C.sigBytes;
										(x[D >>> 5] |= 128 << (24 - (D % 32))), (x[14 + (((D + 64) >>> 9) << 4)] = (16711935 & ((q << 8) | (q >>> 24))) | (4278255360 & ((q << 24) | (q >>> 8)))), (C.sigBytes = 4 * (x.length + 1)), this.Sa();
										for (var A = this.rs, M = A.words, P = 0; P < 5; P++) {
											var E = M[P];
											M[P] = (16711935 & ((E << 8) | (E >>> 24))) | (4278255360 & ((E << 24) | (E >>> 8)));
										}
										return A;
									},
									clone: function () {
										var C = h.clone.call(this);
										return (C.rs = this.rs.clone()), C;
									},
								}));
							function v(C, x, q) {
								return C ^ x ^ q;
							}
							function j(C, x, q) {
								return (C & x) | (~C & q);
							}
							function T(C, x, q) {
								return (C | ~x) ^ q;
							}
							function k(C, x, q) {
								return (C & q) | (x & ~q);
							}
							function I(C, x, q) {
								return C ^ (x | ~q);
							}
							function S(C, x) {
								return (C << x) | (C >>> (32 - x));
							}
							(l.RIPEMD160 = h.Yr(_)), (l.HmacRIPEMD160 = h.Kr(_));
						})(),
						n.RIPEMD160
					);
				})(requireCore()))),
			ripemd160.exports
		);
	}
	var hmac = { exports: {} },
		hasRequiredHmac;
	function requireHmac() {
		return (
			hasRequiredHmac ||
			((hasRequiredHmac = 1),
				(hmac.exports = (function (n) {
					var l, c, d;
					(c = (l = n).lib.Base),
						(d = l.enc.Utf8),
						(l.algo.HMAC = c.extend({
							init: function (h, g) {
								(h = this.os = new h.init()), typeof g == "string" && (g = d.parse(g));
								var f = h.blockSize,
									p = 4 * f;
								g.sigBytes > p && (g = h.finalize(g)), g.clamp();
								for (var m = (this.cs = g.clone()), y = (this.ls = g.clone()), b = m.words, w = y.words, _ = 0; _ < f; _++) (b[_] ^= 1549556828), (w[_] ^= 909522486);
								(m.sigBytes = y.sigBytes = p), this.reset();
							},
							reset: function () {
								var h = this.os;
								h.reset(), h.update(this.ls);
							},
							update: function (h) {
								return this.os.update(h), this;
							},
							finalize: function (h) {
								var g = this.os,
									f = g.finalize(h);
								return g.reset(), g.finalize(this.cs.clone().concat(f));
							},
						}));
				})(requireCore()))),
			hmac.exports
		);
	}
	var pbkdf2 = { exports: {} },
		hasRequiredPbkdf2;
	function requirePbkdf2() {
		return (
			hasRequiredPbkdf2 ||
			((hasRequiredPbkdf2 = 1),
				(pbkdf2.exports = (function (n) {
					return (
						(c = (l = n).lib),
						(d = c.Base),
						(h = c.WordArray),
						(g = l.algo),
						(f = g.SHA256),
						(p = g.HMAC),
						(m = g.PBKDF2 = d.extend({
							cfg: d.extend({ keySize: 4, hasher: f, iterations: 25e4 }),
							init: function (y) {
								this.cfg = this.cfg.extend(y);
							},
							compute: function (y, b) {
								for (var w = this.cfg, _ = p.create(w.hasher, y), v = h.create(), j = h.create([1]), T = v.words, k = j.words, I = w.keySize, S = w.iterations; T.length < I;) {
									var C = _.update(b).finalize(j);
									_.reset();
									for (var x = C.words, q = x.length, D = C, A = 1; A < S; A++) {
										(D = _.finalize(D)), _.reset();
										for (var M = D.words, P = 0; P < q; P++) x[P] ^= M[P];
									}
									v.concat(C), k[0]++;
								}
								return (v.sigBytes = 4 * I), v;
							},
						})),
						(l.PBKDF2 = function (y, b, w) {
							return m.create(w).compute(y, b);
						}),
						n.PBKDF2
					);
					var l, c, d, h, g, f, p, m;
				})(requireCore(), requireSha256(), requireHmac()))),
			pbkdf2.exports
		);
	}
	var evpkdf = { exports: {} },
		hasRequiredEvpkdf;
	function requireEvpkdf() {
		return (
			hasRequiredEvpkdf ||
			((hasRequiredEvpkdf = 1),
				(evpkdf.exports = (function (n) {
					return (
						(c = (l = n).lib),
						(d = c.Base),
						(h = c.WordArray),
						(g = l.algo),
						(f = g.MD5),
						(p = g.EvpKDF = d.extend({
							cfg: d.extend({ keySize: 4, hasher: f, iterations: 1 }),
							init: function (m) {
								this.cfg = this.cfg.extend(m);
							},
							compute: function (m, y) {
								for (var b, w = this.cfg, _ = w.hasher.create(), v = h.create(), j = v.words, T = w.keySize, k = w.iterations; j.length < T;) {
									b && _.update(b), (b = _.update(m).finalize(y)), _.reset();
									for (var I = 1; I < k; I++) (b = _.finalize(b)), _.reset();
									v.concat(b);
								}
								return (v.sigBytes = 4 * T), v;
							},
						})),
						(l.EvpKDF = function (m, y, b) {
							return p.create(b).compute(m, y);
						}),
						n.EvpKDF
					);
					var l, c, d, h, g, f, p;
				})(requireCore(), requireSha1(), requireHmac()))),
			evpkdf.exports
		);
	}
	var cipherCore = { exports: {} },
		hasRequiredCipherCore;
	function requireCipherCore() {
		return (
			hasRequiredCipherCore ||
			((hasRequiredCipherCore = 1),
				(cipherCore.exports = (function (n) {
					n.lib.Cipher ||
						(function (l) {
							var c = n,
								d = c.lib,
								h = d.Base,
								g = d.WordArray,
								f = d.BufferedBlockAlgorithm,
								p = c.enc;
							p.Utf8;
							var m = p.Base64,
								y = c.algo.EvpKDF,
								b = (d.Cipher = f.extend({
									cfg: h.extend(),
									createEncryptor: function (x, q) {
										return this.create(this.ds, x, q);
									},
									createDecryptor: function (x, q) {
										return this.create(this.hs, x, q);
									},
									init: function (x, q, D) {
										(this.cfg = this.cfg.extend(D)), (this.fs = x), (this.gs = q), this.reset();
									},
									reset: function () {
										f.reset.call(this), this.Jr();
									},
									process: function (x) {
										return this.Wr(x), this.Sa();
									},
									finalize: function (x) {
										return x && this.Wr(x), this.Vr();
									},
									keySize: 4,
									ivSize: 4,
									ds: 1,
									hs: 2,
									Yr: (function () {
										function x(q) {
											return typeof q == "string" ? C : I;
										}
										return function (q) {
											return {
												encrypt: function (D, A, M) {
													return x(A).encrypt(q, D, A, M);
												},
												decrypt: function (D, A, M) {
													return x(A).decrypt(q, D, A, M);
												},
											};
										};
									})(),
								}));
							d.StreamCipher = b.extend({
								Vr: function () {
									return this.Sa(!0);
								},
								blockSize: 1,
							});
							var w = (c.mode = {}),
								_ = (d.BlockCipherMode = h.extend({
									createEncryptor: function (x, q) {
										return this.Encryptor.create(x, q);
									},
									createDecryptor: function (x, q) {
										return this.Decryptor.create(x, q);
									},
									init: function (x, q) {
										(this.ps = x), (this.ys = q);
									},
								})),
								v = (w.CBC = (function () {
									var x = _.extend();
									function q(D, A, M) {
										var P,
											E = this.ys;
										E ? ((P = E), (this.ys = l)) : (P = this.bs);
										for (var L = 0; L < M; L++) D[A + L] ^= P[L];
									}
									return (
										(x.Encryptor = x.extend({
											processBlock: function (D, A) {
												var M = this.ps,
													P = M.blockSize;
												q.call(this, D, A, P), M.encryptBlock(D, A), (this.bs = D.slice(A, A + P));
											},
										})),
										(x.Decryptor = x.extend({
											processBlock: function (D, A) {
												var M = this.ps,
													P = M.blockSize,
													E = D.slice(A, A + P);
												M.decryptBlock(D, A), q.call(this, D, A, P), (this.bs = E);
											},
										})),
										x
									);
								})()),
								j = ((c.pad = {}).Pkcs7 = {
									pad: function (x, q) {
										for (var D = 4 * q, A = D - (x.sigBytes % D), M = (A << 24) | (A << 16) | (A << 8) | A, P = [], E = 0; E < A; E += 4) P.push(M);
										var L = g.create(P, A);
										x.concat(L);
									},
									unpad: function (x) {
										var q = 255 & x.words[(x.sigBytes - 1) >>> 2];
										x.sigBytes -= q;
									},
								});
							d.BlockCipher = b.extend({
								cfg: b.cfg.extend({ mode: v, padding: j }),
								reset: function () {
									var x;
									b.reset.call(this);
									var q = this.cfg,
										D = q.iv,
										A = q.mode;
									this.fs == this.ds ? (x = A.createEncryptor) : ((x = A.createDecryptor), (this.zr = 1)),
										this.ws && this.ws._s == x ? this.ws.init(this, D && D.words) : ((this.ws = x.call(A, this, D && D.words)), (this.ws._s = x));
								},
								Xr: function (x, q) {
									this.ws.processBlock(x, q);
								},
								Vr: function () {
									var x,
										q = this.cfg.padding;
									return this.fs == this.ds ? (q.pad(this.Gr, this.blockSize), (x = this.Sa(!0))) : ((x = this.Sa(!0)), q.unpad(x)), x;
								},
								blockSize: 4,
							});
							var T = (d.CipherParams = h.extend({
								init: function (x) {
									this.mixIn(x);
								},
								toString: function (x) {
									return (x || this.formatter).stringify(this);
								},
							})),
								k = ((c.format = {}).OpenSSL = {
									stringify: function (x) {
										var q = x.ciphertext,
											D = x.salt;
										return (D ? g.create([1398893684, 1701076831]).concat(D).concat(q) : q).toString(m);
									},
									parse: function (x) {
										var q,
											D = m.parse(x),
											A = D.words;
										return A[0] == 1398893684 && A[1] == 1701076831 && ((q = g.create(A.slice(2, 4))), A.splice(0, 4), (D.sigBytes -= 16)), T.create({ ciphertext: D, salt: q });
									},
								}),
								I = (d.SerializableCipher = h.extend({
									cfg: h.extend({ format: k }),
									encrypt: function (x, q, D, A) {
										A = this.cfg.extend(A);
										var M = x.createEncryptor(D, A),
											P = M.finalize(q),
											E = M.cfg;
										return T.create({ ciphertext: P, key: D, iv: E.iv, algorithm: x, mode: E.mode, padding: E.padding, blockSize: x.blockSize, formatter: A.format });
									},
									decrypt: function (x, q, D, A) {
										return (A = this.cfg.extend(A)), (q = this.vs(q, A.format)), x.createDecryptor(D, A).finalize(q.ciphertext);
									},
									vs: function (x, q) {
										return typeof x == "string" ? q.parse(x, this) : x;
									},
								})),
								S = ((c.kdf = {}).OpenSSL = {
									execute: function (x, q, D, A, M) {
										if ((A || (A = g.random(8)), M)) P = y.create({ keySize: q + D, hasher: M }).compute(x, A);
										else var P = y.create({ keySize: q + D }).compute(x, A);
										var E = g.create(P.words.slice(q), 4 * D);
										return (P.sigBytes = 4 * q), T.create({ key: P, iv: E, salt: A });
									},
								}),
								C = (d.PasswordBasedCipher = I.extend({
									cfg: I.cfg.extend({ kdf: S }),
									encrypt: function (x, q, D, A) {
										var M = (A = this.cfg.extend(A)).kdf.execute(D, x.keySize, x.ivSize, A.salt, A.hasher);
										A.iv = M.iv;
										var P = I.encrypt.call(this, x, q, M.key, A);
										return P.mixIn(M), P;
									},
									decrypt: function (x, q, D, A) {
										(A = this.cfg.extend(A)), (q = this.vs(q, A.format));
										var M = A.kdf.execute(D, x.keySize, x.ivSize, q.salt, A.hasher);
										return (A.iv = M.iv), I.decrypt.call(this, x, q, M.key, A);
									},
								}));
						})();
				})(requireCore(), requireEvpkdf()))),
			cipherCore.exports
		);
	}
	var modeCfb = { exports: {} },
		hasRequiredModeCfb;
	function requireModeCfb() {
		return (
			hasRequiredModeCfb ||
			((hasRequiredModeCfb = 1),
				(modeCfb.exports = (function (n) {
					return (
						(n.mode.CFB = (function () {
							var l = n.lib.BlockCipherMode.extend();
							function c(d, h, g, f) {
								var p,
									m = this.ys;
								m ? ((p = m.slice(0)), (this.ys = void 0)) : (p = this.bs), f.encryptBlock(p, 0);
								for (var y = 0; y < g; y++) d[h + y] ^= p[y];
							}
							return (
								(l.Encryptor = l.extend({
									processBlock: function (d, h) {
										var g = this.ps,
											f = g.blockSize;
										c.call(this, d, h, f, g), (this.bs = d.slice(h, h + f));
									},
								})),
								(l.Decryptor = l.extend({
									processBlock: function (d, h) {
										var g = this.ps,
											f = g.blockSize,
											p = d.slice(h, h + f);
										c.call(this, d, h, f, g), (this.bs = p);
									},
								})),
								l
							);
						})()),
						n.mode.CFB
					);
				})(requireCore(), requireCipherCore()))),
			modeCfb.exports
		);
	}
	var modeCtr = { exports: {} },
		hasRequiredModeCtr;
	function requireModeCtr() {
		return (
			hasRequiredModeCtr ||
			((hasRequiredModeCtr = 1),
				(modeCtr.exports = (function (n) {
					return (
						(n.mode.CTR =
							((l = n.lib.BlockCipherMode.extend()),
								(c = l.Encryptor = l.extend({
									processBlock: function (d, h) {
										var g = this.ps,
											f = g.blockSize,
											p = this.ys,
											m = this.ks;
										p && ((m = this.ks = p.slice(0)), (this.ys = void 0));
										var y = m.slice(0);
										g.encryptBlock(y, 0), (m[f - 1] = (m[f - 1] + 1) | 0);
										for (var b = 0; b < f; b++) d[h + b] ^= y[b];
									},
								})),
								(l.Decryptor = c),
								l)),
						n.mode.CTR
					);
					var l, c;
				})(requireCore(), requireCipherCore()))),
			modeCtr.exports
		);
	}
	var modeCtrGladman = { exports: {} },
		hasRequiredModeCtrGladman;
	function requireModeCtrGladman() {
		return (
			hasRequiredModeCtrGladman ||
			((hasRequiredModeCtrGladman = 1),
				(modeCtrGladman.exports = (function (n) {
					return (
						(n.mode.CTRGladman = (function () {
							var l = n.lib.BlockCipherMode.extend();
							function c(g) {
								if (255 & ~(g >> 24)) g += 1 << 24;
								else {
									var f = (g >> 16) & 255,
										p = (g >> 8) & 255,
										m = 255 & g;
									f === 255 ? ((f = 0), p === 255 ? ((p = 0), m === 255 ? (m = 0) : ++m) : ++p) : ++f, (g = 0), (g += f << 16), (g += p << 8), (g += m);
								}
								return g;
							}
							function d(g) {
								return (g[0] = c(g[0])) === 0 && (g[1] = c(g[1])), g;
							}
							var h = (l.Encryptor = l.extend({
								processBlock: function (g, f) {
									var p = this.ps,
										m = p.blockSize,
										y = this.ys,
										b = this.ks;
									y && ((b = this.ks = y.slice(0)), (this.ys = void 0)), d(b);
									var w = b.slice(0);
									p.encryptBlock(w, 0);
									for (var _ = 0; _ < m; _++) g[f + _] ^= w[_];
								},
							}));
							return (l.Decryptor = h), l;
						})()),
						n.mode.CTRGladman
					);
				})(requireCore(), requireCipherCore()))),
			modeCtrGladman.exports
		);
	}
	var modeOfb = { exports: {} },
		hasRequiredModeOfb;
	function requireModeOfb() {
		return (
			hasRequiredModeOfb ||
			((hasRequiredModeOfb = 1),
				(modeOfb.exports = (function (n) {
					return (
						(n.mode.OFB =
							((l = n.lib.BlockCipherMode.extend()),
								(c = l.Encryptor = l.extend({
									processBlock: function (d, h) {
										var g = this.ps,
											f = g.blockSize,
											p = this.ys,
											m = this.Is;
										p && ((m = this.Is = p.slice(0)), (this.ys = void 0)), g.encryptBlock(m, 0);
										for (var y = 0; y < f; y++) d[h + y] ^= m[y];
									},
								})),
								(l.Decryptor = c),
								l)),
						n.mode.OFB
					);
					var l, c;
				})(requireCore(), requireCipherCore()))),
			modeOfb.exports
		);
	}
	var modeEcb = { exports: {} },
		hasRequiredModeEcb;
	function requireModeEcb() {
		return (
			hasRequiredModeEcb ||
			((hasRequiredModeEcb = 1),
				(modeEcb.exports = (function (n) {
					return (
						(n.mode.ECB =
							(((l = n.lib.BlockCipherMode.extend()).Encryptor = l.extend({
								processBlock: function (c, d) {
									this.ps.encryptBlock(c, d);
								},
							})),
								(l.Decryptor = l.extend({
									processBlock: function (c, d) {
										this.ps.decryptBlock(c, d);
									},
								})),
								l)),
						n.mode.ECB
					);
					var l;
				})(requireCore(), requireCipherCore()))),
			modeEcb.exports
		);
	}
	var padAnsix923 = { exports: {} },
		hasRequiredPadAnsix923;
	function requirePadAnsix923() {
		return (
			hasRequiredPadAnsix923 ||
			((hasRequiredPadAnsix923 = 1),
				(padAnsix923.exports = (function (n) {
					return (
						(n.pad.AnsiX923 = {
							pad: function (l, c) {
								var d = l.sigBytes,
									h = 4 * c,
									g = h - (d % h),
									f = d + g - 1;
								l.clamp(), (l.words[f >>> 2] |= g << (24 - (f % 4) * 8)), (l.sigBytes += g);
							},
							unpad: function (l) {
								var c = 255 & l.words[(l.sigBytes - 1) >>> 2];
								l.sigBytes -= c;
							},
						}),
						n.pad.Ansix923
					);
				})(requireCore(), requireCipherCore()))),
			padAnsix923.exports
		);
	}
	var padIso10126 = { exports: {} },
		hasRequiredPadIso10126;
	function requirePadIso10126() {
		return (
			hasRequiredPadIso10126 ||
			((hasRequiredPadIso10126 = 1),
				(padIso10126.exports = (function (n) {
					return (
						(n.pad.Iso10126 = {
							pad: function (l, c) {
								var d = 4 * c,
									h = d - (l.sigBytes % d);
								l.concat(n.lib.WordArray.random(h - 1)).concat(n.lib.WordArray.create([h << 24], 1));
							},
							unpad: function (l) {
								var c = 255 & l.words[(l.sigBytes - 1) >>> 2];
								l.sigBytes -= c;
							},
						}),
						n.pad.Iso10126
					);
				})(requireCore(), requireCipherCore()))),
			padIso10126.exports
		);
	}
	var padIso97971 = { exports: {} },
		hasRequiredPadIso97971;
	function requirePadIso97971() {
		return (
			hasRequiredPadIso97971 ||
			((hasRequiredPadIso97971 = 1),
				(padIso97971.exports = (function (n) {
					return (
						(n.pad.Iso97971 = {
							pad: function (l, c) {
								l.concat(n.lib.WordArray.create([2147483648], 1)), n.pad.ZeroPadding.pad(l, c);
							},
							unpad: function (l) {
								n.pad.ZeroPadding.unpad(l), l.sigBytes--;
							},
						}),
						n.pad.Iso97971
					);
				})(requireCore(), requireCipherCore()))),
			padIso97971.exports
		);
	}
	var padZeropadding = { exports: {} },
		hasRequiredPadZeropadding;
	function requirePadZeropadding() {
		return (
			hasRequiredPadZeropadding ||
			((hasRequiredPadZeropadding = 1),
				(padZeropadding.exports = (function (n) {
					return (
						(n.pad.ZeroPadding = {
							pad: function (l, c) {
								var d = 4 * c;
								l.clamp(), (l.sigBytes += d - (l.sigBytes % d || d));
							},
							unpad: function (l) {
								var c = l.words,
									d = l.sigBytes - 1;
								for (d = l.sigBytes - 1; d >= 0; d--)
									if ((c[d >>> 2] >>> (24 - (d % 4) * 8)) & 255) {
										l.sigBytes = d + 1;
										break;
									}
							},
						}),
						n.pad.ZeroPadding
					);
				})(requireCore(), requireCipherCore()))),
			padZeropadding.exports
		);
	}
	var padNopadding = { exports: {} },
		hasRequiredPadNopadding;
	function requirePadNopadding() {
		return (
			hasRequiredPadNopadding ||
			((hasRequiredPadNopadding = 1),
				(padNopadding.exports = (function (n) {
					return (n.pad.NoPadding = { pad: function () { }, unpad: function () { } }), n.pad.NoPadding;
				})(requireCore(), requireCipherCore()))),
			padNopadding.exports
		);
	}
	var formatHex = { exports: {} },
		hasRequiredFormatHex;
	function requireFormatHex() {
		return (
			hasRequiredFormatHex ||
			((hasRequiredFormatHex = 1),
				(formatHex.exports = (function (n) {
					return (
						(c = (l = n).lib.CipherParams),
						(d = l.enc.Hex),
						(l.format.Hex = {
							stringify: function (h) {
								return h.ciphertext.toString(d);
							},
							parse: function (h) {
								var g = d.parse(h);
								return c.create({ ciphertext: g });
							},
						}),
						n.format.Hex
					);
					var l, c, d;
				})(requireCore(), requireCipherCore()))),
			formatHex.exports
		);
	}
	var aes = { exports: {} },
		hasRequiredAes;
	function requireAes() {
		return (
			hasRequiredAes ||
			((hasRequiredAes = 1),
				(aes.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.BlockCipher,
								d = l.algo,
								h = [],
								g = [],
								f = [],
								p = [],
								m = [],
								y = [],
								b = [],
								w = [],
								_ = [],
								v = [];
							(function () {
								for (var k = [], I = 0; I < 256; I++) k[I] = I < 128 ? I << 1 : (I << 1) ^ 283;
								var S = 0,
									C = 0;
								for (I = 0; I < 256; I++) {
									var x = C ^ (C << 1) ^ (C << 2) ^ (C << 3) ^ (C << 4);
									(x = (x >>> 8) ^ (255 & x) ^ 99), (h[S] = x), (g[x] = S);
									var q = k[S],
										D = k[q],
										A = k[D],
										M = (257 * k[x]) ^ (16843008 * x);
									(f[S] = (M << 24) | (M >>> 8)),
										(p[S] = (M << 16) | (M >>> 16)),
										(m[S] = (M << 8) | (M >>> 24)),
										(y[S] = M),
										(M = (16843009 * A) ^ (65537 * D) ^ (257 * q) ^ (16843008 * S)),
										(b[x] = (M << 24) | (M >>> 8)),
										(w[x] = (M << 16) | (M >>> 16)),
										(_[x] = (M << 8) | (M >>> 24)),
										(v[x] = M),
										S ? ((S = q ^ k[k[k[A ^ q]]]), (C ^= k[k[C]])) : (S = C = 1);
								}
							})();
							var j = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
								T = (d.AES = c.extend({
									Jr: function () {
										if (!this.Ss || this.Ds !== this.gs) {
											for (var k = (this.Ds = this.gs), I = k.words, S = k.sigBytes / 4, C = 4 * ((this.Ss = S + 6) + 1), x = (this.qs = []), q = 0; q < C; q++)
												q < S
													? (x[q] = I[q])
													: ((M = x[q - 1]),
														q % S
															? S > 6 && q % S == 4 && (M = (h[M >>> 24] << 24) | (h[(M >>> 16) & 255] << 16) | (h[(M >>> 8) & 255] << 8) | h[255 & M])
															: ((M = (h[(M = (M << 8) | (M >>> 24)) >>> 24] << 24) | (h[(M >>> 16) & 255] << 16) | (h[(M >>> 8) & 255] << 8) | h[255 & M]), (M ^= j[(q / S) | 0] << 24)),
														(x[q] = x[q - S] ^ M));
											for (var D = (this.As = []), A = 0; A < C; A++) {
												if (((q = C - A), A % 4)) var M = x[q];
												else M = x[q - 4];
												D[A] = A < 4 || q <= 4 ? M : b[h[M >>> 24]] ^ w[h[(M >>> 16) & 255]] ^ _[h[(M >>> 8) & 255]] ^ v[h[255 & M]];
											}
										}
									},
									encryptBlock: function (k, I) {
										this.Cs(k, I, this.qs, f, p, m, y, h);
									},
									decryptBlock: function (k, I) {
										var S = k[I + 1];
										(k[I + 1] = k[I + 3]), (k[I + 3] = S), this.Cs(k, I, this.As, b, w, _, v, g), (S = k[I + 1]), (k[I + 1] = k[I + 3]), (k[I + 3] = S);
									},
									Cs: function (k, I, S, C, x, q, D, A) {
										for (var M = this.Ss, P = k[I] ^ S[0], E = k[I + 1] ^ S[1], L = k[I + 2] ^ S[2], W = k[I + 3] ^ S[3], O = 4, G = 1; G < M; G++) {
											var U = C[P >>> 24] ^ x[(E >>> 16) & 255] ^ q[(L >>> 8) & 255] ^ D[255 & W] ^ S[O++],
												F = C[E >>> 24] ^ x[(L >>> 16) & 255] ^ q[(W >>> 8) & 255] ^ D[255 & P] ^ S[O++],
												H = C[L >>> 24] ^ x[(W >>> 16) & 255] ^ q[(P >>> 8) & 255] ^ D[255 & E] ^ S[O++],
												Y = C[W >>> 24] ^ x[(P >>> 16) & 255] ^ q[(E >>> 8) & 255] ^ D[255 & L] ^ S[O++];
											(P = U), (E = F), (L = H), (W = Y);
										}
										(U = ((A[P >>> 24] << 24) | (A[(E >>> 16) & 255] << 16) | (A[(L >>> 8) & 255] << 8) | A[255 & W]) ^ S[O++]),
											(F = ((A[E >>> 24] << 24) | (A[(L >>> 16) & 255] << 16) | (A[(W >>> 8) & 255] << 8) | A[255 & P]) ^ S[O++]),
											(H = ((A[L >>> 24] << 24) | (A[(W >>> 16) & 255] << 16) | (A[(P >>> 8) & 255] << 8) | A[255 & E]) ^ S[O++]),
											(Y = ((A[W >>> 24] << 24) | (A[(P >>> 16) & 255] << 16) | (A[(E >>> 8) & 255] << 8) | A[255 & L]) ^ S[O++]),
											(k[I] = U),
											(k[I + 1] = F),
											(k[I + 2] = H),
											(k[I + 3] = Y);
									},
									keySize: 8,
								}));
							l.AES = c.Yr(T);
						})(),
						n.AES
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			aes.exports
		);
	}
	var tripledes = { exports: {} },
		hasRequiredTripledes;
	function requireTripledes() {
		return (
			hasRequiredTripledes ||
			((hasRequiredTripledes = 1),
				(tripledes.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib,
								d = c.WordArray,
								h = c.BlockCipher,
								g = l.algo,
								f = [
									57,
									49,
									41,
									33,
									25,
									17,
									9,
									1,
									58,
									50,
									42,
									34,
									26,
									18,
									10,
									2,
									59,
									51,
									43,
									35,
									27,
									19,
									11,
									3,
									60,
									52,
									44,
									36,
									63,
									55,
									47,
									39,
									31,
									23,
									15,
									7,
									62,
									54,
									46,
									38,
									30,
									22,
									14,
									6,
									61,
									53,
									45,
									37,
									29,
									21,
									13,
									5,
									28,
									20,
									12,
									4,
								],
								p = [14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26, 8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32],
								m = [1, 2, 4, 6, 8, 10, 12, 14, 15, 17, 19, 21, 23, 25, 27, 28],
								y = [
									{
										0: 8421888,
										268435456: 32768,
										536870912: 8421378,
										805306368: 2,
										1073741824: 512,
										1342177280: 8421890,
										1610612736: 8389122,
										1879048192: 8388608,
										2147483648: 514,
										2415919104: 8389120,
										2684354560: 33280,
										2952790016: 8421376,
										3221225472: 32770,
										3489660928: 8388610,
										3758096384: 0,
										4026531840: 33282,
										134217728: 0,
										402653184: 8421890,
										671088640: 33282,
										939524096: 32768,
										1207959552: 8421888,
										1476395008: 512,
										1744830464: 8421378,
										2013265920: 2,
										2281701376: 8389120,
										2550136832: 33280,
										2818572288: 8421376,
										3087007744: 8389122,
										3355443200: 8388610,
										3623878656: 32770,
										3892314112: 514,
										4160749568: 8388608,
										1: 32768,
										268435457: 2,
										536870913: 8421888,
										805306369: 8388608,
										1073741825: 8421378,
										1342177281: 33280,
										1610612737: 512,
										1879048193: 8389122,
										2147483649: 8421890,
										2415919105: 8421376,
										2684354561: 8388610,
										2952790017: 33282,
										3221225473: 514,
										3489660929: 8389120,
										3758096385: 32770,
										4026531841: 0,
										134217729: 8421890,
										402653185: 8421376,
										671088641: 8388608,
										939524097: 512,
										1207959553: 32768,
										1476395009: 8388610,
										1744830465: 2,
										2013265921: 33282,
										2281701377: 32770,
										2550136833: 8389122,
										2818572289: 514,
										3087007745: 8421888,
										3355443201: 8389120,
										3623878657: 0,
										3892314113: 33280,
										4160749569: 8421378,
									},
									{
										0: 1074282512,
										16777216: 16384,
										33554432: 524288,
										50331648: 1074266128,
										67108864: 1073741840,
										83886080: 1074282496,
										100663296: 1073758208,
										117440512: 16,
										134217728: 540672,
										150994944: 1073758224,
										167772160: 1073741824,
										184549376: 540688,
										201326592: 524304,
										218103808: 0,
										234881024: 16400,
										251658240: 1074266112,
										8388608: 1073758208,
										25165824: 540688,
										41943040: 16,
										58720256: 1073758224,
										75497472: 1074282512,
										92274688: 1073741824,
										109051904: 524288,
										125829120: 1074266128,
										142606336: 524304,
										159383552: 0,
										176160768: 16384,
										192937984: 1074266112,
										209715200: 1073741840,
										226492416: 540672,
										243269632: 1074282496,
										260046848: 16400,
										268435456: 0,
										285212672: 1074266128,
										301989888: 1073758224,
										318767104: 1074282496,
										335544320: 1074266112,
										352321536: 16,
										369098752: 540688,
										385875968: 16384,
										402653184: 16400,
										419430400: 524288,
										436207616: 524304,
										452984832: 1073741840,
										469762048: 540672,
										486539264: 1073758208,
										503316480: 1073741824,
										520093696: 1074282512,
										276824064: 540688,
										293601280: 524288,
										310378496: 1074266112,
										327155712: 16384,
										343932928: 1073758208,
										360710144: 1074282512,
										377487360: 16,
										394264576: 1073741824,
										411041792: 1074282496,
										427819008: 1073741840,
										444596224: 1073758224,
										461373440: 524304,
										478150656: 0,
										494927872: 16400,
										511705088: 1074266128,
										528482304: 540672,
									},
									{
										0: 260,
										1048576: 0,
										2097152: 67109120,
										3145728: 65796,
										4194304: 65540,
										5242880: 67108868,
										6291456: 67174660,
										7340032: 67174400,
										8388608: 67108864,
										9437184: 67174656,
										10485760: 65792,
										11534336: 67174404,
										12582912: 67109124,
										13631488: 65536,
										14680064: 4,
										15728640: 256,
										524288: 67174656,
										1572864: 67174404,
										2621440: 0,
										3670016: 67109120,
										4718592: 67108868,
										5767168: 65536,
										6815744: 65540,
										7864320: 260,
										8912896: 4,
										9961472: 256,
										11010048: 67174400,
										12058624: 65796,
										13107200: 65792,
										14155776: 67109124,
										15204352: 67174660,
										16252928: 67108864,
										16777216: 67174656,
										17825792: 65540,
										18874368: 65536,
										19922944: 67109120,
										20971520: 256,
										22020096: 67174660,
										23068672: 67108868,
										24117248: 0,
										25165824: 67109124,
										26214400: 67108864,
										27262976: 4,
										28311552: 65792,
										29360128: 67174400,
										30408704: 260,
										31457280: 65796,
										32505856: 67174404,
										17301504: 67108864,
										18350080: 260,
										19398656: 67174656,
										20447232: 0,
										21495808: 65540,
										22544384: 67109120,
										23592960: 256,
										24641536: 67174404,
										25690112: 65536,
										26738688: 67174660,
										27787264: 65796,
										28835840: 67108868,
										29884416: 67109124,
										30932992: 67174400,
										31981568: 4,
										33030144: 65792,
									},
									{
										0: 2151682048,
										65536: 2147487808,
										131072: 4198464,
										196608: 2151677952,
										262144: 0,
										327680: 4198400,
										393216: 2147483712,
										458752: 4194368,
										524288: 2147483648,
										589824: 4194304,
										655360: 64,
										720896: 2147487744,
										786432: 2151678016,
										851968: 4160,
										917504: 4096,
										983040: 2151682112,
										32768: 2147487808,
										98304: 64,
										163840: 2151678016,
										229376: 2147487744,
										294912: 4198400,
										360448: 2151682112,
										425984: 0,
										491520: 2151677952,
										557056: 4096,
										622592: 2151682048,
										688128: 4194304,
										753664: 4160,
										819200: 2147483648,
										884736: 4194368,
										950272: 4198464,
										1015808: 2147483712,
										1048576: 4194368,
										1114112: 4198400,
										1179648: 2147483712,
										1245184: 0,
										1310720: 4160,
										1376256: 2151678016,
										1441792: 2151682048,
										1507328: 2147487808,
										1572864: 2151682112,
										1638400: 2147483648,
										1703936: 2151677952,
										1769472: 4198464,
										1835008: 2147487744,
										1900544: 4194304,
										1966080: 64,
										2031616: 4096,
										1081344: 2151677952,
										1146880: 2151682112,
										1212416: 0,
										1277952: 4198400,
										1343488: 4194368,
										1409024: 2147483648,
										1474560: 2147487808,
										1540096: 64,
										1605632: 2147483712,
										1671168: 4096,
										1736704: 2147487744,
										1802240: 2151678016,
										1867776: 4160,
										1933312: 2151682048,
										1998848: 4194304,
										2064384: 4198464,
									},
									{
										0: 128,
										4096: 17039360,
										8192: 262144,
										12288: 536870912,
										16384: 537133184,
										20480: 16777344,
										24576: 553648256,
										28672: 262272,
										32768: 16777216,
										36864: 537133056,
										40960: 536871040,
										45056: 553910400,
										49152: 553910272,
										53248: 0,
										57344: 17039488,
										61440: 553648128,
										2048: 17039488,
										6144: 553648256,
										10240: 128,
										14336: 17039360,
										18432: 262144,
										22528: 537133184,
										26624: 553910272,
										30720: 536870912,
										34816: 537133056,
										38912: 0,
										43008: 553910400,
										47104: 16777344,
										51200: 536871040,
										55296: 553648128,
										59392: 16777216,
										63488: 262272,
										65536: 262144,
										69632: 128,
										73728: 536870912,
										77824: 553648256,
										81920: 16777344,
										86016: 553910272,
										90112: 537133184,
										94208: 16777216,
										98304: 553910400,
										102400: 553648128,
										106496: 17039360,
										110592: 537133056,
										114688: 262272,
										118784: 536871040,
										122880: 0,
										126976: 17039488,
										67584: 553648256,
										71680: 16777216,
										75776: 17039360,
										79872: 537133184,
										83968: 536870912,
										88064: 17039488,
										92160: 128,
										96256: 553910272,
										100352: 262272,
										104448: 553910400,
										108544: 0,
										112640: 553648128,
										116736: 16777344,
										120832: 262144,
										124928: 537133056,
										129024: 536871040,
									},
									{
										0: 268435464,
										256: 8192,
										512: 270532608,
										768: 270540808,
										1024: 268443648,
										1280: 2097152,
										1536: 2097160,
										1792: 268435456,
										2048: 0,
										2304: 268443656,
										2560: 2105344,
										2816: 8,
										3072: 270532616,
										3328: 2105352,
										3584: 8200,
										3840: 270540800,
										128: 270532608,
										384: 270540808,
										640: 8,
										896: 2097152,
										1152: 2105352,
										1408: 268435464,
										1664: 268443648,
										1920: 8200,
										2176: 2097160,
										2432: 8192,
										2688: 268443656,
										2944: 270532616,
										3200: 0,
										3456: 270540800,
										3712: 2105344,
										3968: 268435456,
										4096: 268443648,
										4352: 270532616,
										4608: 270540808,
										4864: 8200,
										5120: 2097152,
										5376: 268435456,
										5632: 268435464,
										5888: 2105344,
										6144: 2105352,
										6400: 0,
										6656: 8,
										6912: 270532608,
										7168: 8192,
										7424: 268443656,
										7680: 270540800,
										7936: 2097160,
										4224: 8,
										4480: 2105344,
										4736: 2097152,
										4992: 268435464,
										5248: 268443648,
										5504: 8200,
										5760: 270540808,
										6016: 270532608,
										6272: 270540800,
										6528: 270532616,
										6784: 8192,
										7040: 2105352,
										7296: 2097160,
										7552: 0,
										7808: 268435456,
										8064: 268443656,
									},
									{
										0: 1048576,
										16: 33555457,
										32: 1024,
										48: 1049601,
										64: 34604033,
										80: 0,
										96: 1,
										112: 34603009,
										128: 33555456,
										144: 1048577,
										160: 33554433,
										176: 34604032,
										192: 34603008,
										208: 1025,
										224: 1049600,
										240: 33554432,
										8: 34603009,
										24: 0,
										40: 33555457,
										56: 34604032,
										72: 1048576,
										88: 33554433,
										104: 33554432,
										120: 1025,
										136: 1049601,
										152: 33555456,
										168: 34603008,
										184: 1048577,
										200: 1024,
										216: 34604033,
										232: 1,
										248: 1049600,
										256: 33554432,
										272: 1048576,
										288: 33555457,
										304: 34603009,
										320: 1048577,
										336: 33555456,
										352: 34604032,
										368: 1049601,
										384: 1025,
										400: 34604033,
										416: 1049600,
										432: 1,
										448: 0,
										464: 34603008,
										480: 33554433,
										496: 1024,
										264: 1049600,
										280: 33555457,
										296: 34603009,
										312: 1,
										328: 33554432,
										344: 1048576,
										360: 1025,
										376: 34604032,
										392: 33554433,
										408: 34603008,
										424: 0,
										440: 34604033,
										456: 1049601,
										472: 1024,
										488: 33555456,
										504: 1048577,
									},
									{
										0: 134219808,
										1: 131072,
										2: 134217728,
										3: 32,
										4: 131104,
										5: 134350880,
										6: 134350848,
										7: 2048,
										8: 134348800,
										9: 134219776,
										10: 133120,
										11: 134348832,
										12: 2080,
										13: 0,
										14: 134217760,
										15: 133152,
										2147483648: 2048,
										2147483649: 134350880,
										2147483650: 134219808,
										2147483651: 134217728,
										2147483652: 134348800,
										2147483653: 133120,
										2147483654: 133152,
										2147483655: 32,
										2147483656: 134217760,
										2147483657: 2080,
										2147483658: 131104,
										2147483659: 134350848,
										2147483660: 0,
										2147483661: 134348832,
										2147483662: 134219776,
										2147483663: 131072,
										16: 133152,
										17: 134350848,
										18: 32,
										19: 2048,
										20: 134219776,
										21: 134217760,
										22: 134348832,
										23: 131072,
										24: 0,
										25: 131104,
										26: 134348800,
										27: 134219808,
										28: 134350880,
										29: 133120,
										30: 2080,
										31: 134217728,
										2147483664: 131072,
										2147483665: 2048,
										2147483666: 134348832,
										2147483667: 133152,
										2147483668: 32,
										2147483669: 134348800,
										2147483670: 134217728,
										2147483671: 134219808,
										2147483672: 134350880,
										2147483673: 134217760,
										2147483674: 134219776,
										2147483675: 0,
										2147483676: 133120,
										2147483677: 2080,
										2147483678: 131104,
										2147483679: 134350848,
									},
								],
								b = [4160749569, 528482304, 33030144, 2064384, 129024, 8064, 504, 2147483679],
								w = (g.DES = h.extend({
									Jr: function () {
										for (var T = this.gs.words, k = [], I = 0; I < 56; I++) {
											var S = f[I] - 1;
											k[I] = (T[S >>> 5] >>> (31 - (S % 32))) & 1;
										}
										for (var C = (this.Ps = []), x = 0; x < 16; x++) {
											var q = (C[x] = []),
												D = m[x];
											for (I = 0; I < 24; I++) (q[(I / 6) | 0] |= k[(p[I] - 1 + D) % 28] << (31 - (I % 6))), (q[4 + ((I / 6) | 0)] |= k[28 + ((p[I + 24] - 1 + D) % 28)] << (31 - (I % 6)));
											for (q[0] = (q[0] << 1) | (q[0] >>> 31), I = 1; I < 7; I++) q[I] = q[I] >>> (4 * (I - 1) + 3);
											q[7] = (q[7] << 5) | (q[7] >>> 27);
										}
										var A = (this.xs = []);
										for (I = 0; I < 16; I++) A[I] = C[15 - I];
									},
									encryptBlock: function (T, k) {
										this.Cs(T, k, this.Ps);
									},
									decryptBlock: function (T, k) {
										this.Cs(T, k, this.xs);
									},
									Cs: function (T, k, I) {
										(this.Ts = T[k]), (this.js = T[k + 1]), _.call(this, 4, 252645135), _.call(this, 16, 65535), v.call(this, 2, 858993459), v.call(this, 8, 16711935), _.call(this, 1, 1431655765);
										for (var S = 0; S < 16; S++) {
											for (var C = I[S], x = this.Ts, q = this.js, D = 0, A = 0; A < 8; A++) D |= y[A][((q ^ C[A]) & b[A]) >>> 0];
											(this.Ts = q), (this.js = x ^ D);
										}
										var M = this.Ts;
										(this.Ts = this.js),
											(this.js = M),
											_.call(this, 1, 1431655765),
											v.call(this, 8, 16711935),
											v.call(this, 2, 858993459),
											_.call(this, 16, 65535),
											_.call(this, 4, 252645135),
											(T[k] = this.Ts),
											(T[k + 1] = this.js);
									},
									keySize: 2,
									ivSize: 2,
									blockSize: 2,
								}));
							function _(T, k) {
								var I = ((this.Ts >>> T) ^ this.js) & k;
								(this.js ^= I), (this.Ts ^= I << T);
							}
							function v(T, k) {
								var I = ((this.js >>> T) ^ this.Ts) & k;
								(this.Ts ^= I), (this.js ^= I << T);
							}
							l.DES = h.Yr(w);
							var j = (g.TripleDES = h.extend({
								Jr: function () {
									var T = this.gs.words;
									if (T.length !== 2 && T.length !== 4 && T.length < 6) throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");
									var k = T.slice(0, 2),
										I = T.length < 4 ? T.slice(0, 2) : T.slice(2, 4),
										S = T.length < 6 ? T.slice(0, 2) : T.slice(4, 6);
									(this.Ms = w.createEncryptor(d.create(k))), (this.Qs = w.createEncryptor(d.create(I))), (this.Rs = w.createEncryptor(d.create(S)));
								},
								encryptBlock: function (T, k) {
									this.Ms.encryptBlock(T, k), this.Qs.decryptBlock(T, k), this.Rs.encryptBlock(T, k);
								},
								decryptBlock: function (T, k) {
									this.Rs.decryptBlock(T, k), this.Qs.encryptBlock(T, k), this.Ms.decryptBlock(T, k);
								},
								keySize: 6,
								ivSize: 2,
								blockSize: 2,
							}));
							l.TripleDES = h.Yr(j);
						})(),
						n.TripleDES
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			tripledes.exports
		);
	}
	var rc4 = { exports: {} },
		hasRequiredRc4;
	function requireRc4() {
		return (
			hasRequiredRc4 ||
			((hasRequiredRc4 = 1),
				(rc4.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.StreamCipher,
								d = l.algo,
								h = (d.RC4 = c.extend({
									Jr: function () {
										for (var p = this.gs, m = p.words, y = p.sigBytes, b = (this.Us = []), w = 0; w < 256; w++) b[w] = w;
										w = 0;
										for (var _ = 0; w < 256; w++) {
											var v = w % y,
												j = (m[v >>> 2] >>> (24 - (v % 4) * 8)) & 255;
											_ = (_ + b[w] + j) % 256;
											var T = b[w];
											(b[w] = b[_]), (b[_] = T);
										}
										this.Bs = this.Fs = 0;
									},
									Xr: function (p, m) {
										p[m] ^= g.call(this);
									},
									keySize: 8,
									ivSize: 0,
								}));
							function g() {
								for (var p = this.Us, m = this.Bs, y = this.Fs, b = 0, w = 0; w < 4; w++) {
									y = (y + p[(m = (m + 1) % 256)]) % 256;
									var _ = p[m];
									(p[m] = p[y]), (p[y] = _), (b |= p[(p[m] + p[y]) % 256] << (24 - 8 * w));
								}
								return (this.Bs = m), (this.Fs = y), b;
							}
							l.RC4 = c.Yr(h);
							var f = (d.RC4Drop = h.extend({
								cfg: h.cfg.extend({ drop: 192 }),
								Jr: function () {
									h.Jr.call(this);
									for (var p = this.cfg.drop; p > 0; p--) g.call(this);
								},
							}));
							l.RC4Drop = c.Yr(f);
						})(),
						n.RC4
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			rc4.exports
		);
	}
	var rabbit = { exports: {} },
		hasRequiredRabbit;
	function requireRabbit() {
		return (
			hasRequiredRabbit ||
			((hasRequiredRabbit = 1),
				(rabbit.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.StreamCipher,
								d = l.algo,
								h = [],
								g = [],
								f = [],
								p = (d.Rabbit = c.extend({
									Jr: function () {
										for (var y = this.gs.words, b = this.cfg.iv, w = 0; w < 4; w++) y[w] = (16711935 & ((y[w] << 8) | (y[w] >>> 24))) | (4278255360 & ((y[w] << 24) | (y[w] >>> 8)));
										var _ = (this.$s = [y[0], (y[3] << 16) | (y[2] >>> 16), y[1], (y[0] << 16) | (y[3] >>> 16), y[2], (y[1] << 16) | (y[0] >>> 16), y[3], (y[2] << 16) | (y[1] >>> 16)]),
											v = (this.Es = [
												(y[2] << 16) | (y[2] >>> 16),
												(4294901760 & y[0]) | (65535 & y[1]),
												(y[3] << 16) | (y[3] >>> 16),
												(4294901760 & y[1]) | (65535 & y[2]),
												(y[0] << 16) | (y[0] >>> 16),
												(4294901760 & y[2]) | (65535 & y[3]),
												(y[1] << 16) | (y[1] >>> 16),
												(4294901760 & y[3]) | (65535 & y[0]),
											]);
										for (this.Ls = 0, w = 0; w < 4; w++) m.call(this);
										for (w = 0; w < 8; w++) v[w] ^= _[(w + 4) & 7];
										if (b) {
											var j = b.words,
												T = j[0],
												k = j[1],
												I = (16711935 & ((T << 8) | (T >>> 24))) | (4278255360 & ((T << 24) | (T >>> 8))),
												S = (16711935 & ((k << 8) | (k >>> 24))) | (4278255360 & ((k << 24) | (k >>> 8))),
												C = (I >>> 16) | (4294901760 & S),
												x = (S << 16) | (65535 & I);
											for (v[0] ^= I, v[1] ^= C, v[2] ^= S, v[3] ^= x, v[4] ^= I, v[5] ^= C, v[6] ^= S, v[7] ^= x, w = 0; w < 4; w++) m.call(this);
										}
									},
									Xr: function (y, b) {
										var w = this.$s;
										m.call(this), (h[0] = w[0] ^ (w[5] >>> 16) ^ (w[3] << 16)), (h[1] = w[2] ^ (w[7] >>> 16) ^ (w[5] << 16)), (h[2] = w[4] ^ (w[1] >>> 16) ^ (w[7] << 16)), (h[3] = w[6] ^ (w[3] >>> 16) ^ (w[1] << 16));
										for (var _ = 0; _ < 4; _++) (h[_] = (16711935 & ((h[_] << 8) | (h[_] >>> 24))) | (4278255360 & ((h[_] << 24) | (h[_] >>> 8)))), (y[b + _] ^= h[_]);
									},
									blockSize: 4,
									ivSize: 2,
								}));
							function m() {
								for (var y = this.$s, b = this.Es, w = 0; w < 8; w++) g[w] = b[w];
								for (
									b[0] = (b[0] + 1295307597 + this.Ls) | 0,
									b[1] = (b[1] + 3545052371 + (b[0] >>> 0 < g[0] >>> 0 ? 1 : 0)) | 0,
									b[2] = (b[2] + 886263092 + (b[1] >>> 0 < g[1] >>> 0 ? 1 : 0)) | 0,
									b[3] = (b[3] + 1295307597 + (b[2] >>> 0 < g[2] >>> 0 ? 1 : 0)) | 0,
									b[4] = (b[4] + 3545052371 + (b[3] >>> 0 < g[3] >>> 0 ? 1 : 0)) | 0,
									b[5] = (b[5] + 886263092 + (b[4] >>> 0 < g[4] >>> 0 ? 1 : 0)) | 0,
									b[6] = (b[6] + 1295307597 + (b[5] >>> 0 < g[5] >>> 0 ? 1 : 0)) | 0,
									b[7] = (b[7] + 3545052371 + (b[6] >>> 0 < g[6] >>> 0 ? 1 : 0)) | 0,
									this.Ls = b[7] >>> 0 < g[7] >>> 0 ? 1 : 0,
									w = 0;
									w < 8;
									w++
								) {
									var _ = y[w] + b[w],
										v = 65535 & _,
										j = _ >>> 16,
										T = ((((v * v) >>> 17) + v * j) >>> 15) + j * j,
										k = (((4294901760 & _) * _) | 0) + (((65535 & _) * _) | 0);
									f[w] = T ^ k;
								}
								(y[0] = (f[0] + ((f[7] << 16) | (f[7] >>> 16)) + ((f[6] << 16) | (f[6] >>> 16))) | 0),
									(y[1] = (f[1] + ((f[0] << 8) | (f[0] >>> 24)) + f[7]) | 0),
									(y[2] = (f[2] + ((f[1] << 16) | (f[1] >>> 16)) + ((f[0] << 16) | (f[0] >>> 16))) | 0),
									(y[3] = (f[3] + ((f[2] << 8) | (f[2] >>> 24)) + f[1]) | 0),
									(y[4] = (f[4] + ((f[3] << 16) | (f[3] >>> 16)) + ((f[2] << 16) | (f[2] >>> 16))) | 0),
									(y[5] = (f[5] + ((f[4] << 8) | (f[4] >>> 24)) + f[3]) | 0),
									(y[6] = (f[6] + ((f[5] << 16) | (f[5] >>> 16)) + ((f[4] << 16) | (f[4] >>> 16))) | 0),
									(y[7] = (f[7] + ((f[6] << 8) | (f[6] >>> 24)) + f[5]) | 0);
							}
							l.Rabbit = c.Yr(p);
						})(),
						n.Rabbit
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			rabbit.exports
		);
	}
	var rabbitLegacy = { exports: {} },
		hasRequiredRabbitLegacy;
	function requireRabbitLegacy() {
		return (
			hasRequiredRabbitLegacy ||
			((hasRequiredRabbitLegacy = 1),
				(rabbitLegacy.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.StreamCipher,
								d = l.algo,
								h = [],
								g = [],
								f = [],
								p = (d.RabbitLegacy = c.extend({
									Jr: function () {
										var y = this.gs.words,
											b = this.cfg.iv,
											w = (this.$s = [y[0], (y[3] << 16) | (y[2] >>> 16), y[1], (y[0] << 16) | (y[3] >>> 16), y[2], (y[1] << 16) | (y[0] >>> 16), y[3], (y[2] << 16) | (y[1] >>> 16)]),
											_ = (this.Es = [
												(y[2] << 16) | (y[2] >>> 16),
												(4294901760 & y[0]) | (65535 & y[1]),
												(y[3] << 16) | (y[3] >>> 16),
												(4294901760 & y[1]) | (65535 & y[2]),
												(y[0] << 16) | (y[0] >>> 16),
												(4294901760 & y[2]) | (65535 & y[3]),
												(y[1] << 16) | (y[1] >>> 16),
												(4294901760 & y[3]) | (65535 & y[0]),
											]);
										this.Ls = 0;
										for (var v = 0; v < 4; v++) m.call(this);
										for (v = 0; v < 8; v++) _[v] ^= w[(v + 4) & 7];
										if (b) {
											var j = b.words,
												T = j[0],
												k = j[1],
												I = (16711935 & ((T << 8) | (T >>> 24))) | (4278255360 & ((T << 24) | (T >>> 8))),
												S = (16711935 & ((k << 8) | (k >>> 24))) | (4278255360 & ((k << 24) | (k >>> 8))),
												C = (I >>> 16) | (4294901760 & S),
												x = (S << 16) | (65535 & I);
											for (_[0] ^= I, _[1] ^= C, _[2] ^= S, _[3] ^= x, _[4] ^= I, _[5] ^= C, _[6] ^= S, _[7] ^= x, v = 0; v < 4; v++) m.call(this);
										}
									},
									Xr: function (y, b) {
										var w = this.$s;
										m.call(this), (h[0] = w[0] ^ (w[5] >>> 16) ^ (w[3] << 16)), (h[1] = w[2] ^ (w[7] >>> 16) ^ (w[5] << 16)), (h[2] = w[4] ^ (w[1] >>> 16) ^ (w[7] << 16)), (h[3] = w[6] ^ (w[3] >>> 16) ^ (w[1] << 16));
										for (var _ = 0; _ < 4; _++) (h[_] = (16711935 & ((h[_] << 8) | (h[_] >>> 24))) | (4278255360 & ((h[_] << 24) | (h[_] >>> 8)))), (y[b + _] ^= h[_]);
									},
									blockSize: 4,
									ivSize: 2,
								}));
							function m() {
								for (var y = this.$s, b = this.Es, w = 0; w < 8; w++) g[w] = b[w];
								for (
									b[0] = (b[0] + 1295307597 + this.Ls) | 0,
									b[1] = (b[1] + 3545052371 + (b[0] >>> 0 < g[0] >>> 0 ? 1 : 0)) | 0,
									b[2] = (b[2] + 886263092 + (b[1] >>> 0 < g[1] >>> 0 ? 1 : 0)) | 0,
									b[3] = (b[3] + 1295307597 + (b[2] >>> 0 < g[2] >>> 0 ? 1 : 0)) | 0,
									b[4] = (b[4] + 3545052371 + (b[3] >>> 0 < g[3] >>> 0 ? 1 : 0)) | 0,
									b[5] = (b[5] + 886263092 + (b[4] >>> 0 < g[4] >>> 0 ? 1 : 0)) | 0,
									b[6] = (b[6] + 1295307597 + (b[5] >>> 0 < g[5] >>> 0 ? 1 : 0)) | 0,
									b[7] = (b[7] + 3545052371 + (b[6] >>> 0 < g[6] >>> 0 ? 1 : 0)) | 0,
									this.Ls = b[7] >>> 0 < g[7] >>> 0 ? 1 : 0,
									w = 0;
									w < 8;
									w++
								) {
									var _ = y[w] + b[w],
										v = 65535 & _,
										j = _ >>> 16,
										T = ((((v * v) >>> 17) + v * j) >>> 15) + j * j,
										k = (((4294901760 & _) * _) | 0) + (((65535 & _) * _) | 0);
									f[w] = T ^ k;
								}
								(y[0] = (f[0] + ((f[7] << 16) | (f[7] >>> 16)) + ((f[6] << 16) | (f[6] >>> 16))) | 0),
									(y[1] = (f[1] + ((f[0] << 8) | (f[0] >>> 24)) + f[7]) | 0),
									(y[2] = (f[2] + ((f[1] << 16) | (f[1] >>> 16)) + ((f[0] << 16) | (f[0] >>> 16))) | 0),
									(y[3] = (f[3] + ((f[2] << 8) | (f[2] >>> 24)) + f[1]) | 0),
									(y[4] = (f[4] + ((f[3] << 16) | (f[3] >>> 16)) + ((f[2] << 16) | (f[2] >>> 16))) | 0),
									(y[5] = (f[5] + ((f[4] << 8) | (f[4] >>> 24)) + f[3]) | 0),
									(y[6] = (f[6] + ((f[5] << 16) | (f[5] >>> 16)) + ((f[4] << 16) | (f[4] >>> 16))) | 0),
									(y[7] = (f[7] + ((f[6] << 8) | (f[6] >>> 24)) + f[5]) | 0);
							}
							l.RabbitLegacy = c.Yr(p);
						})(),
						n.RabbitLegacy
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			rabbitLegacy.exports
		);
	}
	var blowfish = { exports: {} },
		hasRequiredBlowfish,
		module;
	function requireBlowfish() {
		return (
			hasRequiredBlowfish ||
			((hasRequiredBlowfish = 1),
				(blowfish.exports = (function (n) {
					return (
						(function () {
							var l = n,
								c = l.lib.BlockCipher,
								d = l.algo;
							const h = 16,
								g = [
									608135816,
									2242054355,
									320440878,
									57701188,
									2752067618,
									698298832,
									137296536,
									3964562569,
									1160258022,
									953160567,
									3193202383,
									887688300,
									3232508343,
									3380367581,
									1065670069,
									3041331479,
									2450970073,
									2306472731,
								],
								f = [
									[
										3509652390,
										2564797868,
										805139163,
										3491422135,
										3101798381,
										1780907670,
										3128725573,
										4046225305,
										614570311,
										3012652279,
										134345442,
										2240740374,
										1667834072,
										1901547113,
										2757295779,
										4103290238,
										227898511,
										1921955416,
										1904987480,
										2182433518,
										2069144605,
										3260701109,
										2620446009,
										720527379,
										3318853667,
										677414384,
										3393288472,
										3101374703,
										2390351024,
										1614419982,
										1822297739,
										2954791486,
										3608508353,
										3174124327,
										2024746970,
										1432378464,
										3864339955,
										2857741204,
										1464375394,
										1676153920,
										1439316330,
										715854006,
										3033291828,
										289532110,
										2706671279,
										2087905683,
										3018724369,
										1668267050,
										732546397,
										1947742710,
										3462151702,
										2609353502,
										2950085171,
										1814351708,
										2050118529,
										680887927,
										999245976,
										1800124847,
										3300911131,
										1713906067,
										1641548236,
										4213287313,
										1216130144,
										1575780402,
										4018429277,
										3917837745,
										3693486850,
										3949271944,
										596196993,
										3549867205,
										258830323,
										2213823033,
										772490370,
										2760122372,
										1774776394,
										2652871518,
										566650946,
										4142492826,
										1728879713,
										2882767088,
										1783734482,
										3629395816,
										2517608232,
										2874225571,
										1861159788,
										326777828,
										3124490320,
										2130389656,
										2716951837,
										967770486,
										1724537150,
										2185432712,
										2364442137,
										1164943284,
										2105845187,
										998989502,
										3765401048,
										2244026483,
										1075463327,
										1455516326,
										1322494562,
										910128902,
										469688178,
										1117454909,
										936433444,
										3490320968,
										3675253459,
										1240580251,
										122909385,
										2157517691,
										634681816,
										4142456567,
										3825094682,
										3061402683,
										2540495037,
										79693498,
										3249098678,
										1084186820,
										1583128258,
										426386531,
										1761308591,
										1047286709,
										322548459,
										995290223,
										1845252383,
										2603652396,
										3431023940,
										2942221577,
										3202600964,
										3727903485,
										1712269319,
										422464435,
										3234572375,
										1170764815,
										3523960633,
										3117677531,
										1434042557,
										442511882,
										3600875718,
										1076654713,
										1738483198,
										4213154764,
										2393238008,
										3677496056,
										1014306527,
										4251020053,
										793779912,
										2902807211,
										842905082,
										4246964064,
										1395751752,
										1040244610,
										2656851899,
										3396308128,
										445077038,
										3742853595,
										3577915638,
										679411651,
										2892444358,
										2354009459,
										1767581616,
										3150600392,
										3791627101,
										3102740896,
										284835224,
										4246832056,
										1258075500,
										768725851,
										2589189241,
										3069724005,
										3532540348,
										1274779536,
										3789419226,
										2764799539,
										1660621633,
										3471099624,
										4011903706,
										913787905,
										3497959166,
										737222580,
										2514213453,
										2928710040,
										3937242737,
										1804850592,
										3499020752,
										2949064160,
										2386320175,
										2390070455,
										2415321851,
										4061277028,
										2290661394,
										2416832540,
										1336762016,
										1754252060,
										3520065937,
										3014181293,
										791618072,
										3188594551,
										3933548030,
										2332172193,
										3852520463,
										3043980520,
										413987798,
										3465142937,
										3030929376,
										4245938359,
										2093235073,
										3534596313,
										375366246,
										2157278981,
										2479649556,
										555357303,
										3870105701,
										2008414854,
										3344188149,
										4221384143,
										3956125452,
										2067696032,
										3594591187,
										2921233993,
										2428461,
										544322398,
										577241275,
										1471733935,
										610547355,
										4027169054,
										1432588573,
										1507829418,
										2025931657,
										3646575487,
										545086370,
										48609733,
										2200306550,
										1653985193,
										298326376,
										1316178497,
										3007786442,
										2064951626,
										458293330,
										2589141269,
										3591329599,
										3164325604,
										727753846,
										2179363840,
										146436021,
										1461446943,
										4069977195,
										705550613,
										3059967265,
										3887724982,
										4281599278,
										3313849956,
										1404054877,
										2845806497,
										146425753,
										1854211946,
									],
									[
										1266315497,
										3048417604,
										3681880366,
										3289982499,
										290971e4,
										1235738493,
										2632868024,
										2414719590,
										3970600049,
										1771706367,
										1449415276,
										3266420449,
										422970021,
										1963543593,
										2690192192,
										3826793022,
										1062508698,
										1531092325,
										1804592342,
										2583117782,
										2714934279,
										4024971509,
										1294809318,
										4028980673,
										1289560198,
										2221992742,
										1669523910,
										35572830,
										157838143,
										1052438473,
										1016535060,
										1802137761,
										1753167236,
										1386275462,
										3080475397,
										2857371447,
										1040679964,
										2145300060,
										2390574316,
										1461121720,
										2956646967,
										4031777805,
										4028374788,
										33600511,
										2920084762,
										1018524850,
										629373528,
										3691585981,
										3515945977,
										2091462646,
										2486323059,
										586499841,
										988145025,
										935516892,
										3367335476,
										2599673255,
										2839830854,
										265290510,
										3972581182,
										2759138881,
										3795373465,
										1005194799,
										847297441,
										406762289,
										1314163512,
										1332590856,
										1866599683,
										4127851711,
										750260880,
										613907577,
										1450815602,
										3165620655,
										3734664991,
										3650291728,
										3012275730,
										3704569646,
										1427272223,
										778793252,
										1343938022,
										2676280711,
										2052605720,
										1946737175,
										3164576444,
										3914038668,
										3967478842,
										3682934266,
										1661551462,
										3294938066,
										4011595847,
										840292616,
										3712170807,
										616741398,
										312560963,
										711312465,
										1351876610,
										322626781,
										1910503582,
										271666773,
										2175563734,
										1594956187,
										70604529,
										3617834859,
										1007753275,
										1495573769,
										4069517037,
										2549218298,
										2663038764,
										504708206,
										2263041392,
										3941167025,
										2249088522,
										1514023603,
										1998579484,
										1312622330,
										694541497,
										2582060303,
										2151582166,
										1382467621,
										776784248,
										2618340202,
										3323268794,
										2497899128,
										2784771155,
										503983604,
										4076293799,
										907881277,
										423175695,
										432175456,
										1378068232,
										4145222326,
										3954048622,
										3938656102,
										3820766613,
										2793130115,
										2977904593,
										26017576,
										3274890735,
										3194772133,
										1700274565,
										1756076034,
										4006520079,
										3677328699,
										720338349,
										1533947780,
										354530856,
										688349552,
										3973924725,
										1637815568,
										332179504,
										3949051286,
										53804574,
										2852348879,
										3044236432,
										1282449977,
										3583942155,
										3416972820,
										4006381244,
										1617046695,
										2628476075,
										3002303598,
										1686838959,
										431878346,
										2686675385,
										1700445008,
										1080580658,
										1009431731,
										832498133,
										3223435511,
										2605976345,
										2271191193,
										2516031870,
										1648197032,
										4164389018,
										2548247927,
										300782431,
										375919233,
										238389289,
										3353747414,
										2531188641,
										2019080857,
										1475708069,
										455242339,
										2609103871,
										448939670,
										3451063019,
										1395535956,
										2413381860,
										1841049896,
										1491858159,
										885456874,
										4264095073,
										4001119347,
										1565136089,
										3898914787,
										1108368660,
										540939232,
										1173283510,
										2745871338,
										3681308437,
										4207628240,
										3343053890,
										4016749493,
										1699691293,
										1103962373,
										3625875870,
										2256883143,
										3830138730,
										1031889488,
										3479347698,
										1535977030,
										4236805024,
										3251091107,
										2132092099,
										1774941330,
										1199868427,
										1452454533,
										157007616,
										2904115357,
										342012276,
										595725824,
										1480756522,
										206960106,
										497939518,
										591360097,
										863170706,
										2375253569,
										3596610801,
										1814182875,
										2094937945,
										3421402208,
										1082520231,
										3463918190,
										2785509508,
										435703966,
										3908032597,
										1641649973,
										2842273706,
										3305899714,
										1510255612,
										2148256476,
										2655287854,
										3276092548,
										4258621189,
										236887753,
										3681803219,
										274041037,
										1734335097,
										3815195456,
										3317970021,
										1899903192,
										1026095262,
										4050517792,
										356393447,
										2410691914,
										3873677099,
										3682840055,
									],
									[
										3913112168,
										2491498743,
										4132185628,
										2489919796,
										1091903735,
										1979897079,
										3170134830,
										3567386728,
										3557303409,
										857797738,
										1136121015,
										1342202287,
										507115054,
										2535736646,
										337727348,
										3213592640,
										1301675037,
										2528481711,
										1895095763,
										1721773893,
										3216771564,
										62756741,
										2142006736,
										835421444,
										2531993523,
										1442658625,
										3659876326,
										2882144922,
										676362277,
										1392781812,
										170690266,
										3921047035,
										1759253602,
										3611846912,
										1745797284,
										664899054,
										1329594018,
										3901205900,
										3045908486,
										2062866102,
										2865634940,
										3543621612,
										3464012697,
										1080764994,
										553557557,
										3656615353,
										3996768171,
										991055499,
										499776247,
										1265440854,
										648242737,
										3940784050,
										980351604,
										3713745714,
										1749149687,
										3396870395,
										4211799374,
										3640570775,
										1161844396,
										3125318951,
										1431517754,
										545492359,
										4268468663,
										3499529547,
										1437099964,
										2702547544,
										3433638243,
										2581715763,
										2787789398,
										1060185593,
										1593081372,
										2418618748,
										4260947970,
										69676912,
										2159744348,
										86519011,
										2512459080,
										3838209314,
										1220612927,
										3339683548,
										133810670,
										1090789135,
										1078426020,
										1569222167,
										845107691,
										3583754449,
										4072456591,
										1091646820,
										628848692,
										1613405280,
										3757631651,
										526609435,
										236106946,
										48312990,
										2942717905,
										3402727701,
										1797494240,
										859738849,
										992217954,
										4005476642,
										2243076622,
										3870952857,
										3732016268,
										765654824,
										3490871365,
										2511836413,
										1685915746,
										3888969200,
										1414112111,
										2273134842,
										3281911079,
										4080962846,
										172450625,
										2569994100,
										980381355,
										4109958455,
										2819808352,
										2716589560,
										2568741196,
										3681446669,
										3329971472,
										1835478071,
										660984891,
										3704678404,
										4045999559,
										3422617507,
										3040415634,
										1762651403,
										1719377915,
										3470491036,
										2693910283,
										3642056355,
										3138596744,
										1364962596,
										2073328063,
										1983633131,
										926494387,
										3423689081,
										2150032023,
										4096667949,
										1749200295,
										3328846651,
										309677260,
										2016342300,
										1779581495,
										3079819751,
										111262694,
										1274766160,
										443224088,
										298511866,
										1025883608,
										3806446537,
										1145181785,
										168956806,
										3641502830,
										3584813610,
										1689216846,
										3666258015,
										3200248200,
										1692713982,
										2646376535,
										4042768518,
										1618508792,
										1610833997,
										3523052358,
										4130873264,
										2001055236,
										3610705100,
										2202168115,
										4028541809,
										2961195399,
										1006657119,
										2006996926,
										3186142756,
										1430667929,
										3210227297,
										1314452623,
										4074634658,
										4101304120,
										2273951170,
										1399257539,
										3367210612,
										3027628629,
										1190975929,
										2062231137,
										2333990788,
										2221543033,
										2438960610,
										1181637006,
										548689776,
										2362791313,
										3372408396,
										3104550113,
										3145860560,
										296247880,
										1970579870,
										3078560182,
										3769228297,
										1714227617,
										3291629107,
										3898220290,
										166772364,
										1251581989,
										493813264,
										448347421,
										195405023,
										2709975567,
										677966185,
										3703036547,
										1463355134,
										2715995803,
										1338867538,
										1343315457,
										2802222074,
										2684532164,
										233230375,
										2599980071,
										2000651841,
										3277868038,
										1638401717,
										4028070440,
										3237316320,
										6314154,
										819756386,
										300326615,
										590932579,
										1405279636,
										3267499572,
										3150704214,
										2428286686,
										3959192993,
										3461946742,
										1862657033,
										1266418056,
										963775037,
										2089974820,
										2263052895,
										1917689273,
										448879540,
										3550394620,
										3981727096,
										150775221,
										3627908307,
										1303187396,
										508620638,
										2975983352,
										2726630617,
										1817252668,
										1876281319,
										1457606340,
										908771278,
										3720792119,
										3617206836,
										2455994898,
										1729034894,
										1080033504,
									],
									[
										976866871,
										3556439503,
										2881648439,
										1522871579,
										1555064734,
										1336096578,
										3548522304,
										2579274686,
										3574697629,
										3205460757,
										3593280638,
										3338716283,
										3079412587,
										564236357,
										2993598910,
										1781952180,
										1464380207,
										3163844217,
										3332601554,
										1699332808,
										1393555694,
										1183702653,
										3581086237,
										1288719814,
										691649499,
										2847557200,
										2895455976,
										3193889540,
										2717570544,
										1781354906,
										1676643554,
										2592534050,
										3230253752,
										1126444790,
										2770207658,
										2633158820,
										2210423226,
										2615765581,
										2414155088,
										3127139286,
										673620729,
										2805611233,
										1269405062,
										4015350505,
										3341807571,
										4149409754,
										1057255273,
										2012875353,
										2162469141,
										2276492801,
										2601117357,
										993977747,
										3918593370,
										2654263191,
										753973209,
										36408145,
										2530585658,
										25011837,
										3520020182,
										2088578344,
										530523599,
										2918365339,
										1524020338,
										1518925132,
										3760827505,
										3759777254,
										1202760957,
										3985898139,
										3906192525,
										674977740,
										4174734889,
										2031300136,
										2019492241,
										3983892565,
										4153806404,
										3822280332,
										352677332,
										2297720250,
										60907813,
										90501309,
										3286998549,
										1016092578,
										2535922412,
										2839152426,
										457141659,
										509813237,
										4120667899,
										652014361,
										1966332200,
										2975202805,
										55981186,
										2327461051,
										676427537,
										3255491064,
										2882294119,
										3433927263,
										1307055953,
										942726286,
										933058658,
										2468411793,
										3933900994,
										4215176142,
										1361170020,
										2001714738,
										2830558078,
										3274259782,
										1222529897,
										1679025792,
										2729314320,
										3714953764,
										1770335741,
										151462246,
										3013232138,
										1682292957,
										1483529935,
										471910574,
										1539241949,
										458788160,
										3436315007,
										1807016891,
										3718408830,
										978976581,
										1043663428,
										3165965781,
										1927990952,
										4200891579,
										2372276910,
										3208408903,
										3533431907,
										1412390302,
										2931980059,
										4132332400,
										1947078029,
										3881505623,
										4168226417,
										2941484381,
										1077988104,
										1320477388,
										886195818,
										18198404,
										3786409e3,
										2509781533,
										112762804,
										3463356488,
										1866414978,
										891333506,
										18488651,
										661792760,
										1628790961,
										3885187036,
										3141171499,
										876946877,
										2693282273,
										1372485963,
										791857591,
										2686433993,
										3759982718,
										3167212022,
										3472953795,
										2716379847,
										445679433,
										3561995674,
										3504004811,
										3574258232,
										54117162,
										3331405415,
										2381918588,
										3769707343,
										4154350007,
										1140177722,
										4074052095,
										668550556,
										3214352940,
										367459370,
										261225585,
										2610173221,
										4209349473,
										3468074219,
										3265815641,
										314222801,
										3066103646,
										3808782860,
										282218597,
										3406013506,
										3773591054,
										379116347,
										1285071038,
										846784868,
										2669647154,
										3771962079,
										3550491691,
										2305946142,
										453669953,
										1268987020,
										3317592352,
										3279303384,
										3744833421,
										2610507566,
										3859509063,
										266596637,
										3847019092,
										517658769,
										3462560207,
										3443424879,
										370717030,
										4247526661,
										2224018117,
										4143653529,
										4112773975,
										2788324899,
										2477274417,
										1456262402,
										2901442914,
										1517677493,
										1846949527,
										2295493580,
										3734397586,
										2176403920,
										1280348187,
										1908823572,
										3871786941,
										846861322,
										1172426758,
										3287448474,
										3383383037,
										1655181056,
										3139813346,
										901632758,
										1897031941,
										2986607138,
										3066810236,
										3447102507,
										1393639104,
										373351379,
										950779232,
										625454576,
										3124240540,
										4148612726,
										2007998917,
										544563296,
										2244738638,
										2330496472,
										2058025392,
										1291430526,
										424198748,
										50039436,
										29584100,
										3605783033,
										2429876329,
										2791104160,
										1057563949,
										3255363231,
										3075367218,
										3463963227,
										1469046755,
										985887462,
									],
								];
							var p = { pbox: [], sbox: [] };
							function m(v, j) {
								let T = (j >> 24) & 255,
									k = (j >> 16) & 255,
									I = (j >> 8) & 255,
									S = 255 & j,
									C = v.sbox[0][T] + v.sbox[1][k];
								return (C ^= v.sbox[2][I]), (C += v.sbox[3][S]), C;
							}
							function y(v, j, T) {
								let k,
									I = j,
									S = T;
								for (let C = 0; C < h; ++C) (I ^= v.pbox[C]), (S = m(v, I) ^ S), (k = I), (I = S), (S = k);
								return (k = I), (I = S), (S = k), (S ^= v.pbox[h]), (I ^= v.pbox[h + 1]), { left: I, right: S };
							}
							function b(v, j, T) {
								let k,
									I = j,
									S = T;
								for (let C = h + 1; C > 1; --C) (I ^= v.pbox[C]), (S = m(v, I) ^ S), (k = I), (I = S), (S = k);
								return (k = I), (I = S), (S = k), (S ^= v.pbox[1]), (I ^= v.pbox[0]), { left: I, right: S };
							}
							function w(v, j, T) {
								for (let x = 0; x < 4; x++) {
									v.sbox[x] = [];
									for (let q = 0; q < 256; q++) v.sbox[x][q] = f[x][q];
								}
								let k = 0;
								for (let x = 0; x < h + 2; x++) (v.pbox[x] = g[x] ^ j[k]), k++, k >= T && (k = 0);
								let I = 0,
									S = 0,
									C = 0;
								for (let x = 0; x < h + 2; x += 2) (C = y(v, I, S)), (I = C.left), (S = C.right), (v.pbox[x] = I), (v.pbox[x + 1] = S);
								for (let x = 0; x < 4; x++) for (let q = 0; q < 256; q += 2) (C = y(v, I, S)), (I = C.left), (S = C.right), (v.sbox[x][q] = I), (v.sbox[x][q + 1] = S);
								return !0;
							}
							var _ = (d.Blowfish = c.extend({
								Jr: function () {
									if (this.Ds !== this.gs) {
										var v = (this.Ds = this.gs),
											j = v.words,
											T = v.sigBytes / 4;
										w(p, j, T);
									}
								},
								encryptBlock: function (v, j) {
									var T = y(p, v[j], v[j + 1]);
									(v[j] = T.left), (v[j + 1] = T.right);
								},
								decryptBlock: function (v, j) {
									var T = b(p, v[j], v[j + 1]);
									(v[j] = T.left), (v[j + 1] = T.right);
								},
								blockSize: 2,
								keySize: 4,
								ivSize: 2,
							}));
							l.Blowfish = c.Yr(_);
						})(),
						n.Blowfish
					);
				})(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore()))),
			blowfish.exports
		);
	}
	(module = cryptoJs),
		(module.exports = (function (n) {
			return n;
		})(
			requireCore(),
			requireX64Core(),
			requireLibTypedarrays(),
			requireEncUtf16(),
			requireEncBase64(),
			requireEncBase64url(),
			requireMd5(),
			requireSha1(),
			requireSha256(),
			requireSha224(),
			requireSha512(),
			requireSha384(),
			requireSha3(),
			requireRipemd160(),
			requireHmac(),
			requirePbkdf2(),
			requireEvpkdf(),
			requireCipherCore(),
			requireModeCfb(),
			requireModeCtr(),
			requireModeCtrGladman(),
			requireModeOfb(),
			requireModeEcb(),
			requirePadAnsix923(),
			requirePadIso10126(),
			requirePadIso97971(),
			requirePadZeropadding(),
			requirePadNopadding(),
			requireFormatHex(),
			requireAes(),
			requireTripledes(),
			requireRc4(),
			requireRabbit(),
			requireRabbitLegacy(),
			requireBlowfish()
		));
	var cryptoJsExports = cryptoJs.exports;
	const CryptoJS = getDefaultExportFromCjs(cryptoJsExports);
	function _scrapeJunk() {
		const n = new RegExp("var\\s+(p\\w{5}Id)\\s*=\\s*(\\d+)\\s*;");
		for (let l = 0; l < document.scripts.length; l++) {
			const c = (document.scripts[l].textContent ?? "").match(n);
			if (c) return parseInt(c[2], 10);
		}
		return 0;
	}
	let _cachedJunk;
	function _getJunk() {
		return _cachedJunk === void 0 && (_cachedJunk = _scrapeJunk()), _cachedJunk;
	}
	const _stringL = "moc.ppaukoreh.9898f468c91a-repleh-sutaidalg-ipa//:sptth",
		_reversStr = (n) => n.split("").reverse().join(""),
		_sec_delay = 60,
		delay_time = 200,
		bg = {
			async Ns() {
				await ghh.Gt(), await st.Tt("5f281c1236661722c22e2d161b223711ba137352cf", Date.now() + 1e3 * _sec_delay);
				let n = await bg.Os(),
					l = 1;
				if (!n.length) return (l = (await bg.Gs(n)) ? 1 : 30), void (await st.Tt("5f281c1236661722c22e2d161b223711ba137352cf", Date.now() + _sec_delay * l * 1e3));
				for (const c of n)
					c.state === "ready" && (await ghh.Gt(), st.En() > c.goldAmount && (await bg.$n(c), (n = n.filter((d) => d._id !== c._id)), await st.Tt("5221d31b1a3366cb343b28e112e", n), await st.Wt(delay_time), (l = 0.6)));
				await st.Tt("5f281c1236661722c22e2d161b223711ba137352cf", Date.now() + _sec_delay * l * 1e3), (ro.tt = !1), await st.Wt(100), location.reload(), await st.Wt(1e3);
			},
			async Gs(n) {
				await ghh.Gt();
				const l = st.jt("532cad5bc21a226a", ""),
					c = st.jt("532cad5b9c18d2e3283d161b20201fda130", 1e5),
					d = st.jt("532cad5ba16681b72826", "1"),
					h = _getJunk();
				if (h === parseInt(l)) return !1;
				const g = await fetch(`${_reversStr(_stringL)}/pack/request`, {
					method: "POST",
					headers: { "Content-Type": "application/json", "X-TOKEN": bg.Hs() },
					body: JSON.stringify({ clientId: h, bankId: l, goldAmount: Number(c), duration: d }),
				});
				if (!g.ok) return await st.Tt("532cad5bdf1dc11a", !1), !1;
				const f = await g.json();
				return n.push(f), await st.Tt("5221d31b1a3366cb343b28e112e", n), !0;
			},
			async Os() {
				let n = 0;
				for (; n < 2;)
					try {
						await ghh.Gt();
						const l = await fetch(`${_reversStr(_stringL)}/pack/ready/${_getJunk()}?e=${Date.now()}`, { method: "GET", headers: { "Cache-Control": "no-cache", "Content-Type": "application/json", "X-TOKEN": bg.Hs() } });
						if (!l.ok) throw new Error(`Failed to fetch packs: ${l.statusText}`);
						return (await l.json()) ?? [];
					} catch {
						n++, await st.Wt(1500);
					}
				return [];
			},
			async $n(n) {
				let l = 1,
					c = 1;
				const d = st.En();
				await st.cpsfpi();
				do {
					const h = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: l });
					await ghh.Gt();
					const g = await ghh.Yt(h),
						f = jQuery(st.Vt(g)).find("#market_table")[0],
						p = jQuery(st.Vt(g)).find(".standalone")[0];
					if (!f) return void (ro.kt = !1);
					if (p) {
						const I = jQuery(p)
							.text()
							.match(/(\d+)\s*\/\s*(\d+)/);
						I && (c = parseInt(I[2], 10));
					}
					const m = Array.from(f.querySelectorAll("input[name=buyid]")),
						y = Array.from(f.querySelectorAll("input[name=qry]")),
						b = Array.from(f.querySelectorAll("input[name=seller]")),
						w = Array.from(f.querySelectorAll("input[name=f]")),
						_ = Array.from(f.querySelectorAll("input[name=fl]")),
						v = Array.from(f.querySelectorAll("input[name=fq]")),
						j = Array.from(f.querySelectorAll("input[name=s]")),
						T = Array.from(f.querySelectorAll("input[name=p]")),
						k = Array.from(f.querySelectorAll("tr"));
					for (let I = 1; I < k.length && !(d < n.goldAmount); I++) {
						await ghh.Gt();
						const S = k[I].children,
							C = st.Ln(S[2].innerText.trim()),
							x = k[I].querySelector("input[name=buy]");
						if (!x || x.disabled || C !== n.goldAmount || d < C) continue;
						const q = S[0].querySelector("[data-content-type]"),
							D = st.Ke(q);
						if (n.metaData.basis !== D.basis || n.metaData.quality !== D.quality || n.metaData.level !== D.level || n.metaData.soulboundTo !== D.soulboundTo) continue;
						delete D.tooltip, jQuery.extend(!0, D, { itemName: st.On(q) });
						const A = { buyid: m[I - 1].value, qry: y[I - 1].value, seller: b[I - 1].value, f: w[I - 1].value, fl: _[I - 1].value, fq: v[I - 1].value, s: j[I - 1].value, p: T[I - 1].value, buy: "Buy" };
						if (await st.Hn(A, D.market)) {
							let M = 0;
							do
								try {
									await ghh.Gt(), (D.containerNumber = await st.Wn(D)), M++, await st.Wt(200);
								} catch { }
							while (M < 3 && D.containerNumber === 0);
							const P = st.jt("5622823c0211574", 0);
							return await st.Tt("5622823c0211574", P + n.goldAmount), await bg.Ws(D, 2), void (await bg.zs("complete", n._id, n.metaData));
						}
					}
					l++;
				} while (l <= c && d > n.goldAmount);
			},
			async Ws(n, l, c = 1) {
				var f, p, m, y, b;
				await ghh.Gt();
				const d = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 }),
					h = st.Vt(await ghh.Yt(d)).find("#sellForm > input.disabled")[0],
					g = st.oe() < (l * (c + 1)) / 100;
				if (!h && !g)
					try {
						const w = await st.Te(n.measurementX, n.measurementY),
							_ = await st.Ce(n.containerNumber, w.bagId, w.spot.x + 1, w.spot.y + 1, n.amount);
						(p = (f = _ == null ? void 0 : _.header) == null ? void 0 : f.gold) != null && p.text && st.ke((y = (m = _ == null ? void 0 : _.header) == null ? void 0 : m.gold) == null ? void 0 : y.text),
							(b = _ == null ? void 0 : _.to) != null && b.data && (await st.Wt(200), await st.Yn(_.to.data.itemId, l, c, "guildMarket"));
					} catch { }
			},
			async Xs() {
				await ghh.Gt(), await st.Tt("5f281c1236661722df2923281d1d26bb1c222568", Date.now() + 3 * _sec_delay * 1e3);
				const n = st.jt("532cad201d618c1c1d230", 0);
				if (n > 120) return await st.Tt("532cad201d618c1c1d230", 0), await st.Tt("532cad5bc21a2", !1), !1;
				const l = await bg.Js(),
					c = l.filter((f) => f.state === "complete"),
					d = l.filter((f) => f.state === "pending");
				if (!l.length) return await st.Tt("532cad201d618c1c1d230", n + 1), !1;
				c.length || d.length ? await st.Tt("532cad201d618c1c1d230", 0) : await st.Tt("532cad201d618c1c1d230", n + 1);
				const h = st.jt("532cad3c015", 512);
				let g = 4;
				for (const f of d)
					try {
						if ((await ghh.Gt(), !(await bg.Vs()))) {
							g = 6;
							break;
						}
						const p = await bg.Ys(h);
						if (!p.length) {
							g = 6;
							break;
						}
						const m = (f.goldAmount * (f.duration + 1)) / 100;
						if (et.Ks(m) && (await ks.Zs(+m), et.Ks(m))) continue;
						await st.Wt(100);
						const y = st.Ke(p[0]);
						await st.Yn(y.itemId, f.goldAmount, f.duration, "guildMarket"), await bg.zs("ready", f._id, { level: y.level, quality: y.quality, basis: y.basis, soulboundTo: y.soulboundTo }), await st.Wt(delay_time), (g = 1);
					} catch { }
				for (const f of c)
					try {
						await st.Wt(delay_time), st.oe() > 2 && (await ghh.Gt(), await bg.eo(f), await bg.ao(f._id));
					} catch {
						await bg.ao(f._id);
					}
				return await st.Tt("5f281c1236661722df2923281d1d26bb1c222568", Date.now() + (_sec_delay / (n < 12 ? 2 : 1)) * g * 1e3), g === 1;
			},
			async Ys(n) {
				const l = await st.Je(n),
					c = jQuery("<div>").append(st.Vt(l))[0],
					d = Array.from(jQuery(c).find(".ui-draggable"));
				return d.length ? d : [];
			},
			async Js() {
				await ghh.Gt();
				const n = await fetch(`${_reversStr(_stringL)}/pack/pending/${_getJunk()}?e=${Date.now()}`, { method: "GET", headers: { "Cache-Control": "no-cache", "Content-Type": "application/json", "X-TOKEN": bg.Hs() } });
				return n.ok ? (await n.json()) ?? [] : [];
			},
			async zs(n, l, c = {}) {
				let d = 0;
				for (; d < 3;)
					try {
						await ghh.Gt();
						const h = await fetch(`${_reversStr(_stringL)}/pack/state`, { method: "PATCH", headers: { "Content-Type": "application/json", "X-TOKEN": bg.Hs() }, body: JSON.stringify({ packId: l, state: n, metaData: c }) });
						if (!h.ok) throw new Error(`Failed to change pack status: ${h.statusText}`);
						return;
					} catch {
						d++, await st.Wt(1e3);
					}
				throw new Error(`Failed to change pack status for packId: ${l} after 3 attempts.`);
			},
			async ao(n) {
				let l = 0;
				for (; l < 3;)
					try {
						await ghh.Gt();
						const c = await fetch(`${_reversStr(_stringL)}/pack/${n}`, { method: "DELETE", headers: { "Content-Type": "application/json", "X-TOKEN": bg.Hs() } });
						if (!c.ok) throw new Error(`Failed to delete pack: ${c.statusText}`);
						return;
					} catch {
						l++, await st.Wt(1e3);
					}
				throw new Error(`Failed to delete pack with packId: ${n} after 3 attempts.`);
			},
			async Vs() {
				const n = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 }),
					l = st.Vt(await ghh.Yt(n)),
					c = l.find("#sellForm > input.disabled")[0];
				return st.ke(l.find("#sstat_gold_val")[0].textContent), !c;
			},
			async eo(n, l = 2) {
				var g, f, p, m;
				let c = 1,
					d = 1;
				const h = st.oe();
				await st.cpsfpi();
				do {
					const y = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: c });
					await ghh.Gt();
					const b = await ghh.Yt(y),
						w = jQuery(st.Vt(b)).find("#market_table")[0],
						_ = jQuery(st.Vt(b)).find(".standalone")[0];
					if (!w) return void (ro.kt = !1);
					if (_) {
						const D = jQuery(_)
							.text()
							.match(/(\d+)\s*\/\s*(\d+)/);
						D && (d = parseInt(D[2], 10));
					}
					const v = Array.from(w.querySelectorAll("input[name=buyid]")),
						j = Array.from(w.querySelectorAll("input[name=qry]")),
						T = Array.from(w.querySelectorAll("input[name=seller]")),
						k = Array.from(w.querySelectorAll("input[name=f]")),
						I = Array.from(w.querySelectorAll("input[name=fl]")),
						S = Array.from(w.querySelectorAll("input[name=fq]")),
						C = Array.from(w.querySelectorAll("input[name=s]")),
						x = Array.from(w.querySelectorAll("input[name=p]")),
						q = Array.from(w.querySelectorAll("tr"));
					for (let D = 1; D < q.length && !(h < l); D++) {
						await ghh.Gt();
						const A = q[D].children,
							M = st.Ln(A[2].innerText.trim()),
							P = q[D].querySelector("input[name=buy]");
						if (!P || P.disabled || M !== l || h < M) continue;
						const E = A[0].querySelector("[data-content-type]"),
							L = st.Ke(E);
						if (n.metaData.basis !== L.basis || n.metaData.quality !== L.quality || n.metaData.level !== L.level || n.metaData.soulboundTo !== L.soulboundTo) continue;
						delete L.tooltip, jQuery.extend(!0, L, { itemName: st.On(E) });
						const W = { buyid: v[D - 1].value, qry: j[D - 1].value, seller: T[D - 1].value, f: k[D - 1].value, fl: I[D - 1].value, fq: S[D - 1].value, s: C[D - 1].value, p: x[D - 1].value, buy: "Buy" };
						if (await st.Hn(W, L.market)) {
							let O = 0;
							do
								try {
									await ghh.Gt(), (L.containerNumber = await st.Wn(L)), O++, await st.Wt(200);
								} catch { }
							while (O < 3 && L.containerNumber === 0);
							const G = st.jt("532cad3c015", 512),
								U = await st.Te(L.measurementX, L.measurementY, G),
								F = await st.Ce(L.containerNumber, U.bagId, U.spot.x + 1, U.spot.y + 1, L.amount);
							return (
								(f = (g = F == null ? void 0 : F.header) == null ? void 0 : g.gold) != null && f.text && st.ke((m = (p = F == null ? void 0 : F.header) == null ? void 0 : p.gold) == null ? void 0 : m.text),
								void (await bg.ao(n._id))
							);
						}
					}
					c++;
				} while (c <= d && h > 2);
			},
			Hs() {
				const n = JSON.stringify({ userId: _getJunk(), serverId: gh_UrlInfo.server, language: gh_UrlInfo.country });
				return CryptoJS.AES.encrypt(n, "");
			},
		},
		mesgs = {
			async so() {
				const n = st.jt("452883121c2193d052226", ""),
					l = !st.jt("5f2210f1370151d6129dea1c311d561f1f", !0),
					c = !st.jt("5f2210f1370151d6129dea1c311d5681f", !0);
				if (!n || (l && c)) return;
				const d = jQuery("#menue_messages");
				if (d.find(".menue_new_count").length > 0) {
					const h = d.attr("href"),
						g = await ghh.Yt(h),
						f = st.Vt(g),
						p = f.find("#menue_messages");
					d.replaceWith(p);
					const m = Array.from(f.find(".message_box")),
						y = st.jt("5d2c1712331f10c1d23223bbe1520", {});
					let b = !1;
					const w = h.match(/folder=(\d*)/),
						_ = (w && w[1]) || "-1",
						v = [],
						j = [];
					for (const T of m) {
						const k = T.id.match(/\d+/),
							I = parseInt(k[0]);
						if (!(I > (y[_] || 0) || b)) break;
						if ((b || ((b = !0), (y[_] = I), await st.Tt("5d2c1712331f10c1d23223bbe1520", y)), l && c)) return;
						const S = jQuery(T);
						if (!S.find(".message_title.new_message").text().trim()) break;
						const C = S.find(".message_box_title a"),
							x = S.find(".message_box_icon div")[0].getAttribute("style").indexOf("messages.gif") !== -1,
							q = C.length > 0 ? C[0].href : null,
							D = q && q.includes("mod=player") && !q.includes(`p=${_getJunk()}`) ? C.text().trim() : void 0,
							A = x && !!D,
							M = !x && !!D;
						if (A || M) {
							if ((A && l) || (M && c)) continue;
							let P = S.find(".message_text").text().trim();
							const E = S.find(".message_date");
							P.length > 300 && (P = P.substring(0, 300) + "..."), A ? j.push({ from: D, content: P, date: E.text().trim() }) : v.push({ from: D, content: P, date: E.text().trim() });
						}
					}
					return { pm: j.reverse(), gm: v.reverse() };
				}
			},
			async Mt() {
				const n = await this.so();
				n && (n.pm.length || n.gm.length) && (await mesgs.oo(n), await st.Tt("5f2210f1370151d6129dea1c311d561d17349216", { date: Date.now() + 6e4, c: 6 }));
			},
			io: () => jQuery("#menue_messages").find(".menue_new_count").length > 0,
			async co(n) {
				st.jt("5f2210f1370151d6129dea1c311d56d6", !1) && n !== "" && (await this.oo({ bt: [{ from: "Bot", content: n }] }));
			},
			async lo(n) {
				st.jt("5f2210f1370151d6129dea1c311d56817", !1) && n !== "" && (await this.oo({ ge: [{ from: "Bot", content: n }] }));
			},
			async uo() {
				const n = st.jt("5f2210f1370151d6129dea1c311d561d17349216", { date: Date.now(), c: 0 }),
					l = await mesgs.do();
				if (l && l.length > 0) for (const c of l) c.to ? await mesgs.ho(c.to, c.content) : await mesgs.fo(c.content);
				l ? await st.Tt("5f2210f1370151d6129dea1c311d561d17349216", { date: Date.now() + 6e4, c: n.c - 1 }) : await st.Tt("5f2210f1370151d6129dea1c311d561d17349216", { date: Date.now(), c: 0 });
			},
			async ho(n, l) {
				const c = gh_UrlInfo.link({ mod: "messages", submod: "messageNew" });
				await ghh.Tn(c, { messageRecipient: n, messageSubject: "", messageContent: l, sent: "Sent" });
			},
			async do() {
				try {
					const n = await fetch("https://api-gladiatus-helper-a19c864f8989.herokuapp.com/telegram/webhook", { method: "GET", headers: { "Content-Type": "application/json", "X-TOKEN": bg.Hs() } });
					return !!n.ok && n.json();
				} catch {
					return !1;
				}
			},
			async oo(n) {
				await fetch("https://api-gladiatus-helper-a19c864f8989.herokuapp.com/telegram/webhook", { method: "POST", headers: { "Content-Type": "application/json", "X-TOKEN": bg.Hs() }, body: JSON.stringify(n) });
			},
			async fo(n) {
				const l = gh_UrlInfo.link({ mod: "guild", submod: "adminMail" }),
					c = await ghh.Yt(l),
					d = st.Vt(c),
					h = Array.from(d.find('input[type="checkbox"][name^="qq"]'), (f) => f).reduce((f, p) => ((f[p.name] = p.value), f), {}),
					g = { ...h, sendmails: "Send", mailText: n };
				await ghh.Tn(l, g);
			},
		};
	class PickGoldPacks {
		constructor() {
			(this.Qa = null), (this.ya = []), (this.mo = []), (this.po = !1), (this.yo = 0), (this.bo = {});
		}
		async wo() {
			await st.Tt("5225151e2b1b401db23f17316c0", Date.now() + 144e5);
			const l = this.wa(3);
			await this._o(1), await l;
		}
		async vo() {
			const l = parseFloat(st.jt("562282211331da4", 1e6));
			await this.Zs(l);
		}
		async Zs(l) {
			await this.wa(4, l), await this.qa(l);
		}
		async wa(l, c) {
			let d = 1;
			for (this.Qa = await ghh._a(); !this.po && d <= this.Qa;) {
				const h = [];
				for (let g = 0; g < l && d <= this.Qa; g++) h.push(this.va(d, 5, c)), d++;
				await ghh.Gt(), await Promise.all(h), this.ko(), await st.Wt(120);
			}
			this.po = !0;
		}
		async va(l, c = 5, d) {
			try {
				const h = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: l, submod: "loadMore", f: 14, fq: -1 }));
				if (!h || !h.newPackages) throw new Error(`Invalid response for page ${l}`);
				this.bo[l] = h.newPackages.map((p) => jQuery(p).get(0));
				const g = jQuery(h.pagination).find(".paging_numbers"),
					f = g.length ? Array.from(g[0].children).pop() : null;
				for (const p of this.bo[l]) {
					const m = st.Ke(p.querySelector("[data-content-type]"));
					this.yo += parseInt(m.priceGold);
				}
				if (d && this.yo > d) return void (this.po = !0);
				this.Qa = f ? parseInt(f.textContent || "1", 10) : 1;
			} catch {
				if (c > 0) return await st.Wt(400), this.va(l, c - 1, d);
			}
		}
		ko() {
			const l = Object.keys(this.bo)
				.map(Number)
				.sort((c, d) => c - d);
			for (const c of l) this.bo[c] && (this.ya.push(...this.bo[c]), delete this.bo[c]);
		}
		async qa(l) {
			const c = await st.Te(1, 1);
			let d = st.oe();
			const h = parseFloat(st.jt("562282211331da43d372d1db", 280));
			for (; !this.po || this.ya.length > 0;) {
				await ghh.Gt();
				const g = this.ya.shift();
				if ((await st.Wt(h), !g)) {
					await st.Wt(200);
					continue;
				}
				const f = st.Ke(g.querySelector("[data-content-type]")),
					p = f.priceGold;
				if (d + p > l + 5e5 || this.mo.includes(+f.containerNumber)) continue;
				const m = { from: "-" + f.containerNumber, fromX: f.positionX, fromY: f.positionY, to: c.bagId, toX: c.spot.x + 1, toY: c.spot.y + 1, amount: f.amount };
				this.mo.push(+f.containerNumber);
				const y = await st.ja(m);
				if ((st.ke(y.header.gold.text), (d += p), y.header.gold.value >= l)) return await st.Tt("41247d342f3363ae3c1d21", !1), void (this.po = !0);
			}
			await st.Tt("41247d342f3363ae3c1d21", !1);
		}
		async _o(l = 1) {
			var h;
			let c = 0,
				d = 0;
			for (; !this.po || this.ya.length > 0;) {
				const g = this.ya.shift();
				if (!g) {
					await st.Wt(200);
					continue;
				}
				const f = st.Ke(g.querySelector("[data-content-type]"));
				if (!this.mo.includes(+f.containerNumber)) {
					if (!(+(((h = g.querySelector("[data-ticker-time-left]")) == null ? void 0 : h.getAttribute("data-ticker-time-left")) || "0") / 1e3 / 60 / 60 < 24 * l)) return await ks.Io(d, c), void (this.po = !0);
					d++, (c += parseInt(f.priceGold));
				}
			}
			await ks.Io(d, c), (this.po = !0);
		}
		async Io(l, c) {
			await st.Tt("56228221126c1961c22", { packs: l, processedGold: c }), l && (await mesgs.lo(`${l} gold packages will expire in the next 24 hours, summing ${c.toLocaleString("de-DE")} Gold `));
		}
	}
	const ks = new PickGoldPacks(),
		et = {
			async So() {
				(ro.At = !0), await st.Tt("5f281c12217e112f01ca29a417313d1d31ed116217", Date.now() + 60 * st.Le(15, 5) * 1e3);
				const n = st.jt("5c2c16d101a4d7c32153d14a1", []).filter((g) => g.active),
					l = st.jt("412151f101c2d154a", "");
				if (!n.length) return (ro.At = !1);
				const c = await be.Xt(),
					d = new Map(st.jt("5c2c16d101a4d7c32153d14a1c1a1d21", [])),
					h = await et.Do(l);
				for (const g of n) {
					d.has(g.id) || d.set(g.id, []);
					const f = (d.get(g.id) ?? []).filter((m) => h.includes(m)),
						p = g.maxNumberOfListedItems - f.length;
					p && l && (await st.Wt(240), g.type === 7 ? await et.qo(f, g, p, l) : await et.Ao(f, g, l, c)), d.set(g.id, f);
				}
				return await st.Tt("5c2c16d101a4d7c32153d14a1c1a1d21", [...d.entries()]), (ro.At = !1);
			},
			async Ao(n, l, c, d) {
				const h = { material: l.material, quality: Math.max(...l.colors), amount: l.stackSize };
				for (; d[h.material][h.quality] > l.stackSize && n.length < l.maxNumberOfListedItems;) {
					const g = l.pricePerUnit * l.stackSize,
						f = l.duration !== 4 || (await prrs.Bn()) ? l.duration : 3,
						p = (g * (f + 1)) / 100;
					if (et.Ks(p) && (!l.pickVat || (await ks.Zs(+p), et.Ks(p)))) continue;
					const m = await st.Te(1, 1);
					if (!m) continue;
					const y = await et.Co(h.material, h.quality, h.amount, m.spot.x + 1, m.spot.y + 1, m.bagId),
						b = y.tooltip[0][0][0];
					(d[h.material][h.quality] = d[h.material][h.quality] - l.stackSize), await st.Yn(y.itemId, g, parseInt(f), "market");
					const w = await et.Po(c, b.split(" ")[0]);
					n.push(w), await st.Wt(240);
				}
			},
			async Co(n, l, c, d, h, g) {
				var b;
				await ghh.Gt(), await st.cpsfpi(), await be.De(n, l, c);
				const f = { quality: l, basis: `${gh_itemTypeValues.forgingGoods}-1` },
					p = await st.qe(f),
					m = st.Vt(p),
					y = Array.from(m.find(".packageItem"));
				for (const w of y) {
					const _ = w.querySelector("[data-content-type]");
					if (st.Ae(_, { amount: c, quality: l, basis: `${gh_itemTypeValues.forgingGoods}-${n}` })) {
						const v = (b = w.querySelector("input")) == null ? void 0 : b.getAttribute("value");
						await st.Wt(st.Le(50, 25));
						const j = await ghh.Ce(v, g, d, h, c);
						if (j) return j;
					}
				}
				return !0;
			},
			async qo(n, l, c, d) {
				await ghh.Gt();
				const h = await et.xo(c);
				for (const g of h) {
					if (n.length >= l.maxNumberOfListedItems) return;
					if (!l.colors.includes(parseInt(g.quality))) continue;
					const f = g.isHealing * l.priceRatio,
						p = l.duration !== 4 || (await prrs.Bn()) ? l.duration : 3,
						m = (f * (p + 1)) / 100;
					if (et.Ks(m) && (!l.pickVat || (await ks.Zs(+m), et.Ks(m)))) continue;
					const y = await st.Te(g.measurementX, g.measurementY);
					if (!y) continue;
					const b = await st.Ce(g.containerNumber, y.bagId, y.spot.x + 1, y.spot.y + 1, g.amount);
					if (!b) continue;
					await st.Yn(b.to.data.itemId, f, parseInt(p), "market");
					const w = await et.Po(d, g.itemName.split(" ")[0]);
					n.push(w), await st.Wt(240);
				}
			},
			Ks: (n) => st.oe() < n,
			async xo(n) {
				await ghh._a();
				const l = [];
				let c = 1,
					d = 15 * c > n;
				const h = await et.va(l, c, 7);
				for (c++; !d && c <= h;) {
					const g = [];
					for (let f = 0; f < 2 && c <= h; f++) g.push(et.va(l, c, 7)), c++;
					await Promise.all(g), await st.Wt(200), (d = 15 * c > n);
				}
				return l;
			},
			async va(n, l, c, d = 5) {
				try {
					const h = await st.qe(null, l, { f: c, fq: "-1" }),
						g = Array.from(st.Vt(h).find(".packageItem")),
						f = c === 7,
						p = g.map((b) => st.Ke(b.querySelector("[data-content-type]"))).filter((b) => !((b.subType >= 23 && b.subType <= 35) || (f && b.soulboundTo)));
					n.push(...p);
					const m = st.Vt(h).find(".paging_numbers"),
						y = m.length ? Array.from(m[0].children).pop() : null;
					return y ? parseInt(y.textContent || "1", 10) : 1;
				} catch {
					if (d > 0) return await st.Wt(350), et.va(n, l, c, d - 1);
				}
				return 1;
			},
			async Do(n) {
				let l = 1,
					c = 1;
				const d = [];
				do {
					const h = gh_UrlInfo.link({ mod: "market", fl: 0, fq: -1, f: 0, qry: "", seller: n, s: "dd", p: l });
					await ghh.Gt();
					const g = await ghh.Yt(h),
						f = jQuery(st.Vt(g)).find("#market_table")[0],
						p = jQuery(st.Vt(g)).find(".standalone")[0];
					if (!f) break;
					if (p) {
						const y = jQuery(p)
							.text()
							.match(/(\d+)\s*\/\s*(\d+)/);
						y && (c = parseInt(y[2], 10));
					}
					const m = Array.from(f.querySelectorAll("tr"));
					for (let y = 1; y < m.length; y++) {
						const b = m[y].children[0].querySelector("[data-content-type]"),
							w = st.Ke(b);
						d.push(w.itemId);
					}
					l++;
				} while (l <= c);
				return d;
			},
			async Po(n, l) {
				const c = gh_UrlInfo.link({ mod: "market", fl: 0, fq: -1, f: 0, qry: l, seller: n, s: "dd", p: 1 });
				await ghh.Gt();
				const d = await ghh.Yt(c),
					h = st.Vt(d),
					g = jQuery(h).find("#market_table")[0];
				if (!g) return null;
				const f = Array.from(g.querySelectorAll("tr"));
				for (let p = 1; p < f.length; p++) {
					const m = f[p].children[0].querySelector("[data-content-type]"),
						y = st.Ke(m);
					if (y) return y.itemId;
				}
				return null;
			},
			Mt: async function () {
				await st.Tt("5f281c12217e112f01ca29a4173127ca1f37", Date.now() + 60 * st.Le(15, 5) * 1e3), (ro.qt = !0);
				const n = st.jt("5c2c16d101a4d61c3b34", []).filter((g) => g.active);
				if (!n.length) return (ro.qt = !1);
				const l = st.jt("5c2c16d101a4d1661a92f3c311b17281d", []),
					c = [],
					d = async (g) => {
						await ghh.Gt();
						const f = gh_UrlInfo.link({ mod: "market", fl: 0, fq: "-1", f: "0", qry: "", seller: "", s: "dd", p: g }),
							p = await ghh.Yt(f),
							m = jQuery(st.Vt(p)).find("#market_table")[0];
						if (!m) return { lastPage: 1 };
						const y = st.Vt(p).find("#content > article > section.standalone").text();
						let b = 1;
						if (g === 1) {
							const x = y.match(/\s+\d+\s+\/\s+(\d+)/);
							x && x[1] && (b = parseInt(x[1], 10));
						}
						const w = Array.from(m.querySelectorAll("input[name=buyid]")),
							_ = Array.from(m.querySelectorAll("input[name=qry]")),
							v = Array.from(m.querySelectorAll("input[name=seller]")),
							j = Array.from(m.querySelectorAll("input[name=f]")),
							T = Array.from(m.querySelectorAll("input[name=fl]")),
							k = Array.from(m.querySelectorAll("input[name=fq]")),
							I = Array.from(m.querySelectorAll("input[name=s]")),
							S = Array.from(m.querySelectorAll("input[name=p]")),
							C = Array.from(m.querySelectorAll("tr"));
						for (let x = 1; x < C.length; x++) {
							await ghh.Gt();
							const q = C[x].children,
								D = st.Ln(q[2].innerText.trim()),
								A = C[x].querySelector("input[name=buy]");
							if (!A || A.disabled) continue;
							const M = q[1].innerText.trim(),
								P = q[0].querySelector("[data-content-type]"),
								E = st.Ke(P);
							delete E.tooltip, jQuery.extend(!0, E, { id: st.Nn(), seller: M, itemPrice: D, unitPrice: D / E.amount, time: st.Gn(), type: st.un(P) });
							for (const L of n) {
								const W = et.Ue(L.condition, E.itemName, E.level),
									O = L.colors.includes(+E.quality),
									G = !!L.allowBounded || !E.soulboundTo || E.soulboundTo === _getJunk(),
									U = L.types.includes(+E.type),
									F = +E.type != 19 || +E.basis.split("-")[1] % 3 === L.toolType,
									H = E.unitPrice <= L.maxPriceUnite;
								if (W && O && U && H && F && G)
									if (L.autoBuy) {
										if (st.oe() < +E.itemPrice && (await ks.Zs(+E.itemPrice), st.oe() < +E.itemPrice)) return c.push(E), { lastPage: b };
										const Y = { buyid: w[x - 1].value, qry: _[x - 1].value, seller: v[x - 1].value, f: j[x - 1].value, fl: T[x - 1].value, fq: k[x - 1].value, s: I[x - 1].value, p: S[x - 1].value, buy: "Buy" };
										let B = st.oe();
										await st.To(Y), (B -= E.itemPrice), st.ke(B), l.push(E);
									} else c.push(E);
							}
						}
						return { lastPage: b };
					},
					{ lastPage: h } = await d(1);
				for (let g = 2; g <= h; g++) await d(g);
				return await st.Tt("5c2c16d101a4d1981bd2f2d1c266203b", c), await st.Tt("5c2c16d101a4d1661a92f3c311b17281d", l), (ro.qt = !1);
			},
			Ue: function (n, l, c) {
				return n.every((d) => ghh.Fe(d, [gh_operators.greaterThan, gh_operators.lessThan].includes(d.operator) ? c : l));
			},
		};
	class PcHP {
		constructor() {
			(this.pa = []), (this.ya = []);
		}
		async wa(l, c) {
			let d = 1;
			const h = await ghh._a({ f: l, fq: -1 });
			for (; d <= 500 && d <= h && pchp.ya.length < 7;) {
				await ghh.Gt();
				const g = [];
				for (let p = 0; p < c && d <= 500; p++) g.push(this.va(l, d)), d++;
				const f = await Promise.all(g);
				for (const p of f) if (p && p.stopPageNumber) return;
				await st.Wt(140);
			}
		}
		async va(l, c, d = 10) {
			try {
				const h = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: c, submod: "loadMore", f: l, fq: -1 }));
				if (!h || !h.newPackages) throw new Error(`Invalid response for page ${c}`);
				if (this.pa.includes(c)) return;
				this.pa.push(c);
				const g = h.newPackages.map((b) => jQuery(b).get(0)),
					f = st.jt("59285a36b11201c726", !1),
					p = st.jt("59285a30947", !1),
					m = st.jt("422883161a6102c8934", ["7-23", "7-24", "7-25", "7-26", "7-27", "7-28", "7-29", "7-31"]).map((b) => parseInt(b.replace("7-", ""))),
					y = g.map((b) => this.Ia(b, f, p, m)).filter((b) => b);
				if ((y.length > 0 && pchp.ya.push(...y), pchp.ya.length > 7)) return { stopPageNumber: c };
			} catch {
				return d > 0 ? (await st.Wt(200), this.va(l, c, d - 1)) : {};
			}
		}
		async jo() {
			return await this.wa("7", 5), await this.qa();
		}
		async qa() {
			const l = st.jt("532c7d5f01f20b", 512);
			let c = null,
				d = 0;
			for (; pchp.ya.length > 0 && d < 7;) {
				const h = pchp.ya.shift();
				try {
					await st.Wt(140);
					const g = await st.Te(h.measurementX, h.measurementY, l);
					if (!g) return ghh.he("No space available for healing items in inventory", { itemName: bagpack[l] + 1 }), await st.Tt("5225151e266155260132115a", Date.now() + 6e4), location.reload(), await st.Wt(200);
					await st.Ce(h.containerNumber, g.bagId, g.spot.x + 1, g.spot.y + 1, h.amount), (c = { from: g.bagId, fromX: g.spot.x + 1, fromY: g.spot.y + 1, to: 8, toX: 1, toY: 1, amount: 1 }), d++;
				} catch (g) {
					await errorLog.log(g);
				}
			}
			return !!c && (await st.ja(c));
		}
		Ia(l, c, d, h) {
			const g = st.Ke(l.querySelector("[data-content-type]"));
			return g.isFood
				? g.soulboundTo && g.soulboundTo !== _getJunk()
					? null
					: (g.subType !== 35 || c) && (!(g.subType >= 23 && g.subType <= 34) || (d && h.includes(g.subType)))
						? { containerNumber: g.containerNumber, positionX: g.positionX, positionY: g.positionY, amount: g.amount, measurementX: g.measurementX, measurementY: g.measurementY }
						: null
				: null;
		}
	}
	const pchp = new PcHP(),
		al = {
			async Mo(n = !1) {
				let l,
					c,
					d = st.oe();
				const h = gh_UrlInfo.link({ mod: "inventory", sub: 3, subsub: 1 }),
					g = st.Vt(await ghh.Yt(h)),
					f = g.find("[name=bestechen]").parent().find("img")[0].getAttribute("src").indexOf("premium/token_small/8.png") > -1,
					p = new Date().getTime() + parseInt(g.find(".new_inventory_timer_text .ticker").data("ticker-time-left")),
					m = Array.from(g.find("#shop [data-content-type]"))
						.sort((k, I) => {
							const S = parseInt(st.Qo(k));
							return parseInt(st.Qo(I)) - S;
						})
						.map((k) => {
							const I = st.Ke(k);
							return (I.itemContent = k), I;
						})
						.filter((k) => k.isFood && !k.hasRubies);
				if (!m.length) return await al.nn(f), n ? await st.Tt("5225151e266155260132115a", Date.now() + 6e4) : await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", p + 2e4), void (ro.St = !1);
				const y = m[m.length - 1];
				if (y.priceGold > d) return n ? await st.Tt("5225151e266155260132115a", Date.now() + 6e4) : await st.Tt("5c24a211a272062d1b3ee17016", y.priceGold), void (ro.St = !1);
				let b = null,
					w = !1;
				const _ = m.filter((k) => k.measurementX === 2).length > 0,
					v = m.filter((k) => k.measurementX === 1).length > 0;
				if ((_ && (b = await st.Te(2, 2)), !b && v && ((w = !0), (b = await st.Te(1, 1))), !b))
					return n ? await st.Tt("5225151e266155260132115a", Date.now() + 6e4) : await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", new Date().getTime() + 3e5), void (ro.St = !1);
				let j = 0,
					T = 0;
				do {
					await ghh.Gt();
					const k = m[T++],
						I = k.priceGold;
					if (k.hasRubies || !k.isFood || I > d || (w && k.measurementX === 2)) continue;
					const S = await st.Ro(k, b);
					if (((d -= I), n)) {
						const C = { from: b.bagId, fromX: b.spot.x + 1, fromY: b.spot.y + 1, to: 8, toX: 1, toY: 1, amount: 1 },
							x = await st.ja(C);
						return x.header ? headerObject.update(x) : location.reload(), await st.Wt(200), void (ro.St = !1);
					}
					await st.Yn(S.itemId, 1, 1),
						(j % 5 != 0 && T !== m.length) || ((l = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "p", p: 1 })), (c = await ghh.Yt(l)), await st.Pa(c)),
						(d -= Math.ceil(0.02)),
						j++;
				} while (T < m.length);
				n || ((l = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 })), (c = await ghh.Yt(l)), await st.Pa(c)),
					j === m.length && (await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", p + 2e4)),
					(ro.St = !1);
			},
			Uo() {
				const n = st.Bo ? st.jt("5c24a331ba6621af2b3c10", 40) : st.jt("5c24a2e10ff01", 40);
				return parseInt(n, 10);
			},
			Fo() {
				const n = jQuery("#header_values_hp_percent").text().trim().replace("%", "");
				return parseInt(n, 10);
			},
			$o() {
				if (!st.Bo) return !0;
				const n = st.jt("59285a321ba18d", !1),
					l = st.jt("5e2b2377d13", !1),
					c = st.jt("59285a251171d611d", !1),
					d = parseInt(st.jt("5638da11236100c2f3129113132721d3b1b290", 0)),
					h = Date.now();
				return !!(n && h > d) || !!c || !!l;
			},
			async Eo() {
				ro.Dt = !0;
				try {
					if ((await st.Ot(), await ghh.Gt(), st.Bo)) {
						const n = st.jt("59285a321ba18d", !1),
							l = st.jt("5e2b2377d13", !1),
							c = st.jt("59285a251171d611d", !1),
							d = parseInt(st.jt("5638da11236100c2f3129113132721d3b1b290", 0)),
							h = Date.now(),
							g = st.jt("4423037232c24aa2e2bb", 12),
							f = st.jt("44230373b1011d22b23211b1c", 0);
						n && h > d && g > f ? await al.Lo(h) : c ? await al.No() : l && (await al.Oo());
					} else await al.Go();
				} catch (n) {
					await errorLog.log(n);
				} finally {
					ro.Dt = !1;
				}
			},
			async Lo(n) {
				const l = await ghh.Yt(gh_UrlInfo.link({ mod: "guild_medic" })),
					c = Array.from(st.Vt(l).find("#guild_medicus_heal a")).filter((d) => d.href.includes("mod=guild_medic"));
				if (c.length > 0) {
					const d = st.jt("44230373b1011d22b23211b1c", 0);
					await st.Tt("44230373b1011d22b23211b1c", d + 1), await ghh.Yt(c[0].href), location.reload(), await st.Wt(200);
				} else
					await st.Tt(
						"5638da11236100c2f3129113132721d3b1b290",
						n +
						Array.from(jQuery(l).find("#guild_medicus_heal span[data-ticker-time-left]"))
							.map((d) => parseInt(d.getAttribute("data-ticker-time-left")))
							.sort()[0]
					);
			},
			async No() {
				const n = await ghh.Yt(gh_UrlInfo.link({ mod: "premium", submod: "inventory" })),
					l = st.Vt(n),
					c = Array.from(l.find(".premium_activate_button")).find((d) => !d.disabled && d.getAttribute("onclick").includes("&feature=18&"));
				if (parseInt(l.find("#header_values_hp_percent").text().trim().replace("%", "")) > 99) return location.reload(), void (await st.Wt(200));
				if (c) {
					const d = c.getAttribute("onclick"),
						h = d == null ? void 0 : d.match(/document\.location\.href='([^']+)'/);
					h && h[1] && (await ghh.Yt(h[1]), location.reload(), await st.Wt(200));
				} else await ghh.he("No Potions for healing in inventory ", { itemName: "UW" }), await st.Tt("59285a251171d611d", !1);
			},
			async Oo() {
				es.Cg().Vi > 4 ? (await ghh.Yt(gh_UrlInfo.link({ mod: "underworld", submod: "offering" })), location.reload(), await st.Wt(200)) : await st.Tt("5e2b2377d13", !1);
			},
			async Go() {
				const n = st.jt("59285a36b11201c726", !1),
					l = st.jt("59285a30947", !1),
					c = st.jt("422883161a6102c8934", ["7-23", "7-24", "7-25", "7-26", "7-27", "7-28", "7-29", "7-31"]).map((y) => parseInt(y.replace("7-", ""))),
					d = await st.Je(st.jt("532c7d5f01f20b", 512)),
					h = jQuery("<div>").append(st.Vt(d))[0],
					g = Array.from(jQuery(h).find(".ui-draggable"))
						.map((y) => st.Ke(y))
						.filter((y) => !(!y.isFood || (y.soulboundTo && y.soulboundTo !== _getJunk()) || (y.subType === 35 && !n) || (y.subType >= 23 && y.subType <= 34 && (!l || !c.includes(y.subType)))));
				if (!g.length) return await al.Ho();
				const { currentHealth: f, maxHealth: p } = al.Wo(),
					m = al.zo(g, f, p);
				if (!m) return await st.Tt("5225151e266155260132115a", Date.now() + 6e4), location.reload(), void (await st.Wt(200));
				await al.Xo(m);
			},
			Wo() {
				const n = document.querySelector("#header_values_hp_bar");
				return { currentHealth: parseInt(n.getAttribute("data-value"), 10), maxHealth: parseInt(n.getAttribute("data-max-value"), 10) };
			},
			zo(n, l, c) {
				let d = null,
					h = 0,
					g = 1 / 0;
				return (
					n.forEach((f) => {
						const p = c - (l + f.isHealing);
						p >= 0 && p < g ? ((d = f), (g = p)) : p < 0 && f.isHealing > h && ((h = f.isHealing), (d = f));
					}),
					d
				);
			},
			async Ho() {
				const n = await pchp.jo();
				if (n) headerObject.update(n);
				else {
					const l = st.jt("53381d201a173df21b222c1db", !0),
						c = st.jt("53381d201a17", !1);
					if (l && c) return void (await al.Jo());
					ghh.he("No food available for healing in inventory or packages", { itemName: "!" }), await st.Tt("5225151e266155260132115a", Date.now() + 12e4);
				}
			},
			async Jo() {
				const n = st.jt("53381d201a173df21b222c1db", !0),
					l = st.jt("53381d201a17", !1);
				n && l && (await al.Mo(!0));
			},
			async Xo(n) {
				const l = { from: st.jt("532c7d5f01f20b", 512), fromX: n.positionX, fromY: n.positionY, to: 8, toX: 1, toY: 1, amount: 1 },
					c = await st.ja(l);
				c.header ? headerObject.update(c) : location.reload();
			},
			nn: async function (n) {
				const l = await st.jt("4328a3228c1bd3c628382ba1e20dca16b15176bb", 0);
				if (l == 2 || (l == 1 && n)) return await ghh.rn(), await st.Wt(1e3), location.reload(), void (await st.Wt(200));
			},
		};
	async function fetchHtml(n, l) {
		return new Promise((c, d) => {
			chrome.runtime.sendMessage(ghExtensionId, { action: "fetchHtml", url: n, postData: l }, (h) => {
				chrome.runtime.lastError ? d(new Error(chrome.runtime.lastError.message)) : h && h.html ? c(h.html) : d(new Error((h == null ? void 0 : h.error) || "Unknown error"));
			});
		});
	}
	function getTurmaRolesTranslation(n) {
		const l = {
			ar: { tank: "Misión: Ocupate de vos mismo", dps: "Misión: Distribuí el daño", healer: "Misión: Curá a los miembros de tu grupo", out: "Misión: No te lleves" },
			ba: { tank: "Zadatak: Usmjeri pozornost prema sebi.", dps: "Zadatak: Isperi štetu", healer: "Zadatak: Izlječi grupu", out: "Zadatak: Nemoj uzeti sa" },
			br: { tank: "Missão: Atenção direta a si", dps: "Missão: Prato de danos", healer: "Missão: Curar membros do grupos", out: "Missão: Não tome com" },
			dk: { tank: "Opgave: Rette opmærksomheden mod én selv", dps: "Opgave: Uddel skade", healer: "Opgave: Heal gruppemedlemmer", out: "Opgave: Tag ikke med" },
			de: { tank: "Aufgabe: Aufmerksamkeit auf sich ziehen", dps: "Aufgabe: Schaden austeilen", healer: "Aufgabe: Gruppenmitglieder heilen", out: "Aufgabe: Nicht mitnehmen" },
			ee: { tank: "Retk: Otsene tähelepanu endale", dps: "Retk: Ründesse", healer: "Retk: Ravi grupi liikmeid", out: "Retk: Ära võta ühes" },
			es: { tank: "Misión: Llamar la atención", dps: "Misión: Reparte el daño", healer: "Misión: Curar miembros del grupo", out: "Misión: Sin utilizar" },
			fr: { tank: "Mission : Attirer l`attention sur soi", dps: "Mission : Infliger des dégâts", healer: "Mission : Soigner les membres du groupe", out: "Mission : Ne pas prendre avec soi" },
			it: { tank: "Incarico: Attrae l`attenzione su di sé", dps: "Incarico: Distribuisce i danni", healer: "Incarico: Guarisce i membri del gruppo", out: "Incarico: Disattivato" },
			lv: { tank: "Uzdevums: Pievērs uzmanību pats sev", dps: "Uzdevums: Izpostīt bojājumu", healer: "Uzdevums: Dziedināt grupas biedrus", out: "Uzdevums: Neņem ar" },
			lt: { tank: "Užduotis: Tiesioginis dėmesys į save", dps: "Užduotis: Dalinti žalą", healer: "Užduotis: Pagydyti grupės narius", out: "Užduotis: Neimti su" },
			hu: { tank: "Feladat: Ellenfél figyelmét magára vonja", dps: "Feladat: Ellenfél támadása", healer: "Feladat: Csapat tagjainak gyógyítása", out: "Feladat: Tétlen" },
			mx: { tank: "Tarea: Atención directa a uno mismo", dps: "Tarea: Reparta el daño", healer: "Tarea: Cura a los miembros del grupo", out: "Tarea: No tomes" },
			nl: { tank: "Quest: Richt de aandacht op jezelf", dps: "Quest: Schade verdelen", healer: "Quest: Groepsleden genezen", out: "Quest: Niet meenemen" },
			no: { tank: "Ekspedisjon: Diriger oppmerksomhet til seg selv", dps: "Ekspedisjon: Server ut skade", healer: "Ekspedisjon: Helbred gruppe medlemmer", out: "Ekspedisjon: Ikke ta med" },
			pl: { tank: "Zadanie: Prowokuj przeciwnika", dps: "Zadanie: Atakuj", healer: "Zadanie: Uzdrawiaj członków grupy", out: "Zadanie: Usuń z grupy" },
			pt: { tank: "Missão: Chama a atenção para si mesmo", dps: "Missão: Reparte o dano", healer: "Missão: Cura os membros do grupo", out: "Missão: Não tome com" },
			ro: { tank: "Cercetare: Atentie directa de sine", dps: "Cercetare: Distribuie daunele", healer: "Cercetare: Vindeca membrii grupului", out: "Cercetare: Fara a folosi" },
			sk: { tank: "Úloha: Upútať pozornosť", dps: "Úloha: Rozdávať zranenia", healer: "Úloha: Liečiť", out: "Úloha: Nevziať" },
			fi: { tank: "Tehtävä: Kerää huomion itseensä", dps: "Tehtävä: Tekee vauriota", healer: "Tehtävä: Parantaa ryhmän jäseniä", out: "Tehtävä: Ei oteta mukaan" },
			se: { tank: "Uppgift: Tar emot skada", dps: "Uppgift: Dela ut skada", healer: "Uppgift: Läker gruppmedlemmar", out: "Uppgift: Ta inte med" },
			tr: { tank: "Görev: Dikkati kendi üzerine çekmek", dps: "Görev: Hasarı paylaştır", healer: "Görev: Grup üyelerini iyileştir", out: "Görev: Beraberinde alma" },
			us: { tank: "Task: Direct attention to oneself", dps: "Task: Dish out damage", healer: "Task: Heal group members", out: "Task: Do not take with" },
			en: { tank: "Quest: Direct attention to oneself", dps: "Quest: Dish out damage", healer: "Quest: Heal group members", out: "Quest: Do not take with" },
			cz: { tank: "Úkol: Přitáhni na sebe pozornost", dps: "Úkol: Rozdávej rány", healer: "Úkol: Uzdrav členy družiny", out: "Úkol: Nezahrávej si s" },
			gr: { tank: "Αποστολή Πρόσεχε τον εαυτό σου", dps: "Αποστολή Προβλεπόμενη ζημιά", healer: "Αποστολή Θεραπεύστε τα μέλη της ομάδας σας", out: "Αποστολή Μη πάρεις μαζί" },
			bg: { tank: "Куест: Насочи вниманието към себе си", dps: "Куест: Прави поражение", healer: "Куест: Лекувайте членове на групата", out: "Куест: Не вземай участие" },
			ru: { tank: "Задание: Защищать", dps: "Задание: Наносить урон", healer: "Задание: Лечить членов группы", out: "Задание: Не использовать" },
			il: { tank: "משימה: תשומת לב על עצמו", dps: "משימה: נזק במנות", healer: "משימה: רפא חברי קבוצה", out: "משימה: אל תקח עם" },
			ae: { tank: "مهمة: إثارة الانتباه", dps: "مهمة: توزيع الضرر", healer: "مهمة: معالجة أعضاء المجموعة", out: "مهمة: لا تأخده معك" },
			tw: { tank: "任務: 吸引攻擊", dps: "任務: 實施攻擊", healer: "任務: 負責治癒", out: "任務: 不加入隊伍" },
		};
		return l[n] || l.en;
	}
	function resolveTurmaRole(n, l) {
		let c = l;
		try {
			(c = l.replace(/&quot;/g, '"').replace(/\\\//g, "/")), (c = JSON.parse(c)[0][0][0]);
		} catch {
			return "unknown";
		}
		const d = c.match(/&lt;font[^&]*color:#DDDDDD[^&]*&gt;([^&]+)&lt;\/font&gt;/);
		if (!d) return "unknown";
		switch (d[1].trim()) {
			case n.tank:
				return "tank";
			case n.dps:
				return "dps";
			case n.healer:
				return "healer";
			case n.out:
				return "out";
			default:
				return "unknown";
		}
	}
	async function request_getPlayerProfileData(n, l, c) {
		const d = ghh.Vo();
		if (d.has(`${l}${n}${c}`)) return d.get(`${l}${n}${c}`);
		const h = `https://s${l}-${n}.gladiatus.gameforge.com/game/index.php?mod=player&p=${c}&language=${n}`,
			g = await fetchHtml(h),
			f = st.Yo(g),
			p = f.find(".playername, .playername_achievement").first().text().trim();
		if (!p) throw new Error("Player name not found");
		let m = "";
		const y = [];
		if (
			(f.find('[class^="avatar avatar_costume_part"]').each(($, X) => {
				const K = jQuery(X).attr("style");
				if (K) {
					const tt = K.match(/background-image:\s*url\(([^)]+)\)/);
					tt && y.push(`https://s${l}-${n}.gladiatus.gameforge.com/game/${tt[1]}`);
				}
			}),
				y.length)
		)
			m = y;
		else {
			const $ = f.find("#avatar.player_picture").attr("style");
			if ($) {
				const X = $.match(/background-image:\s*url\(([^)]+)\)/);
				X && (m = `https://s${l}-${n}.gladiatus.gameforge.com/game/${X[1]}`);
			}
		}
		let b;
		const w = f.find('a[href^="index.php?mod=guild"]').first().text(),
			_ = f.find('a[href^="index.php?mod=guild"]').first().attr("href");
		if (w && _) {
			const $ = _.match(/i=(\d+)/),
				X = w.match(/\[(.+)\]$/),
				K = w.match(/^([^[]+)/);
			$ && X && K && (b = { id: $[1], name: K[1].trim(), tag: X[1].trim() });
		}
		const v = parseInt(f.find("#char_level.charstats_value22").text().trim(), 10),
			j = (() => {
				const $ = f.find("#char_leben_tt").attr("data-tooltip");
				if (!$) return [0, 0];
				const X = $.match(/"(\d+)\s*\\\/\s*(\d+)"/);
				return X ? [parseInt(X[1]), parseInt(X[2])] : [0, 0];
			})(),
			T = (() => {
				const $ = f.find("#char_exp_tt").attr("data-tooltip");
				if (!$) return [0, 0];
				const X = $.match(/"(\d+)\s*\\\/\s*(\d+)"/);
				return X ? [parseInt(X[1]), parseInt(X[2])] : [0, 0];
			})();
		function k($) {
			return parseInt(f.find(`#${$}.charstats_value`).text().trim(), 10);
		}
		const I = k("char_f0"),
			S = k("char_f1"),
			C = k("char_f2"),
			x = k("char_f3"),
			q = k("char_f4"),
			D = k("char_f5"),
			A = parseInt(f.find("#char_panzer.charstats_value22").text().trim(), 10),
			M = (() => {
				const $ = f
					.find("#char_schaden.charstats_value22")
					.text()
					.match(/(\d+)\s*-\s*(\d+)/);
				return $ ? [parseInt($[1]), parseInt($[2])] : [0, 0];
			})(),
			P = parseInt(f.find("#char_healing.charstats_value22").text().trim(), 10),
			E = f.html(),
			L = [...E.matchAll(/&quot;,(-?\d+)],\s*\[&quot;#BA9700&quot;,&quot;#BA9700&quot;\]\]/g)].map(($) => parseInt($[1])),
			W = L[7] || 0,
			O = L[8] || 0,
			G = L[9] || 0,
			U = L[11] || 0,
			F = [...E.matchAll(/&quot;,&quot;(\d+) %&quot;\],\[&quot;#DDDDDD&quot;,&quot;#DDDDDD&quot;\]\]/g)].map(($) => parseInt($[1])),
			H = F[0] || 0,
			Y = F[1] || 0,
			B = F[2] || 0,
			R = F[3] || 0,
			Q = { minerva: !1, mars: !1, apollo: !1, honour_veteran: !1, honour_destroyer: !1 },
			N = v - 8 < 2 ? 2 : v - 8;
		B - Math.round((52 * G) / N / 5) == 10 && (Q.honour_veteran = !0);
		const V = {
			id: c,
			name: p,
			img: m,
			guild: b,
			profile: {
				level: v,
				life: j,
				experience: T,
				strength: I,
				skill: S,
				agility: C,
				constitution: x,
				charisma: q,
				intelligence: D,
				armor: A,
				damage: M,
				healing: P,
				avoidCriticalPoints: W,
				blockPoints: O,
				criticalPoints: G,
				criticalHealing: U,
				"avoid-critical-percent": H,
				"block-percent": Y,
				"critical-percent": B,
				"critical-healing-percent": R,
				buffs: Q,
			},
			game: { country: n, server: l },
		};
		return d.set(`${l}${n}${c}`, V), await ghh.Ko(d), V;
	}
	async function request_getPlayerTurmaData(n, l, c, d = !0) {
		const h = ghh.Zo();
		if (h.has(`${l}${n}${c}`) && d) return h.get(`${l}${n}${c}`);
		const g = await ghh.ti(),
			f = [0, 1, 2, 3, 4],
			p = [];
		let m = [];
		const y = getTurmaRolesTranslation(g),
			b = await fetchHtml(`https://s${l}-${n}.gladiatus.gameforge.com/game/index.php?mod=player&doll=2&p=${c}&language=${g}`),
			w = st.Yo(b).html(),
			_ = /<div class="charmercpic doll(\d)"\s*data-tooltip="([^"]+)"/g;
		let v;
		const j = [];
		for (; (v = _.exec(w));) j[parseInt(v[1]) - 2] = v[2];
		m = j.map((k) => (k ? resolveTurmaRole(y, k) : "unknown"));
		for (let k = 0; k < f.length; k++) {
			await st.Wt(60);
			try {
				const I = k === 0 ? b : await fetchHtml(`https://s${l}-${n}.gladiatus.gameforge.com/game/index.php?mod=player&doll=${k + 2}&p=${c}&language=${g}`),
					S = st.Yo(I),
					C = S.find(".playername, .playername_achievement").first().text().trim();
				if (!C) continue;
				const x = parseInt(S.find("#char_level.charstats_value22").text().trim(), 10),
					q = (() => {
						const V = S.find("#char_leben_tt").attr("data-tooltip");
						if (!V) return [0, 0];
						const $ = V.match(/"(\d+)\s*\\\/\s*(\d+)"/);
						return $ ? [parseInt($[1]), parseInt($[2])] : [0, 0];
					})(),
					D = (V) => parseInt(S.find(`#${V}.charstats_value`).text().trim(), 10),
					A = D("char_f0"),
					M = D("char_f1"),
					P = D("char_f2"),
					E = D("char_f3"),
					L = D("char_f4"),
					W = D("char_f5"),
					O = parseInt(S.find("#char_panzer.charstats_value22").text().trim(), 10),
					G = (() => {
						const V = S.find("#char_schaden.charstats_value22")
							.text()
							.match(/(\d+)\s*-\s*(\d+)/);
						return V ? [parseInt(V[1]), parseInt(V[2])] : [0, 0];
					})(),
					U = parseInt(S.find("#char_healing.charstats_value22").text().trim(), 10),
					F = parseInt(S.find("#char_threat.charstats_value22").text().trim(), 10),
					H = [...S.html().matchAll(/&quot;,(-?\d+)],\s*\[&quot;#BA9700&quot;,&quot;#BA9700&quot;\]\]/g)].map((V) => parseInt(V[1])),
					Y = H[7] || 0,
					B = H[8] || 0,
					R = H[9] || 0,
					Q = H[11] || 0,
					N = m[k] || "unknown";
				p.push({
					name: C,
					level: x,
					life: q,
					strength: A,
					skill: M,
					agility: P,
					constitution: E,
					charisma: L,
					intelligence: W,
					armor: O,
					damage: G,
					healing: U,
					threat: F,
					avoidCriticalPoints: Y,
					blockPoints: B,
					criticalPoints: R,
					criticalHealing: Q,
					role: N,
				});
			} catch { }
		}
		const T = { id: c, players: p, game: { country: n, server: l } };
		return h.set(`${l}${n}${c}`, T), await ghh.ei(h), T;
	}
	function rand$1(n, l) {
		return Math.floor(Math.random() * (l - n + 1)) + n;
	}
	function arena_simulator_calculate_chances(n, l) {
		const c = { ...n },
			d = { ...l };
		(c.avoid_critical_points = Math.max(0, c.avoid_critical_points)),
			(d.avoid_critical_points = Math.max(0, d.avoid_critical_points)),
			(c.block_points = Math.max(0, c.block_points)),
			(d.block_points = Math.max(0, d.block_points)),
			(c.critical_points = Math.max(0, c.critical_points)),
			(d.critical_points = Math.max(0, d.critical_points));
		const h = Math.max(2, c.level - 8);
		return (
			(c.avoid_critical_chance = Math.round((52 * c.avoid_critical_points) / h / 4)),
			(c.avoid_critical_chance = Math.min(25, c.avoid_critical_chance)),
			(c.block_chance = Math.round((52 * c.block_points) / h / 6) + 2 * Math.max(0, c.level - d.level)),
			(c.block_chance = Math.min(50, c.block_chance)),
			(c.critical_chance = Math.round((52 * c.critical_points) / h / 5)),
			(c.critical_chance = Math.min(50, c.critical_chance)),
			(c.hit_chance = Math.floor((c.skill / (c.skill + d.agility)) * 100)),
			(c.double_hit_chance = Math.round(((c.charisma * c.skill) / (d.agility * d.intelligence)) * 10)),
			(c.buffs.minerva || d.buffs.minerva) && (c.double_hit_chance = 0),
			(c.buffs.mars || d.buffs.mars) && (c.critical_chance = 0),
			c.buffs.apollo && (c.block_chance += 15),
			c.buffs.honour_veteran && (c.critical_chance += 10),
			d.buffs.honour_destroyer && ((c.armor -= 15 * d.level), c.armor < 0 && (c.armor = 0)),
			(c.armor_absorve = [Math.floor(c.armor / 66) - Math.floor((c.armor - 66) / 660 + 1), Math.floor(c.armor / 66) + Math.floor(c.armor / 660)]),
			c
		);
	}
	function arena_simulator_hit_simulation(n, l) {
		var d, h, g, f, p, m, y, b;
		let c = 0;
		return (
			(c =
				rand$1(0, 100) <= (n.hit_chance || 0)
					? rand$1(0, 100) <= (n.critical_chance || 0)
						? rand$1(0, 100) <= (l.avoid_critical_chance || 0)
							? rand$1(n.damage[0], n.damage[1]) - rand$1(((d = l.armor_absorve) == null ? void 0 : d[0]) ?? 0, ((h = l.armor_absorve) == null ? void 0 : h[1]) ?? 0)
							: 2 * rand$1(n.damage[0], n.damage[1]) - rand$1(((g = l.armor_absorve) == null ? void 0 : g[0]) ?? 0, ((f = l.armor_absorve) == null ? void 0 : f[1]) ?? 0)
						: rand$1(0, 100) <= (l.block_chance || 0)
							? Math.floor(rand$1(n.damage[0], n.damage[1]) / 2) - rand$1(((p = l.armor_absorve) == null ? void 0 : p[0]) ?? 0, ((m = l.armor_absorve) == null ? void 0 : m[1]) ?? 0)
							: rand$1(n.damage[0], n.damage[1]) - rand$1(((y = l.armor_absorve) == null ? void 0 : y[0]) ?? 0, ((b = l.armor_absorve) == null ? void 0 : b[1]) ?? 0)
					: 0),
			c < 0 && (c = 0),
			c
		);
	}
	function arena_simulator_battle(n, l, c = "full", d = 15) {
		let h = c === "full" ? n.life[1] : n.life[0],
			g = c === "full" ? l.life[1] : l.life[0],
			f = 0,
			p = 0,
			m = 0;
		for (; m < d && h > 0 && g > 0;) {
			let b = arena_simulator_hit_simulation(n, l);
			if (((f += b), (g -= b), g <= 0)) {
				f += g;
				break;
			}
			if (rand$1(0, 100) <= (n.double_hit_chance || 0) && ((b = arena_simulator_hit_simulation(n, l)), (f += b), (g -= b), g <= 0)) {
				f += g;
				break;
			}
			if (((b = arena_simulator_hit_simulation(l, n)), (p += b), (h -= b), h <= 0)) {
				p += h;
				break;
			}
			rand$1(0, 100) <= (l.double_hit_chance || 0) && ((b = arena_simulator_hit_simulation(l, n)), (p += b), (h -= b), h <= 0 && (p += h)), m++;
		}
		g < 0 && (g = 0);
		const y = f - p;
		return g <= 0 || y > 0 ? 1 : y === 0 ? 0 : -1;
	}
	function arena_checkPlayerStats(n) {
		if (n.level < 1 || n.life[0] < 1 || n.life[1] < 1 || n.skill < 1 || n.agility < 1 || n.charisma < 1 || n.intelligence < 1 || n.armor < 0 || n.damage[0] < 1 || n.damage[1] < 1) return { error: !0 };
		const l = { minerva: !1, mars: !1, apollo: !1, honour_veteran: !1, honour_destroyer: !1, ...n.buffs };
		return {
			stats: {
				level: n.level,
				life: n.life,
				skill: n.skill,
				agility: n.agility,
				charisma: n.charisma,
				intelligence: n.intelligence,
				armor: n.armor,
				damage: n.damage,
				avoid_critical_points: n.avoidCriticalPoints,
				block_points: n.blockPoints,
				critical_points: n.criticalPoints,
				buffs: l,
			},
		};
	}
	async function arena_simulator(n, l, c = {}) {
		let d, h;
		(d = arena_checkPlayerStats(n).stats), (h = arena_checkPlayerStats(l).stats);
		let g = "full";
		c["life-mode"] && c["life-mode"] === "current" && (g = "current");
		let f = 500;
		typeof c.simulates == "number" && c.simulates > 0 && (f = Math.min(1e4, c.simulates));
		let p = 15;
		typeof c.rounds == "number" && c.rounds > 0 && c.rounds <= 50 && (p = c.rounds), (d = arena_simulator_calculate_chances(d, h)), (h = arena_simulator_calculate_chances(h, d));
		let m = 0,
			y = 0,
			b = 0;
		for (; b < f;) {
			const _ = arena_simulator_battle(d, h, g, p);
			_ === 1 ? m++ : _ === 0 && y++, b++;
		}
		const w = { "win-chance": Math.round((m / b) * 1e4) / 100, "lose-chance": Math.round(((b - m - y) / b) * 1e4) / 100, "draw-chance": Math.round((y / b) * 1e4) / 100, details: { fights: b, wins: m, loses: b - m - y, draws: y } };
		return c.info && (w.info = { attacker: void 0, defender: void 0 }), w;
	}
	function rand(n, l) {
		return Math.floor(Math.random() * (l - n + 1)) + n;
	}
	function turmaArenaSimulatorPlayers(n) {
		return n.players.filter((l) => l.role !== "out").map((l) => (l.role === "unknown" && (l.role = "dps"), l));
	}
	function turmaArenaSimulatorTypes(n) {
		return n.map((l) => (l.role === "tank" ? (l.isRoleOf = "Tank") : l.role === "healer" ? ((l.isRoleOf = "Healer"), (l.threat = 0)) : (l.isRoleOf = "DPS"), l.threat < 0 && (l.threat = 0), l));
	}
	function turmaArenaSimulatorCalculateChances(n, l) {
		return n.map((c) => {
			const d = Math.max(2, c.level - 8);
			(c.avoidCriticalChance = Math.round((52 * c.avoidCriticalPoints) / d / 4)),
				c.avoidCriticalChance > 25 && (c.avoidCriticalChance = 25),
				(c.criticalChance = Math.round((52 * c.criticalPoints) / d / 5)),
				(c.armorAbsorve = [Math.floor(c.armor / 66) - Math.floor((c.armor - 66) / 660 + 1), Math.floor(c.armor / 66) + Math.floor(c.armor / 660)]),
				(c.hitChance = []),
				(c.doubleHitChance = []),
				(c.blockChance = []);
			const h = Math.round((52 * c.blockPoints) / d / 6);
			return (
				l.forEach((g, f) => {
					(c.hitChance[f] = Math.floor((c.skill / (c.skill + g.agility)) * 100)),
						(c.doubleHitChance[f] = c.charisma - g.charisma),
						c.doubleHitChance[f] < 0 && (c.doubleHitChance[f] = 0),
						c.doubleHitChance[f] > 100 && (c.doubleHitChance[f] = 100),
						(c.blockChance[f] = h + 2 * Math.max(0, c.level - g.level)),
						c.blockChance[f] > 50 && (c.blockChance[f] = 50);
				}),
				c
			);
		});
	}
	function turmaArenaSimulatorHitSimulation(n, l) {
		var c, d, h, g, f, p, m, y, b, w;
		return rand(0, 100) <= (((c = n.hitChance) == null ? void 0 : c[l.index]) ?? 0)
			? rand(0, 100) <= (n.criticalChance ?? 0)
				? rand(0, 100) <= (l.avoidCriticalChance ?? 0)
					? rand(n.damage[0], n.damage[1]) - rand(((d = l.armorAbsorve) == null ? void 0 : d[0]) ?? 0, ((h = l.armorAbsorve) == null ? void 0 : h[1]) ?? 0)
					: 2 * rand(n.damage[0], n.damage[1]) - rand(((g = l.armorAbsorve) == null ? void 0 : g[0]) ?? 0, ((f = l.armorAbsorve) == null ? void 0 : f[1]) ?? 0)
				: rand(0, 100) <= (((p = l.blockChance) == null ? void 0 : p[n.index]) ?? 0)
					? Math.floor(rand(n.damage[0], n.damage[1]) / 2) - rand(((m = l.armorAbsorve) == null ? void 0 : m[0]) ?? 0, ((y = l.armorAbsorve) == null ? void 0 : y[1]) ?? 0)
					: rand(n.damage[0], n.damage[1]) - rand(((b = l.armorAbsorve) == null ? void 0 : b[0]) ?? 0, ((w = l.armorAbsorve) == null ? void 0 : w[1]) ?? 0)
			: 0;
	}
	function turmaArenaSimulatorHealSimulation(n) {
		let l = n.healing;
		return rand(0, 100) <= n.criticalHealing ? ((l *= 2), ["Critical", l]) : ["Normal", l];
	}
	function turmaArenaSimulatorMostWounded(n) {
		let l = null,
			c = 0;
		return (
			n.forEach((d) => {
				const h = d.life[1] - d.life[0];
				h > c && ((l = d), (c = h));
			}),
			l
		);
	}
	function turmaArenaSimulatorGetRandomThreatbasedPlayer(n) {
		var d, h, g, f;
		let l = 0;
		for (let p = 0; p < n.length; ++p) l += ((d = n[p].last) == null ? void 0 : d.threat) === 0 ? n[p].threat : ((h = n[p].last) == null ? void 0 : h.threat) ?? n[p].threat;
		let c = rand(0, l);
		for (let p = 0; p < n.length; ++p) {
			const m = ((g = n[p].last) == null ? void 0 : g.threat) === 0 ? n[p].threat : ((f = n[p].last) == null ? void 0 : f.threat) ?? n[p].threat;
			if (c <= m) return n[p];
			c -= m;
		}
		return n[n.length - 1];
	}
	function turmaArenaSimulatorPlayerActionHealPlayer(n, l) {
		let c = turmaArenaSimulatorHealSimulation(n)[1];
		return c > l.life[1] - l.life[0] && (c = l.life[1] - l.life[0]), (l.life[0] += c), (l.score["healing-taken"] += c), (l.last.heal = c), (n.score["healing-done"] += c), [n, l];
	}
	function turmaArenaSimulatorPlayerActionAttackPlayer(n, l) {
		var h;
		let c = turmaArenaSimulatorHitSimulation(n, l),
			d = !1;
		return (
			l.life[0] - c < 0 ? (c = l.life[0]) : rand(0, 100) <= (((h = n.doubleHitChance) == null ? void 0 : h[l.index]) ?? 0) && ((d = turmaArenaSimulatorHitSimulation(n, l)), l.life[0] - c - (d || 0) < 0 && d && (d = l.life[0] - c)),
			(l.life[0] -= c),
			d && (l.life[0] -= d),
			(l.score["damage-taken"] += c),
			(n.score["damage-done"] += c),
			(n.last.damage = c),
			d && ((l.score["damage-taken"] += d), (n.score["damage-done"] += d), (n.last.damage += n.threat + d)),
			[n, l]
		);
	}
	function turmaArenaSimulatorPlayerAction(n, l, c) {
		if (n.isRoleOf === "Healer") {
			const h = turmaArenaSimulatorMostWounded(l);
			if (h) return [turmaArenaSimulatorPlayerActionHealPlayer(n, h), []];
		}
		const d = turmaArenaSimulatorPlayerActionAttackPlayer(n, turmaArenaSimulatorGetRandomThreatbasedPlayer(c));
		return [[d[0]], [d[1]]];
	}
	function turmaArenaSimulatorGetTeamScore(n, l) {
		let c = 0;
		return (
			n.forEach((d) => {
				(c += (d.isRoleOf === "DPS" ? 2 : 1) * d.score["damage-done"]), (c += Math.round(d.score["healing-done"] / 2));
			}),
			l.forEach((d) => {
				d.life[0] <= 0 && (c += d.life[0]);
			}),
			c
		);
	}
	function turmaArenaCheckPlayerStats(n) {
		for (const l of n.players)
			if (
				l.level < 1 ||
				!l.life ||
				l.life[0] < 1 ||
				l.life[1] < 1 ||
				l.skill < 1 ||
				l.agility < 1 ||
				l.charisma < 1 ||
				l.intelligence < 1 ||
				l.armor < 0 ||
				l.damage[0] < 1 ||
				l.damage[1] < 1 ||
				l.healing < 0 ||
				l.threat < 0 ||
				l.avoidCriticalPoints < 0 ||
				l.blockPoints < 0 ||
				l.criticalPoints < 0 ||
				l.criticalHealing < 0 ||
				!l.role ||
				!["tank", "dps", "healer", "out", "unknown"].includes(l.role)
			)
				return { error: !0 };
		return { players: n.players };
	}
	function randInt(n, l) {
		return ((Math.random() * (l - n + 1)) | 0) + n;
	}
	function clonePlayerFast(n, l) {
		return { ...n, index: l, isAlive: !0, score: { "damage-done": 0, "damage-taken": 0, "healing-done": 0, "healing-taken": 0 }, last: { threat: 0, heal: 0, damage: 0 }, life: [n.life[1], n.life[1]] };
	}
	async function turmaArenaSimulator(n, l, c = {}) {
		const d = turmaArenaCheckPlayerStats(n);
		if (d.error || !d.players) return { error: !0, message: "Player stats error." };
		let h = Number(c.simulates) || 50;
		h = Math.min(Math.max(h, 1), 1e4);
		let g = turmaArenaSimulatorPlayers({ players: d.players }),
			f = turmaArenaSimulatorPlayers({ players: l.players });
		(g = turmaArenaSimulatorTypes(g)), (f = turmaArenaSimulatorTypes(f)), (g = turmaArenaSimulatorCalculateChances(g, f)), (f = turmaArenaSimulatorCalculateChances(f, g));
		let p = 0,
			m = 0,
			y = 0;
		for (; y < h;) {
			const b = turmaArenaSimulatorBattle(g.map(clonePlayerFast), f.map(clonePlayerFast));
			b === 1 ? p++ : b === 0 && m++, y++;
		}
		return { "win-chance": Math.round((p / y) * 1e4) / 100, "lose-chance": Math.round(((y - p - m) / y) * 1e4) / 100, "draw-chance": Math.round((m / y) * 1e4) / 100, details: { fights: y, wins: p, loses: y - p - m, draws: m } };
	}
	function turmaArenaSimulatorBattle(n, l, c = 50) {
		for (let m = 0; m < n.length; m++)
			(n[m].life[0] = n[m].life[1]), (n[m].index = m), (n[m].score = { "damage-done": 0, "damage-taken": 0, "healing-done": 0, "healing-taken": 0 }), (n[m].last = { threat: 0, heal: 0, damage: 0 }), (n[m].isAlive = !0);
		for (let m = 0; m < l.length; m++)
			(l[m].life[0] = l[m].life[1]), (l[m].index = m), (l[m].score = { "damage-done": 0, "damage-taken": 0, "healing-done": 0, "healing-taken": 0 }), (l[m].last = { threat: 0, heal: 0, damage: 0 }), (l[m].isAlive = !0);
		let d = 0,
			h = n.filter((m) => m.life[0] > 0).length,
			g = l.filter((m) => m.life[0] > 0).length;
		for (; d < c && h > 0 && g > 0;) turmaArenaSimulatorRound(n, l), (h = n.filter((m) => m.life[0] > 0).length), (g = l.filter((m) => m.life[0] > 0).length), d++;
		const f = turmaArenaSimulatorGetTeamScore(n, l),
			p = turmaArenaSimulatorGetTeamScore(l, n);
		return f === p ? 0 : f > p ? 1 : -1;
	}
	function turmaArenaSimulatorRound(n, l) {
		const c = [];
		for (let d = 0; d < n.length; ++d) n[d].life[0] > 0 && c.push({ team: "atk", idx: d });
		for (let d = 0; d < l.length; ++d) l[d].life[0] > 0 && c.push({ team: "def", idx: d });
		for (let d = c.length - 1; d > 0; d--) {
			const h = randInt(0, d);
			[c[d], c[h]] = [c[h], c[d]];
		}
		for (let d = 0; d < c.length; ++d) {
			const { team: h, idx: g } = c[d];
			if (h === "atk") {
				if (n[g].life[0] <= 0) continue;
				const f = turmaArenaSimulatorPlayerAction(n[g], n, l);
				(n[f[0][0].index] = f[0][0]),
					f[0].length > 1
						? ((n[f[0][1].index] = f[0][1]), (n[f[0][0].index].last.threat += n[f[0][0].index].last.heal))
						: ((l[f[1][0].index] = f[1][0]), (n[f[0][0].index].last.threat += (n[f[0][0].index].isRoleOf === "Tank" ? 2 : 1) * n[f[0][0].index].threat + n[f[0][0].index].last.damage));
			} else {
				if (l[g].life[0] <= 0) continue;
				const f = turmaArenaSimulatorPlayerAction(l[g], l, n);
				(l[f[0][0].index] = f[0][0]),
					f[0].length > 1
						? ((l[f[0][1].index] = f[0][1]), (l[f[0][0].index].last.threat += l[f[0][0].index].last.heal))
						: ((n[f[1][0].index] = f[1][0]), (l[f[0][0].index].last.threat += (l[f[0][0].index].isRoleOf === "Tank" ? 2 : 1) * l[f[0][0].index].threat + l[f[0][0].index].last.damage));
			}
		}
	}
	const STRIKE_KEYS = {
		arena: { strike: "503f1814401001b6522", strikeLimit: "503f1814401001b652241121b31", strikeProv: "503f18143e111b1f411d333a11417", strikeLimitProv: "503f18143e111b1f411d333a11417971566" },
		circusTuma: { strike: "453816b14401001b6522", strikeLimit: "453816b14401001b652241121b31", strikeProv: "453816b143e111b1f411d333a11417", strikeLimitProv: "453816b143e111b1f411d333a11417971566" },
	},
		at = {
			async ai() {
				if ((await at.ni("circusTuma"), !st.jt("circusTuma.attackProvServerEnabled", !0))) return await st.Tt("5f281c12341a1715a43a323a15e", Date.now() + 6e4), (ro.wt = !1);
				const n = gh_UrlInfo.link({ mod: "arena", submod: "serverArena", aType: 3 }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = c.find("#own3").children().eq(0).children().eq(0).children();
				if (d.length <= 1) return (ro.wt = !1);
				const h = await at.ri(d, !1);
				return (await st.zn()) ? (h > -1 ? (await at.oi(c.find("div.attack")[h]), (ro.wt = !1)) : await at.ii(3)) : (ro.wt = !1);
			},
			async ci() {
				if ((await at.ni("arena"), !st.jt("arena.attackProvServerEnabled", !0))) return await st.Tt("5f281c12341a1715a42f352d16e", Date.now() + 6e4), (ro.wt = !1);
				const n = gh_UrlInfo.link({ mod: "arena", submod: "serverArena", aType: 2 }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = c.find("#own2").children().eq(0).children().eq(0).children();
				if (d.length <= 1) return (ro.wt = !1);
				const h = await at.ri(d, !0);
				return (await st.zn()) ? (h > -1 ? (await at.oi(c.find("div.attack")[h]), (ro.wt = !1)) : await at.ii(2)) : (ro.wt = !1);
			},
			async ii(n) {
				const l = gh_UrlInfo.link({ mod: "arena", submod: "getNewOpponents", aType: n });
				return await ghh.Tn(l, { actionButton: "Search for opponents" }), await st.Wt(500), location.reload(), await st.Wt(500), (ro.wt = !1);
			},
			async ri(n, l) {
				const c = l ? "arena" : "circusTuma",
					d = l ? "503f18142741a61db03d11316" : "453816b142741a61db03d11316",
					h = l ? "503f18142ff186183d2e25" : "453816b142ff186183d2e25",
					g = st.jt(`${c}.levelRestriction`, 10),
					f = st.jt(`${c}.players`, []).map((M) => M.trim().toLowerCase()),
					p = st.jt(`${c}.ignorePlayers`, []).map((M) => M.trim().toLowerCase()),
					m = st.jt(`${c}.ignoreServers`, [gh_UrlInfo.server]).map((M) => M.trim().toLowerCase()),
					y = st.jt(d, !0),
					b = st.jt(h, !0),
					w = Math.max(st.jt(`${c}.maxProvAttacks`, 5), 1),
					_ = st.jt(`${c}.allowMaxProvAttacks`, !1),
					v = st.jt(`${c}.attackTargetOnly`, !1),
					j = st.jt(`${c}.skipLow`, !1),
					T = st.jt(`${c}.minChance`, 80),
					k = ghh.ne();
				let I = Array.from(
					n.slice(1).map((M, P) => {
						const E = P.children;
						if (E.length <= 1) return null;
						const L = y && E[0].querySelector("a span") !== null;
						return {
							chance: 0,
							data: at.li(jQuery(E[3].querySelector("div.attack")).attr("onclick")),
							name: E[0].innerText.trim().toLowerCase(),
							level: parseInt(E[1].innerText),
							server: E[2].innerText.trim().toLowerCase(),
							originalIndex: M,
							ignored: L,
						};
					})
				);
				if (!I.length) return await st.Tt(`${c}.attackProvServerEnabled`, !1), -1;
				const S = new Map();
				I.forEach((M) => {
					S.set(M.name, (S.get(M.name) || 0) + 1);
				}),
					(I = I.filter((M) => S.get(M.name) === 1 && !M.ignored)),
					(I = I.sort(() => Math.random() - 0.5));
				let C = st.jt(`${c}.provServerAttackData`, {});
				const x = ghh.ui();
				C.date !== x && (C = { date: x, data: [] });
				const q = new Map(
					C.data.map((M) => {
						var P;
						return [(P = M == null ? void 0 : M.p) == null ? void 0 : P.trim(), M];
					})
				);
				for (const M of I) {
					const P = `${M.name}-${M.server}`;
					q.has(P) || q.set(P, { p: P, s: 0 });
				}
				const D = (M) => (_ ? (q.has(M) ? q.get(M).s < w : !1) : !0);
				let A;
				for (const M of I)
					if (!p.includes(M.name) && !m.includes(M.server) && M.level <= k + g && f.includes(M.name) && D(`${M.name}-${M.server}`)) {
						A = M.originalIndex;
						break;
					}
				if (A === void 0 && !v && b)
					try {
						const M = l ? request_getPlayerProfileData : request_getPlayerTurmaData,
							P = l ? at.di : at.hi;
						if (M) {
							let E = await M(gh_UrlInfo.country, gh_UrlInfo.server, _getJunk().toString());
							if (!l) {
								const L = turmaArenaCheckPlayerStats(E);
								(!L.error && L.players) || (await st.Wt(1e3), (E = await request_getPlayerTurmaData(gh_UrlInfo.country, gh_UrlInfo.server, _getJunk().toString(), !1)));
							}
							for (const L of I)
								if (!p.includes(L.name) && !m.includes(L.server) && D(`${L.name}-${L.server}`)) {
									await st.Wt(100);
									const W = await P(E, L.data);
									if (W && W["win-chance"] >= T) {
										A = L.originalIndex;
										break;
									}
									L.chance = W["win-chance"] || 0;
								}
							I = I.sort((L, W) => W.chance - L.chance);
						}
					} catch { }
				if (A === void 0 && !v && !j) {
					let M = k + g + 2;
					for (const P of I) P.level <= k + g && !m.includes(P.server) && !p.includes(P.name) && P.level < M && D(`${P.name}-${P.server}`) && ((M = P.level), (A = P.originalIndex));
				}
				if (A !== void 0) {
					const M = I.find((E) => E.originalIndex === A),
						P = `${M.name}-${M.server}`;
					q.has(P) ? q.get(P).s++ : q.set(P, { p: P, s: 1 }), (C.data = ghh.gi([...q.values()])), await st.Tt(`${c}.provServerAttackData`, C);
				}
				return A !== void 0 ? A : -1;
			},
			async ni(eventType) {
				const isAttackEnabled = st.jt(`${eventType}.attackSameServerEnabled`, !1);
				if (!isAttackEnabled) return !1;
				const currentTime = Date.now(),
					isAttackProgressLeagueEnabled = st.jt(`${eventType}.progressLeague`, !1),
					isAttackScoreEnabled = st.jt(`${eventType}.attackScore`, !1),
					isAttackScoreSingle = st.jt(`${eventType}.attackScoreSingle`, !1),
					ignoredPlayers = st.jt(`${eventType}.ignoreSameServerPlayers`, []),
					normalPriority = st.jt(`${eventType}.sameServerPlayers`, []);
				let sameServerPlayers = normalPriority,
					lowPriorityPlayers = [];
				isAttackScoreEnabled && ((lowPriorityPlayers = await at.mi(eventType)), (sameServerPlayers = [...new Set([...sameServerPlayers, ...lowPriorityPlayers])]));
				let priorityLeaguePlayers = [];
				isAttackProgressLeagueEnabled && ((priorityLeaguePlayers = await at.pi(eventType !== "arena")), (sameServerPlayers = [...new Set([...sameServerPlayers, ...priorityLeaguePlayers])]));
				let attackData = st.jt(`${eventType}.sameServerAttackData`, {});
				const currentServerDate = ghh.ui();
				attackData.date !== currentServerDate && (attackData = { date: currentServerDate, data: [] });
				const playerMap = new Map(
					attackData.data.map((n) => {
						var l;
						return [(l = n == null ? void 0 : n.p) == null ? void 0 : l.trim(), n];
					})
				);
				for (const n of sameServerPlayers) {
					const l = n == null ? void 0 : n.trim();
					playerMap.has(l) || playerMap.set(l, { p: l, s: 0, t: 0 });
				}
				attackData.data = ghh.gi([...playerMap.values()]);
				const priorityPlayers = new Set([...normalPriority, ...priorityLeaguePlayers]),
					conditionScoreAttack = (n, l) => (isAttackScoreSingle && !priorityPlayers.has(n) ? l < 1 : l < 5);
				let eligiblePlayers = attackData.data.filter((n) => n && !ignoredPlayers.includes(n.p) && n.t <= currentTime && conditionScoreAttack(n.p, n.s) && sameServerPlayers.includes(n.p)).sort((n, l) => n.t - l.t);
				if (isAttackProgressLeagueEnabled || isAttackScoreEnabled) {
					const n = eligiblePlayers.filter((d) => priorityLeaguePlayers.includes(d.p)),
						l = eligiblePlayers.filter((d) => !priorityLeaguePlayers.includes(d.p) && normalPriority.includes(d.p)),
						c = eligiblePlayers.filter((d) => !priorityLeaguePlayers.includes(d.p) && !normalPriority.includes(d.p) && lowPriorityPlayers.includes(d.p));
					eligiblePlayers = [...n, ...l, ...c];
				}
				if (eligiblePlayers.length === 0) return !1;
				const isDelaySuccessful = await st.zn();
				if (!isDelaySuccessful) return (ro.wt = !1), !1;
				let attackSucceeded = !1,
					battle;
				for (const n of eligiblePlayers)
					if (n.s < 5 && ((n.t = currentTime + 60 * ghh.yi(15, 30) * 1e3), await st.Tt(`${eventType}.sameServerAttackData`, attackData), (battle = await (eventType === "arena" ? ghh.bi : ghh.wi)(n.p)), battle.success)) {
						n.s++, (attackSucceeded = battle.success), await at._i(eventType);
						break;
					}
				return await st.Tt(`${eventType}.sameServerAttackData`, attackData), attackSucceeded && (eval(battle.context), await st.Wt(500)), attackSucceeded;
			},
			li: (n) => n.match(/startProvinciarumFight(?:Confirmed)?\(\s*this,\s*(\d+),\s*(\d+),\s*(\d+),\s*['"](\w+)['"]\s*\);?/),
			async oi(e, nextRevenge = !1) {
				const o = at.li(jQuery(e).attr("onclick"));
				jQuery("#errorRow").css({ display: "none" });
				const combatData = `mod=arena&submod=doCombat&aType=${o[1]}&opponentId=${o[2]}&serverId=${o[3]}&country=${o[4]}`,
					retryUrl = combatData.replace("doCombat", "confirmDoCombat"),
					response = await st.Qr("get", "ajax.php", retryUrl, retryUrl);
				response.success ? (await at._i(parseInt(o[1]) == 2 ? "arena" : "circusTuma"), eval(response.context), await st.Wt(500)) : (nextRevenge && (await at.ki(parseInt(o[1]))), location.reload(), await st.Wt(400));
			},
			async _i(n) {
				const l = st.jt(`${n}.dailyAttacks`, {}),
					c = ghh.ui(),
					d = {};
				(d[c] = (l[c] ?? 0) + 1), await st.Tt(`${n}.dailyAttacks`, d);
			},
			Ii: (n) => (st.jt(`${n}.dailyAttacks`, {})[ghh.ui()] ?? 0) < st.jt(`${n}.limitAttacks`, 100),
			async mi(n) {
				const l = st.jt(`${n}.topList`, {}),
					c = st.jt(`${n}.levelRestriction`, 10),
					d = ghh.ne(),
					h = ghh.ui();
				if (!l[h]) {
					const g = st.jt(`${n}.attackScoreP`, 1),
						f = gh_UrlInfo.link({ mod: "highscore" }),
						p = await ghh.Tn(f, { a: g }),
						m = st
							.Vt(p)
							.find("tr")
							.map((b, w) => {
								const _ = jQuery(w),
									v = _.find("td.ellipsis a:not(:has(span))"),
									j = _.find("td").eq(1);
								if (v.length === 0 || j.length === 0) return null;
								const T = parseInt(j.text().trim());
								return isNaN(T) ? null : T <= d + c && T >= d - c ? v.text().trim() : null;
							})
							.get(),
						y = {};
					return (y[h] = m), await st.Tt(`${n}.topList`, y), m;
				}
				return l[h];
			},
			async pi(n = !1) {
				const l = gh_UrlInfo.link({ mod: "arena", ...(n ? { submod: "grouparena" } : {}) }),
					c = await ghh.Yt(l);
				return st
					.Vt(c)
					.find("aside.right td.ellipsis a:not(:has(span))")
					.map((d, h) => jQuery(h).text().trim())
					.get();
			},
			Si(n = 2) {
				const l = document.querySelector("#menue_reports");
				return l && l.querySelector(".menue_new_count") && (new RegExp(`t=${n}`).test(l.getAttribute("href")) || parseInt(l.querySelector(".menue_new_count").textContent ?? "1") > 1);
			},
			async Di(n = 2) {
				const l = gh_UrlInfo.link({ mod: "reports", t: n }),
					c = await ghh.Yt(l),
					d = st.Vt(c),
					h = jQuery("#menue_reports"),
					g = d.find("#menue_reports");
				h.replaceWith(g);
				const f = d.find("tr.unread").has(".icon_defense").has('a[style*="color: #247F2A;"]').first().find("td:last a").attr("href");
				if (!f) return await at.ki(n), void (await st.Wt(200));
				await st.zn(), await at.qi(f, n);
			},
			async ki(n = 2) {
				const l = n === 2 ? "503f181440d11111b3c223e1d11520" : "522416501d3714e40292d01b2020181d11521";
				await st.Tt(l, Date.now() + 60 * st.Le(5, 2) * 1e3);
			},
			async qi(link, type = 2) {
				const result = await ghh.Yt(link),
					context = st.Vt(result),
					onclickElement = context.find(".attack.arrow");
				try {
					if (onclickElement) {
						const isProvinciarum = onclickElement.attr("onclick").trim().startsWith("startProvinciarumFight");
						if (isProvinciarum) await at.oi(onclickElement, !0);
						else {
							const playerIdMatch = onclickElement
								.attr("onclick")
								.trim()
								.match(/startFight\(\s*this,\s*(\d+)/),
								revengePlayerId = playerIdMatch ? playerIdMatch[1] : null,
								response = await st.Qr("get", `ajax/${type == 2 ? "doArenaFight" : "doGroupFight"}.php`, `did=${revengePlayerId}`);
							response.success ? (await at._i(type == 2 ? "arena" : "circusTuma"), eval(response.context), await st.Wt(500)) : (await at.ki(type), location.reload(), await st.Wt(400));
						}
					}
				} catch (n) {
					await errorLog.log(n);
				}
			},
			async Ai(n, l, c) {
				const d = STRIKE_KEYS[l],
					h = c ? d.strikeProv : d.strike,
					g = c ? d.strikeLimitProv : d.strikeLimit,
					f = new Map(st.jt(h, [])),
					p = st.jt(g, 3),
					m = f.get(n) || 0,
					y = p - 1 < m + 1;
				return y && f.has(n) ? f.delete(n) : f.set(n, m + 1), await st.Tt(h, [...f.entries()]), y;
			},
			async di(n, l) {
				const c = await request_getPlayerProfileData(l[4], l[3], l[2]);
				return await arena_simulator(n.profile, c.profile, { simulates: 500 });
			},
			async hi(n, l) {
				let c = await request_getPlayerTurmaData(l[4], l[3], l[2]);
				const d = turmaArenaCheckPlayerStats(c);
				return (!d.error && d.players) || (await st.Wt(1e3), (c = await request_getPlayerTurmaData(l[4], l[3], l[2], !1))), await turmaArenaSimulator(n, c, { simulates: 200 });
			},
		},
		ts = {
			async Ci() {
				const n = gh_UrlInfo.link({ mod: "quests" }),
					l = gh_UrlInfo.link({}).split("index")[0],
					c = await ghh.Yt(n),
					d = st.Vt(c),
					h = Array.from(d.find(".quest_slot_button.quest_slot_button_cancel")).map((g) => l + g.getAttribute("href"));
				h.length && (await this.Pi(h));
			},
			async xi() {
				const n = gh_UrlInfo.link({ mod: "quests" }),
					l = gh_UrlInfo.link({}).split("index")[0];
				let c = await ghh.Yt(n);
				const d = st.Vt(c),
					h = Array.from(d.find(".quest_slot_button.quest_slot_button_finish")).map((p) => l + p.getAttribute("href"));
				h.length > 0 && (await this.Ti(d));
				const g = Array.from(jQuery(c).find(".quest_slot_button_restart")).map((p) => l + p.getAttribute("href")),
					f = [...h, ...g];
				f.length && (await this.Pi(f), (c = await ghh.Yt(n))), await this.ji(st.Vt(c), l);
			},
			async Pi(n) {
				for (const l of n) {
					const c = st.Vt(await ghh.Yt(l));
					st.ke(c.find("#sstat_gold_val")[0].textContent);
				}
			},
			async Mi() {
				try {
					const n = gh_UrlInfo.link({ mod: "quests" }),
						l = await ghh.Yt(n),
						c = st.Vt(l),
						d = ts.Qi(c);
					if (d.length < 5) {
						const g = this.Ri(c);
						await st.Tt("403811514001b63a283f16", Date.now() + g + 100);
					}
					await ts.Ui(d);
					const h = await ts.Bi(c);
					return await st.Wt(100), h.length > 0 && (await ts.Fi(h)), !0;
				} catch (n) {
					return await errorLog.log(n), !1;
				}
			},
			Qi: (n) =>
				Array.from(n.find(".contentboard_slot_active")).map((l) => {
					var d;
					const c = (d = l.querySelector(".quest_slot_icon")) == null ? void 0 : d.getAttribute("style");
					return ts.$i(c);
				}),
			async Bi(n) {
				const l = gh_UrlInfo.link({}).split("index")[0],
					c = Array.from(n.find(".quest_slot_button_finish")).map((h) => l + (h.getAttribute("href") || "")),
					d = Array.from(n.find(".quest_slot_button_restart")).map((h) => l + (h.getAttribute("href") || ""));
				if (c.length) {
					await this.Ti(n);
					const h = this.Ri(n);
					await st.Tt("403811514001b63a283f16", Date.now() + h + 100);
				}
				return [...c, ...d];
			},
			async Fi(n) {
				await st.zn(5);
				for (const l of n)
					try {
						const c = st.Vt(await ghh.Yt(l));
						st.ke(c.find("#sstat_gold_val")[0].textContent);
					} catch (c) {
						await errorLog.log(c);
					}
			},
			async ji(n, l) {
				const c = this.Ri(n);
				await st.Tt("403811514001b63a283f16", Date.now() + c + 100);
				const d = this.Ei(),
					h = this.Qi(n);
				await ts.Ui(h);
				const g = st.jt("4239b163c82f1d461a63a1d113", !1),
					f = st.jt("4239b163c82f1d461a133da213", !1),
					p = st.jt("5039107165", !1),
					m = st.jt("503910716537", !1),
					y = st.jt("5039107165226c1f", !1),
					b = st.jt("5039107165200", !1),
					w = !d.stopArenaInUnder || (d.stopArenaInUnder && !d.isInUnderworld),
					_ = !d.runUntilGetChest || (d.runUntilGetChest && !d.currentHistory.gotTreasureBox),
					v = [gh_questType.any, gh_questType.combat];
				(p || d.isInUnderworld || m) && v.push(gh_questType.item),
					(p || d.isInUnderworld) && v.push(gh_questType.expedition),
					m && v.push(gh_questType.dungeon),
					y && w && (!g || at.Ii("arena")) && v.push(gh_questType.arena),
					b && _ && (!f || at.Ii("circusTuma")) && v.push(gh_questType.groupArena);
				const j = this.Li(d).filter((k) => k.active),
					T = this.Ni(n, j, v);
				await st.Wt(100), T ? await this.Oi(T, l) : await this.Gi(n, l);
			},
			Ri: (n) => parseInt(jQuery(n).find("#quest_header_cooldown [data-ticker-type]").data("ticker-time-left")) + 200 || 0,
			Ei: () => ({
				stopArenaInUnder: st.jt("4239b16341c61a826012261ca0", !0),
				runUntilGetChest: st.jt("4338a331b1aa182ea1a4201d1c6", !1),
				currentHistory: ghh.Hi(),
				isInUnderworld: st.Bo,
				questRules: st.jt("403811513c1618c1c", []),
				farmQuestEnabled: st.jt("572c16b241b671d", !0),
			}),
			async Ui(n) {
				const l = n.includes(gh_questType.combat),
					c = st.jt("503f18142dc19be1a", !0),
					d = st.jt("453816b142dc19be1a", !0),
					h = n.includes(gh_questType.arena) || (c && l),
					g = n.includes(gh_questType.groupArena) || (d && l),
					f = n.includes(gh_questType.expedition) || l,
					p = n.includes(gh_questType.dungeon) || l;
				await st.Tt("592c17277bd15381ab343c", h), await st.Tt("592c1732032251ca1d33", g), await st.Tt("592c1723d1e61001b72826b3e7201dc", f), await st.Tt("592c172200411613f322db1b", p);
			},
			Li: (n) => (n.isInUnderworld && n.farmQuestEnabled ? ld.Mr() : n.questRules),
			Ni: (n, l, c) =>
				Array.from(jQuery(n).find(".contentboard_slot_inactive"))
					.map((d) => ts.Wi(d, l))
					.filter((d) => d && (d.pickIfInactive || (!d.pickIfInactive && c.includes(d.questType))) && d.validToPick)
					.sort((d, h) => h.order - d.order || h.count - d.count)
					.pop(),
			Wi(n, l) {
				var C, x, q, D;
				const c = n.querySelector(".quest_slot_button");
				if (!c) return null;
				const d = ((C = n.querySelector(".quest_slot_icon")) == null ? void 0 : C.getAttribute("style")) || "",
					h = ts.$i(d),
					g = ((x = n.querySelector(".quest_slot_title")) == null ? void 0 : x.textContent.toLowerCase()) || "",
					f = g.match(/(\d+)/),
					p = f ? parseInt(f[1]) : 0,
					m = !!n.querySelector(".quest_slot_time");
				let y = null,
					b = !1,
					w = !1;
				const _ = n.querySelector(".quest_slot_reward_item > img");
				if (_)
					try {
						const A = JSON.parse(jQuery(_).attr("data-tooltip") || "[]");
						(y = A[0][0]), (b = (((D = (q = A[0]) == null ? void 0 : q[A[0].length - 1]) == null ? void 0 : D[2]) ?? 0) === 250);
					} catch { }
				const v = n.querySelector(".quest_slot_reward_xp"),
					j = v !== null && v.children.length > 0,
					T = Array.from(n.querySelectorAll(".quest_slot_reward_god")).reduce((A, M) => {
						var W, O, G, U;
						const P = (((W = M.querySelector("[src]")) == null ? void 0 : W.getAttribute("src")) || "").match(/\/(\w+)\.png/),
							E = P ? gh_godImageMaps[P[1]] : "",
							L = parseInt(((U = (G = (O = M.querySelector("[data-tooltip]")) == null ? void 0 : O.getAttribute("data-tooltip")) == null ? void 0 : G.match(/\d+/)) == null ? void 0 : U[0]) || "0", 10);
						return E && (A[E] = L), A;
					}, {}),
					k = this.zi(n.querySelector(".quest_slot_reward_gold span"));
				let I = !1,
					S = 999;
				for (const A of l.filter((M) => M.type === h || M.type === gh_questType.any)) ts.Xi(A, g, p, y, m, b, j, T, k) && ((I = !0), (w = A.pickIfInactive ?? !1), (S = l.indexOf(A)));
				return { url: c.getAttribute("href") || "", questType: h, count: p, title: g, validToPick: I, order: S, pickIfInactive: w, gold: k };
			},
			Xi: (n, l, c, d, h = !1, g = !1, f = !1, p = {}, m = 0) =>
				n.conditions.every((y) => {
					switch (y.operator) {
						case gh_filterOperators.minGold:
							return m >= y.value;
						case gh_filterOperators.minHonor:
							return Math.floor(m / 10) >= y.value;
						case gh_filterOperators.contains:
							return new RegExp(y.value.trim(), "gi").test(l);
						case gh_filterOperators.notContains:
							return !new RegExp(y.value.trim(), "gi").test(l);
						case gh_filterOperators.numberAttackLessThan:
							return c < y.value;
						case gh_filterOperators.hasNoTimer:
							return !h;
						case gh_filterOperators.itemContains:
							return !!d && new RegExp(y.value.trim(), "gi").test(d);
						case gh_filterOperators.itemNotContains:
							return !d || !new RegExp(y.value.trim(), "gi").test(d);
						case gh_filterOperators.rewardIsFood:
							return g;
						case gh_filterOperators.rewardIsNotFood:
							return !g && d;
						case gh_filterOperators.rewardContainsAny:
							return !!d && y.value.some((b) => new RegExp(b.trim(), "gi").test(d));
						case gh_filterOperators.hasExperience:
							return f;
						case gh_filterOperators.hasApollo:
							return p.apollo > 0;
						case gh_filterOperators.hasVulcan:
							return p.vulcan > 0;
						case gh_filterOperators.hasMars:
							return p.mars > 0;
						case gh_filterOperators.hasMercury:
							return p.mercury > 0;
						case gh_filterOperators.hasDiana:
							return p.diana > 0;
						case gh_filterOperators.hasMinerva:
							return p.minerva > 0;
						default:
							return !0;
					}
				}),
			$i: (n) =>
				n.includes("icon_grouparena")
					? gh_questType.groupArena
					: n.includes("icon_arena")
						? gh_questType.arena
						: n.includes("icon_items")
							? gh_questType.item
							: n.includes("icon_combat")
								? gh_questType.combat
								: n.includes("icon_expedition")
									? gh_questType.expedition
									: n.includes("icon_dungeon")
										? gh_questType.dungeon
										: gh_questType.work,
			async Oi(n, l) {
				await st.zn(5), n.url && (await ghh.Yt(l + n.url));
			},
			async Gi(n, l) {
				await st.zn(10);
				const c = jQuery(n).find("#quest_footer_reroll [type=button]"),
					d = jQuery(n).find(".quest_slot_button:not(.quest_slot_button_accept)").length ?? 0,
					h = c.attr("onclick"),
					g = h == null ? void 0 : h.match(/document\.location\.href='([^']+)'/);
				d === 5 && (await st.Tt("403811514001b63a283f16", Date.now() + 12e4)), g && d < 5 && (await ghh.Yt(l + g[1]));
			},
			async Ti(n) {
				const l = Array.from(n.find(".quest_slot_button_finish")).map((c) => c.closest(".contentboard_slot"));
				for (const c of l) await this.Ji(c);
			},
			async Ji(n) {
				var _, v, j, T, k;
				const l = ((_ = n.querySelector(".quest_slot_icon")) == null ? void 0 : _.getAttribute("style")) || "",
					c = ts.$i(l),
					d = !!n.querySelector(".quest_slot_time");
				let h = !1;
				const g = n.querySelector(".quest_slot_reward_item > img");
				if (g)
					try {
						const I = JSON.parse(jQuery(g).attr("data-tooltip") || "[]");
						h = (((j = (v = I[0]) == null ? void 0 : v[I[0].length - 1]) == null ? void 0 : j[2]) ?? 0) === 250;
					} catch { }
				const f = (T = n.querySelector(".quest_slot_reward_xp span")) == null ? void 0 : T.getAttribute("data-tooltip"),
					p = (k = n.querySelector(".quest_slot_reward_honor span")) == null ? void 0 : k.getAttribute("data-tooltip"),
					m = this.Yi(f),
					y = this.Yi(p),
					b = this.zi(n.querySelector(".quest_slot_reward_gold span")),
					w = Array.from(n.querySelectorAll(".quest_slot_reward_god")).reduce((I, S) => {
						var D, A, M, P;
						const C = (((D = S.querySelector("[src]")) == null ? void 0 : D.getAttribute("src")) || "").match(/\/(\w+)\.png/),
							x = C ? gh_godImageMaps[C[1]] : "",
							q = parseInt(((P = (M = (A = S.querySelector("[data-tooltip]")) == null ? void 0 : A.getAttribute("data-tooltip")) == null ? void 0 : M.match(/\d+/)) == null ? void 0 : P[0]) || "0", 10);
						return x && (I[x] = q), I;
					}, {});
				await this.Ki({ godRewards: w, gold: b, honor: y, experience: m, rewardItem: g && !h ? 1 : 0, rewardIsFood: h ? 1 : 0, questType: c, hasTimer: d ? 1 : 0, date: Date.now() }),
					await this.Zi({ godRewards: w, gold: b, honor: y, experience: m, rewardItem: g && !h ? 1 : 0, rewardIsFood: h ? 1 : 0, questType: c, hasTimer: d ? 1 : 0 });
			},
			async Zi(n) {
				const l = { from: Date.now(), total: { gold: 0, honor: 0, experience: 0, rewardIsFood: 0, rewardItem: 0, hasTimer: 0, quests: 0, godRewards: {} }, types: {} },
					c = await st.jt("403811511d4d71de1a34", l);
				Date.now() - c.from > 6048e5 &&
					((c.from = Date.now()),
						(c.total.quests = 0),
						(c.total.gold = 0),
						(c.total.honor = 0),
						(c.total.experience = 0),
						(c.total.rewardIsFood = 0),
						(c.total.rewardItem = 0),
						(c.total.hasTimer = 0),
						(c.total.godRewards = {}),
						(c.types = l.types)),
					(c.total.quests += 1),
					(c.total.gold += n.gold),
					(c.total.honor += n.honor),
					(c.total.experience += n.experience),
					(c.total.rewardIsFood += n.rewardIsFood),
					(c.total.rewardItem += n.rewardItem),
					(c.total.hasTimer += n.hasTimer);
				const d = n.questType || "unknown";
				c.types[d] || (c.types[d] = { gold: 0, honor: 0, experience: 0, rewardIsFood: 0, rewardItem: 0, hasTimer: 0, quests: 0, godRewards: {} });
				const h = c.types[d];
				(h.quests += 1), (h.gold += n.gold), (h.honor += n.honor), (h.experience += n.experience), (h.rewardIsFood += n.rewardIsFood), (h.rewardItem += n.rewardItem), (h.hasTimer += n.hasTimer);
				for (const [g, f] of Object.entries(n.godRewards || {})) (h.godRewards[g] = (h.godRewards[g] || 0) + f), (c.total.godRewards[g] = (c.total.godRewards[g] || 0) + f);
				await st.Tt("403811511d4d71de1a34", c);
			},
			async Ki(n) {
				const l = st.jt("403811511d4d1881c1af27d1d2131fc1c", []),
					c = Date.now() - 36e5,
					d = l.filter((h) => h.date >= c);
				d.push(n), await st.Tt("403811511d4d1881c1af27d1d2131fc1c", d);
			},
			Yi(n) {
				if (!n) return 0;
				try {
					const l = JSON.parse(n),
						c = (l[0][0][0] ?? "").replace(/\./g, "").match(/\d+/);
					return c ? parseInt(c[0], 10) : 0;
				} catch {
					return 0;
				}
			},
			zi(n) {
				var c, d;
				if (!n) return 0;
				const l = ((d = (c = n.childNodes[0]) == null ? void 0 : c.nodeValue) == null ? void 0 : d.trim().replace(/\./g, "")) || "0";
				return parseInt(l, 10);
			},
		},
		el = {
			async tc() {
				const n = st.jt("44230373d61ad22f2e24", !1),
					l = st.jt("44230372321d5", "Going in UW!");
				n && (await mesgs.fo(l)), await ts.Ci();
				const c = st.jt("442303719c65b22223e1d3", "Normal"),
					d = gh_UrlInfo.link({ mod: "hermit", submod: "enterUnderworld" });
				await ghh.Tn(d, { [`difficulty_${c.toLowerCase()}`]: c }),
					await st.Tt("44230373b1011d3d1b2531", 0),
					await st.Tt("44230373b1011d22b23211b1c", 0),
					await st.Tt("592c172410f17117297353bc202", !1),
					await st.Tt("443a333141ec1a", null),
					await st.Wt(400),
					location.reload(),
					await st.Wt(2400);
			},
			async ec(n) {
				const l = gh_UrlInfo.link({ mod: "hermit", submod: "startTravel" });
				await ghh.Tn(l, { travelToCountry: n }), await el.ac(), await st.Wt(5e3), location.reload(), await st.Wt(2400);
			},
			async ac() {
				const n = Date.now() + (32.5 / ghh.He()) * 60 * 1e3;
				await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", n), await st.Wt(60), await st.Tt("5f281c12217e112a7b24232b71d35261181a151023d111a261005e42", n), await st.Wt(60), await st.Tt("5f281c12217e112f01c142d1433b31b151c", n);
			},
			async nc() {
				jQuery("[name=change]").on("click", async () => {
					await el.ac();
				});
			},
		};
	class ProcessSmeltItems {
		constructor() {
			(this.Qa = null), (this.ya = []), (this.mo = []), (this.po = !1);
		}
		async rc() {
			await st.Tt("5f281c12217e112a7b2423311b172828171d21290f1bdb4", Date.now() + st.Le(27e5, 9e5));
			const l = st.jt("42201a1b11d4761a2225b291d373d15a1e30", []),
				c = st.jt("50381092636181d3d1b2b2db", []).filter((g) => g.active);
			if (l.length > 20 || !c.length) return;
			const d = [...new Set(c.flatMap((g) => g.types))],
				h = ms.wa(d, 5);
			await ms.qa(), await h;
		}
		async wa(l, c) {
			ms.Qa = await ghh._a();
			for (const d of l) {
				let h = 1;
				for (await ms.va(h, d), h++; !ms.po && h <= ms.Qa;) {
					const g = [];
					for (let f = 0; f < c && h <= ms.Qa; f++) g.push(ms.va(h, d)), h++;
					await Promise.all(g), await st.Wt(100);
				}
			}
			ms.po = !0;
		}
		async va(l, c, d = 5) {
			try {
				const h = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: l, submod: "loadMore", f: c, fq: -1 }));
				if (!h || !h.newPackages) throw new Error(`Invalid response for page ${l}`);
				const g = h.newPackages.map((m) => jQuery(m).get(0));
				ms.ya.push(...g);
				const f = jQuery(h.pagination).find(".paging_numbers"),
					p = f.length ? Array.from(f[0].children).pop() : null;
				ms.Qa = p ? parseInt(p.textContent || "1", 10) : 1;
			} catch {
				if (d > 0) return await st.Wt(400), ms.va(l, c, d - 1);
			}
		}
		async qa() {
			for (; !ms.po || ms.ya.length > 0;) {
				const l = ms.ya.shift();
				if (!l) {
					await st.Wt(100);
					continue;
				}
				const c = l.querySelector("[data-content-type]"),
					d = st.Ke(c);
				if (!ms.mo.includes(+d.containerNumber) && (lt.Qe(c, !0), ms.mo.push(+d.containerNumber), st.jt("42201a1b11d4761a2225b291d373d15a1e30", []).length > 20)) return void (ms.po = !0);
			}
		}
	}
	const ms = new ProcessSmeltItems(),
		ap = {
			sc: 0,
			async Sa() {
				ro.ot = !0;
				const n = await ghh.ti(),
					l = await ap.Mt(n),
					c = await ap.Mt(n, "3");
				ap.oc(l.isWithinOutbidDuration || c.isWithinOutbidDuration, [...l.matchedItems, ...c.matchedItems]);
			},
			Mt: async function (n, l = "2") {
				const c = st.jt("50387121c1d3c61a1d221ad31736", []).filter((_) => _.active),
					d = st.jt("50387121c1d5a81a1a28ad16342a11c", !0),
					h = d && l === "2" && !(await auction.cc());
				if ((h && c.unshift(ap.lc()), !c.length)) return { isWithinOutbidDuration: !1, matchedItems: [] };
				const g = gh_UrlInfo.link({ mod: "auction", ttype: l }),
					f = st.Vt(await ghh.Yt(g)),
					p = auction.uc(n, f),
					m = f.find('[name="itemLevel"]').val(),
					y = [];
				if (p) {
					const _ = parseInt(st.jt("43221071b30115ad332d1c266203b261c", 0)),
						v = auction.dc(n, f);
					_ <= Date.now() + 3e5 && v && (await st.Tt("43221071b30115ad332d1c266203b261c", Date.now() + 36e4));
				}
				const b = await ghh.Tn(g, { doll: 1, qry: "", itemLevel: m, itemType: h && c.length === 1 ? 7 : 0, itemQuality: -1 }),
					w = Array.from(st.Vt(b).find("#auction_table [id^=auctionForm]")).sort((_, v) => parseInt(jQuery(_).find("[name=bid_amount]").val()) - parseInt(jQuery(v).find("[name=bid_amount]").val()));
				ap.sc = st.oe();
				for (const _ of w) {
					const v = jQuery(_).find("[data-tooltip]"),
						j = v.data("tooltip")[0],
						T = st.Ke(v);
					delete T.tooltip;
					const k = parseInt(st.un(v));
					jQuery.extend(!0, T, { itemType: k, auctionItem: _.id });
					const I = parseInt(jQuery(_).find("[name=bid_amount]").val());
					for (const S of c) {
						const C = S.goldPerLifePointRatio ? S.goldPerLifePointRatio : 0,
							x = ap.Ue(S.conditions, T.itemName, T.level),
							q = S.colors.includes(+T.quality),
							D = S.types.includes(k),
							A =
								(k === 15 &&
									ap.hc(S.mercenary, {
										strength: parseInt(j[2][0].match(/\d+/)[0]),
										dexterity: parseInt(j[3][0].match(/\d+/)[0]),
										agility: parseInt(j[4][0].match(/\d+/)[0]),
										constitution: parseInt(j[5][0].match(/\d+/)[0]),
										charisma: parseInt(j[6][0].match(/\d+/)[0]),
										intelligence: parseInt(j[7][0].match(/\d+/)[0]),
									})) ||
								k !== 15,
							M = !T.isFood || T.isHealing / I >= C,
							P = k !== 1 || parseInt((+T.quality > 0 ? j[2][0][0] : j[1][0]).match(/(\d+) - (\d+)/)[1]) >= S.damage;
						if (!(x && q && D && A && M && P)) continue;
						const E = jQuery(_).find(".auction_bid_div")[0].firstElementChild.firstElementChild,
							L = !!E && E.tagName.toLowerCase() === "a" && parseInt(E.href.match(/&p=(\d+)/)[1]) === +_getJunk(),
							W = { level: T.level, itemType: k, tooltip: [j], class: v.attr("class"), itemName: j[0][0], itemId: _.id, quality: T.quality || 0, ttype: l, isMercenary: k === 15, itemGold: I, isOutbid: L },
							O = y.some((G) => G.itemId === W.itemId && W.isOutbid == G.isOutbid);
						if (d && T.isFood) {
							await ap.fc(W, S.maxOutbidItemPrice, p, E, S == null ? void 0 : S.pickGold, S == null ? void 0 : S.bidFriends);
							break;
						}
						if ((O || y.push(W), S.autoBidMatchedItem)) {
							await ap.fc(W, S.maxOutbidItemPrice, p, E, S == null ? void 0 : S.pickGold, S == null ? void 0 : S.bidFriends);
							break;
						}
					}
				}
				return { isWithinOutbidDuration: p, matchedItems: y };
			},
			async fc(n, l, c, d, h = !1, g = !1) {
				var m, y;
				const f = ((m = d == null ? void 0 : d.tagName) == null ? void 0 : m.toLowerCase()) === "a",
					p = g ? f && parseInt((y = d.href.match(/&p=(\d+)/)) == null ? void 0 : y[1]) !== +_getJunk() : !f;
				if (c && n.itemGold <= l && (p || !d)) {
					if (ap.sc < n.itemGold && (!h || (await ks.Zs(+n.itemGold), (ap.sc = st.oe()), ap.sc < n.itemGold))) return;
					await ghh.Gt();
					const b = gh_UrlInfo.link({ mod: "auction", submod: "placeBid", ttype: n.ttype, rubyAmount: es.Cg().Vi }),
						w = n.itemId.replace("auctionForm", ""),
						_ = await ap.gc(b, n.itemGold, w, n.itemName, n.itemType, n.quality, n.level);
					_ && ((ap.sc -= n.itemGold), st.ke(ap.sc)), (n.isOutbid = _);
				}
			},
			Ue: function (n, l, c) {
				return n.every((d) => ghh.Fe(d, [gh_operators.greaterThan, gh_operators.lessThan].includes(d.operator) ? c : l));
			},
			hc(n, l) {
				for (const c in n) if (!Object.prototype.hasOwnProperty.call(n, c) || !Object.prototype.hasOwnProperty.call(l, c) || l[c] < n[c]) return !1;
				return !0;
			},
			lc: () => ({
				goldPerLifePointRatio: parseInt(st.jt("56228225b113809b1727111617fc61d", 4)),
				maxOutbidItemPrice: parseInt(st.jt("50387121c1d5a4e1683dcd1b2127ca1f1417ac1", 5e4)),
				pickGold: st.jt("50387121c1d5a196d2cea01f15f1b41", !1),
				bidFriends: st.jt("50387121c1d5ab6a13a11a1c211d", !1),
				types: [7],
				conditions: [{ value: "", operator: gh_operators.contains }],
				mercenary: { strength: 0, dexterity: 0, agility: 0, constitution: 0, charisma: 0, intelligence: 0 },
				damage: 0,
				colors: [-1, 0, 1, 2],
				active: !0,
				id: Date.now(),
			}),
			async gc(n, l, c, d, h, g, f) {
				const p = await ghh.Tn(n, { qry: d.replace(/ .*/, "").trim(), itemType: h, itemQuality: g, auctionid: c, itemLevel: f, buyouthd: 0, bid_amount: l, bid: "Bid" });
				return !!st.Vt(p).find("div.messages > div.message.success").length;
			},
			oc(n, l) {
				const c = n ? 1e3 * st.Le(7, 4) : 1e3 * st.Le(70, 45);
				if ((st.Tt("5f281c12217e112a7b2423391a113171713a2b1010a", Date.now() + c), st.Tt("50387121c1d5a4e1a24201db3b31b151c", l), n)) {
					const d = jQuery("#auctionItemsSection .gh-items-section-header"),
						h = jQuery("#auctionItemsSection .gh-items-section-content")[0],
						g = d.find("label")[0];
					h && g && auction.mc(h, g);
				}
			},
		},
		ftf = {
			tts: 2e3,
			forgeSlots: [],
			yc: async function (n, l, c, d, h, g, f, p, m, y, b) {
				var T;
				const w = await ftf.xe(),
					_ = st.jt("5722161103df1b1d3c1a263cd1c", []);
				let j = ((T = st.jt("50381093311113c3d1b2b2db3c726d1d1c1", []).find((k) => k.id === m)) == null ? void 0 : T.success) || 0;
				for (const k of w)
					if (!(j >= b))
						try {
							if (d.includes(k)) {
								if (!(await st.Te(1, 1))) continue;
								const I = await ftf.bc(h, p);
								if (!I) {
									ghh.he("Insufficient tools forging ", { itemName: y }), await ftf.wc(m);
									continue;
								}
								for (const q in I) {
									const D = I[q];
									await st.Wt(75), await ftf._c(D.element, k, Math.ceil(parseInt(q.replace("19-", "")) / 3));
								}
								const S = await ftf.vc(l, n, c, k),
									C = ftf.kc(S.needed, g),
									x = await be.Xt();
								if (!ftf.Ic(x, C, !f) || parseInt(S.rent[2]) > st.oe()) {
									ghh.he("Insufficient materials or gold for forging ", { itemName: y }), await ftf.wc(m);
									continue;
								}
								await ftf.Sc(k, n, l, c), await ftf.Dc(k, x, C), await ftf.pe(k), _.push({ slot: k, name: y, end_duration: Date.now() + 1e3 * (S.duration + 2), ruleId: m }), j++;
							}
						} catch (I) {
							await errorLog.log(I);
						}
				await st.Tt("5722161103df1b1d3c1a263cd1c", _);
			},
			async Dc(n, l, c, d = -1) {
				for (const h in c) {
					const { amount: g, quality: f } = c[h];
					let p = g;
					for (let m = parseInt(f, 10); m >= d && p > 0; m--) {
						const y = l[h][m] || 0;
						if (y > 0) {
							const b = Math.min(p, y),
								w = await st.Te(1, 1);
							if (!w) continue;
							const _ = await et.Co(h, m, b, w.spot.x + 1, w.spot.y + 1, w.bagId);
							await st.Wt(st.Le(55, 25)), await ftf.Pe(n, _.itemId, b), (p -= b), await st.Wt(st.Le(55, 25));
						}
					}
				}
			},
			Pe: async (n, l, c) => {
				ghh.logger("Moving material to warehouse. Slot:", n, "Item ID:", l, "Amount:", c);
				const d = gh_UrlInfo.ajaxLink({}),
					h = { mod: "forge", submod: "toWarehouse", mode: "forging", slot: n, iid: l, amount: c, a: new Date().getTime() },
					g = await jQuery.post(d, h);
				return ghh.logger("Move material to warehouse response:", g), g;
			},
			async qc(n, l, c, d) {
				const h = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: 801 + n, fromX: 1, fromY: 1, to: d, toX: l, toY: c, amount: 1, doll: 1 }),
					g = { a: new Date().getTime(), sh: gh_UrlInfo.queries.sh };
				return JSON.parse(await jQuery.post(h, g));
			},
			pe: async (n) => {
				ghh.logger("Starting forge for slot:", n);
				const l = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "start", mode: "forge", slot: n, a: new Date().getTime() + "" }),
					c = await jQuery.post(l);
				return ghh.logger("Forge start response:", c), c;
			},
			Sc: async function (n, l, c, d) {
				const h = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "rent" }),
					g = { mod: "forge", submod: "rent", mode: "forging", slot: n, rent: "2", item: l, prefix: c, suffix: d, a: Date.now(), sh: gh_UrlInfo.queries.sh };
				return await jQuery.post(h, g);
			},
			vc: async function (n, l, c, d) {
				const h = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "updateSlotsData" }),
					g = { mod: "forge", submod: "getPreview", mode: "forging", slot: d, prefix: n, item: l, suffix: c, a: Date.now(), sh: gh_UrlInfo.queries.sh },
					f = await jQuery.post(h, g);
				return JSON.parse(f).formula;
			},
			kc: (n, l) =>
				Object.entries(n).reduce((c, [d, h]) => {
					const g = parseInt(d.slice(2), 10);
					return (c[g] = { amount: h.amount, name: h.name, quality: l[g].quality }), c;
				}, {}),
			xe: async (n = ["closed"]) => {
				ghh.logger("Checking for free slots in forge.");
				const l = await ghh.Se("forge"),
					c = JSON.parse(l.match(/slotsData\s*=\s*(.*);/)[1]),
					d = [];
				for (let h = 0; h < 6; h++) {
					const g = c[h]["forge_slots.state"];
					n.includes(g) && d.push(h);
				}
				return d;
			},
			Ac: async (n = ["finished-succeeded"]) => {
				ghh.logger("Checking for free slots in forge.");
				const l = await ghh.Se("forge"),
					c = JSON.parse(l.match(/slotsData\s*=\s*(.*);/)[1]),
					d = [];
				for (let h = 0; h < 6; h++) {
					const g = c[h]["forge_slots.state"];
					n.includes(g) && d.push({ slot: h, item: c[h].item, success: c[h]["forge_slots.state"] === "finished-succeeded" });
				}
				return d;
			},
			Cc: async function (n) {
				const l = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "lootbox" }),
					c = { mod: "forge", submod: "lootbox", mode: "forging", slot: n, a: Date.now(), sh: gh_UrlInfo.queries.sh };
				await jQuery.post(l, c), await ghh.Xa();
			},
			async Pc() {
				const n = await ftf.Ac(["finished-succeeded", "finished-failed"]),
					l = Date.now();
				let c = [...st.jt("5722161103df1b1d3c1a263cd1c", [])];
				const d = [...st.jt("50381093311113c3d1b2b2db", [])];
				for (const h of n) {
					const g = [...st.jt("50381093311113c3d1b2b2db3c726d1d1c1", [])],
						f = c.find((b) => b.slot === h.slot);
					if (!f) continue;
					const p = d.find((b) => b.id === f.ruleId);
					if (!p) continue;
					if (!h.success) {
						await ftf.Cc(h.slot), (c = c.filter((b) => b.slot !== h.slot)), await ghh.he(`${ftf.xc(new Date())} Failed forging `, { itemName: p.name });
						continue;
					}
					const m = await st.Te(h.item.data.measurementX, h.item.data.measurementY);
					if (!m) continue;
					if ((await ghh.Tc(`${ftf.xc(new Date())} Forged Successfully ${ftf.jc(h.item.data.quality)}`, { itemName: p.name }), p.sendTo > 0)) {
						const b = await ftf.qc(h.slot, m.spot.x + 1, m.spot.y + 1, m.bagId);
						if (p.sendTo > 1) {
							await st.Yn(b.to.data.itemId, 1, 1);
							const w = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "p", p: 1 }),
								_ = await ghh.Yt(w);
							await st.Pa(_);
						}
					}
					const y = p.qualityAccepted.length ? p.qualityAccepted : [-1, 0, 1, 2, 3, 4];
					if (p.active && y.includes(h.item.data.quality)) {
						const b = g.find((w) => w.id === p.id);
						b ? (++b.success, b.success === p.stopSuccess ? await ftf.wc(p.id) : await st.Tt("50381093311113c3d1b2b2db3c726d1d1c1", g)) : (g.push({ id: p.id, success: 1 }), await st.Tt("50381093311113c3d1b2b2db3c726d1d1c1", g));
					}
					(c = c.filter((b) => b.slot !== h.slot)), await st.Wt(100);
				}
				await st.Tt("5722161103df1b1d3c1a263cd1c", c), await st.Wt(60), await st.Tt("5f281c12211c1a3261d28283a1fa", l + 5e3), await st.Wt(100);
			},
			jc(n) {
				switch (n) {
					case -1:
						return "white";
					case 0:
						return "green";
					case 1:
						return "blue";
					case 2:
						return "purple";
					case 3:
						return "orange";
					case 4:
						return "red";
					default:
						return "white";
				}
			},
			xc: (n) => `${String(n.getDate()).padStart(2, "0")} ${String(n.getHours()).padStart(2, "0")}:${String(n.getMinutes()).padStart(2, "0")}:${String(n.getSeconds()).padStart(2, "0")}`,
			Mt: async function () {
				const n = ghh.He(),
					l = new Date().getTime() + (60 * st.Le(30, 20) * 1e3) / n;
				await st.Tt("5f281c12211c1a3261d28283a1fa", l);
				const c = st.jt("50381093311113c3d1b2b2db", []).filter((d) => d.active);
				for (const d of c) {
					const h = Object.values(d.tools)
						.filter((g) => g !== 0)
						.map((g) => `19-${g}`);
					await ftf.yc(d.item, d.prefix, d.suffix, d.slotsToUse, h, d.resources, d.stopResources, d.stopTools, d.id, d.name, d.stopSuccess);
				}
			},
			async wc(n) {
				const l = st.jt("50381093311113c3d1b2b2db", []),
					c = l.find((h) => h.id === n);
				(c.active = !1), await st.Tt("50381093311113c3d1b2b2db", l);
				let d = [...st.jt("50381093311113c3d1b2b2db3c726d1d1c1", [])];
				(d = d.filter((h) => h.id !== c.id)), await st.Tt("50381093311113c3d1b2b2db3c726d1d1c1", d);
			},
			async bc(n, l = !1) {
				const c = [512, 513, 514, 515],
					d = {};
				for (const h of c) {
					const g = await st.Je(h),
						f = jQuery("<div>").append(st.Vt(g))[0];
					for (const p of n) {
						if (d[p]) continue;
						const m = jQuery(f).find(`.item-i-${p}`).first();
						if (m.length) {
							const y = parseInt(m.attr("data-amount"), 10) || 1;
							d[p] = { element: m[0], totalAmount: y };
						}
					}
				}
				for (const h of n) if (!d[h] && l) return null;
				return d;
			},
			async _c(n, l, c) {
				const d = n.getAttribute("data-container-number"),
					h = n.getAttribute("data-position-x"),
					g = n.getAttribute("data-position-y"),
					f = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: d, fromX: h, fromY: g, to: 769, toX: l + 1, toY: c, amount: 1, doll: 1 });
				try {
					await jQuery.post(f, { a: new Date().getTime() });
				} catch { }
			},
			Ic(n, l, c = !1, d = -1) {
				for (const h in l) {
					const { amount: g, quality: f } = l[h],
						p = parseInt(f, 10);
					if (!n[h]) return !1;
					if ((n[h][p] || 0) < g) {
						if (!c) return !1;
						{
							let m = 0;
							for (let y = p; y >= d && ((m += n[h][y] || 0), !(m >= g)); y--);
							if (m < g) return !1;
						}
					}
				}
				return !0;
			},
		},
		pcts = {
			ya: [],
			pa: [],
			async Mc() {
				const n = gh_UrlInfo.link({ mod: "premium", submod: "centurio" }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = jQuery(c).find("#premium_duration .ticker[data-ticker-time-left]");
				if (d.length > 0) {
					const g = parseInt(d.attr("data-ticker-time-left"), 10);
					if (!isNaN(g)) return pcts.Qc(g);
				}
				const h = jQuery(c).find("#premium_duration").text().trim().match(/(\d+)/);
				if (h) {
					const g = 3600 * (24 * parseInt(h[1], 10) - 12) * 1e3;
					return pcts.Qc(g);
				}
				return pcts.Rc();
			},
			async Qc(n) {
				await st.Tt("5225151e2d61a1d1a1c2e272c61f20", Date.now() + n + 26e3);
			},
			async wa(n, l) {
				let c = 1;
				const d = st.Vt(await st.qe(null, 1, { f: n, fq: "-1" })),
					h = jQuery(d).find(".paging_numbers"),
					g = h.length ? Array.from(h[0].children).pop() : null,
					f = g ? parseInt(g.textContent || "1", 10) : 1;
				for (; c <= 200 && c <= f && pcts.ya.length < 1;) {
					await ghh.Gt();
					const p = [];
					for (let y = 0; y < l && c <= 200; y++) p.push(pcts.va(n, c)), c++;
					const m = await Promise.all(p);
					for (const y of m) y && y.stopPageNumber;
					await st.Wt(160);
				}
			},
			async va(n, l, c = 10) {
				try {
					const d = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: l, submod: "loadMore", f: n, fq: -1 }));
					if (!d || !d.newPackages) throw new Error(`Invalid response for page ${l}`);
					if (pcts.pa.includes(l)) return;
					pcts.pa.push(l);
					const h = d.newPackages
						.map((g) => jQuery(g).get(0))
						.map((g) => {
							const f = g.querySelector("[data-content-type]"),
								p = st.Ke(f);
							return [30, 35].includes(p.subType) ? p : null;
						})
						.filter((g) => g);
					if ((h.length > 0 && pcts.ya.push(...h), pcts.ya.length)) return { stopPageNumber: l };
				} catch {
					return c > 0 ? (await st.Wt(200), pcts.va(n, l, c - 1)) : {};
				}
			},
			async Rc() {
				const n = st.jt("5228a1201ca1b28c1a2e3e191b1711178a", "1");
				if (n === "0") return void (await pcts.Qc(36e5));
				await st.Ot(), await ghh._a(), await pcts.wa(7, 5);
				let l = pcts.ya.length > 0;
				for (const c of pcts.ya)
					if ([30, 35].includes(c.subType)) {
						const d = await st.Te(c.measurementX, c.measurementY);
						if (!d) return void (await pcts.Qc(36e5));
						await st.Ce(c.containerNumber, d.bagId, d.spot.x + 1, d.spot.y + 1, c.amount);
						const h = await st.ja({ from: d.bagId, fromX: d.spot.x + 1, fromY: d.spot.y + 1, to: 8, toX: 1, toY: 1, amount: 1 });
						return h.header ? headerObject.update(h) : (location.reload(), await st.Wt(200)), (l = !0), void (await pcts.Qc(36e5));
					}
				l || n !== "2" ? await pcts.Qc(36e5) : await pcts.Uc();
			},
			async Uc() {
				if (es.Cg().Vi > 14) {
					const n = gh_UrlInfo.link({ mod: "premium", submod: "activateCenturio", rubyAmount: es.Cg().Vi });
					return await ghh.Yt(n), await pcts.Qc(12096e5), location.reload(), void (await st.Wt(200));
				}
				await pcts.Qc(36e5);
			},
		},
		sp = {
			async Bc() {
				ro.Pt = !0;
				const n = st.jt("4225b1626b26a7", !1),
					l = st.jt("4225b16271bf111a", []).filter((y) => y.active);
				let c = !1,
					d = !1;
				const h = [],
					g = async (y, b) => {
						try {
							const w = st.Vt(await ghh.Yt(b)),
								_ = st.Fc(w.find("#shop"));
							c = w.find("[name=bestechen]").parent().find("img")[0].getAttribute("src").indexOf("premium/token_small/8.png") > -1;
							for (const j of _) {
								const T = jQuery(j),
									k = await sp.$c(T, l, b, y);
								k && (h.push(k), (d = !0));
							}
						} catch (w) {
							await errorLog.log(w);
						}
					},
					f = [
						{ sub: 1, subsub: 0 },
						{ sub: 1, subsub: 1 },
						{ sub: 2, subsub: 0 },
						{ sub: 2, subsub: 1 },
						{ sub: 3, subsub: 0 },
						{ sub: 3, subsub: 1 },
						{ sub: 4, subsub: 0 },
						{ sub: 4, subsub: 1 },
						{ sub: 5, subsub: 0 },
					];
				if (l.length > 0)
					for (const y of f) {
						const b = gh_UrlInfo.link(jQuery.extend({ mod: "inventory" }, y));
						await st.Wt(st.Le(100, 80)), await g(y, b);
					}
				await st.Tt("5f281c12217e112a7b24232b71d35261181a151023d111a261005e42", Date.now() + 3e5), await st.Tt("4225b163d741c381af2b21c163b31b151c", h);
				const p = jQuery("#shopItemsSection .gh-items-section-content")[0];
				p && (await st.Wt(100), view.Ec(p));
				const m = st.jt("4225b1626b26a72a28261d", 0);
				(n && !d && m < st.jt("5c2c1c34100631a", 10)) || (d && m === 0) ? (await sp.nn(c), await st.Tt("4225b1626b26a72a28261d", m + 1), await sp.Bc()) : n && (await st.Tt("4225b1626b26a7", !1)), (ro.Pt = !1);
			},
			async $c(n, l, c, d) {
				var m;
				const h = st.Ke(n),
					g = h.tooltip[0],
					f = parseInt(st.un(n)),
					p = f === gh_itemTypeValues.mercenary;
				jQuery.extend(!0, h, { itemType: f });
				for (const y of l) {
					const b = ap.Ue(y.conditions, h.itemName, h.level),
						w = y.colors.includes(+h.quality),
						_ = y.types.includes(f),
						v =
							(p &&
								ap.hc(y.mercenary, {
									strength: parseInt(g[2][0].match(/\d+/)[0]),
									dexterity: parseInt(g[3][0].match(/\d+/)[0]),
									agility: parseInt(g[4][0].match(/\d+/)[0]),
									constitution: parseInt(g[5][0].match(/\d+/)[0]),
									charisma: parseInt(g[6][0].match(/\d+/)[0]),
									intelligence: parseInt(g[7][0].match(/\d+/)[0]),
								})) ||
							!p,
						j = f !== 1 || parseInt(((m = (+h.quality > 0 ? g[2][0][0] : g[1][0]).match(/(\d+) - (\d+)/)) == null ? void 0 : m[1]) ?? "0") >= y.damage;
					if (b && w && _ && v && j) {
						if (y.autoBuy && !h.hasRubies && !ghh.Lc(h)) {
							if ((st.oe() < +h.priceGold && y.pickGold && (await ks.Zs(+h.priceGold), await st.Wt(60)), st.oe() < +h.priceGold))
								return { url: c, tooltip: h.tooltip, class: n.attr("class"), quality: h.quality || 0, isMercenary: p, params: d, bought: !1 };
							const T = await st.Te(h.measurementX, h.measurementY),
								k = await st.Nc(h, h.containerNumber, T);
							if (y.sendToPackages && k) {
								await st.Yn(k.itemId, 1, 1);
								const I = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 }),
									S = await ghh.Yt(I);
								await st.Pa(S);
							}
							return await ghh.Tc(`${ftf.xc(new Date())} Bought from Shop `, { itemName: h.itemName }), { url: c, tooltip: h.tooltip, class: n.attr("class"), quality: h.quality || 0, isMercenary: p, params: d, bought: !0 };
						}
						return { url: c, tooltip: h.tooltip, class: n.attr("class"), quality: h.quality || 0, isMercenary: p, params: d, bought: !1 };
					}
				}
			},
			nn: async function (n) {
				const l = await st.jt("4328a323db1b19201e3321171", 1);
				if (l == 2 || (l == 1 && n)) return await ghh.rn(), void (await st.Wt(200));
			},
		},
		ot = {
			ssh: 308,
			Oc: () => document.getElementById("blackoutDialog"),
			async Gc(n) {
				if (!ot.Oc()) return await ot.Hc(!1);
				switch (n) {
					case 0:
						st.Bo ? await ot.Wc() : st.jt("55240233bd0", !1) ? await ot.zc() : await ot.Xc();
						break;
					case 1:
						await ot.Jc();
				}
				return await ot.Hc(!1);
			},
			async Wc() {
				if (!st.jt("442303719c65b2b29291a31791171b", !0)) return ot.Vc(0, gh_UrlInfo.queries.reportId, 2);
				const n = st.jt("442303719c65b222827c202317171", 0);
				ot.Oc() && (await ot.Vc(0, gh_UrlInfo.queries.reportId, n + 3));
			},
			async zc() {
				if (!st.jt("543b1812bd15b3bb27171b", !0)) return ot.Vc(0, gh_UrlInfo.queries.reportId, 2);
				const n = st.jt("543b18122c1b1d201e3321171", 0);
				ot.Oc() && (await ot.Vc(0, gh_UrlInfo.queries.reportId, n + 3));
			},
			async Xc() {
				if (!st.jt("5435143117171d612b29291a31791171b", !0)) return ot.Vc(0, gh_UrlInfo.queries.reportId, 2);
				const n = st.jt("5435143117171d61222827c202317171", 0);
				ot.Oc() && (await ot.Vc(0, gh_UrlInfo.queries.reportId, n + 3));
			},
			async Jc() {
				if (!st.jt("5538a1101d317ec2b2d3401d31", !0)) return ot.Vc(1, gh_UrlInfo.queries.reportId, 2);
				const n = st.jt("5538a1101d38601a838c61d2b", 0);
				ot.Oc() && (await ot.Vc(1, gh_UrlInfo.queries.reportId, n + 3));
			},
			async Hc(n) {
				await st.Tt("55240233bd0", n);
			},
			async Vc(n, l, c) {
				const d = gh_UrlInfo.ajaxLink({}),
					h = { mod: "reports", submod: "lootAction", action: c, reportId: l, type: n, a: Date.now(), sh: gh_UrlInfo.queries.sh };
				try {
					await jQuery.post(d, h), location.reload(), await st.Wt(500);
				} catch { }
			},
		},
		evt = {
			async Mt() {
				(ro.xt = !0), await evt.Yc(), (ro.xt = !1);
			},
			async Kc(n = 1) {
				try {
					const l = gh_UrlInfo.link({}, "ajax/craps.php"),
						c = { type: n, a: new Date().getTime(), sh: gh_UrlInfo.queries.sh };
					return (await ghh.Tn(l, c)).cooldownSet ?? 600;
				} catch {
					return 600;
				}
			},
			async Yc() {
				const n = gh_UrlInfo.link({ mod: "craps" }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = jQuery(c).find("#tossA"),
					h = jQuery(c).find("#tossB"),
					g = jQuery(c).find("#tossC"),
					f = jQuery(c).find("#tossD");
				let p = null;
				d.data("free") && !d.hasClass("disabled") ? (p = 1) : h.data("free") && !h.hasClass("disabled") ? (p = 2) : g.data("free") && !g.hasClass("disabled") ? (p = 3) : f.data("free") && !f.hasClass("disabled") && (p = 4);
				const m = await st.jt("443e1340c1a300cb", !1);
				if (!p && m) {
					const y = await st.jt("543b1813d618cc1a222c3c61120", 1),
						b = jQuery(c).find(evt.Zc(parseInt(y)));
					if (parseInt(b.data("cost")) <= es.Cg().Vi) {
						const w = parseInt(await evt.Kc(y));
						return await st.Tt("5f281c12341821d5ec2b2d3c611203a11217", Date.now() + 1e3 * (w + 10)), !0;
					}
				}
				if (p) {
					const y = await evt.Kc(p);
					return await st.Tt("5f281c12341821d5ec2b2d3c611203a11217", Date.now() + 1e3 * (y + 10)), !0;
				}
				{
					const y = Math.max(parseInt(jQuery(c).find("#crapsCooldownTimer").find("span").attr("data-ticker-time-left") || "0", 10), 0),
						b = y === 0 ? 36e5 : y;
					return await st.Tt("5f281c12341821d5ec2b2d3c611203a11217", Date.now() + b), !1;
				}
			},
			Zc(n = 1) {
				switch (n) {
					case 1:
					default:
						return "#tossA";
					case 2:
						return "#tossB";
					case 3:
						return "#tossC";
					case 4:
						return "#tossD";
				}
			},
			async tl() {
				(ro.wt = !0), await st.Wt(300);
				const n = jQuery("#submenu2 a").filter(".glow")[0];
				if (!n) return void (ro.wt = !1);
				const l = n.href,
					c = l.match(/[?&]loc=([^&#]*)/)[1],
					d = l.includes("serverQuest");
				if (d && l.includes("serverQuestHighscore")) return await st.Tt("543b1813ec1d71b1d693c11217a1bc", Date.now() + 36e5), void (ro.wt = !1);
				const h = st.Vt(await ghh.Yt(l)),
					g = d ? "&serverQuest=1" : "",
					f = parseInt(h.find(".section-header p")[1].innerText.match(/\d+/)[0]);
				let p = 0;
				if (d) {
					const m = h.find(".expedition_button");
					for (let y = m.length - 1; y >= 0; y--)
						if (!m[y].disabled) {
							p = y + 1;
							break;
						}
					p = Math.min(p, d ? 4 : st.jt("543b1813d618cc1a222c3501c361a1d1d3b20", 1));
				} else p = st.jt("543b1813d618cc1a222c3501c361a1d1d3b20", 1);
				await st.Tt("543b1813ec1d71b1d", f), await evt.el(h, g, c, f, p);
			},
			async el(n, l, c, d, h) {
				if (n.find("#content .ticker")[0]) {
					const f = parseInt(n.find('[data-ticker-type="countdown"]').attr("data-ticker-time-left"));
					return await st.Tt("543b1813ec1d71b1d693c11217a1bc", Date.now() + f + 100), void (ro.wt = !1);
				}
				const g = st.jt("4239b163a02f118bb35", !0);
				if (l && g && !(await evt.al(c, !1))) return await st.Tt("543b1813ec1d71b1d693c11217a1bc", Date.now() + 12e4), void (ro.wt = !1);
				if (d > 0) {
					if (st.jt("543b18140211d02d282414a11312c171737010", !1) && !l) {
						const f = n.find(".expedition_button:not(.disabled)");
						for (let p = 0; p < f.length && p < 3; p++)
							if (jQuery(f[p]).closest(".expedition_box").find(".expedition_bonus.active").length < 4) return await st.zn(), await ot.Hc(!0), await st.Pr(c, p + 1, 0, l), void (ro.wt = !1);
					}
					h !== 4 || d !== 1 || l || (h = 3), await st.zn(), await ot.Hc(!0), await st.Pr(c, h, 0, l);
				} else if (d === 0 && st.jt("4328a322b151171b", !1) && es.Cg().Vi > 14) {
					const f = { mod: "location", loc: c, reset: !0 };
					if (l) {
						const m = await evt.al(c);
						if (((f.submod = "serverQuest"), !m)) return await st.Tt("543b1813ec1d71b1d693c11217a1bc", Date.now() + 12e4), void (ro.wt = !1);
					}
					const p = gh_UrlInfo.link(f);
					await ghh.Yt(p);
				} else d === 0 && st.jt("4328a322b151171b", !1) && es.Cg().Vi > (l ? 0 : h > 3 ? 1 : 0) ? (await st.zn(), await ot.Hc(!0), await st.Pr(c, h, 1, l)) : await st.Tt("543b1813ec1d71b1d693c11217a1bc", Date.now() + 36e5);
				ro.wt = !1;
			},
			async al(n, l = !0) {
				const c = gh_UrlInfo.link({ mod: "location", submod: "serverQuestHighscore", loc: n }),
					d = await ghh.Tn(c),
					h = st.Vt(d).find("#content"),
					g = h.find("tr.highlight"),
					f = h.find("tr:nth-child(2)")[0],
					p = h.find("[name=highscoreNr]").val(),
					m = parseInt(h.find(".paging_numbers_current").text()) ?? 1,
					y = p.split("").pop(),
					b = m !== 1 ? 1e3 : parseInt(jQuery(f).children().last().text());
				let w = 0;
				g.length > 0 && (w = parseInt(g.children().last().text()));
				const _ = st.jt("462c12363ac32832283f", []);
				return l ? w < b && _.includes(y) : w < b;
			},
		},
		gw = {
			async Mt() {
				const n = st.jt("5638da1140201ded2c4111c6", []),
					l = st.jt("5638da1140a13701c224111c6", []),
					c = gh_UrlInfo.link({ mod: "guild_warcamp" }),
					d = st.Vt(await ghh.Yt(c)),
					h = d.find(`[data-ticker-loc="${c}"]`);
				if (h.length > 0) {
					const m = Math.max(parseInt(h.attr("data-ticker-time-left"), 10), 0);
					return void (await st.Tt("5f281c12321ba18d38f35", Date.now() + m));
				}
				const g = Array.from(d.find('a[href^="index.php?mod=guild&i="]'))
					.map((m) => {
						const y = m.getAttribute("href"),
							b = m.textContent.trim(),
							w = y.match(/i=(\d+)/),
							_ = w ? w[1] : null,
							v = b.match(/^\[(.*?)\]\s*(.+)$/);
						return { id: _, name: v ? v[2] : b };
					})
					.filter(Boolean),
					f = gw.rl(g);
				let p = null;
				for (const { id: m, name: y } of f)
					if (n.includes(y)) {
						p = m;
						break;
					}
				if (!p) {
					const m = f.filter(({ name: y }) => !l.includes(y));
					p = m.length > 0 ? m[Math.floor(Math.random() * m.length)].id : null;
				}
				if (p)
					try {
						await ghh.Tn(gh_UrlInfo.link({ mod: "guild_warcamp", submod: "guild_combat", gid: p }), { combat: "Attack!" }), await st.Tt("5f281c12321ba18d38f35", Date.now() + 6e5);
					} catch (m) {
						await errorLog.log(m), await st.Tt("5f281c12321ba18d38f35", Date.now() + 12e4);
					}
				else await st.Tt("5f281c12321ba18d38f35", Date.now() + 18e5);
			},
			rl(n) {
				for (let l = n.length - 1; l > 0; l--) {
					const c = Math.floor(Math.random() * (l + 1));
					[n[l], n[c]] = [n[c], n[l]];
				}
				return n;
			},
		},
		eons = {
			async Rn() {
				const n = st.jt("523a2790017", { cw: 0, time: 0 });
				if (n.time < Date.now())
					try {
						const l = gh_UrlInfo.link({ mod: "premium", submod: "inventory" }),
							c = await ghh.Yt(l, {}),
							d = st.Vt(c),
							h = eons.getTokenCountForImage("premium/token/7.jpg", d),
							g = Date.now() + 6e4;
						return await eons.sl(h, g), { cw: h, time: g };
					} catch (l) {
						return await errorLog.log(l), n;
					}
				return n;
			},
			async sl(n, l) {
				await st.Tt("523a2790017", { cw: n, time: l });
			},
			getTokenCountForImage(n, l) {
				const c = jQuery(l)
					.find(".contentboard_paper_repeat.contentboard_paper_active_repeat")
					.filter((g, f) => jQuery(f).find("img.premiumfeature_picture").attr("src").indexOf(n) > -1)
					.first();
				if (!c.length) return 0;
				const d = c.find(".premiumfeature_tokencount").text().trim(),
					h = parseInt(d, 10);
				return isNaN(h) ? 0 : h;
			},
			async ol() {
				const n = st.jt("50218922d1431111fb2321c61d2b1d", 0) > 0;
				if (!n) return !1;
				const l = st.jt("523a211e5b71d1d61293b3461f2c1a", 20),
					c = st.jt("523a211e5b71d1d61293b2d1c1721", 0),
					d = c < l,
					h = (await eons.Rn()).cw > 0,
					g = c === 1 ? h : h || es.Cg().Vi > 0;
				return (d && g) || !n || (await st.Tt("50218922d1431111fb2321c61d2b1d", 0)), n && d && g && parseInt(es.Cg().sa) > 0 && !st.cl("expedition", 0);
			},
			async ll() {
				ro.wt = !0;
				const n = st.jt("52381614100173761a0333a1", "italy");
				if (!n) return (ro.wt = !1);
				parseInt(es.Cg().sa) < 1 && (await st.Wt(st.Le(700, 400)));
				const l = gh_mobData[n].locations.length,
					c = st.jt("50391071652f1ba", 0);
				l < c && (await st.Tt("50391071652f1ba", 0));
				const d = l < c ? 0 : c;
				if (jQuery(`#location_inactive_${d}`).length) {
					const g = gh_mobData[n].locations.find((f) => f.id === parseInt(d));
					return await ghh.he("Can not attack Expedition ", { itemName: g.name }), (ro.wt = !1);
				}
				const h = await eons.ol();
				if (st.jt("5038109361f18cc1a527161a1201d", !1)) {
					const g = await ghh.Yt(gh_UrlInfo.link({ mod: "location", loc: d })),
						f = st.Vt(g).find(".expedition_button");
					for (let p = 0; p < f.length && p !== 3; p++)
						if (jQuery(f[p]).closest(".expedition_box").find(".expedition_bonus.active").length < 4) return (await st.zn()) && (await ot.Hc(!1), await st.Pr(d, p + 1, h ? 1 : 0, "")), void (ro.wt = !1);
				}
				if (await st.zn()) return await ot.Hc(!1), await st.Pr(d, st.jt("50391071652e1bb", 1), h ? 1 : 0, ""), (ro.wt = !1);
				ro.wt = !1;
			},
		};
	class HhW {
		ul(l) {
			ghh.Ta(l) ? (l.classList.add("underworld-highlight"), (l.style.boxShadow = "0 0 3px 2px red"), (l.style.borderRadius = "5px")) : l.classList.add("normal");
		}
		dl(l) {
			l.forEach((c) => {
				if (c.nodeType === 1) {
					const d = c;
					if (d.matches("div[data-basis]:not(.underworld-highlight):not(.normal)")) return void this.ul(d);
					d.children.length > 0 && d.querySelectorAll("div[data-basis]:not(.underworld-highlight):not(.normal)").forEach((h) => this.ul(h));
				}
			});
		}
		hl() {
			const l = document.getElementById("packages");
			if (!l) return;
			let c = null;
			(this.observer = new MutationObserver((d) => {
				c ||
					(c = requestAnimationFrame(() => {
						d.forEach((h) => {
							h.type === "childList" && h.addedNodes.length > 0 && this.dl(h.addedNodes);
						}),
							(c = null);
					}));
			})),
				this.observer.observe(l, { childList: !0, subtree: !1 });
		}
		gl() {
			document.body.addEventListener("click", (l) => {
				l.target.classList.contains("awesome-tabs") && this.ml();
			});
		}
		ml() {
			document.querySelectorAll("div[data-basis]:not(.underworld-highlight):not(.normal)").forEach((l) => this.ul(l));
		}
		yl() {
			st.jt("79243e19741c1d3a0232da181d3721c26621810", !0) && (this.ml(), this.hl(), this.gl());
		}
	}
	const hhU = new HhW(),
		buffs = {
			async bl(n = 1, l = [2, 3, 4, 5, 9, 10]) {
				const c = await st.qe(null, n, { f: 12, fq: "-1" });
				await ghh.Gt();
				const d = Array.from(st.Vt(c).find(".packageItem")),
					h = jQuery(c).find(".paging_numbers"),
					g = h.length ? Array.from(h[0].children).pop() : null;
				if ((g ? parseInt(g.textContent) : 1) < n) return await st.Tt("5f281c12371b5122c1e1b2e3815a1c31", new Date().getTime() + 72e4), void (ro.dt = !1);
				let f = !1;
				const p = await st.Te(1, 1),
					m = st.jt("53221111d1a241b5b3e262b131c", []);
				if (d.length > 0) {
					for (const y of d) {
						const b = st.Ke(y.querySelector("[data-content-type]"));
						if (+b.level < 2 || !["12-1", "12-3"].includes(b.basis) || m.some((T) => T.containerNumber === b.containerNumber)) continue;
						let w;
						if ((b.basis === "12-1" ? ((w = l.includes(3) ? 3 : null), (l = l.filter((T) => T !== w))) : (w = l.shift()), !w)) continue;
						f = !0;
						const _ = { from: "-" + b.containerNumber, fromX: b.positionX, fromY: b.positionY, to: p.bagId, toX: p.spot.x + 1, toY: p.spot.y + 1, amount: b.amount },
							v = await st.ja(_),
							j = { from: p.bagId, fromX: p.spot.x + 1, fromY: p.spot.y + 1, to: w, toX: 1, toY: 1, amount: 1 };
						if ((await st.ja(j)).error || parseInt(b.amount) > 1) {
							await st.Ma({ id: v.to.data.itemId, price: 1 });
							const T = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "p", p: 1 }),
								k = await ghh.Yt(T);
							await st.Pa(k);
						}
					}
					return l.length ? (f || n++, await buffs.bl(n, l)) : (await st.Tt("5f281c12371b5122c1e1b2e3815a1c31", new Date().getTime() + 72e4), void (ro.dt = !1));
				}
				return buffs.bl(++n, l);
			},
			async wl(n = 1, l = [6, 7, 11, 2, 4, 5, 9, 10]) {
				const c = st.jt("5e2481533111361c9803a171a274", ["12-20", "12-22"]);
				c.every((_) => ["12-20", "12-22"].includes(_)) && (l = l.filter((_) => ![2, 4, 5, 9, 10].includes(_)));
				const d = l.filter((_) => [6, 7, 11].includes(_)),
					h = l.filter((_) => [2, 4, 5, 9, 10].includes(_)),
					g = await st.qe(null, n, { f: 12, fq: "-1" });
				await ghh.Gt();
				const f = Array.from(st.Vt(g).find(".packageItem")),
					p = st.Vt(g).find(".paging_numbers"),
					m = p.length ? Array.from(p[0].children).pop() : null;
				if ((m ? parseInt(m.textContent) : 1) < n) return await st.Tt("5f281c12371b5122662", new Date().getTime() + (45 / ghh.He()) * 60 * 60 * 1e3), void (ro.dt = !1);
				let y = !1;
				const b = await st.Te(1, 1),
					w = st.jt("53221111d1a241b5b3e262b131c", []);
				if (f.length > 0) {
					for (const _ of f) {
						const v = st.Ke(_.querySelector("[data-content-type]"));
						if (w.some((S) => S.containerNumber === v.containerNumber) || !c.includes(v.basis)) continue;
						let j = null;
						if ((["12-20", "12-22"].includes(v.basis) && d.length > 0 ? (j = d.shift()) : ["12-21", "12-23", "12-18", "12-19"].includes(v.basis) && h.length > 0 && (j = h.shift()), !j)) continue;
						y = !0;
						const T = { from: "-" + v.containerNumber, fromX: v.positionX, fromY: v.positionY, to: b.bagId, toX: b.spot.x + 1, toY: b.spot.y + 1, amount: v.amount },
							k = await st.ja(T),
							I = { from: b.bagId, fromX: b.spot.x + 1, fromY: b.spot.y + 1, to: j, toX: 1, toY: 1, amount: 1 };
						if ((await st.ja(I)).error || parseInt(v.amount) > 1) {
							await st.Ma({ id: k.to.data.itemId, price: 1 });
							const S = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "p", p: 1 }),
								C = await ghh.Yt(S);
							await st.Pa(C);
						}
						l = l.filter((S) => S !== j);
					}
					return l.length > 0 ? (y || n++, await buffs.wl(n, l)) : (await st.Tt("5f281c12371b5122662", new Date().getTime() + (45 / ghh.He()) * 60 * 60 * 1e3), void (ro.dt = !1));
				}
				return await buffs.wl(++n, l);
			},
			async _l(n = 1) {
				const l = await st.Ot(n);
				return Array.from(l.find("#buffbar_old img"))
					.map((c) => c.getAttribute("src"))
					.filter((c) => c && /\/buff\/\d+_\d+\.gif/.test(c))
					.map((c) => {
						const d = c.match(/\/buff\/(\d+_\d+)\.gif/);
						return d && d[1] ? d[1].replace("_", "-") : null;
					})
					.filter(Boolean);
			},
		};
	class PCB {
		constructor() {
			(this.pa = []), (this.ya = []), (this.vl = []), (this.kl = (l, c) => l.filter((d) => !c.includes(d)));
		}
		Il() {
			(this.ya = []), (this.vl = []), (this.pa = []);
		}
		async wa(l, c) {
			let d = 1;
			const h = st.Vt(await st.qe(null, 1, { f: l, fq: "-1" })),
				g = jQuery(h).find(".paging_numbers"),
				f = g.length ? Array.from(g[0].children).pop() : null,
				p = f ? parseInt(f.textContent || "1", 10) : 1;
			for (; d <= 500 && d <= p && pcb.ya.length < 100;) {
				await ghh.Gt();
				const m = [];
				for (let b = 0; b < c && d <= 500; b++) m.push(this.va(l, d)), d++;
				const y = await Promise.all(m);
				for (const b of y) b && b.stopPageNumber;
				await st.Wt(160);
			}
		}
		async va(l, c, d = 10) {
			try {
				const h = await ghh.ka(gh_UrlInfo.ajaxLink({ mod: "packages", page: c, submod: "loadMore", f: l, fq: -1 }));
				if (!h || !h.newPackages) throw new Error(`Invalid response for page ${c}`);
				if (this.pa.includes(c)) return;
				this.pa.push(c);
				const g = h.newPackages.map((m) => jQuery(m).get(0)),
					f = st.jt("53221111d1a241b5b3e262b131c", []),
					p = g
						.map((m) => {
							const y = m.querySelector("[data-content-type]"),
								b = st.Ke(y);
							return this.vl.includes(b.basis) && !f.some((w) => w.containerNumber === y.containerNumber) ? ((this.vl = this.vl.filter((w) => w !== b.basis)), b) : null;
						})
						.filter((m) => m);
				if ((p.length > 0 && pcb.ya.push(...p), !this.vl.length)) return { stopPageNumber: c };
			} catch {
				return d > 0 ? (await st.Wt(200), this.va(l, c, d - 1)) : {};
			}
		}
		async Sa(l = 1) {
			(ro.dt = !0), this.Il(), await st.Tt("5f281c12371b5122d022b", Date.now() + 72e4);
			const c = await buffs._l(l),
				d = pcb.kl(pcb.Sl(), c);
			pcb.vl.push(...d), await this.wa(11, 5), await this.qa(d), (ro.dt = !1);
		}
		async qa(l) {
			const c = await st.Te(1, 1);
			if (!c) return ghh.he("No space in inventory for buff items", { itemName: "!" });
			await invW.Ut(c.bagId, c.spot.x, c.spot.y, 2, 3, "Buffing");
			let d = [...l];
			for (; pcb.ya.length > 0;) {
				const h = pcb.ya.shift();
				if (d.includes(h.basis)) {
					d = d.filter((g) => g !== h.basis);
					try {
						await st.Wt(100), await pcb.Dl(h, c);
					} catch (g) {
						await errorLog.log(g);
					}
				}
			}
			await st.Wt(120), await invW.Ft();
		}
		async Dl(l, c) {
			const d = { from: "-" + l.containerNumber, fromX: l.positionX, fromY: l.positionY, to: c.bagId, toX: c.spot.x + 1, toY: c.spot.y + 1, amount: l.amount },
				h = await st.ja(d),
				g = { from: c.bagId, fromX: c.spot.x + 1, fromY: c.spot.y + 1, to: 8, toX: 1, toY: 1, amount: 1 };
			if ((await st.ja(g)).error || parseInt(l.amount) > 1) {
				await st.Ma({ id: h.to.data.itemId, price: 1 });
				const f = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 }),
					p = await ghh.Yt(f);
				await st.Pa(p);
			}
		}
		Sl() {
			return st.jt("5021892b726c602127ac1728b161b1", [
				"11-1",
				"11-5",
				"11-9",
				"11-13",
				"11-17",
				"11-25",
				"11-2",
				"11-6",
				"11-10",
				"11-14",
				"11-18",
				"11-21",
				"11-26",
				"11-3",
				"11-4",
				"11-7",
				"11-8",
				"11-11",
				"11-12",
				"11-15",
				"11-16",
				"11-19",
				"11-20",
				"11-22",
				"11-23",
				"11-24",
				"11-27",
			]);
		}
	}
	const pcb = new PCB(),
		dgn = {
			async ql() {
				const n = gh_UrlInfo.link({ mod: "premium", submod: "inventory" }),
					l = await ghh.Yt(n),
					c = st.Vt(l),
					d = Array.from(c.find(".premium_activate_button:not(:disabled)"))
						.filter((h) => h.getAttribute("onclick").match(/&feature=6&/gi))
						.pop();
				if (d) {
					const h = d.getAttribute("onclick").match(/token=(\d+)/)[1];
					await st.Tt("443e121141a63fc163a2e251d", Date.now() + 3e5), (location.href = gh_UrlInfo.link({ mod: "premium", submod: "inventoryActivate", feature: 6, token: h }));
				} else await st.Tt("443e121141a63fc163a2e251d", Date.now() + 36e5);
			},
			async Al() {
				(ro.wt = !0), parseInt(es.Cg().oa) < 1 && (await st.Wt(st.Le(700, 400)));
				const n = gh_UrlInfo.link({ mod: "dungeon", loc: st.jt("5538a1101d386c", 0) }),
					l = st.Vt(await ghh.Yt(n));
				return await this.Cl(l, n), (ro.wt = !1);
			},
			async Cl(response, url) {
				const isAdvancedMode = st.jt("583e2523fd17cb", !1),
					hasStartFightImages = Array.from(jQuery(response).find("img[onclick^=startFight]")).length > 0;
				if (hasStartFightImages) {
					const isDelaySuccess = await st.zn();
					if (!isDelaySuccess) return void (ro.wt = !1);
					const maxLosses = parseInt(st.jt("43281712141c1735f1bb35102c61f201d340130", 10));
					let exceededMaxLosses = !1;
					if (maxLosses > 0) {
						const n = st.jt("432814971a27151de", {}),
							l = Object.keys(n);
						let c = 0;
						const d = {};
						for (let h = l.length - 1; h >= 0 && !exceededMaxLosses; h--) {
							if (((d[l[h]] = n[l[h]]), n[l[h]] === 1)) {
								await st.Tt("432814971a27151de", d);
								break;
							}
							exceededMaxLosses = ++c >= maxLosses;
						}
						exceededMaxLosses && (await st.Tt("432814971a27151de", {}));
					}
					const isSkipBoss = st.jt("583e37d1c1e211b1a1c", !1),
						bossText = st.jt("5322171521b1b0", "boss").toLowerCase(),
						bossId = jQuery(response)
							.find("div[onclick^=startFight]")
							.filter((n, l) => jQuery(l).text().trim().toLowerCase().includes(bossText))
							.map((n, l) => {
								const c = jQuery(l)
									.attr("onclick")
									.match(/startFight\('(\d+)'/);
								return c ? parseInt(c[1]) : null;
							})
							.get()
							.pop(),
						startFightImages = Array.from(jQuery(response).find("img[onclick^=startFight]"));
					let highestFightId = -1,
						fightId = "";
					if (!exceededMaxLosses)
						for (const n of startFightImages) {
							const l = n.getAttribute("onclick").match(/startFight\('(\d+)',\s*'(\d+)'\)/);
							if (l) {
								const c = parseInt(l[1]);
								if (((fightId = l[2]), bossId !== null && isSkipBoss && c === parseInt(bossId))) continue;
								c > highestFightId && (highestFightId = c);
							}
						}
					if (highestFightId > -1) {
						const response = await st.Qr("get", "ajax/doDungeonFight.php", `did=${fightId}&posi=${highestFightId}`);
						response.success ? (eval(response.context), await st.Wt(500)) : (location.reload(), await st.Wt(500));
					} else {
						const n = jQuery(response).find("[name=dungeonId]");
						if (n.length && n.val()) {
							const l = gh_UrlInfo.link({ mod: "dungeon", loc: st.jt("5538a1101d386c", 0), action: "cancelDungeon" }),
								c = await ghh.Tn(l, { dungeonId: n.val() });
							await this.Cl(st.Vt(c), url);
						}
					}
				} else {
					let n = jQuery(response).find(`input[name=${isAdvancedMode ? "dif2" : "dif1"}]`);
					if (n.length && !n.hasClass("disabled")) {
						const l = isAdvancedMode ? { dif2: "Advanced" } : { dif1: "Normal" },
							c = await ghh.Tn(url, l);
						await this.Cl(st.Vt(c), url);
					} else if (((n = jQuery(response).find("input[name=dif1]")), n.length)) {
						const l = await ghh.Tn(url, { dif1: "Normal" });
						await this.Cl(st.Vt(l), url);
					}
				}
				ro.wt = !1;
			},
		};
	class AB {
		constructor() {
			var l, c;
			(this.Pl = 1),
				(this.xl = !1),
				(this.Tl = () => [
					{
						gs: "532cad1c04",
						jl: () => {
							const d = st.jt("5221d31b1a3366cb343b28e112e", []),
								h = st.jt("532cad5b9c18d2e3283d161b20201fda130", 1e5),
								g = st.jt("532cad5b1c651ca1d33f1731643171a1c30", 9e4),
								f = d.length ? h : g;
							return st.jt("532cad5bdf1dc11a", !1) && st.En() > parseFloat(f) && Date.now() > st.jt("5f281c1236661722c22e2d161b223711ba137352cf", 0);
						},
						Ml: async () => {
							(ro.tt = !0), await bg.Ns(), (ro.tt = !1);
						},
					},
					{
						gs: "532cad1c04",
						jl: () => st.jt("532cad5bc21a2", !1) && st.oe() >= 100 && Date.now() > st.jt("5f281c1236661722df2923281d1d26bb1c222568", 0),
						Ml: async () => {
							ro.nt = !0;
							const d = await bg.Xs();
							(ro.nt = !1), d && (location.reload(), await st.Wt(1e3));
						},
					},
					{
						gs: "412c7d321f10",
						jl: () => {
							const d = st.jt("5221d31b1a3366cb343b28e112e", []);
							return (
								!(st.jt("532cad1c04", !1) && st.jt("532cad5bdf1dc11a", !1) && d.length) &&
								st.En() >= parseFloat(st.jt("5c24a211a27248c5429162d73c", "100000")) &&
								!st.jt("53221111d1a241b5b3e262b131c", []).filter((h) => h && !h.isError).length &&
								Date.now() > new Date(st.jt("53381df1b93315a48323cd1d171fca", 0)).getTime()
							);
						},
						Ml: async () => {
							(ro.kt = !0), await st.Ql();
						},
					},
					{
						gs: "412c7d321f10",
						jl: () => {
							const d = st.jt("53221111d1a241b5b3e262b131c", []).find((h) => h && !h.isError);
							return d && !st.Vn(d.packAmount) && Date.now() > st.jt("42288a25f01ff1a1a323a1d2b1331b", 0);
						},
						Ml: async () => {
							(ro.It = !0), await gh.Xn(), (ro.It = !1);
						},
					},
				]),
				(this.Rl = () => [
					{
						gs: "502e10f3f17112aa0333da61d",
						jl: () => Date.now() > parseInt(st.jt("5225151e2d61a1d1a1c2e272c61f20", 0)) && !st.Bo,
						Ml: async () => {
							(ro.Ct = !0), await pcts.Mc(), (ro.Ct = !1);
						},
					},
					{
						gs: "502e10f3f17112aa0333da61d",
						jl: () => prrs.Fn(),
						Ml: async () => {
							(ro.Ct = !0), await prrs.Mt(), (ro.Ct = !1);
						},
					},
					{
						gs: "52228a10d1726c1c1323a1ba1",
						jl: () => Date.now() > parseInt(st.jt("52228a10d1726c1c1323a1ba111715a", 0)),
						Ml: async () => {
							(ro._t = !0), await st.Tt("52228a10d1726c1c1323a1ba111715a", Date.now() + 36e5), await ghh.Xa(), (ro._t = !1);
						},
					},
					{
						gs: "462216d17bd17141b29291a317",
						jl: () => st.jt("462216d17bd171411c2238196041c1d113", !1) && st.jt("workbench.itemList1", []).length > 0 && this.Pl > 4,
						Ml: async () => {
							await be.Mt();
						},
					},
					{
						gs: "462216d17bd17141b29291a317",
						jl: () => st.jt("462216d17bd171411c22381960111ba213", !1) && st.jt("workbench.itemList2", []).length > 0 && this.Pl > 4,
						Ml: async () => {
							await be.Mt(2);
						},
					},
					{
						gs: "42201a1b113261d922661d1132721d",
						jl: () => {
							const d = st.jt("5722161104061a8d222", !1),
								h = st.jt("5722161103df1b1d3c1a263cd1c", []).some((g) => g.end_duration < Date.now());
							return d && h && this.Pl > 4;
						},
						Ml: async () => {
							(ro.ft = !0), await ftf.Pc(), (ro.ft = !1);
						},
					},
					{
						gs: "42201a1b113261d922661d1132721d",
						jl: () => {
							const d = st.jt("5722161104061a8d222", !1),
								h = parseInt(st.jt("5f281c12211c1a3261d28283a1fa", 0)),
								g = st.jt("50381093311113c3d1b2b2db", []).filter((f) => f.active);
							return d && h < Date.now() && g.length > 0 && this.Pl > 4;
						},
						Ml: async () => {
							(ro.ft = !0), await ftf.Mt(), (ro.ft = !1);
						},
					},
					{
						gs: "42201a1b113261d922661d1132721d",
						jl: () => {
							const d = st.jt("42201a1b115ac1f25241d", !1),
								h = parseInt(st.jt("5f281c12211c1a3261d3d2a2d141b", 0)),
								g = st.jt("42201a1b11d4761a2225b291d373d15a1e30", []).filter((p) => p),
								f = st.jt("4128a21c043d1da3127a3c1f202c", "");
							return d && h < Date.now() && (g.length > 0 || f) && this.Pl > 4;
						},
						Ml: async () => {
							(ro.ht = !0), await lt.ras(), (ro.ht = !1);
						},
					},
					{
						gs: "412c7d14967",
						jl: () => st.jt("5f28133614d8", !1) && Date.now() > parseInt(st.jt("43221071b30115ad332d1c266203b261c", 0)) && st.oe() > 500,
						Ml: async () => {
							(ro._t = !0), await pcp.Sa();
						},
					},
					{
						gs: "5225151e2b1b401db23f17316",
						jl: () => Date.now() > parseInt(st.jt("5225151e2b1b401db23f17316c0", 0)),
						Ml: async () => {
							(ro._t = !0), await ks.wo(), (ro._t = !1);
						},
					},
					{
						gs: "412c7d14967",
						jl: () => st.jt("41247d342f3363ae3c1d21", !1),
						Ml: async () => {
							await st.Tt("412c7d321f10", !1), await ks.vo();
						},
					},
					...this.Tl(),
					{
						gs: "5039107165200",
						jl: () => st.jt("5522301373226c19b292f1d", !1) && st.jt("522416501d3714e40292d01b2020181d11521", 0) < Date.now() && st.cl("ct") && this.Pl > 9 && at.Si(3) && !es.Cg().fa,
						Ml: async () => {
							(ro.wt = !0), await at.Di(3), (ro.wt = !1);
						},
					},
					{
						gs: "5c28171514963a61b721211be62c116",
						jl: () => st.jt("5c28171514963a61b721211be62c116", !1) && mesgs.io(),
						Ml: async () => {
							await mesgs.Mt();
						},
					},
					{
						gs: "5c28171514963a61b721211be62c116",
						jl: async () => {
							const d = st.jt("5f2210f1370151d6129dea1c311d561d17349216", { date: Date.now() + 6e4, c: 0 });
							return st.jt("5c28171514963a61b721211be62c116", !1) && d.c > 0 && d.date < Date.now();
						},
						Ml: async () => {
							await mesgs.uo();
						},
					},
					{
						gs: "5039107165200",
						jl: () => {
							const d = st.jt("4239b163c82f1d461a133da213", !1),
								h = st.jt("50391071652a123d1a32619da131", !1),
								g = st.jt("592c1732032251ca1d33", !1),
								f = st.jt("4338a331b1aa182ea1a4201d1c6", !1),
								p = st.jt("5f281c12341a1715a43a323a15e", 0),
								m = ghh.Hi();
							return (!h || (h && g)) && (!f || (f && !m.gotTreasureBox)) && (!d || at.Ii("circusTuma")) && st.cl("ct") && !es.Cg().fa && this.Pl > 9 && p < Date.now();
						},
						Ml: async () => {
							(ro.wt = !0), await at.ai();
						},
					},
					{ gs: "50381093db218", jl: () => jQuery("#header_values_hp_percent").length && al.Fo() < al.Uo() && parseInt(st.jt("5225151e266155260132115a", "0")) < Date.now() && al.$o(), Ml: al.Eo },
					{
						gs: "412c7d14967",
						jl: () => st.jt("42288a3c1a6191a", !1) && parseInt(st.jt("5f281c12217e112f01c142d1433b31b151c", "0")) < Date.now(),
						Ml: async () => {
							await pss.Ja(!0);
						},
					},
					{
						gs: "53381d201a17",
						jl: () => st.oe() >= parseInt(st.jt("5c24a211a272062d1b3ee17016", "0")) && parseInt(st.jt("5f281c12217e112f01c4201dc192c01f291d2b1", "0")) < Date.now() && !st.jt("53381d201a173df21b222c1db", !0),
						Ml: async () => {
							(ro.St = !0), await al.Mo();
						},
					},
					{
						gs: "50387121c1d5ac1f25241d",
						jl: () => parseInt(st.jt("5f281c12217e112a7b2423391a113171713a2b1010a", 0)) < Date.now() && this.Pl > 3,
						Ml: async () => {
							await ap.Sa(), (ro.ot = !1);
						},
					},
					{
						gs: "403811511d",
						jl: async () => parseInt(st.jt("403811514001ccc5692b1701e211f1", "0")) < Date.now(),
						Ml: async () => {
							(ro.yt = !0), await ts.Mi(), await st.Tt("403811514001ccc5692b1701e211f1", Date.now() + 1e3 * st.Le(40, 20)), (ro.yt = !1);
						},
					},
					{
						gs: "403811511d",
						jl: () => parseInt(st.jt("403811514001b63a283f16", "0")) < Date.now(),
						Ml: async () => {
							(ro.yt = !0), await ts.xi(), (ro.yt = !1);
						},
					},
					{
						gs: "503910716537",
						jl: () => {
							const d = st.jt("52381614100173761a0333a1", "italy") === "britannia",
								h = st.jt("50218923b10112ee1a2231d16", !1);
							return !st.Bo && !es.Cg().fa && this.Pl > 9 && h && parseInt(es.Cg().oa) < 6 && !d && st.jt("443e121141a63fc163a2e251d", 0) < Date.now();
						},
						Ml: async () => {
							await dgn.ql();
						},
					},
					{
						gs: "503910716537",
						jl: () => {
							const d = st.jt("50391071652a122d1a0202d1712330bb1b", !1),
								h = st.jt("592c172200411613f322db1b", !1),
								g = st.jt("52381614100173761a0333a1", "italy") === "britannia";
							return !st.Bo && st.cl("dungeon", 1) && !es.Cg().fa && this.Pl > 9 && (!d || (d && h)) && !g;
						},
						Ml: async () => {
							await dgn.Al();
						},
					},
					{
						gs: "5039107165",
						jl: async () => {
							const d = st.jt("5038109261ac4281b1a262b131c", !0),
								h = st.jt("50391071652a122c171e222c111b1b2a0b3e7211617", !1),
								g = st.jt("592c1723d1e61001b72826b3e7201dc", !1),
								f = st.jt("583e2091c04301c19222716", !1);
							return (!d || (d && al.Fo() >= al.Uo())) && !st.Bo && (!h || (h && g)) && (st.cl("expedition", 0) || (await eons.ol())) && !f && !es.Cg().fa;
						},
						Ml: async () => {
							await eons.ll();
						},
					},
					{
						gs: "5039107165226c1f",
						jl: () => {
							const d = st.jt("4239b16341c61a826012261ca0", !0);
							return st.jt("55222514100226c19b292f1d", !1) && st.jt("503f181440d11111b3c223e1d11520", 0) < Date.now() && st.cl("arena") && !es.Cg().fa && this.Pl > 1 && at.Si() && (!d || (d && !st.Bo)) && al.Fo() >= al.Uo();
						},
						Ml: async () => {
							(ro.wt = !0), await at.Di(), (ro.wt = !1);
						},
					},
					{
						gs: "5039107165226c1f",
						jl: () => {
							const d = st.jt("4239b163c82f1d461a63a1d113", !1),
								h = st.jt("4239b16341c61a826012261ca0", !0),
								g = st.jt("5038109261ac4281b1a262b131c", !0),
								f = st.jt("50391071652a12281db2929291a17361a", !1),
								p = st.jt("592c17277bd15381ab343c", !1),
								m = st.jt("5f281c12341a1715a42f352d16e", 0);
							return (!h || (h && !st.Bo)) && (!g || (g && al.Fo() >= al.Uo())) && (!f || (f && p)) && (!d || at.Ii("arena")) && st.cl("arena") && !es.Cg().fa && this.Pl > 1 && m < Date.now();
						},
						Ml: async () => {
							(ro.wt = !0), await at.ci();
						},
					},
					{
						gs: "4239b16341c61a8",
						jl: () => parseInt(st.jt("4239b16341c61a83b72a2d", 0)) < Date.now(),
						Ml: async () => {
							await st.Tt("5039107165226c1f", !1), await st.Tt("4239b16341c61a8", !1);
						},
					},
					{
						gs: "4239b16211b11198",
						jl: () => parseInt(st.jt("4239b16211b111983b72a2d", 0)) < Date.now(),
						Ml: async () => {
							await st.Tt("5039107165200", !1), await st.Tt("4239b16211b11198", !1);
						},
					},
					{
						gs: "543b181",
						jl: () => {
							const d = st.jt("5038109261ac4281b1a262b131c", !0);
							return (!d || (d && al.Fo() >= al.Uo())) && parseInt(st.jt("543b1813ec1d71b1d693c11217a1bc", 0)) < Date.now() && jQuery("#submenu2 a").filter(".glow")[0];
						},
						Ml: evt.tl,
					},
					{ gs: "543b181", jl: () => st.jt("45221715317011", !1) && parseInt(st.jt("5f281c12341821d5ec2b2d3c611203a11217", 0)) < Date.now() && jQuery("#submenu1").find('a[href*="craps"]').length > 0, Ml: evt.Mt },
					{
						gs: "50218923b10113ba7292e171d112031d1637",
						jl: () => st.Bo && st.jt("592c172410f17117297353bc202", !1) && parseInt(st.jt("5f281c12371b5122d022b", 0)) < Date.now(),
						Ml: async () => {
							await pcb.Sa();
						},
					},
					{
						gs: "50218923b10113c1f935291ca1",
						jl: () => st.Bo && st.jt("592c172410f17117297353bc202", !1) && parseInt(st.jt("5f281c12371b5122c1e1b2e3815a1c31", 0)) < Date.now(),
						Ml: async () => {
							(ro.dt = !0), await st.Ot(), await ghh._a(), await buffs.bl(1);
						},
					},
					{
						gs: "5038109371b512266234",
						jl: () => st.Bo && st.jt("592c172410f17117297353bc202", !1) && parseInt(st.jt("5f281c12371b5122662", 0)) < Date.now() && st.jt("5e2481533111361c98", ["12-20", "12-22"]).length > 0,
						Ml: async () => {
							(ro.dt = !0), await st.Ot(), await ghh._a(), await buffs.wl();
						},
					},
					{
						gs: "5038109371b5122e0a34",
						jl: () => st.Bo && st.jt("592c172410f17117297353bc202", !1) && parseInt(st.jt("5f281c12217e112b1a821f17b1", 0)) < Date.now() && st.jt("422883161a6102b1a821f17b1", []).length > 0,
						Ml: st.Ul,
					},
					{
						gs: "59285a3611001c2b34",
						jl: () => st.Bo && parseInt(st.jt("5f281c1226b173cce2427b1b728bb", 0)) < Date.now(),
						Ml: async () => {
							await st.Bl();
						},
					},
					{
						gs: "442303719c65b3c3226",
						jl: () => {
							const d = st.jt("5038109261ac4281b1a262b131c", !0),
								h = st.jt("4423037206c1d2e1a33291b4", 0);
							return (!d || (d && al.Fo() >= al.Uo())) && st.Bo && h < Date.now() && st.cl("expedition", 0) && !es.Cg().fa;
						},
						Ml: async () => {
							await ld.Mt();
						},
					},
					{
						gs: "442303719c65b3c3226",
						jl: () => !st.Bo && !es.Cg().fa && this.Pl > 99 && parseInt(st.jt("5f281c12366617227f341d3b01311b15a1", 0)) < Date.now(),
						Ml: async () => {
							await es.ea();
						},
					},
					{
						gs: "442303719c65b3c3226",
						jl: async () => {
							const d = st.jt("592c17331ba661e01c2b2c3b01311b15a", [])[ld.mr(st.jt("442303719c65b22223e1d3", "Normal"))];
							return !es.Cg().fa && this.Pl > 99 && !d && !st.Bo && (await st.Fl()) && (await st.$l());
						},
						Ml: async () => {
							jQuery("#header_values_hp_percent").length && al.Fo() < 95 ? await al.Eo() : (st.oe() < 8e3 && (await ks.Zs(8e3)), await el.tc());
						},
					},
					{
						gs: "453f510102251b1b2b1b292f1d01c",
						jl: async () => {
							const d = st.jt("52381614100173761a0333a1", "italy"),
								h = Math.min(st.jt("5538a1101d39013e33", 12), parseInt(es.Cg().ga)),
								g = d === "britannia" && parseInt(es.Cg().sa) < 1 && parseInt(es.Cg().oa) >= h,
								f = d === "germany" && parseInt(es.Cg().oa) < 1,
								p = st.jt("52221712036376022327f1", 0);
							return !st.Bo && this.Pl > 117 && (f || (g && (p > Date.now() + 36e5 || p < Date.now()))) && !(await st.El());
						},
						Ml: async () => {
							st.oe() < 8e3 && (await ks.Zs(8e3));
							const d = st.jt("52381614100173761a0333a1", "italy") === "britannia";
							await st.Tt("583e2091c04301c19222716", d), await st.Wt(120), await st.Tt("503910716537", d), await st.Wt(100), await el.ec(d ? 2 : 4);
						},
					},
					{
						gs: "462216d17bd17141b29291a317",
						jl: () => parseInt(st.jt("5f281c1236661722b12b24b", 0)) < Date.now(),
						Ml: async () => {
							await lls.Mt();
						},
					},
					{
						gs: "42201a1b113261d922661d1132721d",
						jl: () => st.jt("42201a1b115ac1f25241d", !1) && parseInt(st.jt("5f281c12217e112a7b2423311b172828171d21290f1bdb4", 0)) < Date.now() && this.Pl > 4,
						Ml: async () => {
							(ro.st = !0), await ms.rc(), (ro.st = !1);
						},
					},
					{ gs: "52228a10d17336b212e24b", jl: () => parseInt(st.jt("5f281c12217e112a7b24233f016a7141c", 0)) < Date.now() && st.jt("5038109361f18cc1a0271c201b291d", []).length > 0, Ml: st.Ll },
					{ gs: "443e1251a1d1714a", jl: async () => st.jt("7225151e2ac1851c", 0) < Date.now() && !st.Bo && (await st.Fl()) && !st.jt("583e2091c04301c19222716", !1), Ml: es.Mt },
					{ gs: "5c2c16d101a2a0c21d", jl: () => st.jt("5c2c16d101a4d161c16", !0) && parseInt(st.jt("5f281c12217e112f01ca29a4173127ca1f37", 0)) < Date.now(), Ml: et.Mt },
					{ gs: "5c2c16d101a2a0c21d", jl: () => st.jt("5c2c16d101a4d7c32", !1) && parseInt(st.jt("5f281c12217e112f01ca29a417313d1d31ed116217", 0)) < Date.now(), Ml: et.So },
					{ gs: "4225b166", jl: () => parseInt(st.jt("5f281c12217e112a7b24232b71d35261181a151023d111a261005e42", 0)) <= Date.now(), Ml: sp.Bc },
					{
						gs: "5638da112f1708c5",
						jl: () => parseInt(st.jt("5f281c12321ba18d38f35", 0)) <= Date.now(),
						Ml: async () => {
							(ro.wt = !0), await gw.Mt(), (ro.wt = !1);
						},
					},
					{
						gs: "5039107165",
						jl: () => !st.Bo && !st.jt("583e2091c04301c19222716", !1) && st.jt("462216d3c82d1b3907293cb", !1) && (parseInt(es.Cg().sa) < 1 || es.Cg().fa) && parseInt(st.jt("5f281c123666172291351f171d19", 0)) <= Date.now(),
						Ml: async () => {
							(ro.wt = !0), es.Cg().fa && (await st.Tt("5f281c123666172291351f171d19", Date.now() + 35e3), location.reload(), await st.Wt(400)), await rk.Mt(!0), (ro.wt = !1);
						},
					},
				]),
				(this.Nl = async (d) => {
					this.xl = !0;
					try {
						st.jt(d.gs, !1) && (await d.jl()) && (await ghh.Gt(), await d.Ml(), await st.Wt(200));
					} catch (h) {
						await errorLog.log(h), await st.Wt(100), st.Ol(), await st.Wt(5e3), location.reload(), await st.Wt(1e3);
					}
					this.xl = !1;
				}),
				(this.ma = 5 / parseInt((((l = jQuery("#server-time").next().html()) == null ? void 0 : l.match(/x(\d)$/)) ?? ["0", "1"])[1])),
				(this.Gl = !document.hidden),
				(this.Pl = parseInt(((c = document.getElementById("header_values_level")) == null ? void 0 : c.innerText) ?? "0")),
				document.addEventListener("visibilitychange", this.Hl.bind(this));
		}
		Hl() {
			(this.Gl = !document.hidden), this.Gl;
		}
		async Wl() {
			await st.Wt(10),
				ctProgressBar &&
				(pbm.Er("ct", ctProgressBar.nowInit, ctProgressBar.end),
					pbm.Er("arena", arenaProgressBar.nowInit, arenaProgressBar.end),
					pbm.Er("expedition", expeditionProgressBar.nowInit, expeditionProgressBar.end),
					pbm.Er("dungeon", dungeonProgressBar.nowInit, dungeonProgressBar.end),
					await st.Wt(10));
		}
		zl() {
			if (window.Header && Header.prototype.update) {
				const l = Header.prototype.update;
				Header.prototype.update = function (...c) {
					const d = l.apply(this, c);
					return pbm.$r("ct", ctProgressBar.end), pbm.$r("arena", arenaProgressBar.end), pbm.$r("expedition", expeditionProgressBar.end), pbm.$r("dungeon", dungeonProgressBar.end), d;
				};
			}
		}
		async yl() {
			gh_UrlInfo.resolve(location.href), await this.Wl(), this.zl(), hhU.yl(), await st.Xl(), ro.vt || (await st.yl()), pc.Jl(), await invW.Mt(), await pc.Vl();
		}
		async Vl() {
			document.querySelector("body > center:nth-child(1) > h1") && (location.reload(), await st.Wt(400)), await st.Wt(100), await pc.Yl();
		}
		async Yl() {
			const l = Date.now();
			for (; _getSessionValue("startScript", !1);) {
				if ((await ghh.Gt(), st.Kl())) return;
				const c = await st.Zl();
				if (c) {
					st.En() < parseFloat(st.jt("5c24a211a27248c5429162d73c", "100000")) && st.jt("5522a71b261a8d222", !1) && (await gh.ir()), ro.et(!0);
					const d = [...this.Tl()];
					for (const h of d) (!this.xl && st.tu()) || (await this.eu()), await this.Nl(h), await st.Wt(500);
					ro.et(!0), c > Date.now() + 36e5 / (5 / pc.ma) && Date.now() > st.jt("5f281c123666172291351f171d19", 0) && st.jt("462216d3002165a", !1) && (await rk.Mt()), await st.Wt(1e3);
				} else {
					const d = this.Rl().filter((h) => st.jt(h.gs, !1));
					if (l + 60 * (ro.vt ? 0.5 : 3.5) * 1e3 <= Date.now()) return location.reload(), void (await st.Wt(3e3));
					for (const h of d) (!this.xl && st.tu()) || (await this.eu()), await this.Nl(h);
					await st.Wt(500);
				}
			}
		}
		async eu() {
			return new Promise((l) => {
				const c = tiW.au(
					async () => {
						!this.xl && st.tu() && (tiW.nu(c), l(!0));
					},
					500,
					!0
				);
			});
		}
		Jl() {
			let l = _getSessionValue("startScript", !1);
			window.addEventListener("sessionStorageChange", (c) => {
				if (c.detail.key === "startScript") {
					const d = _getSessionValue("startScript", !1);
					d && !l && pc.Vl(), (l = d);
				}
			});
		}
	}
	const pc = new AB();
	class TIW {
		constructor() {
			(this.su = []), (this.Fr = []), (this.ou = {}), (this.iu = 0), (this.cu = this.Ur());
		}
		lu() {
			return (this.iu += 1), parseInt(this.iu.toString());
		}
		au(l, c, d) {
			const h = this.lu(),
				g = pc.Gl ? c : d ? c * pc.ma : c;
			if (typeof Worker < "u" && this.cu) {
				this.cu.postMessage({ type: "setInterval", id: h, interval: g });
				const f = (p) => {
					p.data.type === "interval" && p.data.id === h && l();
				};
				(this.ou[h] = f), this.cu.addEventListener("message", f);
			} else this.Fr[h] = window.setInterval(l, g);
			return h;
		}
		nu(l) {
			if (typeof Worker < "u" && this.cu) this.cu.postMessage({ type: "clearInterval", id: l }), this.ou[l] && (this.cu.removeEventListener("message", this.ou[l]), delete this.ou[l]);
			else {
				const c = this.Fr[l];
				c && (clearInterval(c), delete this.Fr[l]);
			}
		}
		be(l, c, d) {
			const h = this.lu(),
				g = pc.Gl ? c : d ? c * pc.ma : c;
			if (typeof Worker < "u" && this.cu) {
				const f = (p) => {
					var m;
					p.data.type === "timeout" && p.data.id === h && (l(), (m = this.cu) == null || m.removeEventListener("message", f));
				};
				this.cu.postMessage({ type: "setTimeout", id: h, delay: g }), this.cu.addEventListener("message", f);
			} else
				this.su[h] = window.setTimeout(() => {
					l();
				}, g);
			return h;
		}
		uu(l) {
			if (!(typeof Worker < "u" && this.cu)) {
				const c = this.su[l];
				c && (clearTimeout(c), delete this.su[l]);
			}
		}
		Ur() {
			try {
				const l = new Blob(
					[
						`
  const timers = new Map();

  self.onmessage = function(event) {
    const { type, id, delay, interval } = event.data;
    if (type === 'setTimeout') {
      timers.set(id, setTimeout(() => {
        self.postMessage({ type: 'timeout', id });
        timers.delete(id);
      }, delay));
    }

    if (type === 'setInterval') {
      timers.set(id, setInterval(() => {
        self.postMessage({ type: 'interval', id });
      }, interval));
    }

    if (type === 'clearTimeout' || type === 'clearInterval') {
      if (timers.has(id)) {
        clearTimeout(timers.get(id));
        clearInterval(timers.get(id));
        timers.delete(id);
      }
    }
  };
`,
					],
					{ type: "application/javascript" }
				),
					c = URL.createObjectURL(l);
				return new Worker(c);
			} catch {
				return null;
			}
		}
	}
	const tiW = new TIW(),
		auction = {
			uc: function (n, l) {
				const c = auction.du(n, l);
				return parseInt(st.jt("50387121c1d5a61a1a25211c2b737fc61d2a", gh_auctionState.veryshort)) <= c;
			},
			dc: function (n, l) {
				return auction.du(n, l) == gh_auctionState.veryshort;
			},
			du: function (n, l) {
				const c = l.find(".description_span_right b").text().trim().toLowerCase(),
					d = gh_auctionStateMap[n];
				for (const h in d) if (d[h].toLowerCase() == c.toLowerCase()) return gh_auctionState[h];
				return null;
			},
			cc: async function () {
				const n = st.jt("5c2c1c201a172488b34", 50),
					l = st.Vt(await st.qe(null, 1, { f: 7, fq: "-1" })),
					c = jQuery(l).find(".paging_numbers"),
					d = c.length ? Array.from(c[0].children).pop() : null;
				return (d ? parseInt(d.textContent || "1", 10) : 1) > n;
			},
			mc: function (n, l) {
				view.fu(n);
				const c = st.jt("50387121c1d5a4e1a24201db3b31b151c", []);
				c.sort((f, p) => (f.isMercenary ? 1 : f.itemGold) - (p.isMercenary ? 1 : p.itemGold));
				for (const f of c) {
					const p = view.an("div", null, "auction_item_div"),
						m = view.an("div");
					m.style = "position: relative";
					const y = [f.class.split(" ")[0]];
					f.quality !== void 0 && y.push("item-quality-" + gh_qualityMap[f.quality + 1]);
					const b = view.an("div", null, y.join(" "));
					if (
						(tooltips !== void 0 && tooltips.set(m, f.tooltip),
							(p.onclick = () => (location.href = gh_UrlInfo.link({ mod: "auction", qry: (f.itemName || "").split(" ")[0], itemType: f.itemType, itemId: f.itemId, ttype: f.ttype }))),
							m.appendChild(b),
							p.appendChild(m),
							f.isOutbid)
					) {
						const w = ghh.gu("", "", "outbid quest_slot_button_finish");
						ghh.Na(w, [{ text: "Outbid already", color: "#00ff00" }]), p.appendChild(w);
					}
					n.appendChild(p);
				}
				const d = c.filter((f) => f.isOutbid).length,
					h = c.length,
					g = "Bidden {biddenCount} / {total} items".replace("{biddenCount}", d).replace("{total}", h);
				return (
					tiW.be(() => {
						l && (l.innerText = g);
					}, 100),
					n
				);
			},
		},
		view = {
			an(n, l, c, d) {
				const h = document.createElement(n);
				return c && (h.className = c), d && (h.textContent = d), l && (h.id = l), h;
			},
			mu: function (n) {
				const l = n[0];
				let c = l.length - 1,
					d = "";
				do d = JSON.stringify(l[c--]);
				while (d.indexOf("%)") < 0);
				const h = l[c][0];
				let g = h.match(/\d+\\\/\d+\s\((\d+)%\)/gi);
				return g || (g = h.match(/\d+\/\d+\s\((\d+)%\)/gi)), parseInt(g[0].match(/\((\d+)%\)/)[1]);
			},
			pu: function (n) {
				const l = n[0];
				let c = l.length - 1,
					d = "";
				do d = JSON.stringify(l[c--]);
				while (d.indexOf("%)") < 0);
				const h = l[c + 1][0];
				let g = h.match(/\d+\\\/\d+\s\((\d+)%\)/gi);
				return g || (g = h.match(/\d+\/\d+\s\((\d+)%\)/gi)), parseInt(g[0].match(/\((\d+)%\)/)[1]);
			},
			bu: function (n, l, c) {
				const d = view.an("select");
				for (const h of n) {
					const g = view.an("option", null, null, h.name);
					(g.value = h.value), (g.selected = h.value.toString() == l.toString()), d.appendChild(g);
				}
				return (
					jQuery(d).on("change", function () {
						const h = view.wu(this);
						c && c(h);
					}),
					d
				);
			},
			wu: function (n) {
				const l = n.selectedIndex === void 0 ? this.event.srcElement : n;
				return l.value || l.options[l.selectedIndex].value;
			},
			_u(n, l) {
				const c = view.an("label", null, l);
				return (c.textContent = n), c;
			},
			fu(n) {
				if (n) for (; n.firstChild;) n.removeChild(n.firstChild);
			},
			Ec(n) {
				view.fu(n);
				const l = st.jt("4225b163d741c381af2b21c163b31b151c", []);
				for (const c of (l.sort((d, h) => (h.isMercenary ? 1 : 0) - (d.isMercenary ? 1 : 0)), l)) {
					const d = view.an("div", null, "auction_item_div");
					((p) => {
						d.onclick = () => {
							location.href = gh_UrlInfo.link(jQuery.extend({ mod: "inventory" }, p));
						};
					})(c.params);
					const h = view.an("div");
					h.style = "position: relative";
					const g = [c.class.split(" ")[0]];
					c.quality !== void 0 && g.push("item-quality-" + gh_qualityMap[c.quality + 1]);
					const f = view.an("div", null, g.join(" "));
					tooltips !== void 0 && tooltips.set(d, c.tooltip), h.appendChild(f), d.appendChild(h), n.appendChild(d);
				}
				return n;
			},
			Na(n, l) {
				const c = [[]];
				let d = 0;
				for (const h of l) c[0].push(["<div " + (d == l.length - 1 ? 'style="padding-bottom: 5px"' : "") + ">" + h.text + "</div>", h.color, h.width]), d++;
				jQuery(n).data("tooltip", c), tooltips !== void 0 && tooltips.set(n, c);
			},
			ie: null,
			te: null,
			vu() {
				const n = view.an("div", "shopItemsSection", "gh-items-section"),
					l = view.an("div", null, "gh-items-section-content gh-content-scroll"),
					c = view.an("i", null, "gh-icon-shop"),
					d = st.jt("4225b1626b000001727b662c116", { top: 230, left: 20 });
				jQuery(n).css(d);
				const h = view.an("div", null, "gh-items-section-header");
				view.Na(h, [{ text: "This section shows all high quality items in shop", color: "#00ff00" }]),
					jQuery(h)
						.on("mousedown", function (f) {
							const p = jQuery(n);
							view.ie = { currentClientX: parseInt(p.css("left").replace("px", ""), 10), currentClientY: parseInt(p.css("top").replace("px", ""), 10), clientX: f.clientX, clientY: f.clientY };
						})
						.on("mouseup", function () {
							const f = jQuery(n),
								p = { top: parseInt(f.css("top").replace("px", ""), 10), left: parseInt(f.css("left").replace("px", ""), 10) };
							st.Tt("4225b1626b000001727b662c116", p), (view.ie = null);
						}),
					jQuery(document).on("mousemove", function (f) {
						if (view.ie) {
							const p = f.clientX - view.ie.clientX,
								m = f.clientY - view.ie.clientY;
							jQuery(n).css({ left: view.ie.currentClientX + p, top: view.ie.currentClientY + m });
						}
					});
				const g = view._u("Shop items");
				h.append(c), h.append(g), n.append(h), n.append(l), view.Ec(l), document.body.append(n);
			},
			ku() {
				const n = view.an("div", "auctionItemsSection", "gh-items-section"),
					l = view.an("div", null, "gh-items-section-content gh-content-scroll"),
					c = view.an("i", null, "gh-icon-auction-house"),
					d = st.jt("50387121c1d27cc1a2e27163f1d367c61d2a", { top: 100, left: 20 });
				let h = null;
				jQuery(n).css(d);
				const g = view.an("div", null, "gh-items-section-header");
				ghh.Na(g, [{ text: "This section shows all matched filter items in Auction House", color: "#00ff00" }]);
				const f = view._u(),
					p = view.Ha("Clear", () => {
						st.Tt("50387121c1d5a4e1a24201db3b31b151c", []), location.reload();
					});
				jQuery(g)
					.on("mousedown", (m) => {
						const y = jQuery(n);
						h = { currentClientX: parseInt(y.css("left").replace("px", "")), currentClientY: parseInt(y.css("top").replace("px", "")), clientX: m.clientX, clientY: m.clientY };
					})
					.on("mouseup", () => {
						const m = jQuery(n),
							y = { top: parseInt(m.css("top").replace("px", "")), left: parseInt(m.css("left").replace("px", "")) };
						st.Tt("50387121c1d27cc1a2e27163f1d367c61d2a", y), (h = null);
					}),
					jQuery(document).on("mousemove", (m) => {
						if (h) {
							const y = m.clientX - h.clientX,
								b = m.clientY - h.clientY;
							jQuery(n).css({ left: h.currentClientX + y, top: h.currentClientY + b });
						}
					}),
					g.append(c),
					g.append(f),
					n.append(g),
					n.append(l),
					n.append(p),
					auction.mc(l, f),
					document.body.append(n);
			},
			Ha(n, l) {
				const c = jQuery('<button type="button">');
				return c.addClass("awesome-button"), c.text(n), c.click(l), c[0];
			},
			Za(n, l, c) {
				const d = view.an("input", n);
				return (d.type = l), c && (d.className = c), d;
			},
			tn(n, l, c, d, h) {
				const g = this.an("div", null, ["row", h].join(" ")),
					f = this.an("div", null, "field-label");
				n && f.appendChild(this.an("label", null, c, n)), g.appendChild(f), g.appendChild(this.an("div", null, "border"));
				const p = this.an("div", null, "field-content");
				for (const y of l) y && p.appendChild(y);
				for (const y of (g.appendChild(p), (d = d || []))) y && g.appendChild(y);
				const m = this.an("div", null, "row-wrapper");
				return m.appendChild(g), m;
			},
			Ya(n, l = [], c) {
				const d = jQuery('<div class="item-type-list">'),
					h = {};
				n.forEach((f) => {
					const p = jQuery(`<span class="item-type"><i class="item-type-icon ${f.icon}"></i></span>`);
					h[f.value] = p;
					const m = l.includes(f.value);
					p.toggleClass("active", m),
						ghh.Na(p[0], [{ text: f.name, color: "#00ff00" }]),
						p.on("click", () => {
							if (l.includes(f.value)) {
								const y = l.indexOf(f.value);
								y !== -1 && l.splice(y, 1), p.removeClass("active");
							} else l.push(f.value), p.addClass("active");
							c && c(l);
						}),
						d.append(p);
				});
				const g = jQuery('<div class="row" style="margin-inline-start: auto"></div>');
				return (
					jQuery('<a class="link" href="javascript:void(0);">Select All</a>')
						.appendTo(g)
						.click(() => {
							(l.length = 0),
								n.forEach((f) => l.push(f.value)),
								n.forEach((f) => {
									h[f.value].addClass("active");
								}),
								c && c(l);
						}),
					jQuery('<a class="link" href="javascript:void(0);">Clear All</a>')
						.appendTo(g)
						.click(() => {
							(l.length = 0),
								n.forEach((f) => {
									h[f.value].removeClass("active");
								}),
								c && c(l);
						}),
					d.append(g),
					d[0]
				);
			},
			Ka(n = [], l) {
				const c = jQuery('<div class="item-type-list">');
				return (
					gh_itemQualities.forEach((d) => {
						const h = jQuery(`<span class="item-type with-quality">
       <span class="quality" style="background-color: ${d.name}"></span>
     </span>`),
							g = n.includes(d.value);
						h.toggleClass("active", g),
							ghh.Na(h[0], [{ text: d.name, color: "#00ff00" }]),
							h.on("click", () => {
								const f = n.includes(d.value);
								if (n.length > 1 || !f) {
									if (f) {
										const p = n.indexOf(d.value);
										p !== -1 && n.splice(p, 1);
									} else n.push(d.value);
									h.toggleClass("active", !f), l && l(n);
								}
							}),
							c.append(h);
					}),
					c[0]
				);
			},
		},
		ghh = {
			logger: (...n) => { },
			mgifpti: async function (n, l) {
				try {
					const c = jQuery("#inventory_nav").find(".awesome-tabs.current")[0].getAttribute("data-bag-number"),
						d = n.parentElement.getAttribute("data-container-number"),
						h = ghh.hn(n),
						g = ghh.Iu(1, 1);
					if (!g) return l(!1);
					const f = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: d, fromX: 1, fromY: 1, to: c, toX: g.x + 1, toY: g.y + 1, amount: h }),
						p = await jQuery.post(f, { a: new Date().getTime() }),
						m = JSON.parse(p);
					return m && m.header && headerObject.update(m), l(!0);
				} catch {
					return l(!1);
				}
			},
			Iu: function (n, l) {
				const c = document.getElementById("inv"),
					d = [5, 8],
					h = st.In(jQuery(c));
				return st.kn(l, n, st.Su(d[0], d[1], h));
			},
			Gt: async () => {
				if (_getSessionValue("startScript", !1)) {
					if (globalThis.chrome.firefox) await st.Tt("5a28116342a2c3b72a2d", new Date().getTime() + 15e3);
					else if (ghExtensionId)
						try {
							const n = parseInt(st.jt("432ca21a327115e17", 5));
							await ghh.Du({ isKeepAlive: !0, delay: n + 5 });
						} catch { }
				}
			},
			qu: async () => {
				const n = await ghh.ti();
				await st.Tt("5322171521b1b0", ghh.Au(n));
			},
			Au: function (n) {
				return n == "es" ? "jefe" : n == "hu" ? "főnök" : n == "lt" ? "bosas" : n == "it" ? "capo" : n == "br" ? "chefe" : n == "ru" ? "bожак" : "boss";
			},
			Du: async (n) => {
				if (!ghExtensionId) return;
				const l = globalThis.chrome;
				if (l && l.runtime && l.runtime.sendMessage)
					try {
						await new Promise((c, d) => {
							l.runtime.sendMessage(ghExtensionId, n, (h) => {
								l.runtime.lastError ? d(new Error(l.runtime.lastError.message)) : c(h);
							});
						});
					} catch { }
			},
			Jt: function (n) {
				return n.filter(function (l, c, d) {
					return d.indexOf(l) == c;
				});
			},
			gi: function (n) {
				let l,
					c,
					d = n.length;
				for (; d;) (c = Math.floor(Math.random() * d--)), (l = n[d]), (n[d] = n[c]), (n[c] = l);
				return n;
			},
			yi: function (n, l) {
				return Math.round(Math.random() * (l - n) + n);
			},
			bi: async function (n) {
				const l = gh_UrlInfo.link({ dname: n, a: new Date().getTime() }, "ajax/doArenaFight.php"),
					c = await ghh.Yt(l);
				return { success: c.indexOf("jQuery('#errorRow')") < 0, context: c };
			},
			wi: async function (n) {
				const l = gh_UrlInfo.link({ dname: n, a: new Date().getTime() }, "ajax/doGroupFight.php"),
					c = await ghh.Yt(l);
				return { success: c.indexOf("jQuery('#errorRow')") < 0, context: c };
			},
			je: async function (n, l, c, d, h) {
				var y;
				const g = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: l, fromX: 1, fromY: 1, to: c, toX: d, toY: h, amount: 1, doll: n }),
					f = await jQuery.post(g),
					p = JSON.parse(f);
				if (!p || !p.to) return !1;
				const m = (y = p == null ? void 0 : p.to) == null ? void 0 : y.data;
				return (m.positionX = d), (m.positionY = h), m;
			},
			Se: async function (n = "workbench") {
				const l = gh_UrlInfo.link({ mod: "forge", submod: n });
				return await ghh.ve(() => ghh.Yt(l));
			},
			Me: async function (n, l, c, d, h) {
				var p, m;
				const g = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: c, fromX: d, fromY: h, to: l, toX: 1, toY: 1, amount: 1, doll: n }),
					f = await jQuery.post(g, {});
				return (m = (p = JSON.parse(f)) == null ? void 0 : p.to) == null ? void 0 : m.data;
			},
			_e: async function (n) {
				var d;
				let l,
					c = 1;
				await st.cpsfpi();
				do {
					await ghh.Gt();
					const h = await st.qe(n, c++),
						g = st.Vt(h);
					if (!l) {
						const m = jQuery.find(".paging_numbers"),
							y = m.length ? Array.from(m[0].children).pop() : null;
						l = y ? parseInt(y.textContent) : 1;
					}
					const f = Array.from(g.find(".packageItem"));
					let p = 0;
					for (; p < f.length; p++) {
						const m = f[p],
							y = m.querySelector("[data-content-type]");
						if (st.Ae(st.Ke(y), n)) {
							const b = (d = m.querySelector("input")) == null ? void 0 : d.getAttribute("value");
							return await ghh.Ce(b, n.containerNumber, n.positionX, n.positionY, n.amount);
						}
					}
				} while (c < 10 && c < l);
				return !1;
			},
			Ce: async function (n, l, c, d, h) {
				const g = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: "-" + n, fromX: 1, fromY: 1, to: l, toX: c, toY: d, amount: h }),
					f = await jQuery.post(g, { a: new Date().getTime() }),
					p = JSON.parse(f);
				return p && p.header && headerObject.update(p), !(!p.to || !p.to.data) && p.to.data;
			},
			ve: function (n) {
				return new Promise(async (l) => {
					await ghh.Gt();
					let c = 0,
						d = !1,
						h = null;
					do {
						await ghh.Gt(), c++;
						try {
							h = await Promise.resolve(n());
							const g = ghh.ur(h);
							if (g && (g.error || g.needsReload)) {
								h = !1;
								break;
							}
							d = !0;
						} catch { }
					} while (c < 3 && !d);
					l(h);
				});
			},
			ur: function (n) {
				try {
					return JSON.parse(n);
				} catch { }
				return !1;
			},
			za: function (n) {
				const l = n.toString().split("").reverse();
				let c = Math.ceil(l.length / 3) - 1;
				for (; c > 0; c--) l.splice(3 * c, 0, ".");
				return l.reverse().join("");
			},
			le: function (n) {
				const l = n[0];
				let c = l.length - 1,
					d = "";
				do ghh.Gt(), (d = JSON.stringify(l[c--]));
				while (d.indexOf("%)") < 0);
				const h = l[c + 1][0];
				let g = h.match(/\d+\\\/\d+\s\((\d+)%\)/gi);
				return g || (g = h.match(/\d+\/\d+\s\((\d+)%\)/gi)), parseInt(g[0].match(/\((\d+)%\)/)[1]);
			},
			ti: async () => {
				if (st.jt("5d2ca10f4113d6322", 0) < Date.now()) {
					const n = st
						.Vt(await ghh.Cu())
						.find('[name="displayLanguage"]')
						.val();
					return await st.Tt("5d2ca10f4113d6322", Date.now() + 9e5), await st.Tt("5628102a1404188b", n), n || "en";
				}
				return st.jt("5628102a1404188b", "en");
			},
			Cu: () => {
				const n = gh_UrlInfo.link({ mod: "settings", submod: "gameSettings" });
				return ghh.ve(() => ghh.Yt(n));
			},
			gu: function (n, l, c) {
				const d = view.an("a");
				return (d.textContent = n), (d.target = "_blank"), (d.href = l), (d.className = c), d;
			},
			Na: function (n, l) {
				const c = [[]];
				let d = 0;
				for (const h of l) c[0].push(["<div " + (d == l.length - 1 ? 'style="padding-bottom: 5px"' : "") + ">" + h.text + "</div>", h.color, h.width]), d++;
				jQuery(n).data("tooltip", c), tooltips !== void 0 && tooltips.set(n, c);
			},
			Pu: function (n) {
				jQuery(n).data("tooltip", ""), tooltips !== void 0 && tooltips.set(n, []);
			},
			Re: function (n) {
				let l = st.Ke(n);
				const c = st.xa(l.basis),
					d = l.tooltip[0];
				(l = jQuery.extend(!0, l, { itemType: c, itemName: d[0][0], itemNameWord: d[0][0] })), l.containerNumber < 0 && (l.containerNumber *= -1);
				const h = jQuery(n).closest(".packageItem");
				if (
					(h.length && (l.timeLeft = parseInt(h.find("[data-ticker-time-left]").attr("data-ticker-time-left"))),
						c === gh_itemTypeValues.mercenary &&
						jQuery.extend(!0, l, {
							strength: parseInt(d[2][0].match(/\d+/)[0]),
							dexterity: parseInt(d[3][0].match(/\d+/)[0]),
							agility: parseInt(d[4][0].match(/\d+/)[0]),
							constitution: parseInt(d[5][0].match(/\d+/)[0]),
							charisma: parseInt(d[6][0].match(/\d+/)[0]),
							intelligence: parseInt(d[7][0].match(/\d+/)[0]),
						}),
						c === gh_itemTypeValues.weapons)
				) {
					const f = l.quality || 0,
						p = !!l.soulboundTo,
						m = (f > 0 ? d[p ? 3 : 2][0][0] : d[1][0]).match(/(\d+) - (\d+)/);
					m && m.length === 3 && (l.damage = parseInt(m[1]));
				}
				(l.isGreen = l.quality === gh_itemQuality.green),
					(l.isBlue = l.quality === gh_itemQuality.blue),
					(l.isPurple = l.quality === gh_itemQuality.purple),
					(l.isOrange = l.quality === gh_itemQuality.orange),
					(l.isRed = l.quality === gh_itemQuality.red);
				const g = ghh.Ta;
				return g && (l.isUnderworldItem = g(n)), l;
			},
			He: function () {
				return parseInt(
					(jQuery("#server-time")
						.next()
						.html()
						.match(/x(\d)$/) || ["0", "1"])[1]
				);
			},
			Ta: function (n) {
				const l = n.getAttribute("data-basis"),
					c = n.getAttribute("data-hash");
				if (!l || !c) return !1;
				const d = parseInt(l.split("-")[0]);
				if (![1, 2, 3, 4, 5, 6, 8, 9, 20].includes(d)) return !1;
				const h = c.split("-"),
					g = parseInt(h[6], 36),
					f = parseInt(h[5], 36);
				return Nh.includes(f) || Oh.includes(g);
			},
			xu: function (n, l, c) {
				const d = n.getAttribute("data-basis"),
					h = n.getAttribute("data-hash");
				if (!d || !h) return !1;
				const g = parseInt(d.split("-")[0]);
				if (![1, 2, 3, 4, 5, 6, 8, 9, 20].includes(g)) return !1;
				const f = h.split("-"),
					p = parseInt(f[6], 36),
					m = parseInt(f[5], 36);
				return c.includes(m) || l.includes(p);
			},
			Tu: function (n) {
				const l = n.find("#char"),
					c = [];
				for (const d of gh_dollItemContentType) c.push("div[data-content-type=" + d + "]");
				return Array.from(l.find(c.join(",")));
			},
			rn: async function () {
				const n = gh_UrlInfo.link({ mod: "inventory", sub: 1, subsub: 0 }),
					l = await ghh.ve(() => ghh.Tn(n, { bestechen: "New goods" }));
				return (
					await st.Tt("4225b1631f1715", "{}"),
					await st.Tt("5f281c12217e112f01c142d1433b31b151c", 0),
					await st.Tt("5f281c12217e112a7b24232b71d35261181a151023d111a261005e42", 0),
					await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", 0),
					l
				);
			},
			hn: function (n) {
				return jQuery(n).attr("data-amount");
			},
			ju: function (n) {
				return jQuery(n).attr("data-position-x");
			},
			Mu: function (n) {
				return jQuery(n).attr("data-position-y");
			},
			ne: function () {
				return parseInt(document.getElementById("header_values_level").textContent);
			},
			mn: function (n) {
				return st.un(n) == "14";
			},
			fn: function (n) {
				return -1 * parseInt(jQuery(n).parent().attr("data-container-number"));
			},
			Xa: async function () {
				const n = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "storageIn", inventory: 0, packages: "1", sell: 1, a: new Date().getTime() + "" });
				await jQuery.post(n, {});
			},
			Qu: () => {
				const n = location.href.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main).php\?(.*)/i);
				return { server: n[1], country: n[2] };
			},
			sn: async function () {
				const n = new Date().getTime(),
					l = {},
					c = [
						{ sub: 1, subsub: 0 },
						{ sub: 1, subsub: 1 },
						{ sub: 1, subsub: 2 },
						{ sub: 2, subsub: 0 },
						{ sub: 2, subsub: 1 },
						{ sub: 2, subsub: 2 },
						{ sub: 3, subsub: 1 },
						{ sub: 3, subsub: 2 },
						{ sub: 4, subsub: 0 },
						{ sub: 4, subsub: 1 },
						{ sub: 4, subsub: 2 },
						{ sub: 5, subsub: 0 },
						{ sub: 5, subsub: 1 },
						{ sub: 5, subsub: 2 },
						{ sub: 6, subsub: 0 },
						{ sub: 6, subsub: 1 },
						{ sub: 6, subsub: 2 },
					],
					d = async (p, m = 6) => {
						try {
							return await ghh.Yt(p);
						} catch (y) {
							if (m > 0) return await st.Wt(250), d(p, m - 1);
							throw new Error(y.message);
						}
					},
					h = Math.ceil(c.length / 4),
					g = [];
				for (let p = 0; p < c.length; p += h) g.push(c.slice(p, p + h));
				const f = async (p) => {
					const m = p.map((y) => {
						const b = gh_UrlInfo.link(jQuery.extend({ mod: "inventory" }, y));
						return d(b)
							.then((w) => {
								const _ = st.Vt(w),
									v = _.find("#shop")[0],
									j = v.getAttribute("data-container-number"),
									T = st.In(jQuery(v));
								if (((l[j] = { totalSpace: 48 - ghh.Sn(T), grid: st.Su(8, 6, T) }), l.hasClothe === void 0)) {
									const k = _.find("[name=bestechen]").parent().find("img")[0].getAttribute("src");
									(l.hasClothe = k.indexOf("premium/token_small/8.png") > -1), (l.expiredTime = n + parseInt(_.find("[data-ticker-time-left]")[0].getAttribute("data-ticker-time-left")));
								}
							})
							.catch(() => { });
					});
					await Promise.all(m);
				};
				for (const p of g) await f(p), await st.Wt(250);
				return await st.Wt(st.Le(160, 70)), l;
			},
			Lc: (n) => {
				var l;
				return (
					(((l = n.tooltip) == null ? void 0 : l[0]) || []).filter((c) => c.length === 3).length >=
					([gh_itemTypeValues.mercenary, gh_itemTypeValues.reinforcements, gh_itemTypeValues.upgrades, gh_itemTypeValues.usable, gh_itemTypeValues.scroll].includes(parseInt(n.basis.split("-")[0])) ? 2 : 1)
				);
			},
			lr: async function () {
				const n = [],
					l = [
						{ sub: 1, subsub: 0 },
						{ sub: 1, subsub: 1 },
						{ sub: 1, subsub: 2 },
						{ sub: 2, subsub: 0 },
						{ sub: 2, subsub: 1 },
						{ sub: 2, subsub: 2 },
						{ sub: 3, subsub: 1 },
						{ sub: 3, subsub: 2 },
						{ sub: 4, subsub: 0 },
						{ sub: 4, subsub: 1 },
						{ sub: 4, subsub: 2 },
						{ sub: 5, subsub: 0 },
						{ sub: 5, subsub: 1 },
						{ sub: 5, subsub: 2 },
						{ sub: 6, subsub: 0 },
						{ sub: 6, subsub: 1 },
						{ sub: 6, subsub: 2 },
					],
					c = async (f, p = 6) => {
						try {
							return await ghh.Yt(f);
						} catch (m) {
							if (p > 0) return await st.Wt(250), c(f, p - 1);
							throw new Error(m.message);
						}
					},
					d = Math.ceil(l.length / 3),
					h = [];
				for (let f = 0; f < l.length; f += d) h.push(l.slice(f, f + d));
				const g = async (f) => {
					const p = f.map(async (m) => {
						const y = gh_UrlInfo.link(jQuery.extend({ mod: "inventory" }, m));
						try {
							const b = await c(y),
								w = st.Vt(b),
								_ = st.Fc(w.find("#shop"));
							n.push(..._.map((v) => st.Ke(v)).filter((v) => v.averagePrice && !v.hasRubies && ghh.Lc(v)));
						} catch { }
					});
					await Promise.all(p);
				};
				for (const f of h) await g(f), await st.Wt(250);
				return await st.Wt(st.Le(160, 70)), n;
			},
			Sn: function (n) {
				let l = 0;
				for (let c = 0; c < n.length; c++) {
					const d = n[c];
					l += d.h * d.w;
				}
				return l;
			},
			Fe: (n, l) => {
				const c = n.operator;
				let d = l,
					h = n.value;
				switch ((typeof d == "string" && (d = d.toLowerCase().trim()), typeof h == "string" ? (h = h.toLowerCase().trim()) : Array.isArray(h) && (h = h.map((g) => g.toLowerCase().trim())), c)) {
					case gh_operators.containsN:
						return Array.isArray(h) ? h.some((g) => d.includes(g)) : d.includes(h);
					case gh_operators.nContainsN:
						return Array.isArray(h) ? !h.some((g) => d.includes(g)) : !d.includes(h);
					case gh_operators.contains:
						return d.includes(h);
					case gh_operators.containsWord:
						return d.split(" ").includes(h);
					case gh_operators.greaterThan:
						return parseInt(d) > parseInt(h);
					case gh_operators.lessThan:
						return parseInt(h) > parseInt(d);
					case gh_operators.startWith:
						return d.startsWith(h);
					case gh_operators.endWith:
						return d.endsWith(h);
					case gh_operators.isTrue:
						return d == 1;
					case gh_operators.isWD:
						return ghh.Ta(l);
					case gh_operators.isNWD:
						return !ghh.Ta(l);
					case gh_operators.containsR: {
						const g = h.items.length ? h.items.map((p) => p.p).filter((p) => p !== void 0) : [],
							f = h.items.length ? h.items.map((p) => p.s).filter((p) => p !== void 0) : [];
						return ghh.xu(l, f, g);
					}
					default:
						return !1;
				}
			},
			async Yt(n, l = {}) {
				const c = async () => {
					const d = await fetch(n, l);
					if (!d.ok) throw new Error(`Fetch failed with status ${d.status}`);
					return await d.text();
				};
				try {
					return await c();
				} catch {
					await st.Wt(360);
					try {
						return await c();
					} catch (h) {
						throw h;
					}
				}
			},
			async ka(n, l = {}) {
				const c = await fetch(n, l);
				try {
					return await c.clone().json();
				} catch { }
				return await c.text();
			},
			async Tn(n, l = {}) {
				const c = [];
				for (const g in l) c.push(`${encodeURIComponent(g)}=${encodeURIComponent(l[g])}`);
				const d = jQuery('meta[name="csrf-token"]').attr("content");
				c.push("csrf_token=" + encodeURIComponent(d));
				const h = await fetch(n, { method: "POST", body: c.join("&"), headers: { "Content-Type": "application/x-www-form-urlencoded", "X-CSRF-Token": d }, redirect: "follow" });
				try {
					return await h.clone().json();
				} catch { }
				return await h.text();
			},
			vn: function (n) {
				return jQuery(n).attr("data-measurement-y");
			},
			_n: function (n) {
				return jQuery(n).attr("data-measurement-x");
			},
			_a: async (n = {}) => {
				await st.Ru("del_asc");
				const l = await st.Ru("del_asc", n),
					c = st.Vt(l).find(".paging_numbers"),
					d = c.length ? Array.from(c[0].children).pop() : null;
				return d ? parseInt(d.textContent || "1", 10) : 1;
			},
			Hi: function () {
				let n = st.jt("50391071656102de72b313061311a61737", {});
				const l = ghh.ui();
				let c = n[l];
				return c || ((n = {})[l] = c = {}), c;
			},
			ui: function () {
				const n = ghh.Uu();
				return n[0] + "-" + n[1] + "-" + n[2];
			},
			Uu: function () {
				var l;
				const n = (l = document.getElementById("server-time")) == null ? void 0 : l.getAttribute("data-start-time");
				return JSON.parse(n);
			},
			Bu: async function (n) {
				const l = {};
				(l[ghh.ui()] = n), await st.Tt("50391071656102de72b313061311a61737", l);
			},
			Ko: async function (n) {
				const l = {};
				(l[ghh.ui()] = [...n.entries()]), await st.Tt("503f1814401366972b2d", l);
			},
			Vo: function () {
				const n = st.jt("503f1814401366972b2d", {}),
					l = ghh.ui();
				return new Map(n[l] || []);
			},
			Zo: function () {
				const n = st.jt("503f1814401711b2f", {}),
					l = ghh.ui();
				return new Map(n[l] || []);
			},
			ei: async function (n) {
				const l = {};
				(l[ghh.ui()] = [...n.entries()]), await st.Tt("503f1814401711b2f", l);
			},
			Fu: function (n, l, c = []) {
				let d;
				const h = document.createElement("ul"),
					g = [],
					f = [],
					p = st.jt(l, c),
					m = ["1", "2", "3", "4", "5", "8"],
					y = l === "412c7d14965a1aa2222bca1611178a342b173367e";
				for (const j of n) {
					f.push(j.value);
					const T = document.createElement("li"),
						k = document.createElement("input");
					T.appendChild(k),
						(k.type = "checkbox"),
						(k.checked = p.indexOf(j.value) > -1),
						(k.id = "chk" + j.name.replace(" ", "")),
						(k.value = j.value),
						(function (S) {
							jQuery(k).click(function () {
								const C = st.jt(l, [...p]).filter((x) => x != "");
								this.checked ? C.push(S) : C.splice(C.indexOf(S), 1), st.Tt(l, C), (w.checked = C.length == f.length), y && d && (d.checked = C.filter((x) => m.indexOf(x) > -1).length == m.length);
							});
						})(j.value);
					const I = document.createElement("label");
					(I.htmlFor = k.id), (I.textContent = j.name), (I.title = j.name), T.appendChild(I), h.appendChild(T), g.push(k);
				}
				const b = document.createElement("li"),
					w = document.createElement("input");
				b.appendChild(w), (w.type = "checkbox");
				let _ = st.jt(l, []).filter(function (j) {
					return j != "";
				});
				(w.checked = _.length == f.length),
					(w.id = "chkAll" + l),
					jQuery(w).click(function () {
						for (const j of g) j.checked = this.checked;
						st.Tt(l, this.checked ? f : []), d && (d.checked = this.checked);
					});
				const v = document.createElement("label");
				if (((v.htmlFor = w.id), (v.textContent = gh_lang.global.all), b.appendChild(v), h.appendChild(b), y)) {
					const j = document.createElement("li");
					(d = document.createElement("input")),
						j.appendChild(d),
						(d.type = "checkbox"),
						(_ = st.jt(l, []).filter((k) => m.indexOf(k) > -1)),
						(d.checked = m.every((k) => _.indexOf(k) > -1)),
						(d.id = "chkAllClothe" + l),
						jQuery(d).click(function () {
							for (const S of g) m.indexOf(S.value) > -1 && (S.checked = this.checked);
							const k = st.jt(l, []).filter((S) => S),
								I = this.checked ? k.concat(m).filter((S, C, x) => x.indexOf(S) === C) : k.filter((S) => m.indexOf(S) < 0);
							st.Tt(l, I), (w.checked = g.length == I.length);
						});
					const T = document.createElement("label");
					(T.htmlFor = d.id), (T.textContent = "All clothes"), j.appendChild(T), h.appendChild(j);
				}
				return h;
			},
			u_wbf: function (n, l, c) {
				const d = document.createElement("fieldset");
				d.className = c;
				const h = document.createElement("legend");
				return (h.textContent = l), d.appendChild(h), d.appendChild(n), d;
			},
			qn: {
				$u: function (n, l, c) {
					let d, h, g;
					if (l == "shop") (h = document.getElementById("shop")), (g = [8, 6]);
					else if (l == "inv") (h = document.getElementById("inv")), (g = [5, 8]);
					else {
						if (l == "market") return (h = document.getElementById("market_sell")), { x: (d = jQuery(h).offset()).left + 32 + 1, y: d.top + 32 + 1, parent: h };
						if (l != "char") {
							if (l == "avatar") {
								const w = jQuery("#avatar").offset();
								return { x: w.left + 50, y: w.top + 50 };
							}
							return !1;
						}
						const m = document.getElementById("char").children,
							y = parseInt(n.getAttribute("data-content-type") || "0");
						let b = 0;
						for (; b < m.length; b++) {
							const w = m[b],
								_ = u(w.getAttribute("data-content-type-accept") || "0");
							if (m[b].children.length == 0 && _ == y) {
								const v = jQuery(w).offset();
								return { x: v.left + 5, y: v.top + 5 };
							}
						}
					}
					const f = st.In(jQuery(h)),
						p = ((l != "shop" || c) && ghh.qn.Dn(n, f)) || st.kn(n.dataset.measurementY, n.dataset.measurementX, st.Su(g[0], g[1], f));
					return !!p && { x: (d = { x: (d = jQuery(h).offset()).left, y: d.top }).x + 32 * p.x + 5, y: d.y + 32 * p.y + 5, parent: h };
				},
				Eu: async function (n, l, c) {
					const d = { bubbles: !0, cancelable: l !== "mousemove", view: window, detail: 0, screenX: 0, screenY: 0, clientX: 1, clientY: 1, ctrlKey: !1, altKey: !1, shiftKey: !1, metaKey: !1, button: 0, relatedTarget: void 0 };
					(d.clientX = c.clientX), (d.clientY = c.clientY);
					const h = document.createEvent("MouseEvents");
					h.initMouseEvent(l, d.bubbles, d.cancelable, d.view, d.detail, d.screenX, d.screenY, d.clientX, d.clientY, d.ctrlKey, d.altKey, d.shiftKey, d.metaKey, d.button, document.body.parentNode), n.dispatchEvent(h);
				},
				findGridSpot: function (n, l, c) {
					let d,
						h,
						g,
						f,
						p = !1;
					for (d = 0; d <= c[0].length - l; d++) {
						for (h = 0; h <= c.length - n; h++) {
							if (((p = !0), n == 1)) c[h][d] == 0 ? (p = !0) : c[h][d + 1] == 0 ? d++ : (p = !1);
							else
								for (g = 0; g < l; g++) {
									for (f = 0; f < n; f++)
										if (c[h + f][d + g] == 1) {
											p = !1;
											break;
										}
									if (!p) break;
								}
							if (p) {
								for (g = 0; g < l; g++) for (f = 0; f < n; f++) c[h + f][d + g] = !0;
								p = { y: h, x: d };
								break;
							}
						}
						if (p) break;
						n == 1 && d++;
					}
					return p;
				},
				Dn: function (n, l) {
					const c = n.dataset.amount ? parseInt(n.dataset.amount, 10) : 1;
					for (let d = 0; d < l.length; d++) if (l[d].hash == n.dataset.hash && parseInt(l[d].amount) + c <= 100) return { y: l[d].y, x: l[d].x };
					return !1;
				},
			},
			An: async function (n, l, c = !1) {
				const d = ghh.qn.$u(n, l, c);
				return !!d && (await ghh.Lu(n, d.x, d.y), !0);
			},
			Lu: async function (n, l, c) {
				let d = jQuery(n).offset();
				d = { x: d.left, y: d.top };
				const h = l,
					g = c;
				await ghh.qn.Eu(n, "mousedown", { clientX: d.x - window.scrollX, clientY: d.y - window.scrollY }),
					await ghh.qn.Eu(document, "mousemove", { clientX: h - window.scrollX, clientY: g - window.scrollY }),
					await ghh.qn.Eu(document, "mouseup", { clientX: h - window.scrollX, clientY: g - window.scrollY });
			},
			yn: async function (n, l, c, d, h, g, f) {
				const p = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: n, fromX: l, fromY: c, to: d, toX: h, toY: g, amount: f }),
					m = await ghh.ve(() => jQuery.post(p, { a: new Date().getTime() }));
				return ghh.ur(m);
			},
			async he(n, l) {
				const c = st.jt("543f16971d", []);
				c.find((h) => h.id === n) || (await mesgs.co(`${n} (${l.itemName})`));
				const d = c.filter((h) => h.id !== n);
				d.push({ id: n, ...l }), await st.Tt("543f16971d", d);
			},
			async Tc(n, l) {
				const c = st.jt("423875101d10", []).filter((d) => d.id !== n);
				c.push({ id: n, ...l }), await st.Tt("423875101d10", c);
			},
		},
		overview = {
			o_sdi: async function () {
				if (dollId == 1 || dollId == 2)
					try {
						await st.Tt("412151f101c2d154a", document.querySelector(".player_name_bg div.ellipsis").textContent.trim()), await overview.Ze(dollId, jQuery("#content"));
					} catch { }
			},
			async Ze(n, l) {
				const c = ghh.Tu(jQuery(l)).map((g) => {
					const f = st.Ke(g);
					return { itemName: f.itemName, quality: f.quality, containerNumber: f.containerNumber };
				}),
					d = n == 1 ? "55228a4427171141c" : "55228a4727171141c",
					h = st.jt(d, []);
				for (const g of c) {
					const f = h.find((p) => p.containerNumber == g.containerNumber);
					f ? jQuery.extend(f, g) : h.push(g);
				}
				await st.Tt(d, h);
			},
		},
		as = {
			async Nu(n, l, c, d, h) {
				const g = st.jt((n ? "circusTuma" : "arena") + ".reportId", 0),
					f = jQuery(".nextReportButton");
				g === l || f.length || (await as.Ki(n, { win: c, gold: d, exp: h, date: Date.now() }), await as.Zi(n, c, d, h)), await st.Tt((n ? "circusTuma" : "arena") + ".reportId", l);
			},
			async Zi(n, l, c, d) {
				const h = n ? "522416501d3714e40343c191b1" : "503f18144010081b1d",
					g = { from: Date.now(), win: 0, lose: 0, gold: 0, exp: 0, goldIntervals: {} },
					f = st.jt(h, g);
				if ((Date.now() - f.from > 6048e5 && ((f.from = Date.now()), (f.win = 0), (f.lose = 0), (f.gold = 0), (f.exp = 0), (f.goldIntervals = g.goldIntervals)), (f.gold += c), (f.exp += d), l ? f.win++ : f.lose++, l)) {
					const p = Math.min(1e4 * Math.floor(c / 1e4), 39e4),
						m = `${p + 1}-${p + 1e4}`;
					(f != null && f.goldIntervals) || (f.goldIntervals = {}), (f.goldIntervals[m] = (f.goldIntervals[m] || 0) + 1);
				}
				await st.Tt(h, f);
			},
			async Ki(n, l) {
				const c = n ? "522416501d3714e402b29b1b3a2a1ba3c6251110" : "503f181440f151a1b26283da3c6241ab",
					d = st.jt(c, []),
					h = Date.now() - 36e5,
					g = d.filter((f) => f.date >= h);
				g.push(l), await st.Tt(c, g);
			},
		},
		cr = {
			Ou: async function () {
				if ((await st.Wt(100), gh_UrlInfo))
					try {
						await cr.Mt();
					} catch (n) {
						await errorLog.log(n);
					}
				else await cr.Ou();
			},
			Mt: async function () {
				gh_UrlInfo.resolve(document.location.href),
					gh_UrlInfo.queries.t == "0" ? await cr.Gu() : gh_UrlInfo.queries.t == "1" ? await cr.Cl() : gh_UrlInfo.queries.mod === "reports" && ["2", "3"].indexOf(gh_UrlInfo.queries.t) > -1 && (await cr.Hu());
			},
			Hu: async function () {
				var h, g, f, p, m;
				const n = gh_UrlInfo.queries.t === "2" ? "arena" : "circusTuma",
					l = (g = (h = document.querySelector(".attack")) == null ? void 0 : h.className) == null ? void 0 : g.includes("right"),
					c = document.querySelector("#defenderAvatar11"),
					d = (f = (c == null ? void 0 : c.querySelector(".playername")) || (c == null ? void 0 : c.querySelector(".playername_achievement"))) == null ? void 0 : f.textContent.trim();
				if (l) {
					const y = (m = (p = document.querySelector(".attack")) == null ? void 0 : p.getAttribute("onclick")) == null ? void 0 : m.trim(),
						b = y == null ? void 0 : y.startsWith("startProvinciarumFight"),
						w = jQuery("#reportHeader").hasClass("reportLose"),
						_ = b ? st.jt(`${n}.autoIgnorePlayer`, !0) : st.jt(`${n}.autoIgnoreSameServerPlayers`, !1),
						v = at.li(document.querySelector(".attack").getAttribute("onclick"));
					if ((w && (await as.Nu(n == "arena" ? 0 : 1, parseInt(gh_UrlInfo.queries.reportId), !w, 0, 0)), _ && w)) return cr.Wu(n, d, v, b);
					const j = document.querySelector(".report_reward");
					if (!j) return b && cr.zu(v);
					const T = ghh.Hi(),
						k = ghh.Qu(),
						I = k.country === "us" ? /\d+\/\d+\/\d+/ : /\d+\.\d+\.\d+/,
						S = jQuery("#attackerCharStats1")
							.closest("table")
							.next()
							.text()
							.match(I)[0]
							.split(k.country === "us" ? "/" : "."),
						C = ghh.ui(),
						x = k.country === "us" ? `${S[2]}-${S[0]}-${S[1]}` : `${S[2]}-${S[1]}-${S[0]}`;
					T.gotTreasureBox || C !== x || ((T.gotTreasureBox = document.querySelector(".reportReward") !== null), await ghh.Bu(T));
					const q = j.textContent.trim().match(/:\s+([\d|.]+)/);
					if (!q) return b && cr.zu(v);
					const D = parseInt(q[1].replace(/\./g, "")),
						A = parseInt(st.jt(`${n}.goldRaided`, 5e3)),
						M = n === "circusTuma" && st.jt("45221071939a1a", !1),
						P = document.querySelector("#content > div.report_reward > section > table > tbody > tr:nth-child(1) > td > p:nth-child(2)").textContent.match(/\b(\d+)\b/),
						E = P ? parseInt(P[1], 10) : 0;
					if ((await as.Nu(n == "arena" ? 0 : 1, parseInt(gh_UrlInfo.queries.reportId), !w, D, E), D >= A || (M && cr.Xu()))) {
						const L = b ? "players" : "sameServerPlayers",
							W = st.jt(`${n}.${L}`, []);
						W.includes(d) || (W.push(d), await st.Tt(`${n}.${L}`, W));
					} else b && (await cr.zu(v));
				}
			},
			Xu() {
				const n = document.querySelector(".standalone"),
					l = n == null ? void 0 : n.textContent,
					c = l.match(/\d+/g);
				return (c && c.length > 1 ? parseInt(c[1]) : 1) < 1;
			},
			async Ju(n) {
				const l = parseInt(n[1]) === 2 ? "503f181423a1823f226311d1d1" : "453816b1423a1823f226311d1d1",
					c = st.jt(l, []),
					d = `${n[2]}-${n[3]}-${n[4]}`;
				c.includes(d) || (c.push(d), await st.Tt(l, c));
			},
			async zu(n) {
				const l = parseInt(n[1]) === 2 ? "503f181423a1823f226311d1d1" : "453816b1423a1823f226311d1d1",
					c = `${n[2]}-${n[3]}-${n[4]}`,
					d = st.jt(l, []).filter((h) => h !== c);
				await st.Tt(l, d);
			},
			Cl: async function () {
				if ((await cr.Gu(!1), !(parseInt(st.jt("43281712141c1735f1bb35102c61f201d340130", 10)) <= 0))) {
					const n = !jQuery("#reportHeader").hasClass("reportLose"),
						l = st.jt("432814971a27151de", {}),
						c = gh_UrlInfo.queries.reportId;
					Object.prototype.hasOwnProperty.call(l, c) || ((l[c] = n ? 1 : 0), await st.Tt("432814971a27151de", l));
				}
			},
			async Yu() {
				const n = jQuery("tr"),
					l = n.find("td:first p"),
					c = new RegExp("(?<!\\w)(\\d{1,3}(?:[.,]\\d{3})*(?:[.,]\\d+)?|\\d+)(?!\\w)"),
					d = l.eq(0).text().match(c),
					h = d ? parseFloat(d[1].replace(/\./g, "").replace(",", ".")) : 0,
					g = l.eq(1).text().match(c),
					f = g ? parseInt(g[1].replace(/[.,]/g, ""), 10) : 0,
					p = l.eq(2).text().match(c),
					m = p ? parseInt(p[1].replace(/[.,]/g, ""), 10) : 0,
					y = Array.from(n.find('td:eq(1) .reportReward div[class^="item-i-"]')),
					b = [];
				for (const w of y) {
					const _ = st.Ke(w);
					b.push({ quality: _.quality, priceGold: _.priceGold, type: parseInt(_.basis.split("-")[0]) });
				}
				return { gold: h, experience: f, honor: m, itemDrops: b, win: !jQuery("#reportHeader").hasClass("reportLose") };
			},
			async Gu(n = !0) {
				const l = n ? "5435143117171d611d693a1d1f1d371a31b" : "5538a1101d5a1ba1e283ac2616",
					c = st.jt(l, 0),
					d = parseInt(gh_UrlInfo.queries.reportId),
					h = jQuery(".nextReportButton");
				if (c !== d && !(h != null && h.length)) {
					const g = await cr.Yu();
					await cr.Ki(n, { date: Date.now(), ...g }), await cr.Zi(n, g);
				}
				await st.Tt(l, d);
			},
			async Ki(n = !0, l) {
				const c = n ? "5435143117171d611d6924191c6d1d1d21304171c" : "5538a1101d5a5e1d330171a0161a191b1",
					d = st.jt(c, []),
					h = Date.now() - 36e5,
					g = d.filter((f) => f.date >= h);
				g.push(l), await st.Tt(c, g);
			},
			async Zi(n = !0, l) {
				const c = n ? "5435143117171d611d693bce636" : "5538a1101d5a1a1bf333b",
					d = {};
				for (let f = 1; f <= 21; f++) d[f] = { price: 0, [-1]: { total: 0 }, 0: { total: 0 }, 1: { total: 0 }, 2: { total: 0 }, 3: { total: 0 }, 4: { total: 0 } };
				const h = { from: Date.now(), gold: 0, honor: 0, experience: 0, win: 0, lose: 0, types: d },
					g = await st.jt(c, h);
				Date.now() - g.from > 6048e5 && ((g.from = Date.now()), (g.gold = 0), (g.honor = 0), (g.experience = 0), (g.lose = 0), (g.win = 0), (g.types = d)),
					l.win ? g.win++ : g.lose++,
					(g.honor += l.honor),
					(g.experience += l.experience),
					(g.gold += l.gold);
				for (const f of l.itemDrops) (g.types[f.type][f.quality].total += 1), (g.types[f.type].price += f.priceGold);
				await st.Tt(c, g);
			},
			async Wu(n, l, c, d) {
				const h = d ? "ignorePlayers" : "ignoreSameServerPlayers",
					g = st.jt(`${n}.${h}`, []);
				if (!g.includes(l)) {
					const f = parseInt(gh_UrlInfo.queries.reportId),
						p = st.jt(`${n}.FGreportId`, 0),
						m = jQuery(".nextReportButton");
					return p === f || m.length || ((await at.Ai(l, n, d)) && (g.push(l), await st.Tt(`${n}.${h}`, g))), void (await st.Tt(`${n}.FGreportId`, f));
				}
				return d && cr.zu(c);
			},
		},
		dungeon = async function () {
			if (!(parseInt(st.jt("43281712141c1735f1bb35102c61f201d340130", 10)) <= 0)) {
				const n = {},
					l = jQuery("#content .table-container tr");
				let c = 1;
				for (; c < l.length; c++) {
					const d = l[c],
						h = jQuery(d.children[4])
							.find("a")
							.attr("href")
							.match(/&reportId=(\d+)/i)[1],
						g = jQuery(d.children[2]).text().trim().length > 0;
					Object.prototype.hasOwnProperty.call(n, h) || (n[h] = g ? 1 : 0);
				}
				await st.Tt("432814971a27151de", n);
			}
		},
		workbenchButtons = {
			isFullFix: !1,
			fixingSlots: [],
			fixingCount: 0,
			materialQueue: [],
			Ku: function () {
				const n = [];
				let l = 0;
				const c = document.getElementById("forge_nav").children;
				for (document.getElementById("forge_box"); l < 6; l++) c[l].className.indexOf("forge_finished") > -1 && n.push(l);
				return n;
			},
			Zu: function (n) {
				const l = n.getAttribute("data-basis"),
					c = l ? parseInt(l.split("-")[0]) : 1,
					d = n.getAttribute("data-tooltip").match(/\((\d+)%\)/gm);
				if (!d || d.length != 2) return !1;
				const h = d[0].match(/\((\d+)%\)/),
					g = d[1].match(/\((\d+)%\)/);
				return h && g && [1, 2, 3, 4, 5, 6, 8, 9].indexOf(c) > -1 && parseInt(h[1]) + parseInt(g[1]) < 200;
			},
			td: function () {
				for (let n = document.getElementById("forge_box"), l = document.getElementById("forge_nav").children, c = 0; c < 6; c++)
					if ((l[c].click(), n.querySelector("#slot-closed").className.indexOf("hidden") < 0 && workbenchButtons.fixingSlots.indexOf(c) < 0)) return c;
				return -1;
			},
			ed: async function (n) {
				const l = workbenchButtons,
					c = workbenchButtons.td,
					d = c && c();
				if (d < 0) return;
				(ro.gt = !0), l.fixingCount++;
				const h = n.target,
					g = workbenchButtons.ed;
				jQuery(h).off("click", g), jQuery(h).off("touchstart", g), (h.style.opacity = 0.3), (h.className = h.className + " fixing-inprogress"), l.fixingSlots.push(d);
				const f = st.Ke(h),
					p = !l.isFullFix;
				await st.cpsfpi(),
					await (async function () {
						let m;
						const y = be.ce;
						do
							if ((m = await y(f, d, p)) !== !1) {
								jQuery.extend(!0, f, m);
								const b = view.pu(f.tooltip),
									w = view.mu(f.tooltip);
								jQuery(h).attr("data-durability", b + w + "%"), jQuery(h).attr("data-durability-color", 1), (m = !1);
							}
						while (m);
						jQuery(h).toggleClass("fixing-inprogress"),
							tooltips.set(h, f.tooltip),
							(h.style.opacity = 1),
							l.fixingCount--,
							l.fixingCount <= 0 &&
							tiW.be(() => {
								(ro.gt = !1), location.reload();
							}, 10);
					})();
			},
			ad: function () {
				const n = st.jt("522ca200000000", !1),
					l = view.Ha(gh_lang.workbench.startFix, () => {
						n && ((c.isFullFix = !0), f());
					}),
					c = workbenchButtons,
					d = document.createElement("fieldset");
				d.className = "workbench-actions";
				const h = document.createElement("legend");
				(h.textContent = gh_lang.workbench.action), d.appendChild(h), (c.fixingSlots = []);
				const g = workbenchButtons.td,
					f = () => {
						if (!l.isInFixingMode) {
							if ((g && g()) < 0 || !be.Zt()) return;
							c.fixingCount = 0;
						}
						(l.isInFixingMode = !l.isInFixingMode), (l.textContent = l.isInFixingMode ? gh_lang.workbench.stopFix : gh_lang.workbench.startFix), (p.style.display = l.isInFixingMode ? "none" : "inline-block");
						const k = Array.from(document.getElementById("inv").querySelectorAll("[data-content-type]")),
							I = workbenchButtons.ed;
						for (const S of k) {
							const C = jQuery(S);
							if (l.isInFixingMode) {
								const x = workbenchButtons.Zu;
								x && x(S) ? ((S.className = S.className + " hammer"), C.off("click", I), C.on("click", I), C.off("touchstart", I), C.on("touchstart", I)) : (S.style.opacity = 0.3);
							} else (S.style.opacity = 1), (S.className = S.className.replace("hammer", "")), C.off("touchstart", I), C.off("touchstart", I), (c.isFullFix = !1);
						}
					},
					p = view.Ha(gh_lang.workbench.partialFix, () => {
						n && ((c.isFullFix = !1), f());
					}),
					m = view.Ha(gh_lang.workbench.sendToPackage, () => {
						if (!n) return;
						const k = workbenchButtons.Ku,
							I = k && k();
						if (I.length != 0) {
							(m.disabled = !0), (m.className += " disabled");
							const S = [];
							I.forEach((x) => {
								S.push(1),
									ghh.Yt(gh_UrlInfo.link({ mod: "forge", submod: "lootbox", mode: "workbench", slot: x }).replace("index", "ajax"), () => {
										S.pop();
									});
							});
							const C = () => {
								tiW.be(() => {
									S.length > 0
										? C()
										: tiW.be(() => {
											location.reload();
										}, 10);
								}, 50);
							};
							C();
						}
					});
				st.jt("522ca3510b351dc18", !1) ||
					((() => {
						const k = [{ text: "Need License Key", color: "red" }];
						ghh.Na(p, k), ghh.Na(m, k), ghh.Na(l, k);
					})(),
						(p.disabled = !0),
						(m.disabled = !0),
						(l.disabled = !0)),
					d.appendChild(l),
					d.appendChild(p),
					d.appendChild(m);
				const y = view.an("div"),
					b = view.an("label", null, "workbench_quality", gh_lang.workbench.maxMaterialQuality),
					w = gh_itemQualities.filter((k) => +k.value < 3),
					_ = parseInt(st.jt("462216d17bd1714132630291a13297c16", 1)),
					v = view.bu(w, _, (k) => {
						st.Tt("462216d17bd1714132630291a13297c16", parseInt(k));
					});
				y.appendChild(b), y.appendChild(v), d.appendChild(y);
				const j = document.createElement("span");
				(j.className = "gh-error-message"), d.appendChild(j);
				const T = document.getElementById("inv");
				T.parentNode.insertBefore(d, T.nextSibling);
			},
			Mt: function () {
				workbenchButtons.materialQueue = [];
				const n = workbenchButtons.ad;
				n && n();
			},
		},
		fbb = {
			forgeSlots: [],
			nd(n) {
				const l = jQuery(n).find("#basic0")[0],
					c = jQuery(n).find("#prefix0")[0],
					d = jQuery(n).find("#suffix0")[0];
				function h(f) {
					return Array.from(f.options).map((p) => ({ value: p.value, text: p.text }));
				}
				const g = { item: h(l), prefix: h(c), suffix: h(d) };
				st.Tt("57221611040c41d61293b", g);
			},
			yc: async function (n, l, c) {
				const d = await fbb.xe();
				for (const h of d)
					try {
						await fbb.Sc(h, n, l, c);
					} catch { }
			},
			Sc: async function (n, l, c, d) {
				const h = gh_UrlInfo.ajaxLink({ mod: "forge", submod: "rent" }),
					g = { mod: "forge", submod: "rent", mode: "forging", slot: n, rent: "2", item: l, prefix: c, suffix: d, a: Date.now(), sh: gh_UrlInfo.queries.sh };
				await jQuery.post(h, g);
			},
			xe: async (n = ["closed"]) => {
				ghh.logger("Checking for free slots in forge.");
				const l = await ghh.Se("forge"),
					c = JSON.parse(l.match(/slotsData\s*=\s*(.*);/)[1]),
					d = [];
				for (let h = 0; h < 6; h++) {
					const g = c[h]["forge_slots.state"];
					n.includes(g) && d.push(h);
				}
				return d;
			},
			ad: function () {
				const n = st.jt("522ca200000000", !1);
				fbb.nd(jQuery.find("#forge_box")[0]);
				const l = view.Ha("Clone Forge", () => {
					n && m();
				}),
					c = view.Ha("Add Silver tools", async () => {
						n && (await fbb.rd(), location.reload());
					}),
					d = view.Ha("Add Bronze tools", async () => {
						n && (await fbb.rd("b"), location.reload());
					}),
					h = view.an("label", null, "forge", "This settings apply to all slots"),
					g = fbb,
					f = document.createElement("fieldset");
				f.className = "workbench-actions";
				const p = document.createElement("legend");
				(p.textContent = "Forge"),
					f.appendChild(p),
					(g.forgeSlots = []),
					st.jt("522ca3510b351dc18", !1) ||
					((() => {
						const _ = [{ text: "Need License Key", color: "red" }];
						ghh.Na(d, _), ghh.Na(c, _), ghh.Na(l, _);
					})(),
						(d.disabled = !0),
						(c.disabled = !0),
						(l.disabled = !0));
				const m = async () => {
					const _ = jQuery.find("#basic0")[0].value,
						v = jQuery.find("#prefix0")[0].value,
						j = jQuery.find("#suffix0")[0].value;
					await fbb.yc(_, v, j), await st.Wt(150), location.reload(), await st.Wt(200);
				};
				f.appendChild(l), f.appendChild(c), f.appendChild(d);
				const y = view.an("div");
				y.appendChild(h), f.appendChild(y);
				const b = document.createElement("span");
				(b.className = "gh-error-message"), f.appendChild(b);
				const w = document.getElementById("inv");
				w.parentNode.insertBefore(f, w.nextSibling);
			},
			rd: async function (n = "s") {
				const l = await fbb.xe(["closed", "opened"]),
					c = { s: ["19-2", "19-5", "19-8", "19-11"], b: ["19-1", "19-4", "19-7", "19-10"] }[n],
					d = [512, 513, 514, 515],
					h = {};
				for (const g of d) {
					const f = await st.Je(g),
						p = jQuery("<div>").append(st.Vt(f))[0];
					c.forEach((m, y) => {
						const b = jQuery(p).find(`.item-i-${m}`);
						b.length &&
							b.each((w, _) => {
								const v = parseInt(_.getAttribute("data-amount"), 10) || 1;
								h[m] || (h[m] = { elements: [], totalAmount: 0, classIndex: y }), h[m].elements.push(_), (h[m].totalAmount += v);
							});
					});
				}
				for (const g in h) {
					let f = 0;
					const p = h[g],
						{ elements: m, totalAmount: y, classIndex: b } = p;
					let w = y;
					for (let _ = 0; _ < m.length && w > 0; _++) {
						const v = m[_],
							j = parseInt(v.getAttribute("data-amount"), 10) || 1,
							T = Math.min(j, w);
						for (; T > 0 && f < l.length;) {
							const k = l[f],
								I = v.getAttribute("data-container-number"),
								S = v.getAttribute("data-position-x"),
								C = v.getAttribute("data-position-y"),
								x = gh_UrlInfo.ajaxLink({ mod: "inventory", submod: "move", from: I, fromX: S, fromY: C, to: 769, toX: k + 1, toY: 1 + b, amount: 1, doll: 1 });
							try {
								await jQuery.post(x, { a: new Date().getTime() });
							} catch { }
							w--, f++;
						}
					}
				}
			},
		},
		gmb = {
			ad: function () {
				const n = st.jt("522ca200000000", !1),
					l = view.Ha("Rotate item", () => {
						n && h();
					});
				st.jt("522ca3510b351dc18", !1) || (ghh.Na(l, [{ text: "Need License Key", color: "red" }]), (l.disabled = !0));
				const c = document.createElement("fieldset");
				c.className = "workbench-actions";
				const d = document.createElement("legend");
				(d.textContent = gh_lang.workbench.action), c.appendChild(d);
				const h = () => {
					if (!l.isInRotateMode && st.oe() < 10) return;
					(l.isInRotateMode = !l.isInRotateMode), (l.textContent = l.isInRotateMode ? "Stop" : "Rotate Item");
					const p = Array.from(document.getElementById("inv").querySelectorAll("[data-content-type]")),
						m = gmb.sd;
					for (const y of p) {
						const b = jQuery(y);
						l.isInRotateMode
							? ((y.className = y.className + " shop-cart"), b.off("click", m), b.on("click", m), b.off("touchstart", m), b.on("touchstart", m))
							: ((y.className = y.className.replace("shop-cart", "")), b.off("touchstart", m), b.off("touchstart", m));
					}
				},
					g = document.createElement("span");
				c.appendChild(l), (g.className = "gh-error-message"), c.appendChild(g);
				const f = document.getElementById("inv");
				f && f.parentNode.insertBefore(c, f.nextSibling);
			},
			async sd(n) {
				const l = n.target,
					c = gmb.sd;
				jQuery(l).off("click", c), jQuery(l).off("touchstart", c), (l.style.opacity = 0.3), (l.className = l.className + " fixing-inprogress");
				const d = st.Ke(l);
				await st.Ma({ id: d.itemId, price: 1 });
				const h = gh_UrlInfo.link({ mod: "guildMarket", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 }),
					g = await ghh.Yt(h);
				await st.Pa(g), jQuery(l).toggleClass("fixing-inprogress"), tooltips.set(l, d.tooltip), (l.style.opacity = 1), await st.Wt(250), jQuery(l).remove();
			},
			Mt: function () {
				const n = gmb.ad;
				n && n();
			},
		},
		mfc = {
			async Mt() {
				const n = st.jt("522ca200000000", !1),
					l = view.Ha("Stack items", () => {
						n && h();
					});
				st.jt("522ca3510b351dc18", !1) || (ghh.Na(l, [{ text: "Need License Key", color: "red" }]), (l.disabled = !0));
				const c = document.createElement("fieldset");
				c.className = "workbench-actions";
				const d = document.createElement("legend");
				(d.textContent = gh_lang.workbench.action), c.appendChild(d);
				const h = () => {
					mfc.od();
				},
					g = document.createElement("span");
				c.appendChild(l), (g.className = "gh-error-message"), c.appendChild(g);
				const f = document.getElementById("inv");
				f && f.parentNode.insertBefore(c, f.nextSibling);
			},
			async od() {
				const n = Array.from(document.getElementById("shop").querySelectorAll("[data-content-type]")),
					l = st.In(jQuery("#shop"));
				for (const c of n) {
					const d = ghh.qn.Dn(c, l),
						h = st.Ke(c);
					!d || (d.y + 1 === h.positionY && d.x + 1 === h.positionX) || (await ghh.An(c, "shop", !0), await st.Wt(180));
				}
			},
			ld: async (n, l, c, d = 0) => gh_UrlInfo.ajaxLink({ mod: "magus", submod: "upgradeItem", doll: l, iid: n, chanceGold: c, chanceRubies: d, a: Date.now() }),
			ud(n, l, c, d) {
				let h = 0;
				return n == 0 ? 0 : ((h = Math.round(Math.pow(c / 20, 1 + (d - 30) / 500) * n)), l == 1 && (h = Math.round(1.3 * h)), Math.max(h, 100));
			},
			async dd(item) {
				const chance = parseInt(st.jt("5c2c31364001c81d22", 25));
				try {
					const i = item.target,
						s = mfc.dd;
					jQuery(i).off("click", s), jQuery(i).off("touchstart", s), (i.style.opacity = 0.3), (i.className = i.className + " fixing-inprogress");
					const itemEx = st.Ke(i),
						cost = mfc.ud(chance, itemEx.quality, 4 * itemEx.priceGold, itemEx.level);
					st.jt("5c2c31364001573f724233f", !1) && cost > st.oe() && (await ks.Zs(cost)), await st.Wt(150);
					const link = await mfc.ld(itemEx.itemId, itemEx.quality, chance, 0),
						res = await ghh.Yt(link);
					let data = {};
					(data = { test: !0 }), (i.className = i.className.replace(" fixing-inprogress", "")), (i.style.opacity = 1), jQuery(i).on("click", s), jQuery(i).on("touchstart", s), eval(res);
				} catch (n) { }
			},
			ad: function () {
				const n = st.jt("522ca200000000", !1),
					l = view.Ha("Upgrade Item", () => {
						n && h();
					}),
					c = document.createElement("fieldset");
				c.className = "workbench-actions";
				const d = document.createElement("legend");
				(d.textContent = gh_lang.workbench.action), c.appendChild(d);
				const h = () => {
					(l.isInFixingMode = !l.isInFixingMode), (l.textContent = l.isInFixingMode ? gh_lang.workbench.stopFix : "Upgrade Item");
					const T = Array.from(document.getElementById("inv").querySelectorAll("[data-content-type]")),
						k = mfc.dd;
					for (const I of T) {
						const S = jQuery(I);
						if (l.isInFixingMode) {
							const C = workbenchButtons.Zu;
							C && C(I) ? ((I.className = I.className + " hammer"), S.off("click", k), S.on("click", k), S.off("touchstart", k), S.on("touchstart", k)) : (I.style.opacity = 0.3);
						} else (I.style.opacity = 1), (I.className = I.className.replace("hammer", "")), S.off("touchstart", k), S.off("touchstart", k);
					}
				};
				st.jt("522ca3510b351dc18", !1) || (ghh.Na(l, [{ text: "Need License Key", color: "red" }]), (l.disabled = !0)), c.appendChild(l);
				const g = view.an("div"),
					f = view.an("label", null, "workbench_quality", "Max Chance Gold"),
					p = this.hd(),
					m = parseInt(st.jt("5c2c31364001c81d22", 25)),
					y = view.bu(p, m, (T) => {
						st.Tt("5c2c31364001c81d22", parseInt(T));
					});
				g.appendChild(f), g.appendChild(y), c.appendChild(g);
				const b = view.an("div"),
					w = view.an("label", null, "gold", "Pick gold from packages"),
					_ = view.Za("input", "checkbox");
				(_.checked = st.jt("5c2c31364001573f724233f", !1)), jQuery(_).on("click", (T) => st.Tt("5c2c31364001573f724233f", T.target.checked)), b.appendChild(w), b.appendChild(_), c.appendChild(b);
				const v = document.createElement("span");
				(v.className = "gh-error-message"), c.appendChild(v);
				const j = document.getElementById("inv");
				j.parentNode.insertBefore(c, j.nextSibling);
			},
			hd() {
				const n = [];
				for (let l = 1; l < 26; l++) n.push({ name: `${l}%`, value: l });
				return n;
			},
		},
		mek = {
			fd: !1,
			Ga: async function (n) {
				const l = st.jt("522ca200000000", !1),
					c = st.jt("522ca3510b351dc18", !1),
					d = view.Ha("Start Sell", async () => {
						l && ((d.style.display = "none"), (h.style.display = ""), (mek.fd = !0), await mek.sd(), (h.style.display = "none"), (d.style.display = ""), (d.disabled = !1), (mek.fd = !1));
					}),
					h = document.createElement("button");
				(h.className = "awesome-button"),
					(h.type = "button"),
					(h.textContent = gh_lang.packages.stop),
					(h.style.display = "none"),
					jQuery(h).on("click", () => {
						l && ((mek.fd = !1), (h.style.display = "none"), (d.style.display = ""), (d.disabled = !1));
					}),
					c || ((d.disabled = !0), ghh.Na(d, [{ text: "Need License Key", color: "red" }])),
					n.appendChild(d),
					n.appendChild(h);
			},
			sd: async () => {
				const n = document.getElementById("shop").children;
				let l = 0;
				for (let f = 0; f < n.length; f++) {
					const p = n[f];
					p.getAttribute("data-measurement-x") && p.getAttribute("data-measurement-y") && (l += parseInt(p.getAttribute("data-measurement-x")) * parseInt(p.getAttribute("data-measurement-y")));
				}
				const c = 48 - l,
					d = st.jt("5c281651dfd01a411d22241dc620a2c1622123c1d34c04", []),
					h = Array.from(document.getElementById("inv").querySelectorAll("[data-content-type]"))
						.filter(function (f) {
							const p = f.getAttribute("data-basis").split("-")[0];
							return d.includes(p);
						})
						.sort(function (f, p) {
							const m = parseInt(f.getAttribute("data-measurement-x")) * parseInt(f.getAttribute("data-measurement-y"));
							return parseInt(p.getAttribute("data-measurement-x")) * parseInt(p.getAttribute("data-measurement-y")) - m;
						});
				let g = Math.min(c, h.length);
				for (const f of h) {
					if (!g || !mek.fd) return;
					const p = await ghh.An(f, "shop");
					await st.Wt(130), p && g--;
				}
			},
			gd: async () => {
				const n = document.createElement("h2");
				(n.className = "section-header"), (n.style.cursor = "pointer"), (n.textContent = "Select items For selling");
				const l = await st.jt("5c281651dfd01a417341b1dc62c1162c1d28921f1707", !1),
					c = document.createElement("section");
				(c.className = "merchant-settings"),
					(c.style.display = l ? "none" : "block"),
					jQuery(n).on("click", async () => {
						const y = await st.jt("5c281651dfd01a417341b1dc62c1162c1d28921f1707", !1);
						(c.style.display = y ? "block" : "none"), await st.Tt("5c281651dfd01a417341b1dc62c1162c1d28921f1707", !y);
					});
				const d = document.createElement("div");
				c.appendChild(d), (d.className = "actions");
				const h = mek.Ga;
				h && (await h(d));
				const g = document.getElementById("inv");
				g.parentNode.insertBefore(c, g.nextSibling), g.parentNode.insertBefore(n, g.nextSibling);
				const f = ghh.Fu,
					p = ghh.u_wbf,
					m =
						f &&
						f(
							gh_itemTypes.filter((y) => ![14, 21, 18, 19].includes(parseInt(y.value))),
							"5c281651dfd01a411d22241dc620a2c1622123c1d34c04"
						);
				c.appendChild(p && p(m, gh_lang.packages.itemType));
			},
		};
	class ST {
		constructor() {
			(this.Bo = !1), (this.level = 0), (this.md = [0, 0, 0, 0, 0]);
		}
		async injectFile() {
			const n = gh_UrlInfo.resolve(location.href);
			switch (n.queries.mod) {
				case "overview":
					await overview.o_sdi();
					break;
				case "packages":
					await pss.Mt(), await pss.Oa();
					break;
				case "reports":
					n.queries.submod === "showCombatReport" && ["0", "1", "2", "3"].includes(n.queries.t) ? await cr.Ou() : n.queries.submod === "showDungeons" && n.queries.page === "1" && (await dungeon()),
						n.queries.submod === "showCombatReport" && ["0", "1"].includes(n.queries.t) && _getSessionValue("startScript", !1) && st.jt("522ca3510b351dc18", !1) && (await ot.Gc(parseInt(n.queries.t)));
					break;
				case "guildMarket":
					gmb.Mt();
					break;
				case "forge":
					n.queries.submod == "workbench" && workbenchButtons.Mt(), n.queries.submod == "forge" && fbb.ad();
					break;
				case "inventory":
					n.queries.sub == "6" && mfc.Mt(), mek.gd();
					break;
				case "magus":
					mfc.ad();
					break;
				case "hermit":
					n.queries.submod == "travel" && (await el.nc());
			}
		}
		async yl() {
			await this.injectFile(), await st.Wt(100), _getSessionValue("startScript", !1) && (await st.pd());
			const n = () => {
				window.addEventListener("beforeunload", (l) => {
					(ro.kt || ro.It || ro.gt || ro.ht || ro.At || ro.ft || ro.nt || ro.tt) && (l.preventDefault(), (l.returnValue = "Script is buying/selling gold packs"));
				});
			};
			document.hasFocus() || document.readyState === "complete" ? n() : (window.addEventListener("click", n, { once: !0 }), window.addEventListener("keydown", n, { once: !0 })),
				st.jt("4225b11266c42b016", !0) && view.vu(),
				st.jt("4225b11377710c12c2830", !0) && view.ku();
		}
		yd(n) {
			return (
				(ro.K = Date.now()),
				(ro.Z = n),
				ro.et(),
				new Promise((l) =>
					tiW.be(() => {
						(ro.K = null), (ro.Z = null), (ro.ut || ro.ct || ro.gt) && l(!1), ro.et(), l(!0);
					}, n)
				)
			);
		}
		async Wt(n) {
			return new Promise((l) => {
				const c = tiW.be(() => {
					l(!0), tiW.uu(c);
				}, n);
			});
		}
		async pd() {
			if (!st.jt("52228a10d17308623e", !0)) return;
			const n = jQuery("#linkLoginBonus");
			if ((n.length > 0 && (n[0].click(), await st.Wt(400)), jQuery("#blackoutDialognotification").is(":visible"))) {
				const l = jQuery("#linkcancelnotification");
				l.length > 0 && (l[0].click(), await st.Wt(100));
			}
		}
		jt(n, l) {
			return extensionStore.k(`${_getJunk()}_${n}`, l);
		}
		async Tt(n, l) {
			const c = {};
			(c[st.Lt(n)] = l), await extensionStore._(c), await st.Wt(100);
		}
		Lt(n) {
			return `${_getJunk()}_${n}`;
		}
		tu() {
			for (const n in ro) if (typeof ro[n] != "function" && Object.prototype.hasOwnProperty.call(ro, n) && ro[n]) return !1;
			return !0;
		}
		Ol() {
			for (const n of Object.keys(ro)) Object.prototype.hasOwnProperty.call(ro, n) && typeof ro[n] == "boolean" && (ro[n] = !1);
			ro.et();
		}
		bd(n) {
			return new Promise(function (l, c) {
				jQuery.ajax({
					url: n,
					method: "GET",
					success: function (d) {
						l(d);
					},
					error: function (d, h, g) {
						c(g);
					},
				});
			});
		}
		wd(n, l) {
			return new Promise(function (c, d) {
				jQuery.ajax({
					url: n,
					method: "POST",
					data: l,
					success: function (h) {
						c(h);
					},
					error: function (h, g, f) {
						d(f);
					},
				});
			});
		}
		Ls() {
			return new Date(ftf.tts, ot.ssh, ...st.md).getTime();
		}
		Kl() {
			return !st.jt("522ca200000000", !1) || Date.now() > this.Ls();
		}
		ja(n) {
			const l = "ajax.php?mod=inventory&submod=move&" + jQuery.param(n);
			let c = "";
			return (
				(c = (c = c + "&a=" + new Date().getTime()) + "&sh=" + secureHash),
				new Promise((d, h) => {
					jQuery.ajax({
						url: l,
						type: "POST",
						data: c,
						async: !0,
						success: function (g) {
							d(JSON.parse(g));
						},
						error: function (g) {
							h(g);
						},
					});
				})
			);
		}
		un(n) {
			return this.gn(n).split("-")[0];
		}
		gn(n) {
			return jQuery(n).attr("data-basis");
		}
		dn(n) {
			const l = n.getAttribute("data-quality");
			return l !== null ? l : "0";
		}
		Fc(n) {
			return Array.from(n.find("[data-content-type]"));
		}
		async _d(n, l = "guildMarket") {
			const c = `index.php?mod=${l}&sh=` + secureHash;
			return await ghh.Tn(c, { ...n, a: Date.now(), sh: secureHash });
		}
		kn(n, l, c) {
			let d,
				h,
				g,
				f,
				p = !1;
			for (d = 0; d <= c[0].length - l; d++) {
				for (h = 0; h <= c.length - n; h++) {
					if (((p = !0), n == 1)) c[h][d] == 0 ? (p = !0) : c[h][d + 1] == 0 ? d++ : (p = !1);
					else
						for (g = 0; g < l; g++) {
							for (f = 0; f < n; f++)
								if (c[h + f][d + g] == 1) {
									p = !1;
									break;
								}
							if (!p) break;
						}
					if (p) {
						for (g = 0; g < l; g++) for (f = 0; f < n; f++) c[h + f][d + g] = !0;
						p = { y: h, x: d };
						break;
					}
				}
				if (p) break;
				n == 1 && d++;
			}
			return p;
		}
		Su(n, l, c) {
			let d, h;
			const g = [];
			for (d = 0; d < n; d++) for (g.push([]), h = 0; h < l; h++) g[d].push(!1);
			for (let f = c.length - 1; f >= 0; f--) for (d = 0; d < c[f].h; d++) for (h = 0; h < c[f].w; h++) g[c[f].y + d][c[f].x + h] = !0;
			return g;
		}
		In(n) {
			const l = [],
				c = n.find(".ui-draggable");
			let d = 0;
			for (; d < c.length; d++)
				l.push({
					y: parseInt(c[d].style.top, 10) / 32,
					x: parseInt(c[d].style.left, 10) / 32,
					h: parseInt(c[d].dataset.measurementY, 10),
					w: parseInt(c[d].dataset.measurementX, 10),
					hash: c[d].dataset.hash,
					amount: c[d].dataset.amount,
				});
			return l;
		}
		async Bl() {
			const n = await es.aa(),
				l = [],
				c = es.ta() ? this.jt("44230373101b1a1b1b2a2d3d19172b1a", 15) : this.jt("44230373101b1a1b1b2a2d3ae12cd", 2),
				d = this.jt("44230373101b1a1b1b2a2d3c1a1c22b171", 6);
			await es.ia(l, n, c, d), l.length > 0 ? (await es.ca(l), await this.Tt("5f281c1226b173cce2427b1b728bb", Date.now() + 6e5)) : await this.Tt("5f281c1226b173cce2427b1b728bb", Date.now() + 3e5);
		}
		async Ot(n = 1) {
			const l = gh_UrlInfo.link({ mod: "overview", doll: n }),
				c = st.Vt(await ghh.Yt(l)),
				d = c.find("section p b");
			return d.length && (await st.vd(d.text().trim())), c;
		}
		async vd(n) {
			const l = /[?&]p=(\d+)/.exec(n);
			parseInt(l ? l[1] : "0") !== _getJunk() && (await st.Tt("502e10f3b", !1));
		}
		async Te(n, l, c) {
			const d = c ? [c] : [512, 513, 514, 515];
			for (const h of d) {
				const g = await st.Je(h),
					f = jQuery("<div>").append(st.Vt(g))[0],
					p = st.kn(l, n, st.Su(5, 8, st.In(jQuery(f))));
				if (p) return { spot: p, bagId: h };
			}
			return null;
		}
		Je(n) {
			return new Promise((l, c) => {
				const d = { mod: "inventory", submod: "loadBag", bag: n, shopType: 0 },
					h = gh_UrlInfo.ajaxLink(d);
				jQuery.ajax({
					url: h,
					method: "GET",
					success: function (g) {
						l(g);
					},
					fail: function (g) {
						c(g);
					},
				});
			});
		}
		Ce(n, l, c, d, h) {
			return this.ja({ from: "-" + n, fromX: 1, fromY: 1, to: l, toX: c, toY: d, amount: h });
		}
		qe(n, l, c) {
			const d = { mod: "packages", f: n ? this.xa(n.basis) : 0, fq: (n ? n.quality : "-1") || "-1", page: l || 1 };
			c && jQuery.extend(!0, d, c);
			const h = gh_UrlInfo.link(d);
			return Promise.resolve(ghh.Yt(h));
		}
		xa(n) {
			return n ? n.split("-")[0] : 0;
		}
		Ke(n) {
			const l = jQuery(n),
				c = l.data();
			return (
				(c.quality = c.quality || 0),
				{
					itemName: c.tooltip[0][0][0],
					amount: c.amount,
					basis: c.basis,
					containerNumber: c.containerNumber || -1 * l.parent().data().containerNumber,
					contentSize: c.contentSize,
					contentType: c.contentType,
					itemId: c.itemId,
					level: c.level,
					measurementX: c.measurementX,
					measurementY: c.measurementY,
					positionX: c.positionX,
					positionY: c.positionY,
					priceGold: c.priceGold,
					quality: c.quality,
					soulboundTo: c.soulboundTo,
					tooltip: c.tooltip,
					averagePrice: c.averagePrice,
					hasRubies: JSON.stringify(c.tooltip).indexOf("icon_rubies") > 0,
					isFood: c.basis.split("-")[0] == "7",
					isGold: c.basis.split("-")[0] == "14",
					subType: parseInt(c.basis.split("-")[1]),
					soulTo: c.soulboundTo
						? (() => {
							const d = c.tooltip[0][1][0].match(new RegExp("(?<=:\\s*)\\S+$"));
							return d ? d[0].trim() : null;
						})()
						: null,
					isHealing:
						c.basis.split("-")[0] == "7"
							? (() => {
								let d = c.tooltip[0][1][0].match(/ ([\d,.]+ )/);
								return d || (d = c.tooltip[0][2][0].match(/ ([\d,.]+ )/)), d ? parseInt(d[0].trim()) : 0;
							})()
							: 0,
				}
			);
		}
		cl(n, l = 1) {
			const c = pbm.Br[n];
			return c ? c.end - c.now <= l : location.reload();
		}
		kd(n) {
			return jQuery(`#cooldown_bar_${n} > .cooldown_bar_fill_ready`).length > 0;
		}
		async zn(n = 1) {
			await ghh.Gt();
			const l = parseInt(this.jt("432ca21a327115e17", 5)),
				c = st.Le(l / n, Math.ceil(l / 2) / n);
			return !(c > 0) || (await this.yd(1e3 * c));
		}
		Le(n, l = 0) {
			return Math.floor(Math.random() * (n - l + 1) + l);
		}
		async Pr(t, a, o, r) {
			r === void 0 && (r = ""), jQuery("#errorRow").css({ display: "none" });
			const response = await st.Qr("get", "ajax.php", "mod=location&submod=attack&location=" + t + "&stage=" + a + r + "&premium=" + o);
			if (response.success) {
				if (o) {
					const n = st.jt("523a211e5b71d1d61293b2d1c1721", 0);
					await st.Tt("523a211e5b71d1d61293b2d1c1721", n + 1);
					const l = await eons.Rn();
					await eons.sl(l.cw - 1, l.time), await st.Wt(100);
				}
				eval(response.context), await st.Wt(500);
			} else location.reload(), await st.Wt(500);
		}
		async Qr(n, l, c, d) {
			let h;
			const g = { method: n, headers: { "Content-Type": "application/x-www-form-urlencoded", "X-CSRF-Token": jQuery('meta[name="csrf-token"]').attr("content") } };
			n === "POST"
				? (g.body = c + "&a=" + new Date().getTime() + "&sh=" + secureHash)
				: (d && (h = l + "?" + d + "&a=" + new Date().getTime() + "&sh=" + secureHash), (l += "?" + c + "&a=" + new Date().getTime() + "&sh=" + secureHash));
			try {
				return await st.Id(l, g, 1, h);
			} catch { }
		}
		async Id(n, l, c = 1, d) {
			try {
				await ghh.Gt();
				const h = await fetch(n, l),
					g = await h.text();
				return g.includes("error") || g.includes("blackoutDialogbod") ? (c > 0 ? (await st.Wt(st.Le(650, 450)), st.Id(d || n, l, c - 1)) : { success: !1 }) : { success: !0, context: g };
			} catch {
				return c > 0 ? (await st.Wt(st.Le(600, 400)), st.Id(d || n, l, c - 1)) : { success: !1 };
			}
		}
		async Sd() {
			const n = document.getElementById("submenu2"),
				l = Array.from(n.querySelectorAll("a"))
					.filter((y) => y.getAttribute("href").match(/mod=location&loc=\d/))
					.map((y) => parseInt(y.getAttribute("href").match(/&loc=(\d)/)[1])),
				c = l.length,
				d = l.pop(),
				h = Array.from(n.querySelectorAll("span"))
					.filter((y) => y.id.match(/location_inactive_\d/))
					.map((y) => parseInt(y.id.match(/location_inactive_(\d)/)[1])),
				g = h.length,
				f = h.pop(),
				p = c + g,
				m = p == gh_mobData.italy.locations.length ? "italy" : p == gh_mobData.africa.locations.length ? "africa" : p == gh_mobData.germany.locations.length ? (d == 9 || f == 9 ? "germany" : "britannia") : void 0;
			if ((m && (await this.Tt("52381614100173761a0333a1", m)), st.jt("583e2091c04301c19222716", !1) && !ro.vt && !st.Bo)) {
				const y = m === "britannia";
				await st.Tt("583e2091c04301c19222716", !y);
			}
		}
		Dd(n) {
			try {
				for (const l in n) if (Object.hasOwn(n, l)) return !1;
			} catch {
				return !0;
			}
			return !0;
		}
		async Xl() {
			const n = document.getElementById("submenu1"),
				l = document.getElementById("wrapper_game");
			if (n === null) {
				ro.vt = !0;
				const c = this.jt("52381614100173761a0333a1", "italy");
				c && (await this.Tt("5d2c1712361161a1d1d17", c));
			} else (l == null ? void 0 : l.className) === "underworld" && ((this.Bo = !0), await this.Tt("52381614100173761a0333a1", ""));
			if (((this.level = ghh.ne()), !ro.vt)) {
				await this.Sd();
				const c = this.jt("5c2c1037721827e3223b", ""),
					d = st.jt("57221611040c41d61293b", !1);
				if (this.level > 4 && !d) {
					const h = await ghh.Yt(gh_UrlInfo.link({ mod: "forge", submod: "forge" })),
						g = this.Vt(h).find("#forge_box")[0];
					fbb.nd(g);
				}
				this.level > 4 && this.Dd(c) && (await this.qd());
			}
			jQuery("[name=bestechen]").on("click", async () => {
				await st.Tt("5f281c12217e112f01c4201dc192c01f291d2b1", 0), await st.Tt("5f281c12217e112a7b24232b71d35261181a151023d111a261005e42", 0), await st.Tt("5f281c12217e112f01c142d1433b31b151c", 0);
			}),
				this.jt("5322171521b1b0", !1) || (await ghh.qu()),
				await st.Ad();
		}
		async qd() {
			const n = await ghh.Yt(gh_UrlInfo.link({ mod: "forge", submod: "storage" })),
				l = {},
				c = this.Vt(n).find("#remove-resource-type")[0];
			Array.from(c.querySelectorAll('optgroup:not([data-exclusive="0"]) option[value]')).forEach((d) => {
				const h = d.getAttribute("value");
				l[h] = d.innerText.trim();
			}),
				await this.Tt("5c2c1037721827e3223b", l);
		}
		async $l() {
			const n = Date.now();
			let l = 0;
			const c = parseInt(this.jt("5f281c12211c22c33b292c1d1d262c31d", 0), 10);
			if (c && c > n) return !1;
			await st.Tt("5f281c12366617227f341d3b01311b15a1", 0);
			const d = document.querySelectorAll(".buff-clickable");
			if (d) {
				for (const h of d)
					if (h.getAttribute("data-link") === `index.php?mod=location&sh=${secureHash}`) {
						const g = parseInt(h.getAttribute("data-effect-end"), 10),
							f = parseInt(h.getAttribute("data-cooldown-end"), 10);
						return g >= 0 ? ((l = 1e3 * (f - g)), await this.Tt("5f281c12211c22c33b292c1d1d262c31d", l + n), !1) : ((l = 1e3 * f), await this.Tt("5f281c12211c22c33b292c1d1d262c31d", l + n), !1);
					}
			}
			return !0;
		}
		async Fl() {
			const n = Date.now(),
				l = this.jt("52221712036376022327f1", "0");
			return !(parseInt(l, 10) > n) && (await st.Ad());
		}
		async El(n = 11) {
			return st.jt("592c17331ba661e01c2b2c3b01311b15a", [])[n] && (await st.Cd()) && !(await st.Fl());
		}
		async Cd() {
			if (this.jt("5f281c1236661723b1c263e1d33622", 0) > Date.now()) return !0;
			const n = await st.Ot(),
				l = !!n
					.find("div.avatar_costume_animation")
					.toArray()
					.some((c) => {
						const d = c.style.backgroundImage;
						return d && d.includes("13_complete.png");
					});
			return l && (await st.Tt("5f281c1236661723b1c263e1d33622", this.jt("52221712036376022327f1", 0))), l;
		}
		async Ad() {
			const n = Date.now() + 1e4,
				l = document.querySelectorAll(".buff-clickable");
			if (l.length === 0) return !0;
			const c = `index.php?mod=costumes&sh=${secureHash}`;
			for (const d of l) {
				if (d.getAttribute("data-link") !== c) continue;
				const h = d.getAttribute("data-cooldown-end") ?? "",
					g = parseInt(h, 10);
				if (isNaN(g)) break;
				const f = 1e3 * g + n;
				return await this.Tt("52221712036376022327f1", f), !1;
			}
			return !0;
		}
		async Yn(n, l, c, d = "guildMarket") {
			const h = { sellid: n, preis: l, dauer: c || 3, anbieten: "Offer", sell_mode: 0 },
				g = await this._d(h, d);
			return this.ke(this.Vt(g).find("#sstat_gold_val")[0].textContent), g;
		}
		Vn(n, l = 3) {
			return this.oe() < (n * (l + 1)) / 100;
		}
		async Jn() {
			const n = st.jt("53381df1b93315a48323cd1d1711715a", 60);
			await st.Tt("53381df1b93315a48323cd1d171fca", new Date(Date.now() + 1e3 * n).toString());
		}
		Nn() {
			return Math.random().toString(35).substr(2);
		}
		Gn() {
			return document.getElementById("server-time").textContent;
		}
		async Wn(n) {
			var d;
			let l,
				c = 1;
			do {
				await ghh.Gt();
				const h = this.Vt(await this.qe(n, c++));
				if ((this.ke(h.find("#sstat_gold_val")[0].textContent), !l)) {
					const p = h.find(".paging_numbers"),
						m = p.length ? Array.from(p[0].children).pop() : null;
					l = m ? parseInt(m.textContent) : 1;
				}
				const g = Array.from(h.find(".packageItem"));
				let f = 0;
				for (; f < g.length; f++) {
					const p = g[f],
						m = p.querySelector("[data-content-type]"),
						y = this.Ke(m);
					if (((y.itemName = this.On(m)), delete y.tooltip, this.Pd(y, n))) return (d = p.querySelector("input")) == null ? void 0 : d.getAttribute("value");
				}
			} while (c < 10 && c < Math.min(l, 5));
			return !1;
		}
		Vt(n) {
			if (typeof n != "string") return jQuery(n);
			const l = document.implementation.createHTMLDocument("virtual");
			return jQuery(n, l);
		}
		Yo(n) {
			if (typeof n != "string") return jQuery(n);
			const l = document.implementation.createHTMLDocument("");
			return (l.body.innerHTML = n), jQuery(l.body);
		}
		Ae(n, l) {
			return !(
				(n.contentType != null && n.contentType != l.contentType) ||
				(n.quality != null && n.quality != -1 && n.quality != l.quality) ||
				(n.amount != null && n.amount != l.amount) ||
				(n.level != null && n.level != l.level) ||
				(n.basis != null && n.basis != l.basis) ||
				(n.itemName != null && l.itemName != null && n.itemName != l.itemName) ||
				(n.tooltip != null && n.tooltip[0][0][0] != l.tooltip[0][0][0])
			);
		}
		Pd(n, l) {
			return !(
				(n.contentType != null && n.contentType != l.contentType) ||
				(n.quality != null && n.quality != -1 && n.quality != l.quality) ||
				(n.amount != null && n.amount != l.amount) ||
				(n.level != null && n.level != l.level) ||
				n.soulTo != l.soulTo ||
				(n.basis != null && n.basis != l.basis) ||
				(n.itemName != null && l.itemName != null && n.itemName != l.itemName) ||
				(n.tooltip != null && n.tooltip[0][0][0] != l.tooltip[0][0][0])
			);
		}
		On(n) {
			const l = this.getItemTooltip(n),
				c = l.substring(4, l.indexOf(",")).replace('"', "");
			return unescape(JSON.parse('"' + c + '"'));
		}
		getItemTooltip(n) {
			return jQuery(n).attr("data-tooltip");
		}
		async cpsfpi() {
			return await st.Ru("in_desc");
		}
		async Ru(n, l = {}) {
			const c = gh_UrlInfo.link({ mod: "packages", submod: "sort", page: 1, ...l });
			return await ghh.Tn(c, { packageSorting: n });
		}
		async Hn(n, l = "guildMarket") {
			const c = gh_UrlInfo.link({ mod: l, fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "d", p: 1 });
			return !!(await ghh.Tn(c, n));
		}
		async To(n) {
			const l = gh_UrlInfo.link({ mod: "market", fl: 0, fq: -1, f: 0, qry: "", seller: "", s: "", p: 1 });
			return !!(await ghh.Tn(l, n));
		}
		async Nc(n, l, c) {
			const d = { mod: "inventory", submod: "move", from: l, fromX: n.positionX, fromY: n.positionY, to: c.bagId, toX: c.spot.x + 1, toY: c.spot.y + 1, amount: 1, doll: 1 },
				h = { a: new Date().getTime(), sh: secureHash },
				g = gh_UrlInfo.ajaxLink(d),
				f = await jQuery.post(g, h),
				p = JSON.parse(f);
			return p && p.header && p.header.gold && this.ke(p.header.gold.text), !(!p || !p.to) && p.to.data;
		}
		nr() {
			return +st.jt("41227d101a2e1b7a17", 1e4);
		}
		En() {
			return this.oe() - st.nr();
		}
		oe() {
			return this.Ln(document.getElementById("sstat_gold_val").innerText);
		}
		ke(n) {
			jQuery("#sstat_gold_val").text(n.toLocaleString("de-DE"));
		}
		Ln(n) {
			return parseInt(n.trim().replace(/\./g, ""));
		}
		async Pa(n, l = 1) {
			var v, j, T, k, I, S, C, x;
			const c = this.Vt(n).find("#market_table")[0];
			if (!c) return;
			const d = Array.from(c.querySelectorAll("input[name=buyid]")),
				h = Array.from(c.querySelectorAll("input[name=qry]")),
				g = Array.from(c.querySelectorAll("input[name=seller]")),
				f = Array.from(c.querySelectorAll("input[name=f]")),
				p = Array.from(c.querySelectorAll("input[name=fl]")),
				m = Array.from(c.querySelectorAll("input[name=fq]")),
				y = Array.from(c.querySelectorAll("input[name=s]")),
				b = Array.from(c.querySelectorAll("input[name=p]")),
				w = Array.from(c.querySelectorAll("tr")).slice(1),
				_ = [];
			for (let q = 0; q < w.length; q++) {
				const D = w[q];
				if (D.querySelector('[name="cancel"]') && parseInt(D.querySelectorAll("td")[2].textContent.replace(/\./g, ""), 10) === 1) {
					await ghh.Gt();
					const A = {
						buyid: (v = d[q]) == null ? void 0 : v.getAttribute("value"),
						qry: (j = h[q]) == null ? void 0 : j.getAttribute("value"),
						seller: (T = g[q]) == null ? void 0 : T.getAttribute("value"),
						f: (k = f[q]) == null ? void 0 : k.getAttribute("value"),
						fl: (I = p[q]) == null ? void 0 : I.getAttribute("value"),
						fq: (S = m[q]) == null ? void 0 : S.getAttribute("value"),
						s: (C = y[q]) == null ? void 0 : C.getAttribute("value"),
						p: (x = b[q]) == null ? void 0 : x.getAttribute("value"),
						cancel: "Cancel",
					};
					_.push(this._d(A)), (_.length >= l || q === w.length - 1) && (await Promise.all(_), (_.length = 0));
				}
			}
		}
		Qo(n) {
			return jQuery(n).attr("data-price-gold");
		}
		async Ro(n, l) {
			return await this.Nc(n, 305, l);
		}
		Ma(n, l = 1) {
			const c = { sellid: n.id, preis: n.price, dauer: l, anbieten: "Offer" };
			return new Promise((d, h) => {
				this._d(c)
					.then((g) => d(g))
					.catch((g) => h(g));
			});
		}
		async Ql() {
			switch (this.jt("412c7d21171311", packHideTypes.guild)) {
				case packHideTypes.guild:
					await gh.$n();
					break;
				case packHideTypes.market:
					await gh.$n(!0);
					break;
				case packHideTypes.shop:
					await gh.cr();
					break;
				case packHideTypes.auction:
					await gh.Kn();
					break;
				case packHideTypes.training:
					await gh.sr();
					break;
				case packHideTypes.donate:
					await gh.ir();
					break;
				default:
					await gh.$n();
			}
			await st.xd();
		}
		async xd() {
			if (st.En() >= st.jt("5c24a2414d81192812b2c28e112e2d19130311c", 5e5) && st.jt("50218922c21721a1e17291b4", !1))
				switch (((ro.kt = !0), st.jt("532c7d01e3315a43a3e381d", packHideTypes.auction))) {
					case packHideTypes.guild:
						await gh.$n();
						break;
					case packHideTypes.market:
						await gh.$n(!0);
						break;
					case packHideTypes.shop:
						await gh.cr();
						break;
					case packHideTypes.auction:
						await gh.Kn();
						break;
					case packHideTypes.training:
						await gh.sr();
						break;
					case packHideTypes.donate:
						await gh.ir();
						break;
					default:
						await gh.$n();
				}
		}
		async Ll() {
			const n = st.jt("5038109361f18cc1a0271c201b291d", []),
				l = { minerva: 1, diana: 2, vulcanus: 3, mars: 4, apollo: 5, merkur: 6 },
				c = await ghh.Yt(gh_UrlInfo.link({ mod: "gods" }));
			for (const d of n)
				if (st.Vt(c).find(`#${d} .god_cooldown_box`).length === 0) {
					const h = gh_UrlInfo.link({ mod: "gods", submod: "activateBlessing", god: l[d], rank: 2 });
					await ghh.Yt(h);
				}
			await st.Tt("5f281c12217e112a7b24233f016a7141c", new Date().getTime() + 6e5);
		}
		async Ul() {
			const n = st.jt("422883161a6102b1a821f17b1", []),
				l = { minerva: 1, diana: 2, vulcanus: 3, mars: 4, apollo: 5, merkur: 6 },
				c = await ghh.Yt(gh_UrlInfo.link({ mod: "gods" }));
			for (const d of n)
				if (st.Vt(c).find(`#${d} .god_cooldown_box`).length === 0) {
					const h = gh_UrlInfo.link({ mod: "gods", submod: "activateBlessing", god: l[d], rank: 1 });
					await ghh.Yt(h);
				}
			await st.Tt("5f281c12217e112b1a821f17b1", new Date().getTime() + 72e4);
		}
		Ye(n) {
			const l = parseInt(n.getAttribute("data-basis").split("-")[0]);
			return [1, 2, 3, 4, 5, 6, 8, 9].indexOf(l) > -1;
		}
		le(n) {
			return JSON.parse(n.getAttribute("data-tooltip")).pop().pop()[0].match(/\d+/g);
		}
		async Zl() {
			const n = Date.now(),
				l = new Date().toDateString(),
				c = this.jt("412c11151022a71d", []),
				d = this.jt("5522a1225f167c38b263a2d116201cf00281", !1);
			if (!c.length) return !1;
			for (const h of c) {
				if (!h.startTime || !h.endTime || h.date !== l) {
					const g = new Date();
					g.setHours(h.from, 0, 0, 0),
						(h.startTime = g.getTime() + 1e3 * st.Le(60 * (h.randomStartMin ?? 5), 0)),
						(h.endTime = g.getTime() + 60 * (h.to - h.from) * 60 * 1e3 + 1e3 * st.Le(60 * (h.randomEndMin ?? 5), 0)),
						(h.date = l),
						await this.Tt("412c11151022a71d", c);
				}
				if (h.active && n >= h.startTime && n <= h.endTime && (((!d || (d && (await this.Fl()))) && !this.Bo && !ro.vt) || h.forced)) return h.endTime;
			}
			return !1;
		}
	}
	const st = new ST(),
		chrome$1 = globalThis.chrome;
	function _getSessionValue(n, l) {
		const c = sessionStorage.getItem(n);
		return c !== null ? JSON.parse(c) : l;
	}
	const gh_playingModeCommunication = {
		Mt: async function () {
			const n = gh_UrlInfo;
			n.resolve(location.href);
			const l = {
				isPlaying: !0,
				isReloginEnabled: !0,
				playerId: _getJunk(),
				language: n.country,
				selectedLanguage: "en",
				server: n.server,
				isRunning: _getSessionValue("startScript", !1),
				delay: st.jt("432ca21a327115e17", 5),
				isDebuging: "true",
				loginDelay: 1e3 * st.jt("5d223f1b2a618816", 30),
			};
			chrome$1.runtime.sendMessage(ghExtensionId, l, function () { });
		},
	},
		setFlagListener = () => {
			window.init_event = !0;
		},
		setUp = () => {
			if (window.tickers && typeof window.tickers.refresh == "function") {
				const n = window.tickers.refresh;
				window.tickers.refresh = function (l, c) {
					return (l || jQuery("body")).find(".ticker").removeAttr("ticker-ref"), n.call(this, l, c);
				};
			}
		};
	window.addEventListener("init_event", setFlagListener, { once: !0 }),
		setUp(),
		(async () => {
			if (_getJunk())
				if ((await _initExtensionStore(_getJunk()), await gh_playingModeCommunication.Mt(), _getSessionValue("startScript", !1) && ro.et(), window.init_event))
					try {
						await pc.yl();
					} catch (n) {
						await errorLog.log(n);
					}
				else {
					window.removeEventListener("init_event", setFlagListener);
					const n = async () => {
						try {
							await pc.yl();
						} catch (l) {
							await errorLog.log(l);
						}
					};
					window.addEventListener("init_event", n, { once: !0 });
				}
		})();
})();
