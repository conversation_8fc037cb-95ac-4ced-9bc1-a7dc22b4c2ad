// Healing System Module - Handles automatic healing and food management

import { HealSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface HealthInfo {
  current: number;
  maximum: number;
  percentage: number;
}

export interface FoodItem {
  id: string;
  name: string;
  healAmount: number;
  count: number;
  isAvailable: boolean;
}

export class HealingSystemModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isHealing: boolean = false;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async checkAndHeal(settings: HealSettings): Promise<boolean> {
    if (this.isHealing || !settings.enabled) return false;

    const healthInfo = this.getCurrentHealth();
    if (!healthInfo) return false;

    if (healthInfo.percentage < settings.minHealth) {
      console.log(`Healing System: Health at ${healthInfo.percentage}%, starting healing process`);
      return await this.performHealing(settings, healthInfo);
    }

    return false;
  }

  private getCurrentHealth(): HealthInfo | null {
    // Try multiple selectors for health display
    const healthSelectors = [
      '#header_values_hp_percent',
      '.health-bar .current',
      '#health_points',
      '.player-health .current'
    ];

    for (const selector of healthSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const text = element.textContent?.trim() || '';
        
        // Try different health formats
        let match = text.match(/(\d+)\/(\d+)/); // "50/100" format
        if (match) {
          const current = parseInt(match[1]);
          const maximum = parseInt(match[2]);
          return {
            current,
            maximum,
            percentage: Math.round((current / maximum) * 100)
          };
        }

        match = text.match(/(\d+)%/); // "75%" format
        if (match) {
          const percentage = parseInt(match[1]);
          return {
            current: percentage,
            maximum: 100,
            percentage
          };
        }
      }
    }

    return null;
  }

  private async performHealing(settings: HealSettings, healthInfo: HealthInfo): Promise<boolean> {
    this.isHealing = true;
    let healingSuccess = false;

    try {
      // Try healing with cervisia first if enabled
      if (settings.healCervisia) {
        healingSuccess = await this.useCervisia();
        if (healingSuccess) {
          console.log('Healing System: Healed with cervisia');
          return true;
        }
      }

      // Try healing with eggs if enabled
      if (settings.healEggs && settings.selectedEggs.length > 0) {
        healingSuccess = await this.useEggs(settings.selectedEggs);
        if (healingSuccess) {
          console.log('Healing System: Healed with eggs');
          return true;
        }
      }

      // Try buying food if enabled and needed
      if (settings.buyFood && settings.buyFoodNeeded) {
        const foodBought = await this.buyFood(settings);
        if (foodBought) {
          // Try healing again with newly bought food
          healingSuccess = await this.useEggs(settings.selectedEggs);
          if (healingSuccess) {
            console.log('Healing System: Healed with purchased food');
            return true;
          }
        }
      }

      console.log('Healing System: No healing options available');
      return false;

    } catch (error) {
      console.error('Healing System: Error during healing:', error);
      return false;
    } finally {
      this.isHealing = false;
    }
  }

  private async useCervisia(): Promise<boolean> {
    // Navigate to tavern if not already there
    if (!window.location.href.includes('mod=location&loc=2')) {
      await this.navigateToLocation('mod=location&loc=2');
    }

    // Look for cervisia button
    const cervisiaButton = document.querySelector('.cervisia_button, [data-action="drink-cervisia"]');
    if (!cervisiaButton || cervisiaButton.hasAttribute('disabled')) {
      return false;
    }

    // Click cervisia button
    (cervisiaButton as HTMLElement).click();
    
    // Wait for healing to complete
    await this.delay(2000);
    
    // Check if health improved
    const newHealth = this.getCurrentHealth();
    return newHealth ? newHealth.percentage > 90 : false;
  }

  private async useEggs(selectedEggs: number[]): Promise<boolean> {
    // Navigate to packages if not already there
    if (!window.location.href.includes('mod=packages')) {
      await this.navigateToLocation('mod=packages');
    }

    const availableFood = this.getAvailableFood();
    
    for (const eggType of selectedEggs) {
      const food = availableFood.find(f => f.id === eggType.toString());
      if (food && food.isAvailable && food.count > 0) {
        const success = await this.consumeFood(food);
        if (success) {
          return true;
        }
      }
    }

    return false;
  }

  private getAvailableFood(): FoodItem[] {
    const foodItems: FoodItem[] = [];
    
    // Parse food items from packages page
    const foodElements = document.querySelectorAll('.package_food, .food-item');
    
    foodElements.forEach((element, index) => {
      const nameElement = element.querySelector('.item_name, .name');
      const countElement = element.querySelector('.item_count, .count');
      const useButton = element.querySelector('.use_button, [data-action="use"]');
      
      const name = nameElement?.textContent?.trim() || `Food ${index}`;
      const count = parseInt(countElement?.textContent?.trim() || '0');
      const isAvailable = useButton && !useButton.hasAttribute('disabled');
      
      // Estimate heal amount based on food type
      let healAmount = 10; // Default
      if (name.toLowerCase().includes('egg')) healAmount = 25;
      if (name.toLowerCase().includes('bread')) healAmount = 15;
      if (name.toLowerCase().includes('meat')) healAmount = 30;
      
      foodItems.push({
        id: (index + 1).toString(),
        name,
        healAmount,
        count,
        isAvailable: !!isAvailable
      });
    });

    return foodItems;
  }

  private async consumeFood(food: FoodItem): Promise<boolean> {
    const useButton = document.querySelector(`[data-food-id="${food.id}"] .use_button, .food-item:nth-child(${food.id}) [data-action="use"]`);
    if (!useButton || useButton.hasAttribute('disabled')) {
      return false;
    }

    console.log(`Healing System: Using ${food.name}`);
    (useButton as HTMLElement).click();
    
    // Wait for consumption
    await this.delay(1500);
    
    // Check if health improved
    const newHealth = this.getCurrentHealth();
    return newHealth ? newHealth.percentage >= 90 : false;
  }

  private async buyFood(settings: HealSettings): Promise<boolean> {
    // Navigate to shop
    if (!window.location.href.includes('mod=location&loc=26')) {
      await this.navigateToLocation('mod=location&loc=26');
    }

    // Look for food items to buy
    const foodShopItems = document.querySelectorAll('.shop_item, .market-item');
    
    for (const item of foodShopItems) {
      const nameElement = item.querySelector('.item_name, .name');
      const buyButton = item.querySelector('.buy_button, [data-action="buy"]');
      const priceElement = item.querySelector('.item_price, .price');
      
      if (!nameElement || !buyButton || buyButton.hasAttribute('disabled')) continue;
      
      const name = nameElement.textContent?.trim().toLowerCase() || '';
      const price = parseInt(priceElement?.textContent?.replace(/\D/g, '') || '0');
      
      // Check if it's a food item we want to buy
      if ((name.includes('egg') || name.includes('bread') || name.includes('meat')) && price < 1000) {
        console.log(`Healing System: Buying ${name} for ${price} gold`);
        (buyButton as HTMLElement).click();
        
        await this.delay(2000);
        return true;
      }
    }

    return false;
  }

  private async navigateToLocation(locationUrl: string): Promise<void> {
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('?')[0];
    
    console.log(`Healing System: Navigating to ${locationUrl}`);
    window.location.href = `${baseUrl}?${locationUrl}`;
    
    // Wait for page load
    await this.waitForPageLoad();
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000); // Additional delay for dynamic content
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  async shouldStopAttacks(settings: HealSettings): Promise<boolean> {
    if (!settings.autoStopAttacks) return false;
    
    const healthInfo = this.getCurrentHealth();
    if (!healthInfo) return false;
    
    // Stop attacks if health is below minimum and no healing options available
    if (healthInfo.percentage < settings.minHealth) {
      const canHeal = await this.canPerformHealing(settings);
      if (!canHeal) {
        console.log('Healing System: Stopping attacks - low health and no healing options');
        return true;
      }
    }
    
    return false;
  }

  private async canPerformHealing(settings: HealSettings): Promise<boolean> {
    // Check if cervisia is available
    if (settings.healCervisia) {
      // This would need to check tavern availability
      return true; // Simplified
    }
    
    // Check if food is available
    if (settings.healEggs) {
      const availableFood = this.getAvailableFood();
      const hasUsableFood = availableFood.some(food => 
        settings.selectedEggs.includes(parseInt(food.id)) && 
        food.isAvailable && 
        food.count > 0
      );
      
      if (hasUsableFood) return true;
    }
    
    // Check if can buy food
    if (settings.buyFood && settings.buyFoodNeeded) {
      // This would need to check gold availability and shop access
      return true; // Simplified
    }
    
    return false;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
