# Gladiatus Helper Bot - TypeScript Edition

A Chrome extension that automates various tasks in the Gladiatus browser game, rewritten in TypeScript for better maintainability and type safety.

## Features

- **Auto Heal**: Automatically heal your character when health is low
- **Hide Gold**: Automatically hide gold in packages to prevent raids
- **Arena & Turma**: Automated combat in arena and turma
- **Expeditions & Dungeons**: Automated exploration and dungeon runs
- **Underworld Runs**: Automated underworld exploration
- **Quest Management**: Intelligent quest selection and completion
- **Market Operations**: Automated buying and selling
- **Banking System**: Guild-based banking for gold management
- **Multi-language Support**: Available in 17+ languages

### Technical Improvements in TypeScript Edition
- **Full TypeScript**: Complete type safety and modern JavaScript features
- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **Comprehensive Testing**: Jest test suite with Chrome extension mocking
- **Modern Build System**: Webpack-based build with development and production modes
- **CSS Organization**: Modular CSS with CSS variables and component-based styling
- **Error Handling**: Robust error handling and logging throughout
- **Extensible Design**: Easy to add new features and modify existing ones

## Development

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Setup

```bash
# Install dependencies
npm install

# Development build with watch mode
npm run dev

# Production build
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Testing
npm run test
npm run test:watch
```

### Project Structure

```
src/
 background/     # Service worker scripts
 content/        # Content scripts
 main/          # Main application logic
 default/       # Default page scripts
 types/         # TypeScript type definitions
 utils/         # Utility functions
 localization/  # Internationalization
 styles/        # CSS styles
```

### Building the Extension

1. Run `npm run build` to create the production build
2. The compiled extension will be in the `dist/` folder
3. Load the `dist/` folder as an unpacked extension in Chrome

## License

MIT License - see LICENSE file for details.

## Author

Pendea Raul
