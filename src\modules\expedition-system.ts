// Expedition System Module - Handles expedition and dungeon automation

import { ExpeditionSettings, DungeonSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface ExpeditionLocation {
  id: string;
  name: string;
  level: number;
  dungeonPoints: number;
  isAvailable: boolean;
  cooldownRemaining: number;
}

export interface DungeonInfo {
  name: string;
  difficulty: string;
  isAdvanced: boolean;
  hasBoss: boolean;
  bossName?: string;
  requiresKey: boolean;
}

export class ExpeditionSystemModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private expeditionCount: number = 0;
  private dungeonCount: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(expeditionSettings: ExpeditionSettings, dungeonSettings: DungeonSettings): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.expeditionCount = 0;
    this.dungeonCount = 0;
    
    console.log('Expedition System: Starting expedition automation');
    
    try {
      if (expeditionSettings.enabled) {
        await this.performExpeditions(expeditionSettings);
      }
      
      if (dungeonSettings.enabled) {
        await this.performDungeons(dungeonSettings);
      }
    } catch (error) {
      console.error('Expedition System: Error in expedition automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Expedition System: Stopped expedition automation');
  }

  private async performExpeditions(settings: ExpeditionSettings): Promise<void> {
    console.log('Expedition System: Starting expeditions');
    
    await this.navigateToExpedition();
    
    while (this.isRunning && this.shouldContinueExpeditions(settings)) {
      try {
        const locations = await this.getAvailableLocations(settings);
        
        if (locations.length === 0) {
          console.log('Expedition System: No available expedition locations');
          break;
        }

        const location = this.selectBestLocation(locations, settings);
        if (!location) {
          console.log('Expedition System: No suitable location found');
          break;
        }

        await this.startExpedition(location, settings);
        this.expeditionCount++;
        
        // Wait for expedition completion
        await this.waitForExpeditionCompletion();
        
        // Collect bonuses if enabled
        if (settings.autoCollectBonuses) {
          await this.collectExpeditionBonuses();
        }
        
        await this.delay(5000);
        
      } catch (error) {
        console.error('Expedition System: Error in expedition loop:', error);
        await this.delay(10000);
      }
    }
  }

  private async performDungeons(settings: DungeonSettings): Promise<void> {
    console.log('Expedition System: Starting dungeons');
    
    await this.navigateToDungeon(settings.location);
    
    while (this.isRunning && this.shouldContinueDungeons(settings)) {
      try {
        const dungeonInfo = await this.getDungeonInfo();
        
        if (!dungeonInfo) {
          console.log('Expedition System: No dungeon information available');
          break;
        }

        await this.enterDungeon(dungeonInfo, settings);
        await this.completeDungeon(dungeonInfo, settings);
        
        this.dungeonCount++;
        
        await this.delay(10000);
        
      } catch (error) {
        console.error('Expedition System: Error in dungeon loop:', error);
        
        // Handle dungeon failure
        if (settings.restartAfterFail > 0 && this.dungeonCount < settings.restartAfterFail) {
          console.log('Expedition System: Restarting dungeon after failure');
          await this.delay(15000);
          continue;
        } else {
          break;
        }
      }
    }
  }

  private async navigateToExpedition(): Promise<void> {
    if (!window.location.href.includes('mod=expedition')) {
      console.log('Expedition System: Navigating to expedition');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=expedition`;
      
      await this.waitForPageLoad();
    }
  }

  private async navigateToDungeon(location: string): Promise<void> {
    if (!window.location.href.includes('mod=dungeon') || !window.location.href.includes(location)) {
      console.log(`Expedition System: Navigating to dungeon at ${location}`);
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=dungeon&loc=${location}`;
      
      await this.waitForPageLoad();
    }
  }

  private async getAvailableLocations(settings: ExpeditionSettings): Promise<ExpeditionLocation[]> {
    const locations: ExpeditionLocation[] = [];
    
    const locationElements = document.querySelectorAll('.expedition_location, .location-item');
    
    for (let i = 0; i < locationElements.length; i++) {
      const element = locationElements[i] as HTMLElement;
      
      try {
        const location = await this.parseExpeditionLocation(element, i);
        if (location && this.isValidExpeditionLocation(location, settings)) {
          locations.push(location);
        }
      } catch (error) {
        console.error('Expedition System: Error parsing location:', error);
      }
    }
    
    return locations;
  }

  private async parseExpeditionLocation(element: HTMLElement, index: number): Promise<ExpeditionLocation | null> {
    const nameElement = element.querySelector('.location_name, .name');
    const levelElement = element.querySelector('.location_level, .level');
    const pointsElement = element.querySelector('.dungeon_points, .points');
    const startButton = element.querySelector('.start_expedition, [data-action="start"]');
    const cooldownElement = element.querySelector('.cooldown, .timer');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    const dungeonPoints = parseInt(pointsElement?.textContent?.trim() || '0');
    const isAvailable = startButton && !startButton.hasAttribute('disabled');
    
    let cooldownRemaining = 0;
    if (cooldownElement) {
      const cooldownText = cooldownElement.textContent?.trim() || '';
      const timeMatch = cooldownText.match(/(\d+):(\d+):(\d+)/);
      if (timeMatch) {
        cooldownRemaining = parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]);
      }
    }
    
    return {
      id: index.toString(),
      name,
      level,
      dungeonPoints,
      isAvailable: !!isAvailable,
      cooldownRemaining
    };
  }

  private isValidExpeditionLocation(location: ExpeditionLocation, settings: ExpeditionSettings): boolean {
    // Must be available
    if (!location.isAvailable) return false;
    
    // Check minimum dungeon points
    if (location.dungeonPoints < settings.minDungeonPoints) return false;
    
    // Check specific location if set
    if (settings.location && !location.name.toLowerCase().includes(settings.location.toLowerCase())) {
      return false;
    }
    
    return true;
  }

  private selectBestLocation(locations: ExpeditionLocation[], settings: ExpeditionSettings): ExpeditionLocation | null {
    if (locations.length === 0) return null;
    
    // Sort by dungeon points (highest first)
    locations.sort((a, b) => b.dungeonPoints - a.dungeonPoints);
    
    return locations[0];
  }

  private async startExpedition(location: ExpeditionLocation, settings: ExpeditionSettings): Promise<void> {
    console.log(`Expedition System: Starting expedition to ${location.name}`);
    
    const locationElement = document.querySelector(`.expedition_location:nth-child(${parseInt(location.id) + 1})`);
    if (!locationElement) return;
    
    const startButton = locationElement.querySelector('.start_expedition, [data-action="start"]');
    if (!startButton || startButton.hasAttribute('disabled')) return;
    
    // Handle cooldown removal if enabled
    if (settings.removeCooldown === 'hourglass' && location.cooldownRemaining > 0) {
      await this.removeCooldownWithHourglass();
    }
    
    (startButton as HTMLElement).click();
    await this.delay(2000);
  }

  private async removeCooldownWithHourglass(): Promise<void> {
    const hourglassButton = document.querySelector('.use_hourglass, [data-action="use-hourglass"]');
    if (hourglassButton && !hourglassButton.hasAttribute('disabled')) {
      console.log('Expedition System: Using hourglass to remove cooldown');
      (hourglassButton as HTMLElement).click();
      await this.delay(1000);
    }
  }

  private async waitForExpeditionCompletion(): Promise<void> {
    console.log('Expedition System: Waiting for expedition completion');
    
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const completionElement = document.querySelector('.expedition_complete, .completed');
        const collectButton = document.querySelector('.collect_rewards, [data-action="collect"]');
        
        if (completionElement || collectButton) {
          resolve();
        } else {
          setTimeout(checkCompletion, 5000);
        }
      };
      
      setTimeout(checkCompletion, 30000); // Start checking after 30 seconds
    });
  }

  private async collectExpeditionBonuses(): Promise<void> {
    const collectButton = document.querySelector('.collect_rewards, [data-action="collect"]');
    if (collectButton && !collectButton.hasAttribute('disabled')) {
      console.log('Expedition System: Collecting expedition bonuses');
      (collectButton as HTMLElement).click();
      await this.delay(2000);
    }
  }

  private async getDungeonInfo(): Promise<DungeonInfo | null> {
    const nameElement = document.querySelector('.dungeon_name, .name');
    const difficultyElement = document.querySelector('.dungeon_difficulty, .difficulty');
    const bossElement = document.querySelector('.boss_name, .boss');
    const keyElement = document.querySelector('.gate_key, .key-required');
    
    if (!nameElement) return null;
    
    return {
      name: nameElement.textContent?.trim() || '',
      difficulty: difficultyElement?.textContent?.trim() || 'normal',
      isAdvanced: difficultyElement?.textContent?.includes('advanced') || false,
      hasBoss: !!bossElement,
      bossName: bossElement?.textContent?.trim(),
      requiresKey: !!keyElement
    };
  }

  private async enterDungeon(dungeonInfo: DungeonInfo, settings: DungeonSettings): Promise<void> {
    console.log(`Expedition System: Entering dungeon ${dungeonInfo.name}`);
    
    // Use gate key if required and enabled
    if (dungeonInfo.requiresKey && settings.useGateKey) {
      const keyButton = document.querySelector('.use_gate_key, [data-action="use-key"]');
      if (keyButton && !keyButton.hasAttribute('disabled')) {
        (keyButton as HTMLElement).click();
        await this.delay(2000);
      }
    }
    
    const enterButton = document.querySelector('.enter_dungeon, [data-action="enter"]');
    if (enterButton && !enterButton.hasAttribute('disabled')) {
      (enterButton as HTMLElement).click();
      await this.delay(3000);
    }
  }

  private async completeDungeon(dungeonInfo: DungeonInfo, settings: DungeonSettings): Promise<void> {
    console.log(`Expedition System: Completing dungeon ${dungeonInfo.name}`);
    
    // Handle boss skip if enabled
    if (settings.skipBoss && dungeonInfo.hasBoss) {
      if (!settings.bossName || dungeonInfo.bossName?.includes(settings.bossName)) {
        console.log('Expedition System: Skipping boss as configured');
        const skipButton = document.querySelector('.skip_boss, [data-action="skip"]');
        if (skipButton) {
          (skipButton as HTMLElement).click();
          await this.delay(2000);
        }
      }
    }
    
    // Wait for dungeon completion
    await this.waitForDungeonCompletion();
  }

  private async waitForDungeonCompletion(): Promise<void> {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const completionElement = document.querySelector('.dungeon_complete, .completed');
        const exitButton = document.querySelector('.exit_dungeon, [data-action="exit"]');
        
        if (completionElement || exitButton) {
          resolve();
        } else {
          setTimeout(checkCompletion, 10000);
        }
      };
      
      setTimeout(checkCompletion, 60000); // Start checking after 1 minute
    });
  }

  private shouldContinueExpeditions(settings: ExpeditionSettings): boolean {
    // Add logic for expedition limits if needed
    return this.expeditionCount < 10; // Simplified limit
  }

  private shouldContinueDungeons(settings: DungeonSettings): boolean {
    // Add logic for dungeon limits if needed
    return this.dungeonCount < 5; // Simplified limit
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
