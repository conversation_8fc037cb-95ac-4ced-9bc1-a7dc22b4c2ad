{"version": 3, "file": "default/index.iife.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;ACVA,iDAAiD;AAI1C,MAAM,kBAAkB;IAA/B;QACU,aAAQ,GAAY,KAAK,CAAC;QAC1B,YAAO,GAAqB;YAClC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IA8GJ,CAAC;IA5GC;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,mDAAmD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW;QACjB,OAAO,kCAAkC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW,EAAE,QAAiB,KAAK;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QAEF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,0BAA0B;YAC1D,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5C,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAuC,EAAE,OAAe,WAAW;QAC1E,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAuC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;CACF;;;;;;;UCvHD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;ACNA,0CAA0C;AAC1C,4DAA4D;AAEO;AAEnE,MAAM,aAAa;IAGjB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,2EAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,0BAA0B;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,wCAAwC;QACxC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,oBAAoB;QAC1B,qCAAqC;QACpC,MAAc,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;QAE7C,wBAAwB;QACvB,MAAc,CAAC,OAAO,GAAG;YACxB,WAAW,EAAE,CAAC,GAAY,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACtF,OAAO,EAAE,CAAC,GAAY,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC9E,SAAS,EAAE,CAAC,GAAY,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YAClF,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YAC7C,OAAO,EAAE,CAAC,GAAY,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;SAC/E,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YAE/D,+CAA+C;YAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,0CAA0C;QAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,WAAW,GAAG;;;;;;;;;;;;;KAanB,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;CACF;AAED,4BAA4B;AAC5B,IAAI,aAAa,EAAE,CAAC", "sources": ["webpack://gladiatus-helper-bot-ts/webpack/universalModuleDefinition", "webpack://gladiatus-helper-bot-ts/./src/utils/gladiator-url-helper.ts", "webpack://gladiatus-helper-bot-ts/webpack/bootstrap", "webpack://gladiatus-helper-bot-ts/webpack/runtime/define property getters", "webpack://gladiatus-helper-bot-ts/webpack/runtime/hasOwnProperty shorthand", "webpack://gladiatus-helper-bot-ts/webpack/runtime/make namespace object", "webpack://gladiatus-helper-bot-ts/./src/default/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "// Gladiatus URL parsing and validation utilities\r\n\r\nimport { GladiatorUrlInfo, GladiatorQueries } from '../types';\r\n\r\nexport class GladiatorUrlHelper {\r\n  private resolved: boolean = false;\r\n  private urlInfo: GladiatorUrlInfo = {\r\n    queries: {},\r\n    resolved: false\r\n  };\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus domain\r\n   */\r\n  isGladiatus(url: string): boolean {\r\n    return /gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus game page (not lobby)\r\n   */\r\n  isPlaying(url: string): boolean {\r\n    return /https:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is the Gladiatus lobby\r\n   */\r\n  isLobby(url: string): boolean {\r\n    return /lobby\\.gladiatus\\.gameforge\\.com/.test(url) && url.indexOf('loading') < 0;\r\n  }\r\n\r\n  /**\r\n   * Parse query string into object\r\n   */\r\n  resolveQueries(queryString: string): GladiatorQueries {\r\n    const queries: GladiatorQueries = {};\r\n    const params = queryString.split('&');\r\n\r\n    for (let i = params.length - 1; i >= 0; i--) {\r\n      const [key, value] = params[i].split('=');\r\n      if (key && value) {\r\n        queries[key] = decodeURIComponent(value);\r\n      }\r\n    }\r\n\r\n    return queries;\r\n  }\r\n\r\n  /**\r\n   * Resolve URL information from Gladiatus game URL\r\n   */\r\n  resolve(url: string, force: boolean = false): GladiatorUrlInfo | null {\r\n    if (this.resolved && !force) {\r\n      return this.urlInfo;\r\n    }\r\n\r\n    const match = url.match(\r\n      /https?:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com\\/game\\/(?:index|main)\\.php(?:\\?(.*))?/i\r\n    );\r\n\r\n    if (!match) {\r\n      return null;\r\n    }\r\n\r\n    this.urlInfo = {\r\n      server: match[1],\r\n      country: match[2],\r\n      domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,\r\n      queries: this.resolveQueries(match[3] || ''),\r\n      resolved: true\r\n    };\r\n\r\n    this.resolved = true;\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Build Gladiatus URL with parameters\r\n   */\r\n  buildUrl(params: Record<string, string | number>, page: string = 'index.php'): string {\r\n    const queryParts: string[] = [];\r\n\r\n    for (const key in params) {\r\n      queryParts.push(`${key}=${encodeURIComponent(params[key])}`);\r\n    }\r\n\r\n    // Add security hash if available\r\n    if (this.urlInfo.queries.sh) {\r\n      queryParts.push(`sh=${this.urlInfo.queries.sh}`);\r\n    }\r\n\r\n    return `${page}?${queryParts.join('&')}`;\r\n  }\r\n\r\n  /**\r\n   * Build AJAX URL with parameters\r\n   */\r\n  buildAjaxUrl(params: Record<string, string | number>): string {\r\n    return this.buildUrl(params, 'ajax.php');\r\n  }\r\n\r\n  /**\r\n   * Get current URL info\r\n   */\r\n  getUrlInfo(): GladiatorUrlInfo {\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Reset resolved state\r\n   */\r\n  reset(): void {\r\n    this.resolved = false;\r\n    this.urlInfo = {\r\n      queries: {},\r\n      resolved: false\r\n    };\r\n  }\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Default script for Gladiatus Helper Bot\r\n// This script provides fallback functionality and utilities\r\n\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\n\r\nclass DefaultScript {\r\n  private urlHelper: GladiatorUrlHelper;\r\n\r\n  constructor() {\r\n    this.urlHelper = new GladiatorUrlHelper();\r\n    this.initialize();\r\n  }\r\n\r\n  private initialize(): void {\r\n    console.log('Gladiatus Helper Bot: Default script loaded');\r\n\r\n    // Set up global utilities\r\n    this.setupGlobalUtilities();\r\n\r\n    // Handle any default page functionality\r\n    this.handleDefaultPage();\r\n  }\r\n\r\n  private setupGlobalUtilities(): void {\r\n    // Make URL helper available globally\r\n    (window as any).ghUrlHelper = this.urlHelper;\r\n\r\n    // Add utility functions\r\n    (window as any).ghUtils = {\r\n      isGladiatus: (url?: string) => this.urlHelper.isGladiatus(url || window.location.href),\r\n      isLobby: (url?: string) => this.urlHelper.isLobby(url || window.location.href),\r\n      isPlaying: (url?: string) => this.urlHelper.isPlaying(url || window.location.href),\r\n      getUrlInfo: () => this.urlHelper.getUrlInfo(),\r\n      resolve: (url?: string) => this.urlHelper.resolve(url || window.location.href)\r\n    };\r\n  }\r\n\r\n  private handleDefaultPage(): void {\r\n    const currentUrl = window.location.href;\r\n\r\n    if (this.urlHelper.isGladiatus(currentUrl)) {\r\n      console.log('Gladiatus Helper Bot: Gladiatus domain detected');\r\n\r\n      // Add any default Gladiatus page handling here\r\n      this.addGladiatorStyles();\r\n    }\r\n  }\r\n\r\n  private addGladiatorStyles(): void {\r\n    // Add any default styles or modifications\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      /* Default Gladiatus Helper Bot styles */\r\n      .gh-highlight-underworld {\r\n        border: 2px solid #e74c3c !important;\r\n        box-shadow: 0 0 10px rgba(231, 76, 60, 0.5) !important;\r\n      }\r\n\r\n      .gh-item-quality-white { background-color: var(--gh-item-white, rgba(255, 255, 255, 0.4)) !important; }\r\n      .gh-item-quality-green { background-color: var(--gh-item-green, rgba(0, 255, 0, 0.3)) !important; }\r\n      .gh-item-quality-blue { background-color: var(--gh-item-blue, rgba(81, 89, 247, 0.4)) !important; }\r\n      .gh-item-quality-purple { background-color: var(--gh-item-purple, rgba(227, 3, 224, 0.4)) !important; }\r\n      .gh-item-quality-orange { background-color: var(--gh-item-orange, rgba(255, 106, 0, 0.4)) !important; }\r\n      .gh-item-quality-red { background-color: var(--gh-item-red, rgba(255, 0, 0, 0.4)) !important; }\r\n    `;\r\n\r\n    document.head.appendChild(style);\r\n  }\r\n}\r\n\r\n// Initialize default script\r\nnew DefaultScript();"], "names": [], "sourceRoot": ""}