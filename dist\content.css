/*!********************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./src/styles/variables.css ***!
  \********************************************************************************************************/
/* CSS Variables for Gladiatus Helper Bot */

:root {
  /* Main theme colors */
  --gh-primary: #2c3e50;
  --gh-secondary: #34495e;
  --gh-success: #27ae60;
  --gh-warning: #f39c12;
  --gh-danger: #e74c3c;
  --gh-info: #3498db;

  /* Background colors */
  --gh-bg-primary: #2c3e50;
  --gh-bg-secondary: #34495e;
  --gh-bg-light: #ecf0f1;
  --gh-bg-dark: #1a252f;

  /* Text colors */
  --gh-text-primary: #ffffff;
  --gh-text-secondary: #bdc3c7;
  --gh-text-muted: #95a5a6;
  --gh-text-dark: #2c3e50;

  /* Border colors */
  --gh-border-primary: #34495e;
  --gh-border-secondary: #7f8c8d;
  --gh-border-light: #bdc3c7;

  /* Item quality colors */
  --gh-item-white: rgba(255, 255, 255, 0.4);
  --gh-item-green: rgba(0, 255, 0, 0.3);
  --gh-item-blue: rgba(81, 89, 247, 0.4);
  --gh-item-purple: rgba(227, 3, 224, 0.4);
  --gh-item-orange: rgba(255, 106, 0, 0.4);
  --gh-item-red: rgba(255, 0, 0, 0.4);

  /* Spacing */
  --gh-spacing-xs: 4px;
  --gh-spacing-sm: 8px;
  --gh-spacing-md: 16px;
  --gh-spacing-lg: 24px;
  --gh-spacing-xl: 32px;

  /* Border radius */
  --gh-radius-sm: 4px;
  --gh-radius-md: 8px;
  --gh-radius-lg: 12px;

  /* Shadows */
  --gh-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --gh-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --gh-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* Z-index layers */
  --gh-z-dropdown: 1000;
  --gh-z-sticky: 1020;
  --gh-z-fixed: 1030;
  --gh-z-modal-backdrop: 1040;
  --gh-z-modal: 1050;
  --gh-z-popover: 1060;
  --gh-z-tooltip: 1070;

  /* Transitions */
  --gh-transition-fast: 0.15s ease-in-out;
  --gh-transition-base: 0.3s ease-in-out;
  --gh-transition-slow: 0.5s ease-in-out;

  /* Font sizes */
  --gh-font-xs: 0.75rem;
  --gh-font-sm: 0.875rem;
  --gh-font-base: 1rem;
  --gh-font-lg: 1.125rem;
  --gh-font-xl: 1.25rem;
  --gh-font-2xl: 1.5rem;

  /* Font weights */
  --gh-font-normal: 400;
  --gh-font-medium: 500;
  --gh-font-semibold: 600;
  --gh-font-bold: 700;
}
/*!****************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./src/styles/panel.css ***!
  \****************************************************************************************************/
/* Bot Control Panel Styles */

.gh-panel {
  position: fixed;
  top: var(--gh-spacing-md);
  right: var(--gh-spacing-md);
  z-index: var(--gh-z-fixed);
  width: 280px;
  background: var(--gh-bg-primary);
  border: 1px solid var(--gh-border-primary);
  border-radius: var(--gh-radius-md);
  color: var(--gh-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--gh-font-sm);
  box-shadow: var(--gh-shadow-lg);
  user-select: none;
}

.gh-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gh-spacing-md);
  border-bottom: 1px solid var(--gh-border-primary);
  background: var(--gh-bg-secondary);
  border-radius: var(--gh-radius-md) var(--gh-radius-md) 0 0;
}

.gh-panel-header h3 {
  margin: 0;
  font-size: var(--gh-font-base);
  font-weight: var(--gh-font-semibold);
  color: var(--gh-text-primary);
}

.gh-panel-content {
  padding: var(--gh-spacing-md);
}

.gh-status-section {
  margin-bottom: var(--gh-spacing-md);
}

.gh-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--gh-spacing-sm);
  margin-bottom: var(--gh-spacing-sm);
}

.gh-status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--gh-danger);
  transition: background-color var(--gh-transition-base);
}

.gh-status-dot.active {
  background-color: var(--gh-success);
}

.gh-status-dot.paused {
  background-color: var(--gh-warning);
}

.gh-stats-section {
  margin-bottom: var(--gh-spacing-md);
  padding: var(--gh-spacing-sm);
  background: var(--gh-bg-secondary);
  border-radius: var(--gh-radius-sm);
}

.gh-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--gh-spacing-xs);
}

.gh-stat-item:last-child {
  margin-bottom: 0;
}

.gh-stat-label {
  color: var(--gh-text-secondary);
  font-size: var(--gh-font-xs);
}

.gh-stat-value {
  color: var(--gh-text-primary);
  font-weight: var(--gh-font-medium);
  font-size: var(--gh-font-sm);
}

.gh-actions-section {
  display: flex;
  gap: var(--gh-spacing-sm);
}

/* Button Styles */
.gh-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--gh-spacing-sm) var(--gh-spacing-md);
  border: none;
  border-radius: var(--gh-radius-sm);
  font-size: var(--gh-font-sm);
  font-weight: var(--gh-font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--gh-transition-base);
  user-select: none;
}

.gh-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--gh-shadow-md);
}

.gh-btn:active {
  transform: translateY(0);
}

.gh-btn-primary {
  background: var(--gh-success);
  color: var(--gh-text-primary);
}

.gh-btn-primary:hover {
  background: #229954;
}

.gh-btn-secondary {
  background: var(--gh-bg-secondary);
  color: var(--gh-text-primary);
  border: 1px solid var(--gh-border-primary);
}

.gh-btn-secondary:hover {
  background: var(--gh-border-primary);
}

.gh-btn-sm {
  padding: var(--gh-spacing-xs) var(--gh-spacing-sm);
  font-size: var(--gh-font-xs);
  min-width: 24px;
  height: 24px;
}

.gh-btn-danger {
  background: var(--gh-danger);
  color: var(--gh-text-primary);
}

.gh-btn-danger:hover {
  background: #c0392b;
}

.gh-btn-warning {
  background: var(--gh-warning);
  color: var(--gh-text-primary);
}

.gh-btn-warning:hover {
  background: #d68910;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gh-panel {
    width: 260px;
    top: var(--gh-spacing-sm);
    right: var(--gh-spacing-sm);
  }

  .gh-actions-section {
    flex-direction: column;
  }

  .gh-btn {
    width: 100%;
  }
}
/*!****************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./src/styles/modal.css ***!
  \****************************************************************************************************/
/* Modal Styles */

.gh-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--gh-z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

.gh-modal-content {
  background: var(--gh-bg-primary);
  color: var(--gh-text-primary);
  border-radius: var(--gh-radius-md);
  box-shadow: var(--gh-shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  animation: scaleIn 0.3s ease-out forwards;
}

.gh-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gh-spacing-lg);
  border-bottom: 1px solid var(--gh-border-primary);
  background: var(--gh-bg-secondary);
}

.gh-modal-header h2 {
  margin: 0;
  font-size: var(--gh-font-xl);
  font-weight: var(--gh-font-semibold);
  color: var(--gh-text-primary);
}

.gh-modal-close {
  background: none;
  border: none;
  color: var(--gh-text-secondary);
  font-size: var(--gh-font-2xl);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--gh-radius-sm);
  transition: all var(--gh-transition-base);
}

.gh-modal-close:hover {
  background: var(--gh-bg-primary);
  color: var(--gh-text-primary);
}

.gh-modal-body {
  padding: var(--gh-spacing-lg);
  overflow-y: auto;
  max-height: 70vh;
}

.gh-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--gh-spacing-sm);
  padding: var(--gh-spacing-lg);
  border-top: 1px solid var(--gh-border-primary);
  background: var(--gh-bg-secondary);
}

/* Statistics Modal Specific Styles */
.gh-stat-group {
  margin-bottom: var(--gh-spacing-lg);
}

.gh-stat-group:last-child {
  margin-bottom: 0;
}

.gh-stat-group h3 {
  margin: 0 0 var(--gh-spacing-md) 0;
  font-size: var(--gh-font-lg);
  font-weight: var(--gh-font-semibold);
  color: var(--gh-text-primary);
  border-bottom: 1px solid var(--gh-border-primary);
  padding-bottom: var(--gh-spacing-sm);
}

.gh-stat-group p {
  margin: 0 0 var(--gh-spacing-sm) 0;
  color: var(--gh-text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gh-stat-group p:last-child {
  margin-bottom: 0;
}

/* Settings Modal Styles */
.gh-settings-section {
  margin-bottom: var(--gh-spacing-xl);
}

.gh-settings-section:last-child {
  margin-bottom: 0;
}

.gh-settings-section h3 {
  margin: 0 0 var(--gh-spacing-md) 0;
  font-size: var(--gh-font-lg);
  font-weight: var(--gh-font-semibold);
  color: var(--gh-text-primary);
}

.gh-form-group {
  margin-bottom: var(--gh-spacing-md);
}

.gh-form-group:last-child {
  margin-bottom: 0;
}

.gh-form-label {
  display: block;
  margin-bottom: var(--gh-spacing-xs);
  font-size: var(--gh-font-sm);
  font-weight: var(--gh-font-medium);
  color: var(--gh-text-primary);
}

.gh-form-input,
.gh-form-select,
.gh-form-textarea {
  width: 100%;
  padding: var(--gh-spacing-sm);
  border: 1px solid var(--gh-border-primary);
  border-radius: var(--gh-radius-sm);
  background: var(--gh-bg-secondary);
  color: var(--gh-text-primary);
  font-size: var(--gh-font-sm);
  transition: border-color var(--gh-transition-base);
}

.gh-form-input:focus,
.gh-form-select:focus,
.gh-form-textarea:focus {
  outline: none;
  border-color: var(--gh-info);
}

.gh-form-checkbox {
  display: flex;
  align-items: center;
  gap: var(--gh-spacing-sm);
}

.gh-form-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--gh-success);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
  }
  to {
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.9);
  }
}

.gh-modal.closing {
  animation: fadeOut 0.3s ease-in forwards;
}

.gh-modal.closing .gh-modal-content {
  animation: scaleOut 0.3s ease-in forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gh-modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .gh-modal-header,
  .gh-modal-body,
  .gh-modal-footer {
    padding: var(--gh-spacing-md);
  }

  .gh-modal-footer {
    flex-direction: column;
  }

  .gh-modal-footer .gh-btn {
    width: 100%;
  }
}
/*!************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./src/styles/notifications.css ***!
  \************************************************************************************************************/
/* Notification System Styles */

.gh-notifications {
  position: fixed;
  top: var(--gh-spacing-md);
  left: var(--gh-spacing-md);
  z-index: var(--gh-z-modal);
  max-width: 400px;
  pointer-events: none;
}

.gh-notification {
  display: flex;
  align-items: flex-start;
  gap: var(--gh-spacing-sm);
  padding: var(--gh-spacing-md);
  margin-bottom: var(--gh-spacing-sm);
  background: var(--gh-bg-primary);
  border: 1px solid var(--gh-border-primary);
  border-radius: var(--gh-radius-md);
  box-shadow: var(--gh-shadow-lg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--gh-font-sm);
  color: var(--gh-text-primary);
  pointer-events: auto;
  transform: translateX(-100%);
  animation: slideIn 0.3s ease-out forwards;
}

.gh-notification:last-child {
  margin-bottom: 0;
}

.gh-notification-content {
  flex: 1;
}

.gh-notification-content strong {
  display: block;
  margin-bottom: var(--gh-spacing-xs);
  font-weight: var(--gh-font-semibold);
  color: var(--gh-text-primary);
}

.gh-notification-content p {
  margin: 0;
  color: var(--gh-text-secondary);
  line-height: 1.4;
}

.gh-notification-close {
  background: none;
  border: none;
  color: var(--gh-text-secondary);
  font-size: var(--gh-font-lg);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--gh-radius-sm);
  transition: all var(--gh-transition-base);
}

.gh-notification-close:hover {
  background: var(--gh-bg-secondary);
  color: var(--gh-text-primary);
}

/* Notification Types */
.gh-notification-info {
  border-left: 4px solid var(--gh-info);
}

.gh-notification-success {
  border-left: 4px solid var(--gh-success);
}

.gh-notification-warning {
  border-left: 4px solid var(--gh-warning);
}

.gh-notification-error {
  border-left: 4px solid var(--gh-danger);
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.gh-notification.removing {
  animation: slideOut 0.3s ease-in forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gh-notifications {
    left: var(--gh-spacing-sm);
    right: var(--gh-spacing-sm);
    max-width: none;
  }

  .gh-notification {
    padding: var(--gh-spacing-sm);
  }
}
/*!******************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./src/styles/content.css ***!
  \******************************************************************************************************/
/* Gladiatus Helper Bot - Main Styles */

/* Import modular stylesheets */

/* Legacy variables for compatibility */
:root {
    --main-background: #E7DDBA;
    --main-border: #c4ac70;
    --scroll-bar-track-background: #d3c195;
    --images-folder: 'chrome-extension://__MSG_@@extension_id__/assets/images/';
    --menu-width: 120px;
}
.range_container {
    --_marker-border-clr: rgba(175, 139, 42, 0.6);
    --_marker-size: 15px;
    --_track-heigt: 3px;

    --_tooltip-bg-clr: rgba(0, 0, 0, 0.6);
    --_tooltip-txt-clr: var(--_marker-border-clr);

    width: 100%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    margin-bottom: 18px !important;
}

.sliders_control {
    margin-top:  20px;
    position: relative;
}
#pauseList {
    margin-top: 8px;
    display: block;
}

#fileInput {
    display: none;
}

/* Style the label to look like a button */
.custom-file-input {
    display: inline-block;
    margin: 0 3px;
    padding: 0 6px;
    border: none;
    background-color: #755229; /* Darker brown */
    color: white !important;
    font-weight: normal !important;
    cursor: pointer;
    border-radius: 10px;
    font-size: 12px;
    box-sizing: border-box;
    transition: background-color 0.3s;
}
.slider-tooltip {
    width: 26px;
    position: absolute;
    top: -2.5rem;
    left: 0;
    background-color: var(--_tooltip-bg-clr);
    color: var(--_tooltip-txt-clr);
    font-size: 1rem;
    border-radius: 4px;
    padding: 0.5rem 0.5rem;
    text-align: center;
    translate: -50% 0;
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    pointer-events: all;
    width: var(--_marker-size);
    height: var(--_marker-size);
    background-color: var(--_marker-border-clr);
    border-radius: 50%;
    box-shadow: 0 0 0 1px var(--_marker-border-clr);
    cursor: pointer;
}

input[type=range]::-moz-range-thumb {
    -webkit-appearance: none;
    pointer-events: all;
    width: var(--_marker-size);
    height: var(--_marker-size);
    background-color: var(--_marker-border-clr);
    border-radius: 50%;
    box-shadow: 0 0 0 1px var(--_marker-border-clr);
    cursor: pointer;
}

input[type=range]::-webkit-slider-thumb:hover {
    background: #f7f7f7;
}

input[type=range]::-webkit-slider-thumb:active {
    box-shadow: inset 0 0 3px rgba(195, 171, 111, 0.6), 0 0 9px rgba(195, 171, 111, 0.6);
    -webkit-box-shadow: inset 0 0 3px rgba(195, 171, 111, 0.6), 0 0 9px rgba(195, 171, 111, 0.6);
}

input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: var(--_track-heigt);
    width: -webkit-fill-available;
    position: absolute;
    background-color: var(--_marker-border-clr);
    pointer-events: none;
}

#fromSlider {
    height: 0;
    z-index: 1;
    border: unset;
}

.scale {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    position: relative;
    width: calc(100% - var(--_marker-size));
    margin-inline: auto;
    font-size: 0.8rem;
}

.scale div {
    position: absolute;
    translate: -50% 0;
}

.scale div::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    translate: -50% -125%;
    width: 1px;
    height: 10px;
    background-color:#666;
}
.discord-button {
    position: absolute;
    top: 4px;
    left: 6px;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s;
}
.discord-button {
    transform: translate(0, 0);
    transition: transform .2s ease-in-out;
}

.discord-button:hover {
    transform: translate(-4px, 4px) scale(1.3);
}

.discord-logo {
    width: 24px;
    height: 24px;
}
.gh-content-scroll,
.dropdown-list {
    /* width */
    /* Track */
    /* Handle */
    /* Handle on hover */
}

.gh-content-scroll::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar {
    width: 7px !important;
}

.gh-content-scroll::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-track {
    background: var(--scroll-bar-track-background) !important;
}

.gh-content-scroll::-webkit-scrollbar-thumb,
.dropdown-list::-webkit-scrollbar-thumb {
    background: #612d04 !important;
    border-radius: 5px !important;
}

.gh-content-scroll::-webkit-scrollbar-thumb:hover,
.dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #4F1609 !important;
}

/* Modal Styles */
.gh-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.gh-modal-content {
  background: #2c3e50;
  color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 80%;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.gh-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #34495e;
}

.gh-modal-header h2 {
  margin: 0;
  color: #ecf0f1;
}

.gh-modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.gh-modal-close:hover {
  opacity: 1;
}

.gh-settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #34495e;
  border-radius: 5px;
}

.gh-settings-section h3 {
  margin: 0 0 15px 0;
  color: #3498db;
  font-size: 16px;
}

.gh-settings-section label {
  display: block;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 14px;
}

.gh-settings-section input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.2);
}

.gh-settings-section input[type="number"] {
  margin-left: 10px;
  padding: 5px;
  border: 1px solid #7f8c8d;
  border-radius: 3px;
  background: #2c3e50;
  color: white;
  width: 60px;
}

.gh-modal-footer {
  margin-top: 20px;
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #34495e;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Notifications */
.gh-notifications {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 9999;
  max-width: 400px;
}

.gh-notification {
  background: #2c3e50;
  color: white;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  animation: slideIn 0.3s ease-out;
}

.gh-notification-info {
  border-left: 4px solid #3498db;
}

.gh-notification-success {
  border-left: 4px solid #27ae60;
}

.gh-notification-warning {
  border-left: 4px solid #f39c12;
}

.gh-notification-error {
  border-left: 4px solid #e74c3c;
}

.gh-notification-content {
  flex: 1;
}

.gh-notification-content strong {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.gh-notification-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.gh-notification-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.gh-notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.gh-hide {
    display: none !important;
}

#char .awesome-button {
    padding: 0;
    font-size: 16px;
    line-height: 1;
    width: 20px;
    height: 19px;
}

#char .put-down {
    position: absolute;
    bottom: 23px;
    right: 44px;
}

#char .put-up {
    position: absolute;
    right: 68px;
    bottom: 23px;
}

#char .repair {
    position: absolute;
    right: 14px;
    bottom: 23px;
    width: 20px;
    height: 19px;
}

#char .btn-add-arena-attack-list {
    position: absolute;
    right: 55px;
    top: 21px;
}

#char .btn-add-circus-attack-list {
    position: absolute;
    right: 25px;
    top: 21px;
}

#char .add-remove-player {
    font-weight: bold;
    animation-name: bubble-text;
    animation-duration: 5s;
    animation-fill-mode: forwards;
    animation-timing-function: ease;
    position: absolute;
    text-align: center;
    width: 230px;
    left: 50px;
    font-size: 16px;
}

#char .add-remove-player.add {
    color: lawngreen;
}

#char .add-remove-player.remove {
    color: red;
}

@keyframes bubble-text {
    0% {
        transform: scale(1, 1);
    }
    100% {
        transform: scale(2, 2);
    }
}

.merchant-settings,
.package-settings {
    background-color: var(--main-background);
}

.merchant-settings ul,
.package-settings ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.merchant-settings ul li,
.package-settings ul li {
    display: inline-block;
    white-space: nowrap;
}

.merchant-settings ul li label,
.package-settings ul li label {
    position: relative;
    top: -2px;
}

.merchant-settings ul li,
.package-settings ul li {
    display: inline-block;
}

.merchant-settings .actions,
.package-settings .actions {
    text-align: center;
    margin-left: -10px;
    margin-right: -10px;
}

.merchant-settings {
    margin: 0 21px 10px;
}

.merchant-settings li {
    width: 50%;
}

.package-settings {
    margin: 0 0 10px;
}

.package-settings li {
    width: 25%;
}

.package-settings li .pick-level {
    width: 40px;
}

.package-settings li .pick-gold {
    width: 80px;
}
.package-settings .item-type-list {
    display: flex;
    flex-wrap: wrap;
}
.package-settings .item-type-list .item-type {
    border: 2px solid var(--main-border);
    border-radius: 3px;
    display: block;
    width: 28px;
    cursor: pointer;
    opacity: 0.6;
    margin-right: 2px;
    position: relative;
    height: 28px;
    overflow: hidden;
    margin-bottom: 2px;
}
.package-settings .item-type-list .item-type.active {
    opacity: 1;
    border: 2px solid green;
}
.package-settings .item-type-list .item-type .item-type-icon {
    display: block;
}
.package-settings .item-type-list .item-type .item-type-icon.icon-big-size {
    transform: scale(.5) translate(-36px, -36px);
}
.package-settings .item-type-list .item-type .item-type-icon.item-i-1-3 {
    transform: scale(0.5) translate(-4px,-36px);
}
.package-settings .item-type-list .item-type .item-type-icon.icon-small-size {
    transform: scale(0.9) translate(-2px, -3px);
}
.package-settings .item-type-list .item-type .item-type-icon.icon-medium-size {
    transform: scale(0.6) translate(-6px, -6px);
}
.package-settings .item-type-list .item-type.with-quality {
    display: flex;
    align-items: center;
    justify-content: center;
}
.package-settings .item-type-list .item-type .quality {
    width: 22px;
    height: 22px;
    border-radius: 5px;
    display: block;
}
.package-settings .item-type-list .item-type.separator {
    width: 1px;
    border-color: black;
    background-color: black;
}
.package-settings .item-type-list .item-type.with-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding: 0 5px;
}
.workbench-actions,
.smelter-actions {
    text-align: center;
    margin: 0 21px 10px;
    padding-bottom: 0;
}

.workbench-actions .workbench_quality,
.smelter-actions .workbench_quality {
    display: inline-block;
    width: 155px;
}

.package-settings,
.merchant-settings {
    padding: 7px !important;
}

.package-settings .awesome-button,
.merchant-settings .awesome-button,
.workbench-actions .awesome-button,
.smelter-actions .awesome-button {
    margin-bottom: 5px;
}

.hammer {
    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/hammer1.png'), auto;
}

.burn {
    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn.png'), auto;
}

.packageItem {
    position: relative;
}

.packageItem .burnable {
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn-off.png');
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
    display: block;
    position: absolute;
    top: -4px;
    background-size: cover;
    left: -1px;
    cursor: pointer;
}

.packageItem .burnable.burn-selected {
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn-base.png') !important;
}

.cart {
    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/cart.png'), auto;
}

.fixing-inprogress,
.repair-inprogess,
.loading {
    cursor: not-allowed;
}

.fixing-inprogress::before,
.repair-inprogess::before,
.loading::before {
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    display: block;
    content: '';
    background-position: center;
    transform: translate(-2px, -2px);
}

.fixing-inprogress,
.loading {
    opacity: 0.3;
}

.fixing-inprogress::before,
.loading::before {
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/spinner.gif');
    background-size: 50%;
}

.repair-inprogess {
    z-index: 999;
}

.repair-inprogess::before {
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/hammer_ok.gif');
    background-size: 30px;
}

.gh-overlay {
    width: 100%;
    height: 100%;
    background-color: black;
    position: fixed;
    top: 0;
    z-index: 998;
    opacity: 0.5;
    display: none;
}

.gh-error-message {
    color: red;
    display: block;
    text-align: center;
    font-weight: bold;
    margin-top: 10px;
}

.inventoryBox .section-header {
    margin: 15px 21px 0;
}

.quick-offer {
    right: -50%;
    position: relative;
    transform: translateX(-50%);
    margin-bottom: 5px;
}

.underworld-item {
    background-color: red !important;
}

.gh-timer {
    display: inline-block;
    white-space: nowrap;
    font-size: 13px;
    color: yellow;
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/header_bg.png');
    z-index: 999999;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    min-width: 100px;
}

.gh-timer.bottom {
    position: absolute;
    top: -21px;
    left: 50%;
    transform: translateX(-50%);
    padding: 0px 20px;
    text-align: center;
}

.gh-timer.menu-right {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    padding: 0 10px;
    text-align: center;
}

.gh-pause {
    position: relative;
    width: 26px;
    height: 25px;
    background-color: hsl(32, 50%, 70%);
    border-radius: 4px;
}

.gh-pause:hover {
    background-color: hsl(32, 50%, 80%);
}

.gh-pause .button {
    box-sizing: border-box;
    height: 16px;
    border-color: transparent transparent transparent green;
    transition: 100ms all ease;
    will-change: border-width;
    cursor: pointer;
    border-style: solid;
    border-width: 8px 0 8px 14px;
    display: block;
    width: 16px;
    position: absolute;
    left: 7px;
    top: 5px;
}

.gh-pause.pause .button {
    border-style: double;
    border-width: 0px 0 0px 14px;
    border-left-color: red;
    left: 6px;
}

.gh-pause.disabled {
    cursor: not-allowed !important;
}

.gh-pause.disabled .button {
    opacity: 0.3;
    cursor: not-allowed !important;
    pointer-events: none;
}

#selectedSmeltSummary {
    font-size: 12px;
    font-weight: normal;
}

.single_char_item_bg.repair_slot_bg {
    display: none;
}

.gh-items-section {
    position: fixed;
    left: 0;
    top: 50%;
    width: 141px;
    z-index: 999;
}

.gh-items-section .gh-items-section-header {
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/header_bg.png');
    color: #dcbb96 !important;
    padding: 0 2px;
    display: flex;
    justify-content: flex-start;
    height: 18px;
    border-top-left-radius: 3px;
    border-top-right-radius: 4px;
    cursor: move;
    user-select: none;
}

.gh-items-section .gh-items-section-header label, .popup-header {
    cursor: move;
}
.popup-header {
    width: 100%;
}
.gh-items-section .gh-items-section-content {
    max-height: 100px;
    min-height: 52px;
    left: 0;
    top: 50%;
    background-color: var(--main-background);
    overflow-y: auto;
    padding-bottom: 3px;
}

.gh-items-section .gh-items-section-content .auction_item_div {
    transform: scale(0.5);
    margin: -22px -17px -25px -14px;
    cursor: pointer;
}

.gh-items-section .gh-items-section-content .auction_item_div .outbid {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 0;
}

.gh-items-section .gh-items-section-content .auction_item_div .outbid.quest_slot_button_finish {
    bottom: -2px;
    right: -2px;
    transform: scale(0.8);
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-white {
    filter: drop-shadow(4px 4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(4px -4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(-4px -4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(-4px 4px 4px rgba(255, 255, 255, 0.4));
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-green {
    filter: drop-shadow(5px 5px 5px var(--gh-item-green)) drop-shadow(5px -5px 5px var(--gh-item-green)) drop-shadow(-5px -5px 5px var(--gh-item-green)) drop-shadow(-5px 5px 5px var(--gh-item-green));
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-blue {
    filter: drop-shadow(5px 5px 5px var(--gh-item-blue)) drop-shadow(5px -5px 5px var(--gh-item-blue)) drop-shadow(-5px -5px 5px var(--gh-item-blue)) drop-shadow(-5px 5px 5px var(--gh-item-blue));
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-purple {
    animation: purpleShadowAnimation 2s linear infinite alternate;
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-orange {
    animation: orangeShadowAnimation 2s linear infinite alternate;
}

.gh-items-section .gh-items-section-content .auction_item_div .item-quality-red {
    animation: redShadowAnimation 2s linear infinite alternate;
}

.gh-info {
    margin-left: 3px;
    cursor: pointer;
}
.row {
    display: flex;
    flex-direction: row;
    gap: 4px;
    padding: 6px;
}
.row-wrapper {
    overflow: hidden;
}

.ml-5 {
    margin-left: 5px;
}

.mr-5 {
    margin-right: 5px;
}

ul.dropdown-list {
    visibility: hidden;
    list-style: none;
    margin: 0;
    position: absolute;
    top: 100%;
    left: -2px;
    background: white;
    padding: 0;
    border: 1px solid #ccc;
    z-index: 99999;
    max-height: 200px;
    overflow-y: auto;
}

ul.dropdown-list.open {
    visibility: visible;
}

ul.dropdown-list .dropdown-item {
    display: block;
    padding: 2px 5px;
    white-space: nowrap;
    cursor: initial;
}

ul.dropdown-list .dropdown-item.has-icon {
    max-height: 31px;
}

ul.dropdown-list .dropdown-item.has-icon > div {
    display: inline-block;
    transform: scale(0.75);
}

ul.dropdown-list .dropdown-item.has-icon .dropdown-item-text {
    position: relative;
    top: -10px;
}

ul.dropdown-list .dropdown-item:hover,
ul.dropdown-list .dropdown-item.selected {
    background-color: lightblue;
}

.gh-animate-item-color [data-quality="2"] {
    animation: purpleShadowAnimation 2s linear infinite alternate;
}

.gh-animate-item-color [data-quality="3"] {
    animation: orangeShadowAnimation 2s linear infinite alternate;
}

.gh-animate-item-color [data-quality="4"] {
    animation: redShadowAnimation 2s linear infinite alternate;
}

@keyframes redShadowAnimation {
    0% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-red)) drop-shadow(2px -2px 2px var(--gh-item-red)) drop-shadow(-2px -2px 2px var(--gh-item-red)) drop-shadow(-2px 2px 2px var(--gh-item-red));
    }
    50% {
        filter: drop-shadow(4px 4px 4px var(--gh-item-red)) drop-shadow(4px -4px 4px var(--gh-item-red)) drop-shadow(-4px -4px 4px var(--gh-item-red)) drop-shadow(-4px 4px 4px var(--gh-item-red));
    }
    100% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-red)) drop-shadow(2px -2px 2px var(--gh-item-red)) drop-shadow(-2px -2px 2px var(--gh-item-red)) drop-shadow(-2px 2px 2px var(--gh-item-red));
    }
}

@keyframes orangeShadowAnimation {
    0% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-orange)) drop-shadow(2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px 2px 2px var(--gh-item-orange));
    }
    50% {
        filter: drop-shadow(4px 4px 4px var(--gh-item-orange)) drop-shadow(4px -4px 4px var(--gh-item-orange)) drop-shadow(-4px -4px 4px var(--gh-item-orange)) drop-shadow(-4px 4px 4px var(--gh-item-orange));
    }
    100% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-orange)) drop-shadow(2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px 2px 2px var(--gh-item-orange));
    }
}

@keyframes purpleShadowAnimation {
    0% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-purple)) drop-shadow(2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px 2px 2px var(--gh-item-purple));
    }
    50% {
        filter: drop-shadow(4px 4px 4px var(--gh-item-purple)) drop-shadow(4px -4px 4px var(--gh-item-purple)) drop-shadow(-4px -4px 4px var(--gh-item-purple)) drop-shadow(-4px 4px 4px var(--gh-item-purple));
    }
    100% {
        filter: drop-shadow(2px 2px 2px var(--gh-item-purple)) drop-shadow(2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px 2px 2px var(--gh-item-purple));
    }
}

.gh-icon-auction-house {
    background-repeat: no-repeat;
    background-size: cover;
    width: 13px;
    height: 13px;
    position: relative;
    top: 2px;
    margin: 0 2px;
    display: inline-block;
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/auction-house.png');
}

.gh-icon-shop {
    background-repeat: no-repeat;
    background-size: cover;
    width: 13px;
    height: 13px;
    position: relative;
    top: 2px;
    margin: 0 2px;
    display: inline-block;
    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/shop.png');
}
.shop-cart {
    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/cart.png'), auto;
}


/*# sourceMappingURL=content.css.map*/