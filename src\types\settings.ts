// Settings and configuration type definitions

import { ItemType, ItemQuality } from './gladiatus';

export interface PackageSettings {
  rotateItems: RotateItemsSettings;
  sellItems: SellItemsSettings;
  pickGold: PickGoldSettings;
  operationSpeed: OperationSpeed;
}

export interface RotateItemsSettings {
  enabled: boolean;
  selectedItems: ItemType[];
  colors: ItemQuality[];
  underworldItems: boolean;
  itemsCooldownDays: number;
  collectResourceHourly: boolean;
}

export interface SellItemsSettings {
  enabled: boolean;
  rules: SellRule[];
  autoSellOnRenew: boolean;
}

export interface SellRule {
  id: string;
  conditions: FilterCondition[];
  itemTypes: ItemType[];
  enabled: boolean;
}

export interface FilterCondition {
  type: FilterType;
  value: string | number;
}

export enum FilterType {
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  CONTAINS_ANY = 'contains_any',
  NOT_CONTAINS_ANY = 'not_contains_any',
  CONTAINS_WORD = 'contains_word',
  IS_UNDERWORLD = 'is_underworld',
  IS_NOT_UNDERWORLD = 'is_not_underworld',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  CONTAINS_RESOURCE = 'contains_resource'
}

export interface PickGoldSettings {
  enabled: boolean;
  goldToPick: number;
}

export enum OperationSpeed {
  VERY_FAST = 'very_fast',
  FAST = 'fast',
  NORMAL = 'normal',
  SLOW = 'slow'
}

export interface CostumeSettings {
  wearUnderworld: boolean;
  preventWearUnderworldOnPause: boolean;
  dontPauseIfUnderworldActive: boolean;
  underworldCostume: UnderworldCostume;
  standardProfile: CostumeProfile;
  turmaProfile: CostumeProfile;
  eventProfile: CostumeProfile;
}

export enum UnderworldCostume {
  DIS_PATER_NORMAL = 'dis_pater_normal',
  DIS_PATER_MEDIUM = 'dis_pater_medium',
  DIS_PATER_HARD = 'dis_pater_hard',
  VULCANUS_FORGE = 'vulcanus_forge',
  FERONIAS_EARTHEN_SHIELD = 'feronias_earthen_shield',
  NEPTUNES_FLUID_MIGHT = 'neptunes_fluid_might',
  AELOUS_AERIAL_FREEDOM = 'aelous_aerial_freedom',
  PLUTOS_DEADLY_MIST = 'plutos_deadly_mist',
  JUNOS_BREATH_OF_LIFE = 'junos_breath_of_life',
  WRATH_MOUNTAIN_SCALE_ARMOUR = 'wrath_mountain_scale_armour',
  EAGLE_EYES = 'eagle_eyes',
  SATURNS_WINTER_GARMENT = 'saturns_winter_garment',
  BUBONA_BULL_ARMOUR = 'bubona_bull_armour',
  MERCERIUS_ROBBERS_GARMENTS = 'mercerius_robbers_garments',
  RA_LIGHT_ROBE = 'ra_light_robe'
}

export interface CostumeProfile {
  enabled: boolean;
  costume?: UnderworldCostume;
}

export interface TurmaSettings {
  enabled: boolean;
  levelRestriction: number;
  autoStop: number;
  attackIfQuest: boolean;
  runUntilGetChest: boolean;
  goldRaided: number;
  attackPlayers: string[];
  autoIgnorePlayers: string[];
  ignorePlayers: string[];
  attackSameServer: boolean;
}

export interface ArenaSettings {
  enabled: boolean;
  allowSim: boolean;
  prioritizeChance: number;
  skipLow: boolean;
  loseLimit: number;
  attackTargetPlayers: boolean;
  allowMaxAttacks: boolean;
  autoIgnoreGuildPlayers: boolean;
  attackScore: boolean;
  selectScorePage: number;
  progressLeague: boolean;
  stopAfterLimit: boolean;
  attackGoldWhale: boolean;
  doRevenge: boolean;
  totalWin: boolean;
  limitAttacks: number;
  dailyAttacks: number;
  autoStop: number;
  attackIfQuest: boolean;
  attackIfCombatQuest: boolean;
  goldRaided: number;
  attackPlayers: string[];
  autoIgnorePlayers: string[];
  ignorePlayers: string[];
  attackSameServer: boolean;
  attackScoreSingle: boolean;
}

export interface ExpeditionSettings {
  enabled: boolean;
  minDungeonPoints: number;
  location: string;
  mob: string;
  autoCollectBonuses: boolean;
  travelForDungeon: boolean;
  removeCooldown: CooldownRemovalOption;
  cooldownLimit: number;
  cooldownUsed: number;
}

export enum CooldownRemovalOption {
  HOURGLASS = 'hourglass',
  HOURGLASS_OR_RUBY = 'hourglass_or_ruby'
}

export interface DungeonSettings {
  enabled: boolean;
  location: string;
  useGateKey: boolean;
  isAdvanced: boolean;
  skipBoss: boolean;
  bossName: string;
  restartAfterFail: number;
}

export interface EventSettings {
  enabled: boolean;
  mob: string;
  autoCollectBonuses: boolean;
  autoRenewEvent: boolean;
  followLeader: string;
  autoStop: boolean;
}

export interface UnderworldSettings {
  enabled: boolean;
  noWeapon: boolean;
  sendMail: boolean;
  mailContent: string;
  maxMedics: number;
  usedMedics: number;
  maxRuby: number;
  usedRuby: number;
  buffs: boolean;
  costumes: boolean;
  reinforcements: string[];
  mode: UnderworldMode;
  difficulty: UnderworldDifficulty;
  healGuild: boolean;
  healPotions: boolean;
  healSacrifice: boolean;
  allowMobilisation: boolean;
  allowRuby: boolean;
  attackDisPaterAsap: boolean;
  stopArenaInUnder: boolean;
  healCostumes: boolean;
  allowReinforcements: boolean;
  allowUpgrades: boolean;
  autoBuffGods: boolean;
  autoBuffOils: boolean;
  exitUnder: boolean;
  stayHours: number;
}

export enum UnderworldMode {
  FARM_QUEST = 'farm_quest',
  FARM_MOB = 'farm_mob',
  NORMAL = 'normal'
}

export enum UnderworldDifficulty {
  NORMAL = 'normal',
  MEDIUM = 'medium',
  HARD = 'hard'
}

export interface QuestManagementSettings {
  enabled: boolean;
  autoComplete: boolean;
  dailyLimit: number;
  rules: QuestManagementRule[];
}

export interface QuestManagementRule {
  priority: number;
  questType: string;
  minReward: number;
  maxDifficulty: 'easy' | 'medium' | 'hard';
  autoComplete: boolean;
}

export interface MarketOperationsSettings {
  enabled: boolean;
  checkInterval: number;
  dailyBuyLimit: number;
  autoBuy: AutoBuySettings;
  autoSell: AutoSellSettings;
  auction: AuctionSettings;
}

export interface AutoBuySettings {
  enabled: boolean;
  maxPrice: number;
  maxGoldSpend: number;
  qualityFilter: ItemQuality[];
  minLevel: number;
  maxLevel: number;
  itemNames: string[];
  blacklistedSellers: string[];
}

export interface AutoSellSettings {
  enabled: boolean;
  rules: SellRule[];
}

export interface AuctionSettings {
  enabled: boolean;
  autoBid: boolean;
  autoBuyout: boolean;
  maxBid: number;
  maxBuyout: number;
  bidIncrement: number;
  minTimeRemaining: number;
  qualityFilter: ItemQuality[];
}

export interface ForgeAutomationSettings {
  enabled: boolean;
  autoForge: boolean;
  autoSmelt: boolean;
  maxGoldSpend: number;
  maxCostPerItem: number;
  dailyForgeLimit: number;
  qualityFilter: ItemQuality[];
  minLevel: number;
  maxLevel: number;
  itemTypes: string[];
  minSmeltValue: number;
  smeltQualityFilter: ItemQuality[];
}

export interface RepairAutomationSettings {
  enabled: boolean;
  repairThreshold: number;
  maxCostPerItem: number;
  maxGoldSpend: number;
  itemFilter: string[];
}