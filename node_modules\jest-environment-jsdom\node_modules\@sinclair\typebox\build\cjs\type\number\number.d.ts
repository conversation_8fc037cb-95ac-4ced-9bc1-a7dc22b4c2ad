import type { TSchema, SchemaOptions } from '../schema/index';
import { Kind } from '../symbols/index';
export interface NumberOptions extends SchemaOptions {
    exclusiveMaximum?: number;
    exclusiveMinimum?: number;
    maximum?: number;
    minimum?: number;
    multipleOf?: number;
}
export interface TNumber extends TSchema, NumberOptions {
    [Kind]: 'Number';
    static: number;
    type: 'number';
}
/** `[<PERSON><PERSON>]` Creates a Number type */
export declare function Number(options?: NumberOptions): TNumber;
