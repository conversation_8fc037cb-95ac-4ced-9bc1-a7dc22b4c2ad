import { UnderworldSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface UnderworldStatus {
    isActive: boolean;
    timeRemaining: number;
    currentWave: number;
    totalWaves: number;
    guildHealth: number;
    maxGuildHealth: number;
    playerHealth: number;
    maxPlayerHealth: number;
}
export interface UnderworldBoss {
    name: string;
    health: number;
    maxHealth: number;
    isDisPater: boolean;
    canAttack: boolean;
}
export declare class UnderworldSystemModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private medicsUsed;
    private rubyUsed;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: UnderworldSettings): Promise<void>;
    stop(): Promise<void>;
    private navigateToUnderworld;
    private performUnderworldLoop;
    private getUnderworldStatus;
    private handleUnderworldHealing;
    private healGuild;
    private useHealingPotions;
    private useSacrificeHealing;
    private applyBuffs;
    private manageCostumes;
    private handleUnderworldCombat;
    private findDisPater;
    private getAvailableBosses;
    private parseBossElement;
    private attackBoss;
    private manageReinforcements;
    private shouldContinueUnderworld;
    getUnderworldInfo(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=underworld-system.d.ts.map