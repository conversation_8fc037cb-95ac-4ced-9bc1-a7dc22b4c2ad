import { ForgeAutomationSettings, RepairAutomationSettings, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface ForgeableItem {
    id: string;
    name: string;
    quality: ItemQuality;
    level: number;
    materials: string[];
    canForge: boolean;
    forgeCost: number;
}
export interface RepairableItem {
    id: string;
    name: string;
    durability: number;
    maxDurability: number;
    repairCost: number;
    canRepair: boolean;
}
export interface SmeltableItem {
    id: string;
    name: string;
    quality: ItemQuality;
    level: number;
    smeltValue: number;
    canSmelt: boolean;
}
export declare class ForgeRepairModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private itemsForged;
    private itemsRepaired;
    private itemsSmelted;
    private goldSpent;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(forgeSettings: ForgeAutomationSettings, repairSettings: RepairAutomationSettings): Promise<void>;
    stop(): Promise<void>;
    private performForgeOperations;
    private performRepairOperations;
    private performSmelting;
    private performForging;
    private navigateToForge;
    private navigateToRepair;
    private getForgeableItems;
    private parseForgeableItem;
    private getRepairableItems;
    private parseRepairableItem;
    private getSmeltableItems;
    private parseSmeltableItem;
    private filterItemsForForging;
    private filterItemsForRepair;
    private filterItemsForSmelting;
    private forgeItem;
    private repairItem;
    private smeltItem;
    private shouldContinueForging;
    private shouldContinueRepairing;
    getForgeRepairStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=forge-repair.d.ts.map