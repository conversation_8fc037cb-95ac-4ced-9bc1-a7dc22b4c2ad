// Underworld System Module - Handles underworld combat and management

import { UnderworldSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface UnderworldStatus {
  isActive: boolean;
  timeRemaining: number;
  currentWave: number;
  totalWaves: number;
  guildHealth: number;
  maxGuildHealth: number;
  playerHealth: number;
  maxPlayerHealth: number;
}

export interface UnderworldBoss {
  name: string;
  health: number;
  maxHealth: number;
  isDisPater: boolean;
  canAttack: boolean;
}

export class UnderworldSystemModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private medicsUsed: number = 0;
  private rubyUsed: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: UnderworldSettings): Promise<void> {
    if (this.isRunning || !settings.enabled) return;
    
    this.isRunning = true;
    this.medicsUsed = 0;
    this.rubyUsed = 0;
    
    console.log('Underworld System: Starting underworld automation');
    
    try {
      await this.navigateToUnderworld();
      await this.performUnderworldLoop(settings);
    } catch (error) {
      console.error('Underworld System: Error in underworld automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Underworld System: Stopped underworld automation');
  }

  private async navigateToUnderworld(): Promise<void> {
    if (!window.location.href.includes('mod=underworld')) {
      console.log('Underworld System: Navigating to underworld');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=underworld`;
      
      await this.waitForPageLoad();
    }
  }

  private async performUnderworldLoop(settings: UnderworldSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueUnderworld(settings)) {
      try {
        const status = await this.getUnderworldStatus();
        
        if (!status.isActive) {
          console.log('Underworld System: Underworld not active');
          break;
        }

        // Handle healing
        await this.handleUnderworldHealing(settings, status);
        
        // Handle buffs
        if (settings.buffs) {
          await this.applyBuffs(settings);
        }
        
        // Handle costumes
        if (settings.costumes) {
          await this.manageCostumes(settings);
        }
        
        // Handle combat
        await this.handleUnderworldCombat(settings, status);
        
        // Handle reinforcements
        if (settings.allowReinforcements) {
          await this.manageReinforcements(settings);
        }
        
        await this.delay(10000); // 10 second loop
        
      } catch (error) {
        console.error('Underworld System: Error in underworld loop:', error);
        await this.delay(15000);
      }
    }
  }

  private async getUnderworldStatus(): Promise<UnderworldStatus> {
    const timeElement = document.querySelector('.underworld_timer, .timer');
    const waveElement = document.querySelector('.current_wave, .wave');
    const guildHealthElement = document.querySelector('.guild_health, .guild-hp');
    const playerHealthElement = document.querySelector('.player_health, .player-hp');
    
    const timeText = timeElement?.textContent?.trim() || '0:00:00';
    const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
    const timeRemaining = timeMatch ? 
      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
    
    const waveText = waveElement?.textContent?.trim() || '1/10';
    const waveMatch = waveText.match(/(\d+)\/(\d+)/);
    const currentWave = waveMatch ? parseInt(waveMatch[1]) : 1;
    const totalWaves = waveMatch ? parseInt(waveMatch[2]) : 10;
    
    const guildHealthText = guildHealthElement?.textContent?.trim() || '100/100';
    const guildHealthMatch = guildHealthText.match(/(\d+)\/(\d+)/);
    const guildHealth = guildHealthMatch ? parseInt(guildHealthMatch[1]) : 100;
    const maxGuildHealth = guildHealthMatch ? parseInt(guildHealthMatch[2]) : 100;
    
    const playerHealthText = playerHealthElement?.textContent?.trim() || '100/100';
    const playerHealthMatch = playerHealthText.match(/(\d+)\/(\d+)/);
    const playerHealth = playerHealthMatch ? parseInt(playerHealthMatch[1]) : 100;
    const maxPlayerHealth = playerHealthMatch ? parseInt(playerHealthMatch[2]) : 100;
    
    return {
      isActive: timeRemaining > 0,
      timeRemaining,
      currentWave,
      totalWaves,
      guildHealth,
      maxGuildHealth,
      playerHealth,
      maxPlayerHealth
    };
  }

  private async handleUnderworldHealing(settings: UnderworldSettings, status: UnderworldStatus): Promise<void> {
    // Heal guild if enabled and needed
    if (settings.healGuild && status.guildHealth < status.maxGuildHealth * 0.8) {
      await this.healGuild(settings);
    }
    
    // Heal player if needed
    const playerHealthPercent = (status.playerHealth / status.maxPlayerHealth) * 100;
    if (playerHealthPercent < 80) {
      if (settings.healPotions) {
        await this.useHealingPotions();
      }
      
      if (settings.healSacrifice && playerHealthPercent < 50) {
        await this.useSacrificeHealing();
      }
    }
  }

  private async healGuild(settings: UnderworldSettings): Promise<void> {
    // Use medics if available and under limit
    if (this.medicsUsed < settings.maxMedics) {
      const medicButton = document.querySelector('.use_medic, [data-action="use-medic"]');
      if (medicButton && !medicButton.hasAttribute('disabled')) {
        console.log('Underworld System: Using medic to heal guild');
        (medicButton as HTMLElement).click();
        this.medicsUsed++;
        await this.delay(2000);
        return;
      }
    }
    
    // Use ruby if allowed and under limit
    if (settings.allowRuby && this.rubyUsed < settings.maxRuby) {
      const rubyButton = document.querySelector('.use_ruby, [data-action="use-ruby"]');
      if (rubyButton && !rubyButton.hasAttribute('disabled')) {
        console.log('Underworld System: Using ruby to heal guild');
        (rubyButton as HTMLElement).click();
        this.rubyUsed++;
        await this.delay(2000);
      }
    }
  }

  private async useHealingPotions(): Promise<void> {
    const potionButton = document.querySelector('.use_potion, [data-action="use-potion"]');
    if (potionButton && !potionButton.hasAttribute('disabled')) {
      console.log('Underworld System: Using healing potion');
      (potionButton as HTMLElement).click();
      await this.delay(1500);
    }
  }

  private async useSacrificeHealing(): Promise<void> {
    const sacrificeButton = document.querySelector('.sacrifice_heal, [data-action="sacrifice"]');
    if (sacrificeButton && !sacrificeButton.hasAttribute('disabled')) {
      console.log('Underworld System: Using sacrifice healing');
      (sacrificeButton as HTMLElement).click();
      await this.delay(2000);
    }
  }

  private async applyBuffs(settings: UnderworldSettings): Promise<void> {
    // Auto buff gods if enabled
    if (settings.autoBuffGods) {
      const godBuffButtons = document.querySelectorAll('.god_buff, [data-action="buff-god"]');
      for (const button of godBuffButtons) {
        if (!button.hasAttribute('disabled')) {
          (button as HTMLElement).click();
          await this.delay(1000);
        }
      }
    }
    
    // Auto buff with oils if enabled
    if (settings.autoBuffOils) {
      const oilBuffButtons = document.querySelectorAll('.oil_buff, [data-action="buff-oil"]');
      for (const button of oilBuffButtons) {
        if (!button.hasAttribute('disabled')) {
          (button as HTMLElement).click();
          await this.delay(1000);
        }
      }
    }
  }

  private async manageCostumes(settings: UnderworldSettings): Promise<void> {
    // Heal costumes if enabled
    if (settings.healCostumes) {
      const healCostumeButton = document.querySelector('.heal_costume, [data-action="heal-costume"]');
      if (healCostumeButton && !healCostumeButton.hasAttribute('disabled')) {
        console.log('Underworld System: Healing costumes');
        (healCostumeButton as HTMLElement).click();
        await this.delay(2000);
      }
    }
  }

  private async handleUnderworldCombat(settings: UnderworldSettings, status: UnderworldStatus): Promise<void> {
    // Check for Dis Pater and attack ASAP if enabled
    if (settings.attackDisPaterAsap) {
      const disPater = await this.findDisPater();
      if (disPater && disPater.canAttack) {
        console.log('Underworld System: Attacking Dis Pater ASAP');
        await this.attackBoss(disPater);
        return;
      }
    }
    
    // Regular combat logic
    const availableBosses = await this.getAvailableBosses();
    for (const boss of availableBosses) {
      if (boss.canAttack) {
        await this.attackBoss(boss);
        break;
      }
    }
  }

  private async findDisPater(): Promise<UnderworldBoss | null> {
    const bossElements = document.querySelectorAll('.underworld_boss, .boss');
    
    for (const element of bossElements) {
      const nameElement = element.querySelector('.boss_name, .name');
      const name = nameElement?.textContent?.trim().toLowerCase() || '';
      
      if (name.includes('dis pater') || name.includes('dispater')) {
        return await this.parseBossElement(element as HTMLElement);
      }
    }
    
    return null;
  }

  private async getAvailableBosses(): Promise<UnderworldBoss[]> {
    const bosses: UnderworldBoss[] = [];
    const bossElements = document.querySelectorAll('.underworld_boss, .boss');
    
    for (const element of bossElements) {
      const boss = await this.parseBossElement(element as HTMLElement);
      if (boss) {
        bosses.push(boss);
      }
    }
    
    return bosses;
  }

  private async parseBossElement(element: HTMLElement): Promise<UnderworldBoss | null> {
    const nameElement = element.querySelector('.boss_name, .name');
    const healthElement = element.querySelector('.boss_health, .health');
    const attackButton = element.querySelector('.attack_boss, [data-action="attack"]');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const healthText = healthElement?.textContent?.trim() || '100/100';
    const healthMatch = healthText.match(/(\d+)\/(\d+)/);
    const health = healthMatch ? parseInt(healthMatch[1]) : 100;
    const maxHealth = healthMatch ? parseInt(healthMatch[2]) : 100;
    
    return {
      name,
      health,
      maxHealth,
      isDisPater: name.toLowerCase().includes('dis pater'),
      canAttack: !!(attackButton && !attackButton.hasAttribute('disabled'))
    };
  }

  private async attackBoss(boss: UnderworldBoss): Promise<void> {
    console.log(`Underworld System: Attacking ${boss.name}`);
    
    const bossElements = document.querySelectorAll('.underworld_boss, .boss');
    for (const element of bossElements) {
      const nameElement = element.querySelector('.boss_name, .name');
      if (nameElement?.textContent?.trim() === boss.name) {
        const attackButton = element.querySelector('.attack_boss, [data-action="attack"]');
        if (attackButton && !attackButton.hasAttribute('disabled')) {
          (attackButton as HTMLElement).click();
          await this.delay(3000);
          break;
        }
      }
    }
  }

  private async manageReinforcements(settings: UnderworldSettings): Promise<void> {
    if (settings.reinforcements.length === 0) return;
    
    for (const reinforcement of settings.reinforcements) {
      const reinforcementButton = document.querySelector(`[data-reinforcement="${reinforcement}"]`);
      if (reinforcementButton && !reinforcementButton.hasAttribute('disabled')) {
        console.log(`Underworld System: Calling reinforcement: ${reinforcement}`);
        (reinforcementButton as HTMLElement).click();
        await this.delay(2000);
      }
    }
  }

  private shouldContinueUnderworld(settings: UnderworldSettings): boolean {
    // Check if should exit underworld
    if (settings.exitUnder) {
      const currentTime = new Date().getHours();
      if (currentTime >= settings.stayHours) {
        console.log('Underworld System: Time limit reached, exiting underworld');
        return false;
      }
    }
    
    // Check resource limits
    if (this.medicsUsed >= settings.maxMedics && this.rubyUsed >= settings.maxRuby) {
      console.log('Underworld System: Resource limits reached');
      return false;
    }
    
    return true;
  }

  async getUnderworldInfo(): Promise<any> {
    const status = await this.getUnderworldStatus();
    
    return {
      ...status,
      medicsUsed: this.medicsUsed,
      rubyUsed: this.rubyUsed,
      isRunning: this.isRunning
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
