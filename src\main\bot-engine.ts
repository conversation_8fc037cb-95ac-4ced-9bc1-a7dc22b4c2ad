// Bot engine - core automation logic

import { BotSettings, BotAction } from '../types';
import { StorageManager } from '../utils/storage-manager';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { ArenaCombatModule } from '../modules/arena-combat';
import { TurmaCombatModule } from '../modules/turma-combat';
import { HealingSystemModule } from '../modules/healing-system';
import { PackageManagementModule } from '../modules/package-management';
import { ExpeditionSystemModule } from '../modules/expedition-system';
import { UnderworldSystemModule } from '../modules/underworld-system';
import { GoldManagementModule } from '../modules/gold-management';
import { EventSystemModule } from '../modules/event-system';
import { QuestManagementModule } from '../modules/quest-management';
import { MarketOperationsModule } from '../modules/market-operations';
import { ForgeRepairModule } from '../modules/forge-repair';

export class BotEngine {
  private isRunning: boolean = false;
  private isPaused: boolean = false;
  private currentSettings: BotSettings | null = null;
  private actionInterval: NodeJS.Timeout | null = null;

  // Module instances
  private arenaCombat: ArenaCombatModule;
  private turmaCombat: TurmaCombatModule;
  private healingSystem: HealingSystemModule;
  private packageManagement: PackageManagementModule;
  private expeditionSystem: ExpeditionSystemModule;
  private underworldSystem: UnderworldSystemModule;
  private goldManagement: GoldManagementModule;
  private eventSystem: EventSystemModule;
  private questManagement: QuestManagementModule;
  private marketOperations: MarketOperationsModule;
  private forgeRepair: ForgeRepairModule;

  constructor(private storageManager: StorageManager, private urlHelper: GladiatorUrlHelper) {
    // Initialize all modules
    this.arenaCombat = new ArenaCombatModule(urlHelper, storageManager);
    this.turmaCombat = new TurmaCombatModule(urlHelper, storageManager);
    this.healingSystem = new HealingSystemModule(urlHelper, storageManager);
    this.packageManagement = new PackageManagementModule(urlHelper, storageManager);
    this.expeditionSystem = new ExpeditionSystemModule(urlHelper, storageManager);
    this.underworldSystem = new UnderworldSystemModule(urlHelper, storageManager);
    this.goldManagement = new GoldManagementModule(urlHelper, storageManager);
    this.eventSystem = new EventSystemModule(urlHelper, storageManager);
    this.questManagement = new QuestManagementModule(urlHelper, storageManager);
    this.marketOperations = new MarketOperationsModule(urlHelper, storageManager);
    this.forgeRepair = new ForgeRepairModule(urlHelper, storageManager);
  }

  async start(settings: BotSettings): Promise<void> {
    if (this.isRunning) return;

    this.currentSettings = settings;
    this.isRunning = true;
    this.isPaused = false;

    console.log('Bot Engine: Starting automation');

    // Start main automation loop
    this.startAutomationLoop();
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    this.isPaused = false;

    if (this.actionInterval) {
      clearInterval(this.actionInterval);
      this.actionInterval = null;
    }

    // Stop all modules
    await this.arenaCombat.stop();
    await this.turmaCombat.stop();
    await this.expeditionSystem.stop();
    await this.underworldSystem.stop();
    await this.eventSystem.stop();
    await this.questManagement.stop();
    await this.marketOperations.stop();
    await this.forgeRepair.stop();

    console.log('Bot Engine: Stopped automation');
  }

  async pause(): Promise<void> {
    this.isPaused = true;
    console.log('Bot Engine: Paused automation');
  }

  async resume(): Promise<void> {
    this.isPaused = false;
    console.log('Bot Engine: Resumed automation');
  }

  async updateSettings(settings: BotSettings): Promise<void> {
    this.currentSettings = settings;
    console.log('Bot Engine: Settings updated');
  }

  private startAutomationLoop(): void {
    this.actionInterval = setInterval(async () => {
      if (!this.isRunning || this.isPaused || !this.currentSettings) {
        return;
      }

      try {
        await this.performNextAction();
      } catch (error) {
        console.error('Bot Engine: Error in automation loop:', error);
      }
    }, 5000); // Check every 5 seconds
  }

  private async performNextAction(): Promise<void> {
    if (!this.currentSettings) return;

    try {
      // Determine next action based on settings and current game state
      const nextAction = this.determineNextAction();

      switch (nextAction) {
        case BotAction.HEALING:
          await this.performHealing();
          break;
        case BotAction.ATTACKING:
          await this.performAttacking();
          break;
        case BotAction.PICKING_ITEMS:
          await this.performItemPicking();
          break;
        case BotAction.BUYING_PACK:
          await this.performPackBuying();
          break;
        case BotAction.TRAVELING:
          await this.performTraveling();
          break;
        case BotAction.UNDERWORLD:
          await this.performUnderworld();
          break;
        case BotAction.EVENT:
          await this.performEvents();
          break;
        case BotAction.QUEST_MANAGEMENT:
          await this.performQuests();
          break;
        case BotAction.MARKET_OPERATIONS:
          await this.performMarketOperations();
          break;
        case BotAction.FORGE_REPAIR:
          await this.performForgeRepair();
          break;
        default:
          // Idle - do nothing
          break;
      }
    } catch (error) {
      console.error('Bot Engine: Error in performNextAction:', error);
      // Continue running even if one action fails
    }
  }

  private determineNextAction(): BotAction {
    if (!this.currentSettings) return BotAction.IDLE;

    try {
      // Priority-based action determination

      // 1. Check if healing is needed
      if (this.currentSettings.heal?.enabled && this.needsHealing()) {
        return BotAction.HEALING;
      }

      // 2. Check if arena/turma combat is enabled
      if (this.currentSettings.arena?.enabled && this.canAttackArena()) {
        return BotAction.ATTACKING;
      }

      if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {
        return BotAction.ATTACKING;
      }

      // 3. Check if package management is needed
      if (this.currentSettings.packages?.pickGold?.enabled && this.needsPackageManagement()) {
        return BotAction.PICKING_ITEMS;
      }

      // 4. Check if expeditions are enabled
      if (this.currentSettings.expeditions?.enabled && this.canDoExpedition()) {
        return BotAction.TRAVELING;
      }

      // 5. Check if underworld is active and enabled
      if (this.currentSettings.underworld?.enabled && this.isUnderworldActive()) {
        return BotAction.UNDERWORLD;
      }

      // 6. Check if events are enabled
      if (this.currentSettings.event?.enabled && this.hasActiveEvents()) {
        return BotAction.EVENT;
      }

      // 7. Check if quest management is enabled
      if (this.currentSettings.quest?.enabled && this.hasAvailableQuests()) {
        return BotAction.QUEST_MANAGEMENT;
      }

      // 8. Check if market operations are enabled
      if (this.currentSettings.market?.enabled && this.shouldPerformMarketOperations()) {
        return BotAction.MARKET_OPERATIONS;
      }

      // 9. Check if forge/repair is enabled
      if ((this.currentSettings.forge?.enabled || this.currentSettings.repair?.enabled) && this.shouldPerformForgeRepair()) {
        return BotAction.FORGE_REPAIR;
      }

      return BotAction.IDLE;
    } catch (error) {
      console.error('Bot Engine: Error in determineNextAction:', error);
      return BotAction.IDLE;
    }
  }

  private needsHealing(): boolean {
    // Check current health vs minimum health setting
    const healthElement = document.querySelector('.health-bar, #health_points');
    if (!healthElement) return false;

    const healthText = healthElement.textContent || '';
    const healthMatch = healthText.match(/(\d+)\/(\d+)/);

    if (healthMatch) {
      const currentHealth = parseInt(healthMatch[1]);
      const maxHealth = parseInt(healthMatch[2]);
      const healthPercentage = (currentHealth / maxHealth) * 100;

      return healthPercentage < (this.currentSettings?.heal.minHealth || 50);
    }

    return false;
  }

  private canAttackArena(): boolean {
    // Check if we're on the right page and have attacks available
    return window.location.href.includes('arena') || window.location.href.includes('index');
  }

  private canAttackTurma(): boolean {
    // Check if we're on the right page and have attacks available
    return window.location.href.includes('turma') || window.location.href.includes('index');
  }

  private needsPackageManagement(): boolean {
    // Check if packages need attention
    return true; // Simplified for now
  }

  private canDoExpedition(): boolean {
    // Check if expeditions are available
    return window.location.href.includes('expedition') || window.location.href.includes('index');
  }

  private isUnderworldActive(): boolean {
    // Check if underworld is currently active
    const underworldElement = document.querySelector('.underworld_active, .underworld-timer');
    return !!underworldElement;
  }

  private hasActiveEvents(): boolean {
    // Check if there are active events
    const eventElement = document.querySelector('.event_active, .active-event');
    return !!eventElement;
  }

  private hasAvailableQuests(): boolean {
    // Check if there are available quests
    const questElement = document.querySelector('.available_quest, .quest-available');
    return !!questElement;
  }

  private shouldPerformMarketOperations(): boolean {
    // Check if market operations should be performed
    // This could be based on time intervals, gold thresholds, etc.
    return true; // Simplified for now
  }

  private shouldPerformForgeRepair(): boolean {
    // Check if forge/repair operations should be performed
    // This could be based on damaged items, available materials, etc.
    return true; // Simplified for now
  }

  private async performHealing(): Promise<void> {
    if (!this.currentSettings?.heal) return;

    try {
      console.log('Bot Engine: Performing healing');
      await this.healingSystem.checkAndHeal(this.currentSettings.heal);
    } catch (error) {
      console.error('Bot Engine: Error in healing:', error);
    }
  }

  private async performAttacking(): Promise<void> {
    if (!this.currentSettings) return;

    try {
      console.log('Bot Engine: Performing attacking');

      // Check which combat type to perform
      if (this.currentSettings.arena?.enabled && this.canAttackArena()) {
        await this.arenaCombat.start(this.currentSettings.arena);
      } else if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {
        await this.turmaCombat.start(this.currentSettings.turma);
      }
    } catch (error) {
      console.error('Bot Engine: Error in attacking:', error);
    }
  }

  private async performItemPicking(): Promise<void> {
    if (!this.currentSettings?.packages) return;

    try {
      console.log('Bot Engine: Performing item management');
      await this.packageManagement.processPackages(this.currentSettings.packages);
    } catch (error) {
      console.error('Bot Engine: Error in item management:', error);
    }
  }

  private async performPackBuying(): Promise<void> {
    if (!this.currentSettings?.hideGold) return;

    try {
      console.log('Bot Engine: Performing gold management');
      await this.goldManagement.manageGold(this.currentSettings.hideGold);
    } catch (error) {
      console.error('Bot Engine: Error in gold management:', error);
    }
  }

  private async performTraveling(): Promise<void> {
    if (!this.currentSettings?.expeditions || !this.currentSettings?.dungeon) return;

    try {
      console.log('Bot Engine: Performing expeditions');
      await this.expeditionSystem.start(this.currentSettings.expeditions, this.currentSettings.dungeon);
    } catch (error) {
      console.error('Bot Engine: Error in expeditions:', error);
    }
  }

  private async performUnderworld(): Promise<void> {
    if (!this.currentSettings?.underworld) return;

    try {
      console.log('Bot Engine: Performing underworld activities');
      await this.underworldSystem.start(this.currentSettings.underworld);
    } catch (error) {
      console.error('Bot Engine: Error in underworld:', error);
    }
  }

  private async performEvents(): Promise<void> {
    if (!this.currentSettings?.event) return;

    try {
      console.log('Bot Engine: Performing event activities');
      await this.eventSystem.start(this.currentSettings.event);
    } catch (error) {
      console.error('Bot Engine: Error in events:', error);
    }
  }

  private async performQuests(): Promise<void> {
    if (!this.currentSettings?.quest) return;

    try {
      console.log('Bot Engine: Performing quest management');
      await this.questManagement.start(this.currentSettings.quest);
    } catch (error) {
      console.error('Bot Engine: Error in quest management:', error);
    }
  }

  private async performMarketOperations(): Promise<void> {
    if (!this.currentSettings?.market) return;

    try {
      console.log('Bot Engine: Performing market operations');
      await this.marketOperations.start(this.currentSettings.market);
    } catch (error) {
      console.error('Bot Engine: Error in market operations:', error);
    }
  }

  private async performForgeRepair(): Promise<void> {
    if (!this.currentSettings?.forge || !this.currentSettings?.repair) return;

    try {
      console.log('Bot Engine: Performing forge and repair operations');
      await this.forgeRepair.start(this.currentSettings.forge, this.currentSettings.repair);
    } catch (error) {
      console.error('Bot Engine: Error in forge/repair:', error);
    }
  }
}