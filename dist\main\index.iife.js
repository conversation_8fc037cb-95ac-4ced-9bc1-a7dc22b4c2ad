(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/main/bot-engine.ts":
/*!********************************!*\
  !*** ./src/main/bot-engine.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BotEngine: () => (/* binding */ BotEngine)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
/* harmony import */ var _modules_arena_combat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../modules/arena-combat */ "./src/modules/arena-combat.ts");
/* harmony import */ var _modules_turma_combat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../modules/turma-combat */ "./src/modules/turma-combat.ts");
/* harmony import */ var _modules_healing_system__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../modules/healing-system */ "./src/modules/healing-system.ts");
/* harmony import */ var _modules_package_management__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../modules/package-management */ "./src/modules/package-management.ts");
/* harmony import */ var _modules_expedition_system__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../modules/expedition-system */ "./src/modules/expedition-system.ts");
/* harmony import */ var _modules_underworld_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../modules/underworld-system */ "./src/modules/underworld-system.ts");
/* harmony import */ var _modules_gold_management__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../modules/gold-management */ "./src/modules/gold-management.ts");
/* harmony import */ var _modules_event_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../modules/event-system */ "./src/modules/event-system.ts");
/* harmony import */ var _modules_quest_management__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../modules/quest-management */ "./src/modules/quest-management.ts");
/* harmony import */ var _modules_market_operations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../modules/market-operations */ "./src/modules/market-operations.ts");
/* harmony import */ var _modules_forge_repair__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../modules/forge-repair */ "./src/modules/forge-repair.ts");
// Bot engine - core automation logic












class BotEngine {
    constructor(storageManager, urlHelper) {
        this.storageManager = storageManager;
        this.urlHelper = urlHelper;
        this.isRunning = false;
        this.isPaused = false;
        this.currentSettings = null;
        this.actionInterval = null;
        // Initialize all modules
        this.arenaCombat = new _modules_arena_combat__WEBPACK_IMPORTED_MODULE_1__.ArenaCombatModule(urlHelper, storageManager);
        this.turmaCombat = new _modules_turma_combat__WEBPACK_IMPORTED_MODULE_2__.TurmaCombatModule(urlHelper, storageManager);
        this.healingSystem = new _modules_healing_system__WEBPACK_IMPORTED_MODULE_3__.HealingSystemModule(urlHelper, storageManager);
        this.packageManagement = new _modules_package_management__WEBPACK_IMPORTED_MODULE_4__.PackageManagementModule(urlHelper, storageManager);
        this.expeditionSystem = new _modules_expedition_system__WEBPACK_IMPORTED_MODULE_5__.ExpeditionSystemModule(urlHelper, storageManager);
        this.underworldSystem = new _modules_underworld_system__WEBPACK_IMPORTED_MODULE_6__.UnderworldSystemModule(urlHelper, storageManager);
        this.goldManagement = new _modules_gold_management__WEBPACK_IMPORTED_MODULE_7__.GoldManagementModule(urlHelper, storageManager);
        this.eventSystem = new _modules_event_system__WEBPACK_IMPORTED_MODULE_8__.EventSystemModule(urlHelper, storageManager);
        this.questManagement = new _modules_quest_management__WEBPACK_IMPORTED_MODULE_9__.QuestManagementModule(urlHelper, storageManager);
        this.marketOperations = new _modules_market_operations__WEBPACK_IMPORTED_MODULE_10__.MarketOperationsModule(urlHelper, storageManager);
        this.forgeRepair = new _modules_forge_repair__WEBPACK_IMPORTED_MODULE_11__.ForgeRepairModule(urlHelper, storageManager);
    }
    async start(settings) {
        if (this.isRunning)
            return;
        this.currentSettings = settings;
        this.isRunning = true;
        this.isPaused = false;
        console.log('Bot Engine: Starting automation');
        // Start main automation loop
        this.startAutomationLoop();
    }
    async stop() {
        this.isRunning = false;
        this.isPaused = false;
        if (this.actionInterval) {
            clearInterval(this.actionInterval);
            this.actionInterval = null;
        }
        // Stop all modules
        await this.arenaCombat.stop();
        await this.turmaCombat.stop();
        await this.expeditionSystem.stop();
        await this.underworldSystem.stop();
        await this.eventSystem.stop();
        await this.questManagement.stop();
        await this.marketOperations.stop();
        await this.forgeRepair.stop();
        console.log('Bot Engine: Stopped automation');
    }
    async pause() {
        this.isPaused = true;
        console.log('Bot Engine: Paused automation');
    }
    async resume() {
        this.isPaused = false;
        console.log('Bot Engine: Resumed automation');
    }
    async updateSettings(settings) {
        this.currentSettings = settings;
        console.log('Bot Engine: Settings updated');
    }
    startAutomationLoop() {
        this.actionInterval = setInterval(async () => {
            if (!this.isRunning || this.isPaused || !this.currentSettings) {
                return;
            }
            try {
                await this.performNextAction();
            }
            catch (error) {
                console.error('Bot Engine: Error in automation loop:', error);
            }
        }, 5000); // Check every 5 seconds
    }
    async performNextAction() {
        if (!this.currentSettings)
            return;
        try {
            // Determine next action based on settings and current game state
            const nextAction = this.determineNextAction();
            switch (nextAction) {
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.HEALING:
                    await this.performHealing();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.ATTACKING:
                    await this.performAttacking();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.PICKING_ITEMS:
                    await this.performItemPicking();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.BUYING_PACK:
                    await this.performPackBuying();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.TRAVELING:
                    await this.performTraveling();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.UNDERWORLD:
                    await this.performUnderworld();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.EVENT:
                    await this.performEvents();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.QUEST_MANAGEMENT:
                    await this.performQuests();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.MARKET_OPERATIONS:
                    await this.performMarketOperations();
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.FORGE_REPAIR:
                    await this.performForgeRepair();
                    break;
                default:
                    // Idle - do nothing
                    break;
            }
        }
        catch (error) {
            console.error('Bot Engine: Error in performNextAction:', error);
            // Continue running even if one action fails
        }
    }
    determineNextAction() {
        if (!this.currentSettings)
            return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
        try {
            // Priority-based action determination
            // 1. Check if healing is needed
            if (this.currentSettings.heal?.enabled && this.needsHealing()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.HEALING;
            }
            // 2. Check if arena/turma combat is enabled
            if (this.currentSettings.arena?.enabled && this.canAttackArena()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.ATTACKING;
            }
            if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.ATTACKING;
            }
            // 3. Check if package management is needed
            if (this.currentSettings.packages?.pickGold?.enabled && this.needsPackageManagement()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.PICKING_ITEMS;
            }
            // 4. Check if expeditions are enabled
            if (this.currentSettings.expeditions?.enabled && this.canDoExpedition()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.TRAVELING;
            }
            // 5. Check if underworld is active and enabled
            if (this.currentSettings.underworld?.enabled && this.isUnderworldActive()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.UNDERWORLD;
            }
            // 6. Check if events are enabled
            if (this.currentSettings.event?.enabled && this.hasActiveEvents()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.EVENT;
            }
            // 7. Check if quest management is enabled
            if (this.currentSettings.quest?.enabled && this.hasAvailableQuests()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.QUEST_MANAGEMENT;
            }
            // 8. Check if market operations are enabled
            if (this.currentSettings.market?.enabled && this.shouldPerformMarketOperations()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.MARKET_OPERATIONS;
            }
            // 9. Check if forge/repair is enabled
            if ((this.currentSettings.forge?.enabled || this.currentSettings.repair?.enabled) && this.shouldPerformForgeRepair()) {
                return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.FORGE_REPAIR;
            }
            return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
        }
        catch (error) {
            console.error('Bot Engine: Error in determineNextAction:', error);
            return _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
        }
    }
    needsHealing() {
        // Check current health vs minimum health setting
        const healthElement = document.querySelector('.health-bar, #health_points');
        if (!healthElement)
            return false;
        const healthText = healthElement.textContent || '';
        const healthMatch = healthText.match(/(\d+)\/(\d+)/);
        if (healthMatch) {
            const currentHealth = parseInt(healthMatch[1]);
            const maxHealth = parseInt(healthMatch[2]);
            const healthPercentage = (currentHealth / maxHealth) * 100;
            return healthPercentage < (this.currentSettings?.heal.minHealth || 50);
        }
        return false;
    }
    canAttackArena() {
        // Check if we're on the right page and have attacks available
        return window.location.href.includes('arena') || window.location.href.includes('index');
    }
    canAttackTurma() {
        // Check if we're on the right page and have attacks available
        return window.location.href.includes('turma') || window.location.href.includes('index');
    }
    needsPackageManagement() {
        // Check if packages need attention
        return true; // Simplified for now
    }
    canDoExpedition() {
        // Check if expeditions are available
        return window.location.href.includes('expedition') || window.location.href.includes('index');
    }
    isUnderworldActive() {
        // Check if underworld is currently active
        const underworldElement = document.querySelector('.underworld_active, .underworld-timer');
        return !!underworldElement;
    }
    hasActiveEvents() {
        // Check if there are active events
        const eventElement = document.querySelector('.event_active, .active-event');
        return !!eventElement;
    }
    hasAvailableQuests() {
        // Check if there are available quests
        const questElement = document.querySelector('.available_quest, .quest-available');
        return !!questElement;
    }
    shouldPerformMarketOperations() {
        // Check if market operations should be performed
        // This could be based on time intervals, gold thresholds, etc.
        return true; // Simplified for now
    }
    shouldPerformForgeRepair() {
        // Check if forge/repair operations should be performed
        // This could be based on damaged items, available materials, etc.
        return true; // Simplified for now
    }
    async performHealing() {
        if (!this.currentSettings?.heal)
            return;
        try {
            console.log('Bot Engine: Performing healing');
            await this.healingSystem.checkAndHeal(this.currentSettings.heal);
        }
        catch (error) {
            console.error('Bot Engine: Error in healing:', error);
        }
    }
    async performAttacking() {
        if (!this.currentSettings)
            return;
        try {
            console.log('Bot Engine: Performing attacking');
            // Check which combat type to perform
            if (this.currentSettings.arena?.enabled && this.canAttackArena()) {
                await this.arenaCombat.start(this.currentSettings.arena);
            }
            else if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {
                await this.turmaCombat.start(this.currentSettings.turma);
            }
        }
        catch (error) {
            console.error('Bot Engine: Error in attacking:', error);
        }
    }
    async performItemPicking() {
        if (!this.currentSettings?.packages)
            return;
        try {
            console.log('Bot Engine: Performing item management');
            await this.packageManagement.processPackages(this.currentSettings.packages);
        }
        catch (error) {
            console.error('Bot Engine: Error in item management:', error);
        }
    }
    async performPackBuying() {
        if (!this.currentSettings?.hideGold)
            return;
        try {
            console.log('Bot Engine: Performing gold management');
            await this.goldManagement.manageGold(this.currentSettings.hideGold);
        }
        catch (error) {
            console.error('Bot Engine: Error in gold management:', error);
        }
    }
    async performTraveling() {
        if (!this.currentSettings?.expeditions || !this.currentSettings?.dungeon)
            return;
        try {
            console.log('Bot Engine: Performing expeditions');
            await this.expeditionSystem.start(this.currentSettings.expeditions, this.currentSettings.dungeon);
        }
        catch (error) {
            console.error('Bot Engine: Error in expeditions:', error);
        }
    }
    async performUnderworld() {
        if (!this.currentSettings?.underworld)
            return;
        try {
            console.log('Bot Engine: Performing underworld activities');
            await this.underworldSystem.start(this.currentSettings.underworld);
        }
        catch (error) {
            console.error('Bot Engine: Error in underworld:', error);
        }
    }
    async performEvents() {
        if (!this.currentSettings?.event)
            return;
        try {
            console.log('Bot Engine: Performing event activities');
            await this.eventSystem.start(this.currentSettings.event);
        }
        catch (error) {
            console.error('Bot Engine: Error in events:', error);
        }
    }
    async performQuests() {
        if (!this.currentSettings?.quest)
            return;
        try {
            console.log('Bot Engine: Performing quest management');
            await this.questManagement.start(this.currentSettings.quest);
        }
        catch (error) {
            console.error('Bot Engine: Error in quest management:', error);
        }
    }
    async performMarketOperations() {
        if (!this.currentSettings?.market)
            return;
        try {
            console.log('Bot Engine: Performing market operations');
            await this.marketOperations.start(this.currentSettings.market);
        }
        catch (error) {
            console.error('Bot Engine: Error in market operations:', error);
        }
    }
    async performForgeRepair() {
        if (!this.currentSettings?.forge || !this.currentSettings?.repair)
            return;
        try {
            console.log('Bot Engine: Performing forge and repair operations');
            await this.forgeRepair.start(this.currentSettings.forge, this.currentSettings.repair);
        }
        catch (error) {
            console.error('Bot Engine: Error in forge/repair:', error);
        }
    }
}


/***/ }),

/***/ "./src/main/login-manager.ts":
/*!***********************************!*\
  !*** ./src/main/login-manager.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginManager: () => (/* binding */ LoginManager)
/* harmony export */ });
// Login manager - handles automatic login functionality
class LoginManager {
    constructor(_urlHelper, _storageManager) {
        this._urlHelper = _urlHelper;
        this._storageManager = _storageManager;
        // Constructor parameters are automatically assigned to private properties
    }
    async handleLobby() {
        console.log('Login Manager: Handling lobby page');
        // Set up fetch interception for login requests
        this.interceptFetch();
    }
    interceptFetch() {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const [input, init] = args;
            const url = typeof input === 'string' ? input : input.url;
            // Intercept login link requests
            if (url && url.includes('loginLink')) {
                console.log('Login Manager: Intercepted login request');
                // Handle login logic here
            }
            return originalFetch(input, init);
        };
    }
}


/***/ }),

/***/ "./src/main/ui-manager.ts":
/*!********************************!*\
  !*** ./src/main/ui-manager.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UIManager: () => (/* binding */ UIManager)
/* harmony export */ });
// UI manager - handles bot UI updates and interactions
class UIManager {
    constructor() {
        // UI Manager initialized
    }
    updateStatus(status) {
        // Update status indicator
        const statusIndicator = document.getElementById('gh-status-indicator');
        const statusText = document.getElementById('gh-status-text');
        const toggleButton = document.getElementById('gh-toggle-bot');
        if (statusIndicator && statusText && toggleButton) {
            const statusDot = statusIndicator.querySelector('.gh-status-dot');
            if (status.isActive) {
                if (status.isPaused) {
                    statusDot.style.backgroundColor = '#f39c12'; // Orange for paused
                    statusText.textContent = 'Paused';
                    toggleButton.textContent = 'Resume Bot';
                }
                else {
                    statusDot.style.backgroundColor = '#27ae60'; // Green for active
                    statusText.textContent = 'Active';
                    toggleButton.textContent = 'Stop Bot';
                }
            }
            else {
                // Show different status based on whether it was previously active
                if (status.wasActive) {
                    statusDot.style.backgroundColor = '#f39c12'; // Orange for was active but stopped
                    statusText.textContent = 'Ready (was active)';
                    toggleButton.textContent = 'Start Bot';
                }
                else {
                    statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive
                    statusText.textContent = status.error ? 'Error' : 'Inactive';
                    toggleButton.textContent = 'Start Bot';
                }
            }
        }
        // Update runtime
        this.updateRuntime(status.statistics);
        // Update actions count
        this.updateActionsCount(status.statistics.actionsPerformed);
    }
    updateRuntime(statistics) {
        const runtimeElement = document.getElementById('gh-runtime');
        if (!runtimeElement)
            return;
        const now = new Date();
        const startTime = new Date(statistics.startTime);
        const runtime = now.getTime() - startTime.getTime();
        const hours = Math.floor(runtime / (1000 * 60 * 60));
        const minutes = Math.floor((runtime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((runtime % (1000 * 60)) / 1000);
        runtimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    updateActionsCount(actionsPerformed) {
        const actionsElement = document.getElementById('gh-actions');
        if (actionsElement) {
            actionsElement.textContent = actionsPerformed.toString();
        }
    }
    showStatistics(statistics) {
        // Remove existing modal if present
        const existingModal = document.getElementById('gh-statistics-modal');
        if (existingModal) {
            existingModal.remove();
        }
        // Create and show statistics modal
        const modal = this.createStatisticsModal(statistics);
        modal.id = 'gh-statistics-modal';
        document.body.appendChild(modal);
        // Add close functionality with proper event handling
        const closeBtn = modal.querySelector('.gh-modal-close');
        const modalContent = modal.querySelector('.gh-modal-content');
        closeBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            modal.remove();
        });
        // Prevent modal content clicks from closing modal
        modalContent?.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        // Close on background click only - with delay to prevent immediate closure
        setTimeout(() => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    e.preventDefault();
                    e.stopPropagation();
                    modal.remove();
                }
            });
        }, 100);
        // Close on Escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }
    createStatisticsModal(statistics) {
        const modal = document.createElement('div');
        modal.className = 'gh-modal';
        modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
        const modalContent = document.createElement('div');
        modalContent.className = 'gh-modal-content';
        modalContent.style.cssText = `
      background: #2c3e50;
      color: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 500px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    `;
        modalContent.innerHTML = `
      <div class="gh-modal-header">
        <h2>Bot Statistics</h2>
        <button class="gh-modal-close" style="float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
      </div>
      <div class="gh-modal-body">
        <div class="gh-stat-group">
          <h3>General</h3>
          <p>Start Time: ${new Date(statistics.startTime).toLocaleString()}</p>
          <p>Total Runtime: ${this.formatDuration(statistics.totalRunTime)}</p>
          <p>Actions Performed: ${statistics.actionsPerformed}</p>
          <p>Errors Encountered: ${statistics.errorsEncountered}</p>
        </div>
        <div class="gh-stat-group">
          <h3>Combat</h3>
          <p>Combats Won: ${statistics.combatsWon}</p>
          <p>Combats Lost: ${statistics.combatsLost}</p>
          <p>Win Rate: ${statistics.combatsWon + statistics.combatsLost > 0 ?
            ((statistics.combatsWon / (statistics.combatsWon + statistics.combatsLost)) * 100).toFixed(1) : 0}%</p>
        </div>
        <div class="gh-stat-group">
          <h3>Rewards</h3>
          <p>Gold Earned: ${statistics.goldEarned.toLocaleString()}</p>
          <p>Experience Gained: ${statistics.experienceGained.toLocaleString()}</p>
          <p>Items Found: ${statistics.itemsFound}</p>
        </div>
      </div>
    `;
        modal.appendChild(modalContent);
        return modal;
    }
    formatDuration(milliseconds) {
        const hours = Math.floor(milliseconds / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
        return `${hours}h ${minutes}m ${seconds}s`;
    }
    showNotification(title, message, type = 'info') {
        const container = document.getElementById('gh-notifications');
        if (!container)
            return;
        const notification = document.createElement('div');
        notification.className = `gh-notification gh-notification-${type}`;
        notification.innerHTML = `
      <div class="gh-notification-content">
        <strong>${title}</strong>
        <p>${message}</p>
      </div>
      <button class="gh-notification-close">×</button>
    `;
        container.appendChild(notification);
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
        // Add close button functionality
        notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {
            notification.remove();
        });
    }
}


/***/ }),

/***/ "./src/modules/arena-combat.ts":
/*!*************************************!*\
  !*** ./src/modules/arena-combat.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ArenaCombatModule: () => (/* binding */ ArenaCombatModule)
/* harmony export */ });
// Arena Combat Module - Handles all arena-related combat functionality
class ArenaCombatModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.attackCount = 0;
        this.loseCount = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning)
            return;
        this.isRunning = true;
        this.attackCount = 0;
        this.loseCount = 0;
        console.log('Arena Combat: Starting arena automation');
        try {
            await this.navigateToArena();
            await this.performArenaLoop(settings);
        }
        catch (error) {
            console.error('Arena Combat: Error in arena automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Arena Combat: Stopped arena automation');
    }
    async navigateToArena() {
        const currentUrl = window.location.href;
        if (!currentUrl.includes('mod=arena')) {
            console.log('Arena Combat: Navigating to arena');
            window.location.href = currentUrl.replace(/mod=\w+/, 'mod=arena');
            // Wait for page load
            await this.waitForPageLoad();
        }
    }
    async performArenaLoop(settings) {
        while (this.isRunning && this.shouldContinueAttacking(settings)) {
            try {
                const targets = await this.getAvailableTargets(settings);
                if (targets.length === 0) {
                    console.log('Arena Combat: No suitable targets found');
                    await this.delay(5000);
                    continue;
                }
                const target = this.selectBestTarget(targets, settings);
                if (!target) {
                    console.log('Arena Combat: No target selected');
                    await this.delay(5000);
                    continue;
                }
                const result = await this.attackTarget(target, settings);
                await this.processAttackResult(result);
                // Random delay between attacks
                const delay = Math.random() * 10000 + 5000; // 5-15 seconds
                await this.delay(delay);
            }
            catch (error) {
                console.error('Arena Combat: Error in attack loop:', error);
                await this.delay(10000);
            }
        }
    }
    async getAvailableTargets(settings) {
        const targets = [];
        // Parse arena page for targets
        const targetElements = document.querySelectorAll('.arena_opponent, .opponent_row');
        for (const element of targetElements) {
            try {
                const target = await this.parseTargetElement(element, settings);
                if (target && this.isValidTarget(target, settings)) {
                    targets.push(target);
                }
            }
            catch (error) {
                console.error('Arena Combat: Error parsing target:', error);
            }
        }
        return targets;
    }
    async parseTargetElement(element, settings) {
        const nameElement = element.querySelector('.opponent_name, .player_name');
        const levelElement = element.querySelector('.opponent_level, .level');
        const pointsElement = element.querySelector('.opponent_points, .points');
        const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
        if (!nameElement || !attackButton)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '0');
        const points = parseInt(pointsElement?.textContent?.trim() || '0');
        const id = attackButton.getAttribute('data-target-id') ||
            attackButton.getAttribute('href')?.match(/target=(\d+)/)?.[1] || '';
        // Calculate win chance (simplified)
        const winChance = this.calculateWinChance(level, points);
        // Check if guild member
        const isGuildMember = this.isGuildMember(name, settings);
        return {
            id,
            name,
            level,
            points,
            winChance,
            isGuildMember,
            canAttack: !attackButton.hasAttribute('disabled')
        };
    }
    isValidTarget(target, settings) {
        // Skip if can't attack
        if (!target.canAttack)
            return false;
        // Skip guild members if auto-ignore is enabled
        if (settings.autoIgnoreGuildPlayers && target.isGuildMember)
            return false;
        // Skip if in ignore list
        if (settings.ignorePlayers.includes(target.name))
            return false;
        // Skip if win chance is too low and skipLow is enabled
        if (settings.skipLow && target.winChance < (settings.prioritizeChance || 80))
            return false;
        // Check if targeting specific players
        if (settings.attackTargetPlayers && settings.attackPlayers.length > 0) {
            return settings.attackPlayers.includes(target.name);
        }
        return true;
    }
    selectBestTarget(targets, settings) {
        if (targets.length === 0)
            return null;
        // Sort by win chance (highest first) and then by points
        targets.sort((a, b) => {
            if (Math.abs(a.winChance - b.winChance) < 5) {
                return b.points - a.points; // Higher points first if win chances are similar
            }
            return b.winChance - a.winChance; // Higher win chance first
        });
        return targets[0];
    }
    async attackTarget(target, settings) {
        console.log(`Arena Combat: Attacking ${target.name} (Level ${target.level}, Win Chance: ${target.winChance}%)`);
        // Find and click attack button
        const attackButton = document.querySelector(`[data-target-id="${target.id}"], [href*="target=${target.id}"]`);
        if (!attackButton) {
            throw new Error('Attack button not found');
        }
        // Simulate attack if enabled
        if (settings.allowSim) {
            const simButton = document.querySelector('.simulate_button, [data-action="simulate"]');
            if (simButton) {
                simButton.click();
            }
            else {
                attackButton.click();
            }
        }
        else {
            attackButton.click();
        }
        // Wait for attack result
        await this.waitForAttackResult();
        return this.parseAttackResult();
    }
    async processAttackResult(result) {
        this.attackCount++;
        if (result.won) {
            console.log(`Arena Combat: Victory! Gold: ${result.gold}, XP: ${result.xp}`);
            this.loseCount = 0; // Reset lose count on win
        }
        else {
            console.log('Arena Combat: Defeat');
            this.loseCount++;
        }
        // Update statistics
        await this.updateStatistics(result);
    }
    shouldContinueAttacking(settings) {
        // Check lose limit
        if (settings.loseLimit > 0 && this.loseCount >= settings.loseLimit) {
            console.log('Arena Combat: Lose limit reached');
            return false;
        }
        // Check daily attack limit
        if (settings.dailyAttacks > 0 && this.attackCount >= settings.dailyAttacks) {
            console.log('Arena Combat: Daily attack limit reached');
            return false;
        }
        // Check total attack limit
        if (settings.limitAttacks > 0 && this.attackCount >= settings.limitAttacks) {
            console.log('Arena Combat: Attack limit reached');
            return false;
        }
        return true;
    }
    calculateWinChance(level, points) {
        // Simplified win chance calculation
        // In reality, this would be more complex based on player stats
        const myLevel = this.getPlayerLevel();
        const myPoints = this.getPlayerPoints();
        const levelDiff = myLevel - level;
        const pointsDiff = myPoints - points;
        let winChance = 50; // Base 50%
        winChance += levelDiff * 5; // +/- 5% per level difference
        winChance += pointsDiff * 0.01; // +/- 0.01% per point difference
        return Math.max(0, Math.min(100, winChance));
    }
    isGuildMember(playerName, settings) {
        // Check if player is in the same guild
        // This would need to be implemented based on guild member list
        return false; // Simplified for now
    }
    getPlayerLevel() {
        const levelElement = document.querySelector('.player_level, #player_level');
        return parseInt(levelElement?.textContent?.trim() || '1');
    }
    getPlayerPoints() {
        const pointsElement = document.querySelector('.player_points, #player_points');
        return parseInt(pointsElement?.textContent?.trim() || '0');
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000); // Additional delay for dynamic content
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async waitForAttackResult() {
        return new Promise((resolve) => {
            const checkResult = () => {
                const resultElement = document.querySelector('.combat_result, .attack_result');
                if (resultElement) {
                    resolve();
                }
                else {
                    setTimeout(checkResult, 500);
                }
            };
            setTimeout(checkResult, 2000); // Wait at least 2 seconds
        });
    }
    parseAttackResult() {
        const resultElement = document.querySelector('.combat_result, .attack_result');
        if (!resultElement)
            return { won: false, gold: 0, xp: 0 };
        const won = resultElement.textContent?.includes('Victory') ||
            resultElement.classList.contains('victory') || false;
        const goldMatch = resultElement.textContent?.match(/(\d+)\s*gold/i);
        const xpMatch = resultElement.textContent?.match(/(\d+)\s*xp/i);
        return {
            won,
            gold: goldMatch ? parseInt(goldMatch[1]) : 0,
            xp: xpMatch ? parseInt(xpMatch[1]) : 0
        };
    }
    async updateStatistics(result) {
        // Update bot statistics
        const stats = await this.storageManager.getBotStatus();
        if (stats?.statistics) {
            stats.statistics.actionsPerformed++;
            if (result.won) {
                stats.statistics.combatsWon++;
                stats.statistics.goldEarned += result.gold;
                stats.statistics.experienceGained += result.xp;
            }
            else {
                stats.statistics.combatsLost++;
            }
            await this.storageManager.saveBotStatus(stats);
        }
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/event-system.ts":
/*!*************************************!*\
  !*** ./src/modules/event-system.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EventSystemModule: () => (/* binding */ EventSystemModule)
/* harmony export */ });
// Event System Module - Handles special events and bonus collection
class EventSystemModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.eventCount = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning || !settings.enabled)
            return;
        this.isRunning = true;
        this.eventCount = 0;
        console.log('Event System: Starting event automation');
        try {
            await this.performEventLoop(settings);
        }
        catch (error) {
            console.error('Event System: Error in event automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Event System: Stopped event automation');
    }
    async performEventLoop(settings) {
        while (this.isRunning && this.shouldContinueEvents(settings)) {
            try {
                // Check for active events
                const activeEvents = await this.getActiveEvents();
                if (activeEvents.length === 0) {
                    console.log('Event System: No active events found');
                    await this.delay(30000); // Check again in 30 seconds
                    continue;
                }
                // Process each active event
                for (const event of activeEvents) {
                    if (!this.isRunning)
                        break;
                    await this.processEvent(event, settings);
                    await this.delay(5000);
                }
                // Collect bonuses if enabled
                if (settings.autoCollectBonuses) {
                    await this.collectEventBonuses();
                }
                // Auto renew event if enabled
                if (settings.autoRenewEvent) {
                    await this.renewEvents();
                }
                await this.delay(60000); // Check events every minute
            }
            catch (error) {
                console.error('Event System: Error in event loop:', error);
                await this.delay(30000);
            }
        }
    }
    async getActiveEvents() {
        const events = [];
        // Check main event page
        await this.navigateToEvents();
        const eventElements = document.querySelectorAll('.event_item, .active-event');
        for (let i = 0; i < eventElements.length; i++) {
            const element = eventElements[i];
            try {
                const event = await this.parseEventElement(element, i);
                if (event && event.isActive) {
                    events.push(event);
                }
            }
            catch (error) {
                console.error('Event System: Error parsing event:', error);
            }
        }
        return events;
    }
    async parseEventElement(element, index) {
        const nameElement = element.querySelector('.event_name, .name');
        const timerElement = element.querySelector('.event_timer, .timer');
        const participateButton = element.querySelector('.participate_button, [data-action="participate"]');
        const rewardsElement = element.querySelector('.event_rewards, .rewards');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const timerText = timerElement?.textContent?.trim() || '0:00:00';
        const timeMatch = timerText.match(/(\d+):(\d+):(\d+)/);
        const timeRemaining = timeMatch ?
            parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
        // Determine event type based on name or class
        let type = 'special';
        if (name.toLowerCase().includes('combat') || name.toLowerCase().includes('fight')) {
            type = 'combat';
        }
        else if (name.toLowerCase().includes('collect') || name.toLowerCase().includes('gather')) {
            type = 'collection';
        }
        return {
            id: index.toString(),
            name,
            type,
            isActive: timeRemaining > 0,
            timeRemaining,
            hasRewards: !!rewardsElement,
            canParticipate: !!(participateButton && !participateButton.hasAttribute('disabled'))
        };
    }
    async processEvent(event, settings) {
        console.log(`Event System: Processing event ${event.name}`);
        switch (event.type) {
            case 'combat':
                await this.processCombatEvent(event, settings);
                break;
            case 'collection':
                await this.processCollectionEvent(event, settings);
                break;
            case 'special':
                await this.processSpecialEvent(event, settings);
                break;
        }
        this.eventCount++;
    }
    async processCombatEvent(event, settings) {
        if (!event.canParticipate)
            return;
        console.log(`Event System: Participating in combat event ${event.name}`);
        // Navigate to event combat page
        await this.navigateToEventCombat(event);
        // Look for combat targets
        const targets = await this.getEventTargets(settings);
        for (const target of targets) {
            if (!this.isRunning)
                break;
            await this.attackEventTarget(target);
            await this.delay(5000);
        }
    }
    async processCollectionEvent(event, settings) {
        console.log(`Event System: Processing collection event ${event.name}`);
        // Navigate to event collection page
        await this.navigateToEventCollection(event);
        // Collect available items
        await this.collectEventItems();
    }
    async processSpecialEvent(event, settings) {
        console.log(`Event System: Processing special event ${event.name}`);
        if (event.canParticipate) {
            const eventElement = document.querySelector(`.event_item:nth-child(${parseInt(event.id) + 1})`);
            if (eventElement) {
                const participateButton = eventElement.querySelector('.participate_button, [data-action="participate"]');
                if (participateButton && !participateButton.hasAttribute('disabled')) {
                    participateButton.click();
                    await this.delay(3000);
                }
            }
        }
    }
    async getEventTargets(settings) {
        const targets = [];
        const targetElements = document.querySelectorAll('.event_target, .event-enemy');
        for (const element of targetElements) {
            const nameElement = element.querySelector('.target_name, .name');
            const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
            if (nameElement && attackButton && !attackButton.hasAttribute('disabled')) {
                const name = nameElement.textContent?.trim() || '';
                // Check if this matches the configured mob
                if (!settings.mob || name.toLowerCase().includes(settings.mob.toLowerCase())) {
                    targets.push({
                        name,
                        element,
                        button: attackButton
                    });
                }
            }
        }
        return targets;
    }
    async attackEventTarget(target) {
        console.log(`Event System: Attacking ${target.name}`);
        target.button.click();
        // Wait for combat result
        await this.delay(3000);
        // Check for combat result
        const resultElement = document.querySelector('.combat_result, .event_result');
        if (resultElement) {
            const won = resultElement.textContent?.includes('Victory') || false;
            console.log(`Event System: Combat result - ${won ? 'Victory' : 'Defeat'}`);
        }
    }
    async collectEventItems() {
        const collectButtons = document.querySelectorAll('.collect_button, [data-action="collect"]');
        for (const button of collectButtons) {
            if (!button.hasAttribute('disabled')) {
                console.log('Event System: Collecting event item');
                button.click();
                await this.delay(1500);
            }
        }
    }
    async collectEventBonuses() {
        console.log('Event System: Collecting event bonuses');
        // Look for bonus collection buttons
        const bonusButtons = document.querySelectorAll('.collect_bonus, .bonus_button, [data-action="collect-bonus"]');
        for (const button of bonusButtons) {
            if (!button.hasAttribute('disabled')) {
                console.log('Event System: Collecting bonus');
                button.click();
                await this.delay(2000);
            }
        }
    }
    async renewEvents() {
        console.log('Event System: Auto-renewing events');
        const renewButtons = document.querySelectorAll('.renew_event, [data-action="renew"]');
        for (const button of renewButtons) {
            if (!button.hasAttribute('disabled')) {
                console.log('Event System: Renewing event');
                button.click();
                await this.delay(2000);
            }
        }
    }
    async navigateToEvents() {
        if (!window.location.href.includes('mod=events')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=events`;
            await this.waitForPageLoad();
        }
    }
    async navigateToEventCombat(event) {
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.split('?')[0];
        window.location.href = `${baseUrl}?mod=events&action=combat&event=${event.id}`;
        await this.waitForPageLoad();
    }
    async navigateToEventCollection(event) {
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.split('?')[0];
        window.location.href = `${baseUrl}?mod=events&action=collect&event=${event.id}`;
        await this.waitForPageLoad();
    }
    shouldContinueEvents(settings) {
        // Check auto stop condition
        if (settings.autoStop && this.eventCount >= 50) { // Arbitrary limit
            console.log('Event System: Event limit reached');
            return false;
        }
        // Check if following a leader
        if (settings.followLeader) {
            // This would need leader detection logic
            return true; // Simplified
        }
        return true;
    }
    async getEventStatus() {
        const activeEvents = await this.getActiveEvents();
        return {
            isRunning: this.isRunning,
            eventCount: this.eventCount,
            activeEvents: activeEvents.length,
            lastUpdate: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/expedition-system.ts":
/*!******************************************!*\
  !*** ./src/modules/expedition-system.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ExpeditionSystemModule: () => (/* binding */ ExpeditionSystemModule)
/* harmony export */ });
// Expedition System Module - Handles expedition and dungeon automation
class ExpeditionSystemModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.expeditionCount = 0;
        this.dungeonCount = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(expeditionSettings, dungeonSettings) {
        if (this.isRunning)
            return;
        this.isRunning = true;
        this.expeditionCount = 0;
        this.dungeonCount = 0;
        console.log('Expedition System: Starting expedition automation');
        try {
            if (expeditionSettings.enabled) {
                await this.performExpeditions(expeditionSettings);
            }
            if (dungeonSettings.enabled) {
                await this.performDungeons(dungeonSettings);
            }
        }
        catch (error) {
            console.error('Expedition System: Error in expedition automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Expedition System: Stopped expedition automation');
    }
    async performExpeditions(settings) {
        console.log('Expedition System: Starting expeditions');
        await this.navigateToExpedition();
        while (this.isRunning && this.shouldContinueExpeditions(settings)) {
            try {
                const locations = await this.getAvailableLocations(settings);
                if (locations.length === 0) {
                    console.log('Expedition System: No available expedition locations');
                    break;
                }
                const location = this.selectBestLocation(locations, settings);
                if (!location) {
                    console.log('Expedition System: No suitable location found');
                    break;
                }
                await this.startExpedition(location, settings);
                this.expeditionCount++;
                // Wait for expedition completion
                await this.waitForExpeditionCompletion();
                // Collect bonuses if enabled
                if (settings.autoCollectBonuses) {
                    await this.collectExpeditionBonuses();
                }
                await this.delay(5000);
            }
            catch (error) {
                console.error('Expedition System: Error in expedition loop:', error);
                await this.delay(10000);
            }
        }
    }
    async performDungeons(settings) {
        console.log('Expedition System: Starting dungeons');
        await this.navigateToDungeon(settings.location);
        while (this.isRunning && this.shouldContinueDungeons(settings)) {
            try {
                const dungeonInfo = await this.getDungeonInfo();
                if (!dungeonInfo) {
                    console.log('Expedition System: No dungeon information available');
                    break;
                }
                await this.enterDungeon(dungeonInfo, settings);
                await this.completeDungeon(dungeonInfo, settings);
                this.dungeonCount++;
                await this.delay(10000);
            }
            catch (error) {
                console.error('Expedition System: Error in dungeon loop:', error);
                // Handle dungeon failure
                if (settings.restartAfterFail > 0 && this.dungeonCount < settings.restartAfterFail) {
                    console.log('Expedition System: Restarting dungeon after failure');
                    await this.delay(15000);
                    continue;
                }
                else {
                    break;
                }
            }
        }
    }
    async navigateToExpedition() {
        if (!window.location.href.includes('mod=expedition')) {
            console.log('Expedition System: Navigating to expedition');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=expedition`;
            await this.waitForPageLoad();
        }
    }
    async navigateToDungeon(location) {
        if (!window.location.href.includes('mod=dungeon') || !window.location.href.includes(location)) {
            console.log(`Expedition System: Navigating to dungeon at ${location}`);
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=dungeon&loc=${location}`;
            await this.waitForPageLoad();
        }
    }
    async getAvailableLocations(settings) {
        const locations = [];
        const locationElements = document.querySelectorAll('.expedition_location, .location-item');
        for (let i = 0; i < locationElements.length; i++) {
            const element = locationElements[i];
            try {
                const location = await this.parseExpeditionLocation(element, i);
                if (location && this.isValidExpeditionLocation(location, settings)) {
                    locations.push(location);
                }
            }
            catch (error) {
                console.error('Expedition System: Error parsing location:', error);
            }
        }
        return locations;
    }
    async parseExpeditionLocation(element, index) {
        const nameElement = element.querySelector('.location_name, .name');
        const levelElement = element.querySelector('.location_level, .level');
        const pointsElement = element.querySelector('.dungeon_points, .points');
        const startButton = element.querySelector('.start_expedition, [data-action="start"]');
        const cooldownElement = element.querySelector('.cooldown, .timer');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        const dungeonPoints = parseInt(pointsElement?.textContent?.trim() || '0');
        const isAvailable = startButton && !startButton.hasAttribute('disabled');
        let cooldownRemaining = 0;
        if (cooldownElement) {
            const cooldownText = cooldownElement.textContent?.trim() || '';
            const timeMatch = cooldownText.match(/(\d+):(\d+):(\d+)/);
            if (timeMatch) {
                cooldownRemaining = parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]);
            }
        }
        return {
            id: index.toString(),
            name,
            level,
            dungeonPoints,
            isAvailable: !!isAvailable,
            cooldownRemaining
        };
    }
    isValidExpeditionLocation(location, settings) {
        // Must be available
        if (!location.isAvailable)
            return false;
        // Check minimum dungeon points
        if (location.dungeonPoints < settings.minDungeonPoints)
            return false;
        // Check specific location if set
        if (settings.location && !location.name.toLowerCase().includes(settings.location.toLowerCase())) {
            return false;
        }
        return true;
    }
    selectBestLocation(locations, settings) {
        if (locations.length === 0)
            return null;
        // Sort by dungeon points (highest first)
        locations.sort((a, b) => b.dungeonPoints - a.dungeonPoints);
        return locations[0];
    }
    async startExpedition(location, settings) {
        console.log(`Expedition System: Starting expedition to ${location.name}`);
        const locationElement = document.querySelector(`.expedition_location:nth-child(${parseInt(location.id) + 1})`);
        if (!locationElement)
            return;
        const startButton = locationElement.querySelector('.start_expedition, [data-action="start"]');
        if (!startButton || startButton.hasAttribute('disabled'))
            return;
        // Handle cooldown removal if enabled
        if (settings.removeCooldown === 'hourglass' && location.cooldownRemaining > 0) {
            await this.removeCooldownWithHourglass();
        }
        startButton.click();
        await this.delay(2000);
    }
    async removeCooldownWithHourglass() {
        const hourglassButton = document.querySelector('.use_hourglass, [data-action="use-hourglass"]');
        if (hourglassButton && !hourglassButton.hasAttribute('disabled')) {
            console.log('Expedition System: Using hourglass to remove cooldown');
            hourglassButton.click();
            await this.delay(1000);
        }
    }
    async waitForExpeditionCompletion() {
        console.log('Expedition System: Waiting for expedition completion');
        return new Promise((resolve) => {
            const checkCompletion = () => {
                const completionElement = document.querySelector('.expedition_complete, .completed');
                const collectButton = document.querySelector('.collect_rewards, [data-action="collect"]');
                if (completionElement || collectButton) {
                    resolve();
                }
                else {
                    setTimeout(checkCompletion, 5000);
                }
            };
            setTimeout(checkCompletion, 30000); // Start checking after 30 seconds
        });
    }
    async collectExpeditionBonuses() {
        const collectButton = document.querySelector('.collect_rewards, [data-action="collect"]');
        if (collectButton && !collectButton.hasAttribute('disabled')) {
            console.log('Expedition System: Collecting expedition bonuses');
            collectButton.click();
            await this.delay(2000);
        }
    }
    async getDungeonInfo() {
        const nameElement = document.querySelector('.dungeon_name, .name');
        const difficultyElement = document.querySelector('.dungeon_difficulty, .difficulty');
        const bossElement = document.querySelector('.boss_name, .boss');
        const keyElement = document.querySelector('.gate_key, .key-required');
        if (!nameElement)
            return null;
        return {
            name: nameElement.textContent?.trim() || '',
            difficulty: difficultyElement?.textContent?.trim() || 'normal',
            isAdvanced: difficultyElement?.textContent?.includes('advanced') || false,
            hasBoss: !!bossElement,
            bossName: bossElement?.textContent?.trim(),
            requiresKey: !!keyElement
        };
    }
    async enterDungeon(dungeonInfo, settings) {
        console.log(`Expedition System: Entering dungeon ${dungeonInfo.name}`);
        // Use gate key if required and enabled
        if (dungeonInfo.requiresKey && settings.useGateKey) {
            const keyButton = document.querySelector('.use_gate_key, [data-action="use-key"]');
            if (keyButton && !keyButton.hasAttribute('disabled')) {
                keyButton.click();
                await this.delay(2000);
            }
        }
        const enterButton = document.querySelector('.enter_dungeon, [data-action="enter"]');
        if (enterButton && !enterButton.hasAttribute('disabled')) {
            enterButton.click();
            await this.delay(3000);
        }
    }
    async completeDungeon(dungeonInfo, settings) {
        console.log(`Expedition System: Completing dungeon ${dungeonInfo.name}`);
        // Handle boss skip if enabled
        if (settings.skipBoss && dungeonInfo.hasBoss) {
            if (!settings.bossName || dungeonInfo.bossName?.includes(settings.bossName)) {
                console.log('Expedition System: Skipping boss as configured');
                const skipButton = document.querySelector('.skip_boss, [data-action="skip"]');
                if (skipButton) {
                    skipButton.click();
                    await this.delay(2000);
                }
            }
        }
        // Wait for dungeon completion
        await this.waitForDungeonCompletion();
    }
    async waitForDungeonCompletion() {
        return new Promise((resolve) => {
            const checkCompletion = () => {
                const completionElement = document.querySelector('.dungeon_complete, .completed');
                const exitButton = document.querySelector('.exit_dungeon, [data-action="exit"]');
                if (completionElement || exitButton) {
                    resolve();
                }
                else {
                    setTimeout(checkCompletion, 10000);
                }
            };
            setTimeout(checkCompletion, 60000); // Start checking after 1 minute
        });
    }
    shouldContinueExpeditions(settings) {
        // Add logic for expedition limits if needed
        return this.expeditionCount < 10; // Simplified limit
    }
    shouldContinueDungeons(settings) {
        // Add logic for dungeon limits if needed
        return this.dungeonCount < 5; // Simplified limit
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/forge-repair.ts":
/*!*************************************!*\
  !*** ./src/modules/forge-repair.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ForgeRepairModule: () => (/* binding */ ForgeRepairModule)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
// Forge & Repair Module - Handles automatic forging, smelting, and item repair

class ForgeRepairModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.itemsForged = 0;
        this.itemsRepaired = 0;
        this.itemsSmelted = 0;
        this.goldSpent = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(forgeSettings, repairSettings) {
        if (this.isRunning)
            return;
        this.isRunning = true;
        this.itemsForged = 0;
        this.itemsRepaired = 0;
        this.itemsSmelted = 0;
        this.goldSpent = 0;
        console.log('Forge & Repair: Starting forge and repair automation');
        try {
            if (forgeSettings.enabled) {
                await this.performForgeOperations(forgeSettings);
            }
            if (repairSettings.enabled) {
                await this.performRepairOperations(repairSettings);
            }
        }
        catch (error) {
            console.error('Forge & Repair: Error in automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Forge & Repair: Stopped automation');
    }
    async performForgeOperations(settings) {
        console.log('Forge & Repair: Starting forge operations');
        await this.navigateToForge();
        while (this.isRunning && this.shouldContinueForging(settings)) {
            try {
                // Handle smelting first if enabled
                if (settings.autoSmelt) {
                    await this.performSmelting(settings);
                }
                // Handle forging
                if (settings.autoForge) {
                    await this.performForging(settings);
                }
                await this.delay(10000);
            }
            catch (error) {
                console.error('Forge & Repair: Error in forge operations:', error);
                await this.delay(15000);
            }
        }
    }
    async performRepairOperations(settings) {
        console.log('Forge & Repair: Starting repair operations');
        await this.navigateToRepair();
        while (this.isRunning && this.shouldContinueRepairing(settings)) {
            try {
                const repairableItems = await this.getRepairableItems();
                const itemsToRepair = this.filterItemsForRepair(repairableItems, settings);
                for (const item of itemsToRepair) {
                    if (!this.isRunning)
                        break;
                    if (this.goldSpent + item.repairCost <= settings.maxGoldSpend) {
                        await this.repairItem(item);
                        this.itemsRepaired++;
                        this.goldSpent += item.repairCost;
                        await this.delay(2000);
                    }
                }
                await this.delay(30000);
            }
            catch (error) {
                console.error('Forge & Repair: Error in repair operations:', error);
                await this.delay(15000);
            }
        }
    }
    async performSmelting(settings) {
        console.log('Forge & Repair: Performing smelting operations');
        const smeltableItems = await this.getSmeltableItems();
        const itemsToSmelt = this.filterItemsForSmelting(smeltableItems, settings);
        for (const item of itemsToSmelt) {
            if (!this.isRunning)
                break;
            await this.smeltItem(item);
            this.itemsSmelted++;
            await this.delay(1500);
        }
    }
    async performForging(settings) {
        console.log('Forge & Repair: Performing forging operations');
        const forgeableItems = await this.getForgeableItems();
        const itemsToForge = this.filterItemsForForging(forgeableItems, settings);
        for (const item of itemsToForge) {
            if (!this.isRunning)
                break;
            if (this.goldSpent + item.forgeCost <= settings.maxGoldSpend) {
                await this.forgeItem(item);
                this.itemsForged++;
                this.goldSpent += item.forgeCost;
                await this.delay(3000);
            }
        }
    }
    async navigateToForge() {
        if (!window.location.href.includes('mod=forge')) {
            console.log('Forge & Repair: Navigating to forge');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=forge`;
            await this.waitForPageLoad();
        }
    }
    async navigateToRepair() {
        if (!window.location.href.includes('mod=repair')) {
            console.log('Forge & Repair: Navigating to repair shop');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=repair`;
            await this.waitForPageLoad();
        }
    }
    async getForgeableItems() {
        const items = [];
        const itemElements = document.querySelectorAll('.forgeable_item, .forge-recipe');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parseForgeableItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Forge & Repair: Error parsing forgeable item:', error);
            }
        }
        return items;
    }
    async parseForgeableItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const levelElement = element.querySelector('.item_level, .level');
        const costElement = element.querySelector('.forge_cost, .cost');
        const forgeButton = element.querySelector('.forge_button, [data-action="forge"]');
        const materialsElements = element.querySelectorAll('.material, .required-material');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        const forgeCost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
        // Parse materials
        const materials = [];
        materialsElements.forEach(mat => {
            const matText = mat.textContent?.trim();
            if (matText)
                materials.push(matText);
        });
        // Determine quality from CSS classes
        let quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE;
        const qualityClass = element.className;
        if (qualityClass.includes('green'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN;
        else if (qualityClass.includes('blue'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE;
        else if (qualityClass.includes('purple'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE;
        else if (qualityClass.includes('orange'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE;
        else if (qualityClass.includes('red'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.RED;
        return {
            id: index.toString(),
            name,
            quality,
            level,
            materials,
            canForge: !!(forgeButton && !forgeButton.hasAttribute('disabled')),
            forgeCost
        };
    }
    async getRepairableItems() {
        const items = [];
        const itemElements = document.querySelectorAll('.repairable_item, .damaged-item');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parseRepairableItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Forge & Repair: Error parsing repairable item:', error);
            }
        }
        return items;
    }
    async parseRepairableItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const durabilityElement = element.querySelector('.durability, .condition');
        const costElement = element.querySelector('.repair_cost, .cost');
        const repairButton = element.querySelector('.repair_button, [data-action="repair"]');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const repairCost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
        // Parse durability
        const durabilityText = durabilityElement?.textContent?.trim() || '100/100';
        const durabilityMatch = durabilityText.match(/(\d+)\/(\d+)/);
        const durability = durabilityMatch ? parseInt(durabilityMatch[1]) : 100;
        const maxDurability = durabilityMatch ? parseInt(durabilityMatch[2]) : 100;
        return {
            id: index.toString(),
            name,
            durability,
            maxDurability,
            repairCost,
            canRepair: !!(repairButton && !repairButton.hasAttribute('disabled'))
        };
    }
    async getSmeltableItems() {
        const items = [];
        const itemElements = document.querySelectorAll('.smeltable_item, .inventory-item');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parseSmeltableItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Forge & Repair: Error parsing smeltable item:', error);
            }
        }
        return items;
    }
    async parseSmeltableItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const levelElement = element.querySelector('.item_level, .level');
        const valueElement = element.querySelector('.smelt_value, .value');
        const smeltButton = element.querySelector('.smelt_button, [data-action="smelt"]');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        const smeltValue = parseInt(valueElement?.textContent?.replace(/\D/g, '') || '0');
        // Determine quality from CSS classes
        let quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE;
        const qualityClass = element.className;
        if (qualityClass.includes('green'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN;
        else if (qualityClass.includes('blue'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE;
        else if (qualityClass.includes('purple'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE;
        else if (qualityClass.includes('orange'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE;
        else if (qualityClass.includes('red'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.RED;
        return {
            id: index.toString(),
            name,
            quality,
            level,
            smeltValue,
            canSmelt: !!(smeltButton && !smeltButton.hasAttribute('disabled'))
        };
    }
    filterItemsForForging(items, settings) {
        return items.filter(item => {
            // Check if can forge
            if (!item.canForge)
                return false;
            // Check cost limit
            if (item.forgeCost > settings.maxCostPerItem)
                return false;
            // Check quality filter
            if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
                return false;
            }
            // Check level range
            if (settings.minLevel && item.level < settings.minLevel)
                return false;
            if (settings.maxLevel && item.level > settings.maxLevel)
                return false;
            // Check item type filter
            if (settings.itemTypes.length > 0) {
                const typeMatch = settings.itemTypes.some(type => item.name.toLowerCase().includes(type.toLowerCase()));
                if (!typeMatch)
                    return false;
            }
            return true;
        });
    }
    filterItemsForRepair(items, settings) {
        return items.filter(item => {
            // Check if can repair
            if (!item.canRepair)
                return false;
            // Check durability threshold
            const durabilityPercent = (item.durability / item.maxDurability) * 100;
            if (durabilityPercent > settings.repairThreshold)
                return false;
            // Check cost limit
            if (item.repairCost > settings.maxCostPerItem)
                return false;
            // Check item name filter
            if (settings.itemFilter.length > 0) {
                const nameMatch = settings.itemFilter.some(filter => item.name.toLowerCase().includes(filter.toLowerCase()));
                if (!nameMatch)
                    return false;
            }
            return true;
        });
    }
    filterItemsForSmelting(items, settings) {
        return items.filter(item => {
            // Check if can smelt
            if (!item.canSmelt)
                return false;
            // Check minimum smelt value
            if (item.smeltValue < settings.minSmeltValue)
                return false;
            // Check quality - usually smelt lower quality items
            if (settings.smeltQualityFilter.length > 0 && !settings.smeltQualityFilter.includes(item.quality)) {
                return false;
            }
            return true;
        });
    }
    async forgeItem(item) {
        console.log(`Forge & Repair: Forging ${item.name} for ${item.forgeCost} gold`);
        const itemElement = document.querySelector(`.forgeable_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const forgeButton = itemElement.querySelector('.forge_button, [data-action="forge"]');
        if (forgeButton && !forgeButton.hasAttribute('disabled')) {
            forgeButton.click();
            // Handle confirmation if present
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_forge, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    async repairItem(item) {
        console.log(`Forge & Repair: Repairing ${item.name} for ${item.repairCost} gold`);
        const itemElement = document.querySelector(`.repairable_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const repairButton = itemElement.querySelector('.repair_button, [data-action="repair"]');
        if (repairButton && !repairButton.hasAttribute('disabled')) {
            repairButton.click();
            // Handle confirmation if present
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_repair, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    async smeltItem(item) {
        console.log(`Forge & Repair: Smelting ${item.name} for ${item.smeltValue} materials`);
        const itemElement = document.querySelector(`.smeltable_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const smeltButton = itemElement.querySelector('.smelt_button, [data-action="smelt"]');
        if (smeltButton && !smeltButton.hasAttribute('disabled')) {
            smeltButton.click();
            // Handle confirmation if present
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_smelt, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    shouldContinueForging(settings) {
        // Check gold spending limit
        if (this.goldSpent >= settings.maxGoldSpend) {
            console.log('Forge & Repair: Gold spending limit reached for forging');
            return false;
        }
        // Check daily limits
        if (settings.dailyForgeLimit > 0 && this.itemsForged >= settings.dailyForgeLimit) {
            console.log('Forge & Repair: Daily forge limit reached');
            return false;
        }
        return true;
    }
    shouldContinueRepairing(settings) {
        // Check gold spending limit
        if (this.goldSpent >= settings.maxGoldSpend) {
            console.log('Forge & Repair: Gold spending limit reached for repairs');
            return false;
        }
        return true;
    }
    async getForgeRepairStatus() {
        return {
            isRunning: this.isRunning,
            itemsForged: this.itemsForged,
            itemsRepaired: this.itemsRepaired,
            itemsSmelted: this.itemsSmelted,
            goldSpent: this.goldSpent,
            lastUpdate: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/gold-management.ts":
/*!****************************************!*\
  !*** ./src/modules/gold-management.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoldManagementModule: () => (/* binding */ GoldManagementModule)
/* harmony export */ });
// Gold Management Module - Handles gold hiding, donations, and market operations
class GoldManagementModule {
    constructor(urlHelper, storageManager) {
        this.isProcessing = false;
        this.currentGold = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async manageGold(settings) {
        if (this.isProcessing)
            return;
        this.isProcessing = true;
        console.log('Gold Management: Starting gold management');
        try {
            this.currentGold = await this.getCurrentGold();
            if (this.shouldHideGold(settings)) {
                await this.hideGold(settings);
            }
            console.log('Gold Management: Gold management completed');
        }
        catch (error) {
            console.error('Gold Management: Error managing gold:', error);
            throw error;
        }
        finally {
            this.isProcessing = false;
        }
    }
    async getCurrentGold() {
        const goldElements = [
            '#header_values_gold',
            '.player_gold',
            '.gold-amount',
            '#gold_display'
        ];
        for (const selector of goldElements) {
            const element = document.querySelector(selector);
            if (element) {
                const goldText = element.textContent?.replace(/\D/g, '') || '0';
                return parseInt(goldText);
            }
        }
        return 0;
    }
    shouldHideGold(settings) {
        const excessGold = this.currentGold - settings.pocketMoney;
        return excessGold > settings.minGoldBackup;
    }
    async hideGold(settings) {
        const excessGold = this.currentGold - settings.pocketMoney;
        console.log(`Gold Management: Hiding ${excessGold} gold (keeping ${settings.pocketMoney} pocket money)`);
        // Try primary location first
        if (settings.primary.enabled) {
            const success = await this.hideGoldInLocation(settings.primary, excessGold, settings);
            if (success)
                return;
        }
        // Try backup location if primary failed
        if (settings.backup.enabled) {
            await this.hideGoldInLocation(settings.backup, excessGold, settings);
        }
    }
    async hideGoldInLocation(locationConfig, amount, settings) {
        switch (locationConfig.location) {
            case 'guild':
                return await this.hideGoldInGuild(amount, settings);
            case 'shop':
                return await this.hideGoldInShop(amount, settings);
            case 'market':
                return await this.hideGoldInMarket(amount, settings);
            case 'training':
                return await this.hideGoldInTraining(amount, settings);
            default:
                console.log(`Gold Management: Unknown location type: ${locationConfig.location}`);
                return false;
        }
    }
    async hideGoldInGuild(amount, settings) {
        console.log('Gold Management: Hiding gold in guild');
        await this.navigateToGuild();
        // Look for donation interface
        const donationInput = document.querySelector('#donation_amount, .donation-input');
        const donateButton = document.querySelector('.donate_button, [data-action="donate"]');
        if (!donationInput || !donateButton) {
            console.log('Gold Management: Guild donation interface not found');
            return false;
        }
        // Set donation amount
        donationInput.value = amount.toString();
        donationInput.dispatchEvent(new Event('input', { bubbles: true }));
        // Click donate button
        donateButton.click();
        await this.delay(2000);
        console.log(`Gold Management: Donated ${amount} gold to guild`);
        return true;
    }
    async hideGoldInShop(amount, settings) {
        console.log('Gold Management: Hiding gold by buying shop items');
        await this.navigateToShop();
        let remainingAmount = amount;
        const items = await this.getShopItems(settings);
        for (const item of items) {
            if (remainingAmount <= 0)
                break;
            if (item.price <= remainingAmount && item.canBuy) {
                await this.buyShopItem(item);
                remainingAmount -= item.price;
                await this.delay(1000);
            }
        }
        const spentAmount = amount - remainingAmount;
        console.log(`Gold Management: Spent ${spentAmount} gold on shop items`);
        return spentAmount > 0;
    }
    async hideGoldInMarket(amount, settings) {
        console.log('Gold Management: Hiding gold by buying market items');
        await this.navigateToMarket();
        let remainingAmount = amount;
        const items = await this.getMarketItems(settings);
        for (const item of items) {
            if (remainingAmount <= 0)
                break;
            if (item.price <= remainingAmount && item.canBuy) {
                await this.buyMarketItem(item);
                remainingAmount -= item.price;
                await this.delay(1500);
            }
        }
        const spentAmount = amount - remainingAmount;
        console.log(`Gold Management: Spent ${spentAmount} gold on market items`);
        return spentAmount > 0;
    }
    async hideGoldInTraining(amount, settings) {
        console.log('Gold Management: Hiding gold in skill training');
        await this.navigateToTraining();
        let remainingAmount = amount;
        for (const skill of settings.skillsToTrain) {
            if (remainingAmount <= 0)
                break;
            const trained = await this.trainSkill(skill, remainingAmount);
            remainingAmount -= trained;
        }
        const spentAmount = amount - remainingAmount;
        console.log(`Gold Management: Spent ${spentAmount} gold on skill training`);
        return spentAmount > 0;
    }
    async getShopItems(settings) {
        const items = [];
        const itemElements = document.querySelectorAll('.shop_item, .market-item');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            const item = await this.parseShopItem(element, i);
            if (item && this.isValidPurchase(item, settings)) {
                items.push(item);
            }
        }
        // Sort by price (ascending) for efficient spending
        items.sort((a, b) => a.price - b.price);
        return items;
    }
    async getMarketItems(settings) {
        const items = [];
        const itemElements = document.querySelectorAll('.market_item, .auction-item');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            const item = await this.parseMarketItem(element, i);
            if (item && this.isValidPurchase(item, settings)) {
                items.push(item);
            }
        }
        // Sort by price (ascending)
        items.sort((a, b) => a.price - b.price);
        return items;
    }
    async parseShopItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const priceElement = element.querySelector('.item_price, .price');
        const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
        if (!nameElement || !priceElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
        return {
            id: index.toString(),
            name,
            price,
            quality: 'unknown',
            seller: 'shop',
            canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))
        };
    }
    async parseMarketItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const priceElement = element.querySelector('.item_price, .price');
        const sellerElement = element.querySelector('.seller_name, .seller');
        const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
        if (!nameElement || !priceElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
        const seller = sellerElement?.textContent?.trim() || 'unknown';
        return {
            id: index.toString(),
            name,
            price,
            quality: 'unknown',
            seller,
            canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))
        };
    }
    isValidPurchase(item, settings) {
        // Check minimum price
        if (item.price < settings.minItemPrice)
            return false;
        // Check if buying from specific players
        if (settings.buyFromPlayers.length > 0) {
            return settings.buyFromPlayers.includes(item.seller);
        }
        // Check if item can be bought
        return item.canBuy;
    }
    async buyShopItem(item) {
        console.log(`Gold Management: Buying ${item.name} for ${item.price} gold`);
        const itemElement = document.querySelector(`.shop_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
        if (buyButton && !buyButton.hasAttribute('disabled')) {
            buyButton.click();
        }
    }
    async buyMarketItem(item) {
        console.log(`Gold Management: Buying ${item.name} from ${item.seller} for ${item.price} gold`);
        const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
        if (buyButton && !buyButton.hasAttribute('disabled')) {
            buyButton.click();
            // Handle confirmation if present
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_buy, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    async trainSkill(skillName, maxAmount) {
        console.log(`Gold Management: Training ${skillName} with up to ${maxAmount} gold`);
        const skillElements = document.querySelectorAll('.skill_item, .training-skill');
        for (const element of skillElements) {
            const nameElement = element.querySelector('.skill_name, .name');
            if (nameElement?.textContent?.toLowerCase().includes(skillName.toLowerCase())) {
                const trainButton = element.querySelector('.train_button, [data-action="train"]');
                const costElement = element.querySelector('.training_cost, .cost');
                if (trainButton && !trainButton.hasAttribute('disabled')) {
                    const cost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
                    if (cost <= maxAmount) {
                        trainButton.click();
                        await this.delay(2000);
                        return cost;
                    }
                }
            }
        }
        return 0;
    }
    async navigateToGuild() {
        if (!window.location.href.includes('mod=guild')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=guild`;
            await this.waitForPageLoad();
        }
    }
    async navigateToShop() {
        if (!window.location.href.includes('mod=location&loc=26')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=location&loc=26`;
            await this.waitForPageLoad();
        }
    }
    async navigateToMarket() {
        if (!window.location.href.includes('mod=market')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=market`;
            await this.waitForPageLoad();
        }
    }
    async navigateToTraining() {
        if (!window.location.href.includes('mod=training')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=training`;
            await this.waitForPageLoad();
        }
    }
    async getGoldStatus() {
        return {
            currentGold: await this.getCurrentGold(),
            isProcessing: this.isProcessing,
            lastProcessed: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/healing-system.ts":
/*!***************************************!*\
  !*** ./src/modules/healing-system.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HealingSystemModule: () => (/* binding */ HealingSystemModule)
/* harmony export */ });
// Healing System Module - Handles automatic healing and food management
class HealingSystemModule {
    constructor(urlHelper, storageManager) {
        this.isHealing = false;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async checkAndHeal(settings) {
        if (this.isHealing || !settings.enabled)
            return false;
        const healthInfo = this.getCurrentHealth();
        if (!healthInfo)
            return false;
        if (healthInfo.percentage < settings.minHealth) {
            console.log(`Healing System: Health at ${healthInfo.percentage}%, starting healing process`);
            return await this.performHealing(settings, healthInfo);
        }
        return false;
    }
    getCurrentHealth() {
        // Try multiple selectors for health display
        const healthSelectors = [
            '#header_values_hp_percent',
            '.health-bar .current',
            '#health_points',
            '.player-health .current'
        ];
        for (const selector of healthSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = element.textContent?.trim() || '';
                // Try different health formats
                let match = text.match(/(\d+)\/(\d+)/); // "50/100" format
                if (match) {
                    const current = parseInt(match[1]);
                    const maximum = parseInt(match[2]);
                    return {
                        current,
                        maximum,
                        percentage: Math.round((current / maximum) * 100)
                    };
                }
                match = text.match(/(\d+)%/); // "75%" format
                if (match) {
                    const percentage = parseInt(match[1]);
                    return {
                        current: percentage,
                        maximum: 100,
                        percentage
                    };
                }
            }
        }
        return null;
    }
    async performHealing(settings, healthInfo) {
        this.isHealing = true;
        let healingSuccess = false;
        try {
            // Try healing with cervisia first if enabled
            if (settings.healCervisia) {
                healingSuccess = await this.useCervisia();
                if (healingSuccess) {
                    console.log('Healing System: Healed with cervisia');
                    return true;
                }
            }
            // Try healing with eggs if enabled
            if (settings.healEggs && settings.selectedEggs.length > 0) {
                healingSuccess = await this.useEggs(settings.selectedEggs);
                if (healingSuccess) {
                    console.log('Healing System: Healed with eggs');
                    return true;
                }
            }
            // Try buying food if enabled and needed
            if (settings.buyFood && settings.buyFoodNeeded) {
                const foodBought = await this.buyFood(settings);
                if (foodBought) {
                    // Try healing again with newly bought food
                    healingSuccess = await this.useEggs(settings.selectedEggs);
                    if (healingSuccess) {
                        console.log('Healing System: Healed with purchased food');
                        return true;
                    }
                }
            }
            console.log('Healing System: No healing options available');
            return false;
        }
        catch (error) {
            console.error('Healing System: Error during healing:', error);
            return false;
        }
        finally {
            this.isHealing = false;
        }
    }
    async useCervisia() {
        // Navigate to tavern if not already there
        if (!window.location.href.includes('mod=location&loc=2')) {
            await this.navigateToLocation('mod=location&loc=2');
        }
        // Look for cervisia button
        const cervisiaButton = document.querySelector('.cervisia_button, [data-action="drink-cervisia"]');
        if (!cervisiaButton || cervisiaButton.hasAttribute('disabled')) {
            return false;
        }
        // Click cervisia button
        cervisiaButton.click();
        // Wait for healing to complete
        await this.delay(2000);
        // Check if health improved
        const newHealth = this.getCurrentHealth();
        return newHealth ? newHealth.percentage > 90 : false;
    }
    async useEggs(selectedEggs) {
        // Navigate to packages if not already there
        if (!window.location.href.includes('mod=packages')) {
            await this.navigateToLocation('mod=packages');
        }
        const availableFood = this.getAvailableFood();
        for (const eggType of selectedEggs) {
            const food = availableFood.find(f => f.id === eggType.toString());
            if (food && food.isAvailable && food.count > 0) {
                const success = await this.consumeFood(food);
                if (success) {
                    return true;
                }
            }
        }
        return false;
    }
    getAvailableFood() {
        const foodItems = [];
        // Parse food items from packages page
        const foodElements = document.querySelectorAll('.package_food, .food-item');
        foodElements.forEach((element, index) => {
            const nameElement = element.querySelector('.item_name, .name');
            const countElement = element.querySelector('.item_count, .count');
            const useButton = element.querySelector('.use_button, [data-action="use"]');
            const name = nameElement?.textContent?.trim() || `Food ${index}`;
            const count = parseInt(countElement?.textContent?.trim() || '0');
            const isAvailable = useButton && !useButton.hasAttribute('disabled');
            // Estimate heal amount based on food type
            let healAmount = 10; // Default
            if (name.toLowerCase().includes('egg'))
                healAmount = 25;
            if (name.toLowerCase().includes('bread'))
                healAmount = 15;
            if (name.toLowerCase().includes('meat'))
                healAmount = 30;
            foodItems.push({
                id: (index + 1).toString(),
                name,
                healAmount,
                count,
                isAvailable: !!isAvailable
            });
        });
        return foodItems;
    }
    async consumeFood(food) {
        const useButton = document.querySelector(`[data-food-id="${food.id}"] .use_button, .food-item:nth-child(${food.id}) [data-action="use"]`);
        if (!useButton || useButton.hasAttribute('disabled')) {
            return false;
        }
        console.log(`Healing System: Using ${food.name}`);
        useButton.click();
        // Wait for consumption
        await this.delay(1500);
        // Check if health improved
        const newHealth = this.getCurrentHealth();
        return newHealth ? newHealth.percentage >= 90 : false;
    }
    async buyFood(settings) {
        // Navigate to shop
        if (!window.location.href.includes('mod=location&loc=26')) {
            await this.navigateToLocation('mod=location&loc=26');
        }
        // Look for food items to buy
        const foodShopItems = document.querySelectorAll('.shop_item, .market-item');
        for (const item of foodShopItems) {
            const nameElement = item.querySelector('.item_name, .name');
            const buyButton = item.querySelector('.buy_button, [data-action="buy"]');
            const priceElement = item.querySelector('.item_price, .price');
            if (!nameElement || !buyButton || buyButton.hasAttribute('disabled'))
                continue;
            const name = nameElement.textContent?.trim().toLowerCase() || '';
            const price = parseInt(priceElement?.textContent?.replace(/\D/g, '') || '0');
            // Check if it's a food item we want to buy
            if ((name.includes('egg') || name.includes('bread') || name.includes('meat')) && price < 1000) {
                console.log(`Healing System: Buying ${name} for ${price} gold`);
                buyButton.click();
                await this.delay(2000);
                return true;
            }
        }
        return false;
    }
    async navigateToLocation(locationUrl) {
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.split('?')[0];
        console.log(`Healing System: Navigating to ${locationUrl}`);
        window.location.href = `${baseUrl}?${locationUrl}`;
        // Wait for page load
        await this.waitForPageLoad();
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000); // Additional delay for dynamic content
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async shouldStopAttacks(settings) {
        if (!settings.autoStopAttacks)
            return false;
        const healthInfo = this.getCurrentHealth();
        if (!healthInfo)
            return false;
        // Stop attacks if health is below minimum and no healing options available
        if (healthInfo.percentage < settings.minHealth) {
            const canHeal = await this.canPerformHealing(settings);
            if (!canHeal) {
                console.log('Healing System: Stopping attacks - low health and no healing options');
                return true;
            }
        }
        return false;
    }
    async canPerformHealing(settings) {
        // Check if cervisia is available
        if (settings.healCervisia) {
            // This would need to check tavern availability
            return true; // Simplified
        }
        // Check if food is available
        if (settings.healEggs) {
            const availableFood = this.getAvailableFood();
            const hasUsableFood = availableFood.some(food => settings.selectedEggs.includes(parseInt(food.id)) &&
                food.isAvailable &&
                food.count > 0);
            if (hasUsableFood)
                return true;
        }
        // Check if can buy food
        if (settings.buyFood && settings.buyFoodNeeded) {
            // This would need to check gold availability and shop access
            return true; // Simplified
        }
        return false;
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/market-operations.ts":
/*!******************************************!*\
  !*** ./src/modules/market-operations.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MarketOperationsModule: () => (/* binding */ MarketOperationsModule)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
// Market Operations Module - Handles automated buying, selling, and auction management

class MarketOperationsModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.itemsBought = 0;
        this.itemsSold = 0;
        this.goldSpent = 0;
        this.goldEarned = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning || !settings.enabled)
            return;
        this.isRunning = true;
        this.itemsBought = 0;
        this.itemsSold = 0;
        this.goldSpent = 0;
        this.goldEarned = 0;
        console.log('Market Operations: Starting market automation');
        try {
            await this.performMarketLoop(settings);
        }
        catch (error) {
            console.error('Market Operations: Error in market automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Market Operations: Stopped market automation');
    }
    async performMarketLoop(settings) {
        while (this.isRunning && this.shouldContinueOperations(settings)) {
            try {
                // Handle buying operations
                if (settings.autoBuy.enabled) {
                    await this.performBuyingOperations(settings.autoBuy);
                }
                // Handle selling operations
                if (settings.autoSell.enabled) {
                    await this.performSellingOperations(settings.autoSell);
                }
                // Handle auction operations
                if (settings.auction.enabled) {
                    await this.performAuctionOperations(settings.auction);
                }
                await this.delay(settings.checkInterval * 1000);
            }
            catch (error) {
                console.error('Market Operations: Error in market loop:', error);
                await this.delay(30000);
            }
        }
    }
    async performBuyingOperations(buySettings) {
        console.log('Market Operations: Performing buying operations');
        await this.navigateToMarket();
        const availableItems = await this.getMarketItems();
        const itemsToBuy = this.filterItemsForBuying(availableItems, buySettings);
        for (const item of itemsToBuy) {
            if (!this.isRunning)
                break;
            if (this.goldSpent + item.price <= buySettings.maxGoldSpend) {
                await this.buyItem(item);
                this.itemsBought++;
                this.goldSpent += item.price;
                await this.delay(2000);
            }
        }
    }
    async performSellingOperations(sellSettings) {
        console.log('Market Operations: Performing selling operations');
        await this.navigateToMarket();
        // Get items from inventory to sell
        const inventoryItems = await this.getInventoryItems();
        const itemsToSell = this.filterItemsForSelling(inventoryItems, sellSettings);
        for (const item of itemsToSell) {
            if (!this.isRunning)
                break;
            await this.sellItem(item, sellSettings);
            this.itemsSold++;
            await this.delay(2000);
        }
    }
    async performAuctionOperations(auctionSettings) {
        console.log('Market Operations: Performing auction operations');
        await this.navigateToAuction();
        const auctionItems = await this.getAuctionItems();
        const itemsToBid = this.filterItemsForBidding(auctionItems, auctionSettings);
        for (const item of itemsToBid) {
            if (!this.isRunning)
                break;
            if (auctionSettings.autoBid) {
                await this.bidOnItem(item, auctionSettings);
            }
            if (auctionSettings.autoBuyout && item.canBuyout) {
                await this.buyoutItem(item, auctionSettings);
            }
            await this.delay(3000);
        }
    }
    async navigateToMarket() {
        if (!window.location.href.includes('mod=market')) {
            console.log('Market Operations: Navigating to market');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=market`;
            await this.waitForPageLoad();
        }
    }
    async navigateToAuction() {
        if (!window.location.href.includes('mod=auction')) {
            console.log('Market Operations: Navigating to auction');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=auction`;
            await this.waitForPageLoad();
        }
    }
    async getMarketItems() {
        const items = [];
        const itemElements = document.querySelectorAll('.market_item, .market-listing');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parseMarketItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Market Operations: Error parsing market item:', error);
            }
        }
        return items;
    }
    async parseMarketItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const priceElement = element.querySelector('.item_price, .price');
        const sellerElement = element.querySelector('.seller_name, .seller');
        const levelElement = element.querySelector('.item_level, .level');
        const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
        const timeElement = element.querySelector('.time_remaining, .timer');
        if (!nameElement || !priceElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
        const seller = sellerElement?.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        // Determine quality from CSS classes
        let quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE;
        const qualityClass = element.className;
        if (qualityClass.includes('green'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN;
        else if (qualityClass.includes('blue'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE;
        else if (qualityClass.includes('purple'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE;
        else if (qualityClass.includes('orange'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE;
        else if (qualityClass.includes('red'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.RED;
        // Parse time remaining
        const timeText = timeElement?.textContent?.trim() || '0:00:00';
        const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
        const timeRemaining = timeMatch ?
            parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
        return {
            id: index.toString(),
            name,
            quality,
            level,
            price,
            seller,
            timeRemaining,
            canBuy: !!(buyButton && !buyButton.hasAttribute('disabled')),
            canBid: false // Market items are direct purchase
        };
    }
    async getAuctionItems() {
        const items = [];
        const itemElements = document.querySelectorAll('.auction_item, .auction-listing');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parseAuctionItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Market Operations: Error parsing auction item:', error);
            }
        }
        return items;
    }
    async parseAuctionItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const currentBidElement = element.querySelector('.current_bid, .bid');
        const buyoutElement = element.querySelector('.buyout_price, .buyout');
        const bidderElement = element.querySelector('.current_bidder, .bidder');
        const levelElement = element.querySelector('.item_level, .level');
        const bidButton = element.querySelector('.bid_button, [data-action="bid"]');
        const buyoutButton = element.querySelector('.buyout_button, [data-action="buyout"]');
        const timeElement = element.querySelector('.time_remaining, .timer');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const currentBid = parseInt(currentBidElement?.textContent?.replace(/\D/g, '') || '0');
        const buyoutPrice = parseInt(buyoutElement?.textContent?.replace(/\D/g, '') || '0');
        const bidder = bidderElement?.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        // Determine quality from CSS classes
        let quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE;
        const qualityClass = element.className;
        if (qualityClass.includes('green'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN;
        else if (qualityClass.includes('blue'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE;
        else if (qualityClass.includes('purple'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE;
        else if (qualityClass.includes('orange'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE;
        else if (qualityClass.includes('red'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.RED;
        // Parse time remaining
        const timeText = timeElement?.textContent?.trim() || '0:00:00';
        const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
        const timeRemaining = timeMatch ?
            parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
        return {
            id: index.toString(),
            name,
            quality,
            level,
            currentBid,
            buyoutPrice,
            timeRemaining,
            bidder,
            canBid: !!(bidButton && !bidButton.hasAttribute('disabled')),
            canBuyout: !!(buyoutButton && !buyoutButton.hasAttribute('disabled'))
        };
    }
    filterItemsForBuying(items, settings) {
        return items.filter(item => {
            // Check price limit
            if (item.price > settings.maxPrice)
                return false;
            // Check quality filter
            if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
                return false;
            }
            // Check level range
            if (settings.minLevel && item.level < settings.minLevel)
                return false;
            if (settings.maxLevel && item.level > settings.maxLevel)
                return false;
            // Check item name filter
            if (settings.itemNames.length > 0) {
                const nameMatch = settings.itemNames.some((name) => item.name.toLowerCase().includes(name.toLowerCase()));
                if (!nameMatch)
                    return false;
            }
            // Check seller blacklist
            if (settings.blacklistedSellers.includes(item.seller))
                return false;
            return item.canBuy;
        });
    }
    filterItemsForSelling(items, settings) {
        // This would filter inventory items for selling
        return items.filter(item => {
            // Apply selling rules similar to package management
            return true; // Simplified
        });
    }
    filterItemsForBidding(items, settings) {
        return items.filter(item => {
            // Check if we're already the highest bidder
            if (item.bidder === 'You')
                return false;
            // Check maximum bid
            if (item.currentBid >= settings.maxBid)
                return false;
            // Check time remaining (don't bid on items ending too soon)
            if (item.timeRemaining < settings.minTimeRemaining)
                return false;
            // Check quality and other filters similar to buying
            if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
                return false;
            }
            return item.canBid;
        });
    }
    async buyItem(item) {
        console.log(`Market Operations: Buying ${item.name} for ${item.price} gold`);
        const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
        if (buyButton && !buyButton.hasAttribute('disabled')) {
            buyButton.click();
            // Handle confirmation if present
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_buy, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    async sellItem(item, settings) {
        console.log(`Market Operations: Selling ${item.name}`);
        // This would handle putting items up for sale
        // Implementation would depend on the game's selling interface
    }
    async bidOnItem(item, settings) {
        const bidAmount = Math.min(item.currentBid + settings.bidIncrement, settings.maxBid);
        console.log(`Market Operations: Bidding ${bidAmount} gold on ${item.name}`);
        const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const bidInput = itemElement.querySelector('.bid_input, input[name="bid"]');
        const bidButton = itemElement.querySelector('.bid_button, [data-action="bid"]');
        if (bidInput && bidButton && !bidButton.hasAttribute('disabled')) {
            bidInput.value = bidAmount.toString();
            bidInput.dispatchEvent(new Event('input', { bubbles: true }));
            bidButton.click();
        }
    }
    async buyoutItem(item, settings) {
        if (item.buyoutPrice > settings.maxBuyout)
            return;
        console.log(`Market Operations: Buying out ${item.name} for ${item.buyoutPrice} gold`);
        const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const buyoutButton = itemElement.querySelector('.buyout_button, [data-action="buyout"]');
        if (buyoutButton && !buyoutButton.hasAttribute('disabled')) {
            buyoutButton.click();
            // Handle confirmation
            await this.delay(500);
            const confirmButton = document.querySelector('.confirm_buyout, [data-action="confirm"]');
            if (confirmButton) {
                confirmButton.click();
            }
        }
    }
    async getInventoryItems() {
        // This would get items from the player's inventory
        // Implementation would depend on how inventory is accessed
        return [];
    }
    shouldContinueOperations(settings) {
        // Check gold spending limit
        if (settings.autoBuy.enabled && this.goldSpent >= settings.autoBuy.maxGoldSpend) {
            console.log('Market Operations: Gold spending limit reached');
            return false;
        }
        // Check daily limits
        if (settings.dailyBuyLimit > 0 && this.itemsBought >= settings.dailyBuyLimit) {
            console.log('Market Operations: Daily buy limit reached');
            return false;
        }
        return true;
    }
    async getMarketStatus() {
        return {
            isRunning: this.isRunning,
            itemsBought: this.itemsBought,
            itemsSold: this.itemsSold,
            goldSpent: this.goldSpent,
            goldEarned: this.goldEarned,
            lastUpdate: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/package-management.ts":
/*!*******************************************!*\
  !*** ./src/modules/package-management.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PackageManagementModule: () => (/* binding */ PackageManagementModule)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
// Package Management Module - Handles item rotation, selling, and gold picking

class PackageManagementModule {
    constructor(urlHelper, storageManager) {
        this.isProcessing = false;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async processPackages(settings) {
        if (this.isProcessing)
            return;
        this.isProcessing = true;
        console.log('Package Management: Starting package processing');
        try {
            // Navigate to packages if not already there
            await this.navigateToPackages();
            // Pick gold if enabled
            if (settings.pickGold.enabled) {
                await this.pickGold(settings.pickGold.goldToPick);
            }
            // Rotate items if enabled
            if (settings.rotateItems.enabled) {
                await this.rotateItems(settings.rotateItems);
            }
            // Sell items if enabled
            if (settings.sellItems.enabled) {
                await this.sellItems(settings.sellItems);
            }
            console.log('Package Management: Package processing completed');
        }
        catch (error) {
            console.error('Package Management: Error processing packages:', error);
            throw error;
        }
        finally {
            this.isProcessing = false;
        }
    }
    async navigateToPackages() {
        if (!window.location.href.includes('mod=packages')) {
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            console.log('Package Management: Navigating to packages');
            window.location.href = `${baseUrl}?mod=packages`;
            await this.waitForPageLoad();
        }
    }
    async pickGold(goldToPick) {
        if (goldToPick <= 0)
            return;
        console.log(`Package Management: Picking ${goldToPick} gold`);
        // Look for gold input field and pick button
        const goldInput = document.querySelector('#gold_amount, .gold-input');
        const pickButton = document.querySelector('.pick_gold_button, [data-action="pick-gold"]');
        if (!goldInput || !pickButton) {
            console.log('Package Management: Gold picking interface not found');
            return;
        }
        // Set gold amount
        goldInput.value = goldToPick.toString();
        // Trigger input event
        goldInput.dispatchEvent(new Event('input', { bubbles: true }));
        // Click pick button
        pickButton.click();
        await this.delay(2000);
        console.log('Package Management: Gold picked successfully');
    }
    async rotateItems(rotateSettings) {
        console.log('Package Management: Starting item rotation');
        const items = await this.getPackageItems();
        const itemsToRotate = this.filterItemsForRotation(items, rotateSettings);
        for (const item of itemsToRotate) {
            try {
                await this.rotateItem(item);
                await this.delay(1000); // Delay between rotations
            }
            catch (error) {
                console.error(`Package Management: Error rotating item ${item.name}:`, error);
            }
        }
        console.log(`Package Management: Rotated ${itemsToRotate.length} items`);
    }
    async sellItems(sellSettings) {
        console.log('Package Management: Starting item selling');
        const items = await this.getPackageItems();
        const itemsToSell = this.filterItemsForSelling(items, sellSettings);
        for (const item of itemsToSell) {
            try {
                await this.sellItem(item);
                await this.delay(1000); // Delay between sales
            }
            catch (error) {
                console.error(`Package Management: Error selling item ${item.name}:`, error);
            }
        }
        console.log(`Package Management: Sold ${itemsToSell.length} items`);
    }
    async getPackageItems() {
        const items = [];
        // Parse items from packages page
        const itemElements = document.querySelectorAll('.package_item, .inventory-item');
        for (let i = 0; i < itemElements.length; i++) {
            const element = itemElements[i];
            try {
                const item = await this.parsePackageItem(element, i);
                if (item) {
                    items.push(item);
                }
            }
            catch (error) {
                console.error('Package Management: Error parsing item:', error);
            }
        }
        return items;
    }
    async parsePackageItem(element, index) {
        const nameElement = element.querySelector('.item_name, .name');
        const qualityElement = element.querySelector('.item_quality, .quality');
        const levelElement = element.querySelector('.item_level, .level');
        const priceElement = element.querySelector('.item_price, .price');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const qualityClass = qualityElement?.className || element.className;
        const level = parseInt(levelElement?.textContent?.trim() || '1');
        const price = parseInt(priceElement?.textContent?.replace(/\D/g, '') || '0');
        // Determine quality from CSS classes
        let quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE;
        if (qualityClass.includes('green'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN;
        else if (qualityClass.includes('blue'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE;
        else if (qualityClass.includes('purple'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE;
        else if (qualityClass.includes('orange'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE;
        else if (qualityClass.includes('red'))
            quality = _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.RED;
        // Check for underworld items
        const isUnderworld = element.classList.contains('underworld') ||
            name.toLowerCase().includes('underworld') ||
            element.querySelector('.underworld-icon') !== null;
        // Check for soulbound items
        const isSoulbound = element.classList.contains('soulbound') ||
            element.querySelector('.soulbound-icon') !== null;
        // Get conditioning
        const conditioningElement = element.querySelector('.conditioning, .condition');
        const conditioning = parseInt(conditioningElement?.textContent?.trim() || '100');
        // Check if item can be sold/rotated
        const sellButton = element.querySelector('.sell_button, [data-action="sell"]');
        const rotateButton = element.querySelector('.rotate_button, [data-action="rotate"]');
        return {
            id: index.toString(),
            name,
            quality,
            level,
            price,
            isUnderworld,
            isSoulbound,
            conditioning,
            canSell: !!(sellButton && !sellButton.hasAttribute('disabled')),
            canRotate: !!(rotateButton && !rotateButton.hasAttribute('disabled'))
        };
    }
    filterItemsForRotation(items, settings) {
        return items.filter(item => {
            // Must be able to rotate
            if (!item.canRotate)
                return false;
            // Check if soulbound items should be excluded
            if (item.isSoulbound && !settings.includeSoulbound)
                return false;
            // Check underworld items setting
            if (item.isUnderworld && !settings.underworldItems)
                return false;
            // Check selected item types
            if (settings.selectedItems.length > 0) {
                const itemTypeMatch = settings.selectedItems.some((selectedType) => item.name.toLowerCase().includes(selectedType.toLowerCase()));
                if (!itemTypeMatch)
                    return false;
            }
            // Check quality colors
            if (settings.colors.length > 0) {
                const qualityMatch = settings.colors.includes(item.quality);
                if (!qualityMatch)
                    return false;
            }
            return true;
        });
    }
    filterItemsForSelling(items, settings) {
        return items.filter(item => {
            // Must be able to sell
            if (!item.canSell)
                return false;
            // Don't sell soulbound items
            if (item.isSoulbound)
                return false;
            // Apply selling rules
            for (const rule of settings.rules) {
                if (this.itemMatchesRule(item, rule)) {
                    return true;
                }
            }
            return false;
        });
    }
    itemMatchesRule(item, rule) {
        // Check item type
        if (rule.itemType && !item.name.toLowerCase().includes(rule.itemType.toLowerCase())) {
            return false;
        }
        // Check quality
        if (rule.quality && item.quality !== rule.quality) {
            return false;
        }
        // Check level range
        if (rule.minLevel && item.level < rule.minLevel) {
            return false;
        }
        if (rule.maxLevel && item.level > rule.maxLevel) {
            return false;
        }
        // Check price range
        if (rule.minPrice && item.price < rule.minPrice) {
            return false;
        }
        if (rule.maxPrice && item.price > rule.maxPrice) {
            return false;
        }
        // Check conditioning
        if (rule.minConditioning && item.conditioning < rule.minConditioning) {
            return false;
        }
        return true;
    }
    async rotateItem(item) {
        console.log(`Package Management: Rotating ${item.name}`);
        const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const rotateButton = itemElement.querySelector('.rotate_button, [data-action="rotate"]');
        if (!rotateButton || rotateButton.hasAttribute('disabled'))
            return;
        rotateButton.click();
        await this.delay(1500);
    }
    async sellItem(item) {
        console.log(`Package Management: Selling ${item.name} for ${item.price} gold`);
        const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);
        if (!itemElement)
            return;
        const sellButton = itemElement.querySelector('.sell_button, [data-action="sell"]');
        if (!sellButton || sellButton.hasAttribute('disabled'))
            return;
        sellButton.click();
        // Handle confirmation dialog if present
        await this.delay(500);
        const confirmButton = document.querySelector('.confirm_sell, [data-action="confirm"]');
        if (confirmButton) {
            confirmButton.click();
        }
        await this.delay(1500);
    }
    async shouldProcessPackages(settings) {
        // Check if any package management is enabled
        return settings.pickGold.enabled ||
            settings.rotateItems.enabled ||
            settings.sellItems.enabled;
    }
    async getPackageStatus() {
        if (!window.location.href.includes('mod=packages')) {
            return null;
        }
        const items = await this.getPackageItems();
        const totalItems = items.length;
        const sellableItems = items.filter(item => item.canSell).length;
        const rotatableItems = items.filter(item => item.canRotate).length;
        return {
            totalItems,
            sellableItems,
            rotatableItems,
            lastProcessed: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/quest-management.ts":
/*!*****************************************!*\
  !*** ./src/modules/quest-management.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuestManagementModule: () => (/* binding */ QuestManagementModule)
/* harmony export */ });
// Quest Management Module - Handles automatic quest selection and completion
class QuestManagementModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.questsCompleted = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning || !settings.enabled)
            return;
        this.isRunning = true;
        this.questsCompleted = 0;
        console.log('Quest Management: Starting quest automation');
        try {
            await this.performQuestLoop(settings);
        }
        catch (error) {
            console.error('Quest Management: Error in quest automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Quest Management: Stopped quest automation');
    }
    async performQuestLoop(settings) {
        while (this.isRunning && this.shouldContinueQuests(settings)) {
            try {
                await this.navigateToQuests();
                // Get available quests
                const availableQuests = await this.getAvailableQuests();
                if (availableQuests.length === 0) {
                    console.log('Quest Management: No available quests found');
                    await this.delay(30000);
                    continue;
                }
                // Select best quest based on rules
                const selectedQuest = this.selectBestQuest(availableQuests, settings.rules);
                if (!selectedQuest) {
                    console.log('Quest Management: No suitable quest found');
                    await this.delay(30000);
                    continue;
                }
                // Accept and complete quest
                await this.acceptQuest(selectedQuest);
                if (settings.autoComplete) {
                    await this.completeQuest(selectedQuest, settings);
                }
                this.questsCompleted++;
                await this.delay(10000);
            }
            catch (error) {
                console.error('Quest Management: Error in quest loop:', error);
                await this.delay(15000);
            }
        }
    }
    async navigateToQuests() {
        if (!window.location.href.includes('mod=quest')) {
            console.log('Quest Management: Navigating to quests');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=quest`;
            await this.waitForPageLoad();
        }
    }
    async getAvailableQuests() {
        const quests = [];
        const questElements = document.querySelectorAll('.quest_item, .available-quest');
        for (let i = 0; i < questElements.length; i++) {
            const element = questElements[i];
            try {
                const quest = await this.parseQuestElement(element, i);
                if (quest && quest.isAvailable && !quest.isCompleted) {
                    quests.push(quest);
                }
            }
            catch (error) {
                console.error('Quest Management: Error parsing quest:', error);
            }
        }
        return quests;
    }
    async parseQuestElement(element, index) {
        const nameElement = element.querySelector('.quest_name, .name');
        const descriptionElement = element.querySelector('.quest_description, .description');
        const rewardElement = element.querySelector('.quest_reward, .reward');
        const acceptButton = element.querySelector('.accept_quest, [data-action="accept"]');
        const completedIndicator = element.querySelector('.quest_completed, .completed');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const description = descriptionElement?.textContent?.trim() || '';
        const reward = rewardElement?.textContent?.trim() || '';
        // Determine difficulty based on quest name or description
        let difficulty = 'medium';
        const text = (name + ' ' + description).toLowerCase();
        if (text.includes('easy') || text.includes('simple')) {
            difficulty = 'easy';
        }
        else if (text.includes('hard') || text.includes('difficult') || text.includes('challenging')) {
            difficulty = 'hard';
        }
        // Parse requirements
        const requirements = [];
        const reqElements = element.querySelectorAll('.quest_requirement, .requirement');
        reqElements.forEach(req => {
            const reqText = req.textContent?.trim();
            if (reqText)
                requirements.push(reqText);
        });
        return {
            id: index.toString(),
            name,
            description,
            reward,
            difficulty,
            isCompleted: !!completedIndicator,
            isAvailable: !!(acceptButton && !acceptButton.hasAttribute('disabled')),
            requirements
        };
    }
    selectBestQuest(quests, rules) {
        if (quests.length === 0)
            return null;
        // Sort rules by priority (highest first)
        const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            const matchingQuests = quests.filter(quest => this.questMatchesRule(quest, rule));
            if (matchingQuests.length > 0) {
                // Return the first matching quest for the highest priority rule
                return matchingQuests[0];
            }
        }
        // If no rules match, return the first available quest
        return quests[0];
    }
    questMatchesRule(quest, rule) {
        // Check quest type
        if (rule.questType && !quest.name.toLowerCase().includes(rule.questType.toLowerCase())) {
            return false;
        }
        // Check difficulty
        const difficultyOrder = { easy: 1, medium: 2, hard: 3 };
        if (difficultyOrder[quest.difficulty] > difficultyOrder[rule.maxDifficulty]) {
            return false;
        }
        // Check minimum reward (simplified - would need better reward parsing)
        const rewardValue = this.parseRewardValue(quest.reward);
        if (rewardValue < rule.minReward) {
            return false;
        }
        return true;
    }
    parseRewardValue(reward) {
        // Simple reward parsing - extract numbers from reward string
        const goldMatch = reward.match(/(\d+)\s*gold/i);
        const xpMatch = reward.match(/(\d+)\s*xp/i);
        let value = 0;
        if (goldMatch)
            value += parseInt(goldMatch[1]);
        if (xpMatch)
            value += parseInt(xpMatch[1]) * 0.1; // XP worth less than gold
        return value;
    }
    async acceptQuest(quest) {
        console.log(`Quest Management: Accepting quest "${quest.name}"`);
        const questElement = document.querySelector(`.quest_item:nth-child(${parseInt(quest.id) + 1})`);
        if (!questElement)
            return;
        const acceptButton = questElement.querySelector('.accept_quest, [data-action="accept"]');
        if (acceptButton && !acceptButton.hasAttribute('disabled')) {
            acceptButton.click();
            await this.delay(2000);
        }
    }
    async completeQuest(quest, settings) {
        console.log(`Quest Management: Attempting to complete quest "${quest.name}"`);
        // This would contain logic to actually complete the quest
        // For example, if it's a combat quest, go fight monsters
        // If it's a collection quest, collect items, etc.
        if (quest.name.toLowerCase().includes('combat') || quest.name.toLowerCase().includes('fight')) {
            await this.completeCombatQuest(quest, settings);
        }
        else if (quest.name.toLowerCase().includes('collect') || quest.name.toLowerCase().includes('gather')) {
            await this.completeCollectionQuest(quest, settings);
        }
        else if (quest.name.toLowerCase().includes('travel') || quest.name.toLowerCase().includes('visit')) {
            await this.completeTravelQuest(quest, settings);
        }
        // Check if quest is completed and claim reward
        await this.claimQuestReward(quest);
    }
    async completeCombatQuest(quest, settings) {
        // Navigate to appropriate combat area and fight required enemies
        console.log(`Quest Management: Completing combat quest "${quest.name}"`);
        // This would integrate with arena/turma combat modules
        // For now, just simulate completion
        await this.delay(5000);
    }
    async completeCollectionQuest(quest, settings) {
        // Navigate to appropriate location and collect required items
        console.log(`Quest Management: Completing collection quest "${quest.name}"`);
        // This would integrate with expedition/dungeon modules
        await this.delay(5000);
    }
    async completeTravelQuest(quest, settings) {
        // Navigate to required locations
        console.log(`Quest Management: Completing travel quest "${quest.name}"`);
        // Parse quest requirements to find locations to visit
        for (const requirement of quest.requirements) {
            if (requirement.toLowerCase().includes('visit') || requirement.toLowerCase().includes('go to')) {
                // Extract location and navigate there
                await this.delay(2000);
            }
        }
    }
    async claimQuestReward(quest) {
        // Look for quest completion and claim reward
        const claimButton = document.querySelector('.claim_reward, [data-action="claim"]');
        if (claimButton && !claimButton.hasAttribute('disabled')) {
            console.log(`Quest Management: Claiming reward for quest "${quest.name}"`);
            claimButton.click();
            await this.delay(2000);
        }
    }
    shouldContinueQuests(settings) {
        // Check daily quest limit
        if (settings.dailyLimit > 0 && this.questsCompleted >= settings.dailyLimit) {
            console.log('Quest Management: Daily quest limit reached');
            return false;
        }
        return true;
    }
    async getQuestStatus() {
        return {
            isRunning: this.isRunning,
            questsCompleted: this.questsCompleted,
            lastUpdate: new Date()
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/turma-combat.ts":
/*!*************************************!*\
  !*** ./src/modules/turma-combat.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TurmaCombatModule: () => (/* binding */ TurmaCombatModule)
/* harmony export */ });
// Turma Combat Module - Handles turma (guild war) combat functionality
class TurmaCombatModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.attackCount = 0;
        this.goldRaided = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning || !settings.enabled)
            return;
        this.isRunning = true;
        this.attackCount = 0;
        this.goldRaided = 0;
        console.log('Turma Combat: Starting turma automation');
        try {
            await this.navigateToTurma();
            await this.performTurmaLoop(settings);
        }
        catch (error) {
            console.error('Turma Combat: Error in turma automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Turma Combat: Stopped turma automation');
    }
    async navigateToTurma() {
        const currentUrl = window.location.href;
        if (!currentUrl.includes('mod=turma')) {
            console.log('Turma Combat: Navigating to turma');
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=turma`;
            await this.waitForPageLoad();
        }
    }
    async performTurmaLoop(settings) {
        while (this.isRunning && this.shouldContinueAttacking(settings)) {
            try {
                const targets = await this.getAvailableTargets(settings);
                if (targets.length === 0) {
                    console.log('Turma Combat: No suitable targets found');
                    await this.delay(10000);
                    continue;
                }
                const target = this.selectBestTarget(targets, settings);
                if (!target) {
                    console.log('Turma Combat: No target selected');
                    await this.delay(10000);
                    continue;
                }
                const result = await this.attackTarget(target, settings);
                await this.processAttackResult(result, target);
                // Random delay between attacks (longer than arena)
                const delay = Math.random() * 20000 + 10000; // 10-30 seconds
                await this.delay(delay);
            }
            catch (error) {
                console.error('Turma Combat: Error in attack loop:', error);
                await this.delay(15000);
            }
        }
    }
    async getAvailableTargets(settings) {
        const targets = [];
        // Parse turma page for targets
        const targetElements = document.querySelectorAll('.turma_opponent, .enemy_player');
        for (const element of targetElements) {
            try {
                const target = await this.parseTurmaTarget(element, settings);
                if (target && this.isValidTarget(target, settings)) {
                    targets.push(target);
                }
            }
            catch (error) {
                console.error('Turma Combat: Error parsing target:', error);
            }
        }
        return targets;
    }
    async parseTurmaTarget(element, settings) {
        const nameElement = element.querySelector('.player_name, .name');
        const levelElement = element.querySelector('.player_level, .level');
        const guildElement = element.querySelector('.guild_name, .guild');
        const goldElement = element.querySelector('.player_gold, .gold');
        const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
        const onlineIndicator = element.querySelector('.online_status, .status');
        if (!nameElement || !attackButton)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const level = parseInt(levelElement?.textContent?.trim() || '0');
        const guild = guildElement?.textContent?.trim() || '';
        const gold = parseInt(goldElement?.textContent?.replace(/\D/g, '') || '0');
        const id = attackButton.getAttribute('data-target-id') ||
            attackButton.getAttribute('href')?.match(/target=(\d+)/)?.[1] || '';
        const isOnline = onlineIndicator?.classList.contains('online') ||
            onlineIndicator?.textContent?.includes('online') || false;
        return {
            id,
            name,
            level,
            guild,
            gold,
            isOnline,
            canAttack: !attackButton.hasAttribute('disabled')
        };
    }
    isValidTarget(target, settings) {
        // Skip if can't attack
        if (!target.canAttack)
            return false;
        // Skip if in ignore list
        if (settings.ignorePlayers.includes(target.name))
            return false;
        // Skip if auto-ignore and recently attacked
        if (settings.autoIgnorePlayers.includes(target.name))
            return false;
        // Check level restriction
        if (settings.levelRestriction > 0 && target.level > settings.levelRestriction)
            return false;
        // Check if targeting specific players
        if (settings.attackPlayers.length > 0) {
            return settings.attackPlayers.includes(target.name);
        }
        // Check same server restriction
        if (!settings.attackSameServer) {
            // This would need server detection logic
            return true; // Simplified for now
        }
        return true;
    }
    selectBestTarget(targets, settings) {
        if (targets.length === 0)
            return null;
        // Sort by gold amount (highest first) and then by level (lowest first)
        targets.sort((a, b) => {
            if (Math.abs(a.gold - b.gold) < 1000) {
                return a.level - b.level; // Lower level first if gold is similar
            }
            return b.gold - a.gold; // Higher gold first
        });
        return targets[0];
    }
    async attackTarget(target, settings) {
        console.log(`Turma Combat: Attacking ${target.name} (Level ${target.level}, Gold: ${target.gold})`);
        // Find and click attack button
        const attackButton = document.querySelector(`[data-target-id="${target.id}"], [href*="target=${target.id}"]`);
        if (!attackButton) {
            throw new Error('Attack button not found');
        }
        attackButton.click();
        // Wait for attack result
        await this.waitForAttackResult();
        return this.parseAttackResult();
    }
    async processAttackResult(result, target) {
        this.attackCount++;
        if (result.won) {
            console.log(`Turma Combat: Victory! Raided ${result.gold} gold from ${target.name}`);
            this.goldRaided += result.gold;
            // Add to auto-ignore list if configured
            if (result.gold < 100) { // Low gold raids
                this.addToAutoIgnore(target.name);
            }
        }
        else {
            console.log(`Turma Combat: Defeat against ${target.name}`);
        }
        // Update statistics
        await this.updateStatistics(result);
    }
    addToAutoIgnore(playerName) {
        // This would add player to temporary ignore list
        console.log(`Turma Combat: Adding ${playerName} to auto-ignore list`);
    }
    shouldContinueAttacking(settings) {
        // Check auto stop limit
        if (settings.autoStop > 0 && this.attackCount >= settings.autoStop) {
            console.log('Turma Combat: Auto stop limit reached');
            return false;
        }
        // Check gold raided limit
        if (settings.goldRaided > 0 && this.goldRaided >= settings.goldRaided) {
            console.log('Turma Combat: Gold raided limit reached');
            return false;
        }
        // Check if quest-based attacking
        if (settings.attackIfQuest && !this.hasActiveTurmaQuest()) {
            console.log('Turma Combat: No active turma quest');
            return false;
        }
        return true;
    }
    hasActiveTurmaQuest() {
        // Check if there's an active turma-related quest
        const questElements = document.querySelectorAll('.quest_item, .active-quest');
        for (const quest of questElements) {
            const questText = quest.textContent?.toLowerCase() || '';
            if (questText.includes('turma') || questText.includes('guild') || questText.includes('war')) {
                return true;
            }
        }
        return false;
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async waitForAttackResult() {
        return new Promise((resolve) => {
            const checkResult = () => {
                const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');
                if (resultElement) {
                    resolve();
                }
                else {
                    setTimeout(checkResult, 500);
                }
            };
            setTimeout(checkResult, 3000); // Wait at least 3 seconds for turma
        });
    }
    parseAttackResult() {
        const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');
        if (!resultElement)
            return { won: false, gold: 0, xp: 0 };
        const won = resultElement.textContent?.includes('Victory') ||
            resultElement.textContent?.includes('Won') ||
            resultElement.classList.contains('victory') || false;
        const goldMatch = resultElement.textContent?.match(/(\d+)\s*gold/i);
        const xpMatch = resultElement.textContent?.match(/(\d+)\s*xp/i);
        return {
            won,
            gold: goldMatch ? parseInt(goldMatch[1]) : 0,
            xp: xpMatch ? parseInt(xpMatch[1]) : 0
        };
    }
    async updateStatistics(result) {
        const stats = await this.storageManager.getBotStatus();
        if (stats?.statistics) {
            stats.statistics.actionsPerformed++;
            if (result.won) {
                stats.statistics.combatsWon++;
                stats.statistics.goldEarned += result.gold;
                stats.statistics.experienceGained += result.xp;
            }
            else {
                stats.statistics.combatsLost++;
            }
            await this.storageManager.saveBotStatus(stats);
        }
    }
    async getTurmaStatus() {
        return {
            isRunning: this.isRunning,
            attackCount: this.attackCount,
            goldRaided: this.goldRaided,
            lastUpdate: new Date()
        };
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/modules/underworld-system.ts":
/*!******************************************!*\
  !*** ./src/modules/underworld-system.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UnderworldSystemModule: () => (/* binding */ UnderworldSystemModule)
/* harmony export */ });
// Underworld System Module - Handles underworld combat and management
class UnderworldSystemModule {
    constructor(urlHelper, storageManager) {
        this.isRunning = false;
        this.medicsUsed = 0;
        this.rubyUsed = 0;
        this.urlHelper = urlHelper;
        this.storageManager = storageManager;
    }
    async start(settings) {
        if (this.isRunning || !settings.enabled)
            return;
        this.isRunning = true;
        this.medicsUsed = 0;
        this.rubyUsed = 0;
        console.log('Underworld System: Starting underworld automation');
        try {
            await this.navigateToUnderworld();
            await this.performUnderworldLoop(settings);
        }
        catch (error) {
            console.error('Underworld System: Error in underworld automation:', error);
            throw error;
        }
    }
    async stop() {
        this.isRunning = false;
        console.log('Underworld System: Stopped underworld automation');
    }
    async navigateToUnderworld() {
        if (!window.location.href.includes('mod=underworld')) {
            console.log('Underworld System: Navigating to underworld');
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('?')[0];
            window.location.href = `${baseUrl}?mod=underworld`;
            await this.waitForPageLoad();
        }
    }
    async performUnderworldLoop(settings) {
        while (this.isRunning && this.shouldContinueUnderworld(settings)) {
            try {
                const status = await this.getUnderworldStatus();
                if (!status.isActive) {
                    console.log('Underworld System: Underworld not active');
                    break;
                }
                // Handle healing
                await this.handleUnderworldHealing(settings, status);
                // Handle buffs
                if (settings.buffs) {
                    await this.applyBuffs(settings);
                }
                // Handle costumes
                if (settings.costumes) {
                    await this.manageCostumes(settings);
                }
                // Handle combat
                await this.handleUnderworldCombat(settings, status);
                // Handle reinforcements
                if (settings.allowReinforcements) {
                    await this.manageReinforcements(settings);
                }
                await this.delay(10000); // 10 second loop
            }
            catch (error) {
                console.error('Underworld System: Error in underworld loop:', error);
                await this.delay(15000);
            }
        }
    }
    async getUnderworldStatus() {
        const timeElement = document.querySelector('.underworld_timer, .timer');
        const waveElement = document.querySelector('.current_wave, .wave');
        const guildHealthElement = document.querySelector('.guild_health, .guild-hp');
        const playerHealthElement = document.querySelector('.player_health, .player-hp');
        const timeText = timeElement?.textContent?.trim() || '0:00:00';
        const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
        const timeRemaining = timeMatch ?
            parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
        const waveText = waveElement?.textContent?.trim() || '1/10';
        const waveMatch = waveText.match(/(\d+)\/(\d+)/);
        const currentWave = waveMatch ? parseInt(waveMatch[1]) : 1;
        const totalWaves = waveMatch ? parseInt(waveMatch[2]) : 10;
        const guildHealthText = guildHealthElement?.textContent?.trim() || '100/100';
        const guildHealthMatch = guildHealthText.match(/(\d+)\/(\d+)/);
        const guildHealth = guildHealthMatch ? parseInt(guildHealthMatch[1]) : 100;
        const maxGuildHealth = guildHealthMatch ? parseInt(guildHealthMatch[2]) : 100;
        const playerHealthText = playerHealthElement?.textContent?.trim() || '100/100';
        const playerHealthMatch = playerHealthText.match(/(\d+)\/(\d+)/);
        const playerHealth = playerHealthMatch ? parseInt(playerHealthMatch[1]) : 100;
        const maxPlayerHealth = playerHealthMatch ? parseInt(playerHealthMatch[2]) : 100;
        return {
            isActive: timeRemaining > 0,
            timeRemaining,
            currentWave,
            totalWaves,
            guildHealth,
            maxGuildHealth,
            playerHealth,
            maxPlayerHealth
        };
    }
    async handleUnderworldHealing(settings, status) {
        // Heal guild if enabled and needed
        if (settings.healGuild && status.guildHealth < status.maxGuildHealth * 0.8) {
            await this.healGuild(settings);
        }
        // Heal player if needed
        const playerHealthPercent = (status.playerHealth / status.maxPlayerHealth) * 100;
        if (playerHealthPercent < 80) {
            if (settings.healPotions) {
                await this.useHealingPotions();
            }
            if (settings.healSacrifice && playerHealthPercent < 50) {
                await this.useSacrificeHealing();
            }
        }
    }
    async healGuild(settings) {
        // Use medics if available and under limit
        if (this.medicsUsed < settings.maxMedics) {
            const medicButton = document.querySelector('.use_medic, [data-action="use-medic"]');
            if (medicButton && !medicButton.hasAttribute('disabled')) {
                console.log('Underworld System: Using medic to heal guild');
                medicButton.click();
                this.medicsUsed++;
                await this.delay(2000);
                return;
            }
        }
        // Use ruby if allowed and under limit
        if (settings.allowRuby && this.rubyUsed < settings.maxRuby) {
            const rubyButton = document.querySelector('.use_ruby, [data-action="use-ruby"]');
            if (rubyButton && !rubyButton.hasAttribute('disabled')) {
                console.log('Underworld System: Using ruby to heal guild');
                rubyButton.click();
                this.rubyUsed++;
                await this.delay(2000);
            }
        }
    }
    async useHealingPotions() {
        const potionButton = document.querySelector('.use_potion, [data-action="use-potion"]');
        if (potionButton && !potionButton.hasAttribute('disabled')) {
            console.log('Underworld System: Using healing potion');
            potionButton.click();
            await this.delay(1500);
        }
    }
    async useSacrificeHealing() {
        const sacrificeButton = document.querySelector('.sacrifice_heal, [data-action="sacrifice"]');
        if (sacrificeButton && !sacrificeButton.hasAttribute('disabled')) {
            console.log('Underworld System: Using sacrifice healing');
            sacrificeButton.click();
            await this.delay(2000);
        }
    }
    async applyBuffs(settings) {
        // Auto buff gods if enabled
        if (settings.autoBuffGods) {
            const godBuffButtons = document.querySelectorAll('.god_buff, [data-action="buff-god"]');
            for (const button of godBuffButtons) {
                if (!button.hasAttribute('disabled')) {
                    button.click();
                    await this.delay(1000);
                }
            }
        }
        // Auto buff with oils if enabled
        if (settings.autoBuffOils) {
            const oilBuffButtons = document.querySelectorAll('.oil_buff, [data-action="buff-oil"]');
            for (const button of oilBuffButtons) {
                if (!button.hasAttribute('disabled')) {
                    button.click();
                    await this.delay(1000);
                }
            }
        }
    }
    async manageCostumes(settings) {
        // Heal costumes if enabled
        if (settings.healCostumes) {
            const healCostumeButton = document.querySelector('.heal_costume, [data-action="heal-costume"]');
            if (healCostumeButton && !healCostumeButton.hasAttribute('disabled')) {
                console.log('Underworld System: Healing costumes');
                healCostumeButton.click();
                await this.delay(2000);
            }
        }
    }
    async handleUnderworldCombat(settings, status) {
        // Check for Dis Pater and attack ASAP if enabled
        if (settings.attackDisPaterAsap) {
            const disPater = await this.findDisPater();
            if (disPater && disPater.canAttack) {
                console.log('Underworld System: Attacking Dis Pater ASAP');
                await this.attackBoss(disPater);
                return;
            }
        }
        // Regular combat logic
        const availableBosses = await this.getAvailableBosses();
        for (const boss of availableBosses) {
            if (boss.canAttack) {
                await this.attackBoss(boss);
                break;
            }
        }
    }
    async findDisPater() {
        const bossElements = document.querySelectorAll('.underworld_boss, .boss');
        for (const element of bossElements) {
            const nameElement = element.querySelector('.boss_name, .name');
            const name = nameElement?.textContent?.trim().toLowerCase() || '';
            if (name.includes('dis pater') || name.includes('dispater')) {
                return await this.parseBossElement(element);
            }
        }
        return null;
    }
    async getAvailableBosses() {
        const bosses = [];
        const bossElements = document.querySelectorAll('.underworld_boss, .boss');
        for (const element of bossElements) {
            const boss = await this.parseBossElement(element);
            if (boss) {
                bosses.push(boss);
            }
        }
        return bosses;
    }
    async parseBossElement(element) {
        const nameElement = element.querySelector('.boss_name, .name');
        const healthElement = element.querySelector('.boss_health, .health');
        const attackButton = element.querySelector('.attack_boss, [data-action="attack"]');
        if (!nameElement)
            return null;
        const name = nameElement.textContent?.trim() || '';
        const healthText = healthElement?.textContent?.trim() || '100/100';
        const healthMatch = healthText.match(/(\d+)\/(\d+)/);
        const health = healthMatch ? parseInt(healthMatch[1]) : 100;
        const maxHealth = healthMatch ? parseInt(healthMatch[2]) : 100;
        return {
            name,
            health,
            maxHealth,
            isDisPater: name.toLowerCase().includes('dis pater'),
            canAttack: !!(attackButton && !attackButton.hasAttribute('disabled'))
        };
    }
    async attackBoss(boss) {
        console.log(`Underworld System: Attacking ${boss.name}`);
        const bossElements = document.querySelectorAll('.underworld_boss, .boss');
        for (const element of bossElements) {
            const nameElement = element.querySelector('.boss_name, .name');
            if (nameElement?.textContent?.trim() === boss.name) {
                const attackButton = element.querySelector('.attack_boss, [data-action="attack"]');
                if (attackButton && !attackButton.hasAttribute('disabled')) {
                    attackButton.click();
                    await this.delay(3000);
                    break;
                }
            }
        }
    }
    async manageReinforcements(settings) {
        if (settings.reinforcements.length === 0)
            return;
        for (const reinforcement of settings.reinforcements) {
            const reinforcementButton = document.querySelector(`[data-reinforcement="${reinforcement}"]`);
            if (reinforcementButton && !reinforcementButton.hasAttribute('disabled')) {
                console.log(`Underworld System: Calling reinforcement: ${reinforcement}`);
                reinforcementButton.click();
                await this.delay(2000);
            }
        }
    }
    shouldContinueUnderworld(settings) {
        // Check if should exit underworld
        if (settings.exitUnder) {
            const currentTime = new Date().getHours();
            if (currentTime >= settings.stayHours) {
                console.log('Underworld System: Time limit reached, exiting underworld');
                return false;
            }
        }
        // Check resource limits
        if (this.medicsUsed >= settings.maxMedics && this.rubyUsed >= settings.maxRuby) {
            console.log('Underworld System: Resource limits reached');
            return false;
        }
        return true;
    }
    async getUnderworldInfo() {
        const status = await this.getUnderworldStatus();
        return {
            ...status,
            medicsUsed: this.medicsUsed,
            rubyUsed: this.rubyUsed,
            isRunning: this.isRunning
        };
    }
    async waitForPageLoad() {
        return new Promise((resolve) => {
            const checkLoad = () => {
                if (document.readyState === 'complete') {
                    setTimeout(resolve, 1000);
                }
                else {
                    setTimeout(checkLoad, 100);
                }
            };
            checkLoad();
        });
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}


/***/ }),

/***/ "./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* binding */ BankRequestState),
/* harmony export */   LicenseType: () => (/* binding */ LicenseType)
/* harmony export */ });
// API and network request types
var LicenseType;
(function (LicenseType) {
    LicenseType["FREE_TRIAL"] = "free_trial";
    LicenseType["ESSENTIAL"] = "essential";
    LicenseType["PREMIUM"] = "premium";
    LicenseType["INACTIVE"] = "inactive";
})(LicenseType || (LicenseType = {}));
var BankRequestState;
(function (BankRequestState) {
    BankRequestState["PENDING"] = "pending";
    BankRequestState["PROGRESS"] = "progress";
    BankRequestState["READY"] = "ready";
    BankRequestState["COMPLETE"] = "complete";
})(BankRequestState || (BankRequestState = {}));


/***/ }),

/***/ "./src/types/extension.ts":
/*!********************************!*\
  !*** ./src/types/extension.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideGoldLocation: () => (/* binding */ HideGoldLocation),
/* harmony export */   MessageType: () => (/* binding */ MessageType),
/* harmony export */   PackDuration: () => (/* binding */ PackDuration),
/* harmony export */   PackFilterType: () => (/* binding */ PackFilterType),
/* harmony export */   RenewShopOption: () => (/* binding */ RenewShopOption)
/* harmony export */ });
// Chrome extension specific type definitions
var MessageType;
(function (MessageType) {
    MessageType["LOGIN_REQUEST"] = "LOGIN_REQUEST";
    MessageType["LOGIN_RESPONSE"] = "LOGIN_RESPONSE";
    MessageType["LOGIN_FAILED"] = "LOGIN_FAILED";
    MessageType["SCRIPT_STATUS"] = "SCRIPT_STATUS";
    MessageType["SETTINGS_UPDATE"] = "SETTINGS_UPDATE";
    MessageType["NOTIFICATION"] = "NOTIFICATION";
    MessageType["ERROR"] = "ERROR";
})(MessageType || (MessageType = {}));
var RenewShopOption;
(function (RenewShopOption) {
    RenewShopOption["NOT_ALLOW"] = "not_allow";
    RenewShopOption["CLOTHE_ONLY"] = "clothe_only";
    RenewShopOption["RUBY_OR_CLOTHE"] = "ruby_or_clothe";
})(RenewShopOption || (RenewShopOption = {}));
var HideGoldLocation;
(function (HideGoldLocation) {
    HideGoldLocation["GUILD"] = "guild";
    HideGoldLocation["SHOP"] = "shop";
    HideGoldLocation["AUCTION"] = "auction";
    HideGoldLocation["TRAINING"] = "training";
    HideGoldLocation["MARKET"] = "market";
    HideGoldLocation["DONATE"] = "donate";
})(HideGoldLocation || (HideGoldLocation = {}));
var PackDuration;
(function (PackDuration) {
    PackDuration["TWO_HOURS"] = "2h";
    PackDuration["EIGHT_HOURS"] = "8h";
    PackDuration["TWENTY_FOUR_HOURS"] = "24h";
    PackDuration["FORTY_EIGHT_HOURS"] = "48h";
})(PackDuration || (PackDuration = {}));
var PackFilterType;
(function (PackFilterType) {
    PackFilterType["LOWEST_DURATION"] = "d";
    PackFilterType["HIGHEST_DURATION"] = "dd";
    PackFilterType["LOWEST_PRICE"] = "p";
    PackFilterType["HIGHEST_PRICE"] = "pd";
})(PackFilterType || (PackFilterType = {}));


/***/ }),

/***/ "./src/types/game.ts":
/*!***************************!*\
  !*** ./src/types/game.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BotAction: () => (/* binding */ BotAction),
/* harmony export */   ForgeDestination: () => (/* binding */ ForgeDestination),
/* harmony export */   ToolQuality: () => (/* binding */ ToolQuality),
/* harmony export */   WorkType: () => (/* binding */ WorkType)
/* harmony export */ });
// Game mechanics and bot logic types
var BotAction;
(function (BotAction) {
    BotAction["IDLE"] = "idle";
    BotAction["HEALING"] = "healing";
    BotAction["ATTACKING"] = "attacking";
    BotAction["TRAVELING"] = "traveling";
    BotAction["BUYING_PACK"] = "buying_pack";
    BotAction["PICKING_ITEMS"] = "picking_items";
    BotAction["UNDERWORLD"] = "underworld";
    BotAction["EVENT"] = "event";
    BotAction["SELLING_PACK"] = "selling_pack";
    BotAction["QUEST_MANAGEMENT"] = "quest_management";
    BotAction["MARKET_OPERATIONS"] = "market_operations";
    BotAction["FORGE_REPAIR"] = "forge_repair";
    BotAction["ROTATING_ITEMS"] = "rotating_items";
    BotAction["CHECKING_QUESTS"] = "checking_quests";
    BotAction["FORGING"] = "forging";
    BotAction["SMELTING"] = "smelting";
    BotAction["REPAIRING"] = "repairing";
    BotAction["CHECKING_MARKET"] = "checking_market";
    BotAction["CHECKING_AUCTION"] = "checking_auction";
    BotAction["ACTIVATING_PACTS"] = "activating_pacts";
    BotAction["PROCESSING_PACK"] = "processing_pack";
    BotAction["REQUESTING_PACK"] = "requesting_pack";
})(BotAction || (BotAction = {}));
var WorkType;
(function (WorkType) {
    WorkType["SENATOR"] = "senator";
    WorkType["JEWELLER"] = "jeweller";
    WorkType["STABLE_BOY"] = "stable_boy";
    WorkType["FARMER"] = "farmer";
    WorkType["BUTCHER"] = "butcher";
    WorkType["FISHERMAN"] = "fisherman";
    WorkType["BAKER"] = "baker";
    WorkType["BLACKSMITH"] = "blacksmith";
    WorkType["MASTER_BLACKSMITH"] = "master_blacksmith";
})(WorkType || (WorkType = {}));
var ToolQuality;
(function (ToolQuality) {
    ToolQuality["BRONZE"] = "bronze";
    ToolQuality["SILVER"] = "silver";
    ToolQuality["GOLD"] = "gold";
})(ToolQuality || (ToolQuality = {}));
var ForgeDestination;
(function (ForgeDestination) {
    ForgeDestination["NONE"] = "none";
    ForgeDestination["INVENTORY"] = "inventory";
    ForgeDestination["PACKAGES"] = "packages";
})(ForgeDestination || (ForgeDestination = {}));


/***/ }),

/***/ "./src/types/gladiatus.ts":
/*!********************************!*\
  !*** ./src/types/gladiatus.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GodType: () => (/* binding */ GodType),
/* harmony export */   ItemQuality: () => (/* binding */ ItemQuality),
/* harmony export */   ItemType: () => (/* binding */ ItemType),
/* harmony export */   LocationType: () => (/* binding */ LocationType),
/* harmony export */   QuestType: () => (/* binding */ QuestType)
/* harmony export */ });
// Gladiatus game-specific type definitions
var ItemQuality;
(function (ItemQuality) {
    ItemQuality[ItemQuality["WHITE"] = 0] = "WHITE";
    ItemQuality[ItemQuality["GREEN"] = 1] = "GREEN";
    ItemQuality[ItemQuality["BLUE"] = 2] = "BLUE";
    ItemQuality[ItemQuality["PURPLE"] = 3] = "PURPLE";
    ItemQuality[ItemQuality["ORANGE"] = 4] = "ORANGE";
    ItemQuality[ItemQuality["RED"] = 5] = "RED";
})(ItemQuality || (ItemQuality = {}));
var ItemType;
(function (ItemType) {
    ItemType["WEAPON"] = "weapon";
    ItemType["SHIELD"] = "shield";
    ItemType["HELMET"] = "helmet";
    ItemType["CHEST"] = "chest";
    ItemType["GLOVES"] = "gloves";
    ItemType["SHOES"] = "shoes";
    ItemType["RING"] = "ring";
    ItemType["AMULET"] = "amulet";
    ItemType["USABLE"] = "usable";
    ItemType["REINFORCEMENT"] = "reinforcement";
    ItemType["UPGRADE"] = "upgrade";
    ItemType["RECIPE"] = "recipe";
    ItemType["MERCENARY"] = "mercenary";
    ItemType["FORGING_GOODS"] = "forging_goods";
    ItemType["TOOLS"] = "tools";
    ItemType["SCROLL"] = "scroll";
    ItemType["EVENT_ITEMS"] = "event_items";
})(ItemType || (ItemType = {}));
var QuestType;
(function (QuestType) {
    QuestType["ARENA"] = "arena";
    QuestType["GROUP_ARENA"] = "group_arena";
    QuestType["EXPEDITION"] = "expedition";
    QuestType["DUNGEON"] = "dungeon";
    QuestType["WORK"] = "work";
    QuestType["FIND_ITEM"] = "find_item";
    QuestType["COMBAT"] = "combat";
    QuestType["ANY_QUEST"] = "any_quest";
})(QuestType || (QuestType = {}));
var GodType;
(function (GodType) {
    GodType["APOLLO"] = "apollo";
    GodType["VULCAN"] = "vulcan";
    GodType["MARS"] = "mars";
    GodType["MERCURY"] = "mercury";
    GodType["DIANA"] = "diana";
    GodType["MINERVA"] = "minerva";
})(GodType || (GodType = {}));
var LocationType;
(function (LocationType) {
    LocationType["EXPEDITION"] = "expedition";
    LocationType["DUNGEON"] = "dungeon";
    LocationType["ARENA"] = "arena";
    LocationType["TURMA"] = "turma";
    LocationType["UNDERWORLD"] = "underworld";
    LocationType["MARKET"] = "market";
    LocationType["GUILD"] = "guild";
})(LocationType || (LocationType = {}));


/***/ }),

/***/ "./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.BankRequestState),
/* harmony export */   BotAction: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.BotAction),
/* harmony export */   ButtonStyle: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ButtonStyle),
/* harmony export */   CooldownRemovalOption: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.CooldownRemovalOption),
/* harmony export */   FieldType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.FieldType),
/* harmony export */   FilterType: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.FilterType),
/* harmony export */   ForgeDestination: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ForgeDestination),
/* harmony export */   GodType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.GodType),
/* harmony export */   HideGoldLocation: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.HideGoldLocation),
/* harmony export */   ItemQuality: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemQuality),
/* harmony export */   ItemType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemType),
/* harmony export */   LicenseType: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.LicenseType),
/* harmony export */   LocationType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.LocationType),
/* harmony export */   MessageType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.MessageType),
/* harmony export */   ModalSize: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ModalSize),
/* harmony export */   NotificationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.NotificationType),
/* harmony export */   OperationSpeed: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.OperationSpeed),
/* harmony export */   PackDuration: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackDuration),
/* harmony export */   PackFilterType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackFilterType),
/* harmony export */   QuestType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.QuestType),
/* harmony export */   RenewShopOption: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.RenewShopOption),
/* harmony export */   ToolQuality: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ToolQuality),
/* harmony export */   UnderworldCostume: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldMode),
/* harmony export */   ValidationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ValidationType),
/* harmony export */   WorkType: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.WorkType)
/* harmony export */ });
/* harmony import */ var _gladiatus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gladiatus */ "./src/types/gladiatus.ts");
/* harmony import */ var _extension__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extension */ "./src/types/extension.ts");
/* harmony import */ var _localization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./localization */ "./src/types/localization.ts");
/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./settings */ "./src/types/settings.ts");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api */ "./src/types/api.ts");
/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ "./src/types/ui.ts");
/* harmony import */ var _game__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./game */ "./src/types/game.ts");
// Main type definitions for Gladiatus Helper Bot









/***/ }),

/***/ "./src/types/localization.ts":
/*!***********************************!*\
  !*** ./src/types/localization.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// Localization and internationalization types



/***/ }),

/***/ "./src/types/settings.ts":
/*!*******************************!*\
  !*** ./src/types/settings.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CooldownRemovalOption: () => (/* binding */ CooldownRemovalOption),
/* harmony export */   FilterType: () => (/* binding */ FilterType),
/* harmony export */   OperationSpeed: () => (/* binding */ OperationSpeed),
/* harmony export */   UnderworldCostume: () => (/* binding */ UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* binding */ UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* binding */ UnderworldMode)
/* harmony export */ });
// Settings and configuration type definitions
var FilterType;
(function (FilterType) {
    FilterType["CONTAINS"] = "contains";
    FilterType["NOT_CONTAINS"] = "not_contains";
    FilterType["CONTAINS_ANY"] = "contains_any";
    FilterType["NOT_CONTAINS_ANY"] = "not_contains_any";
    FilterType["CONTAINS_WORD"] = "contains_word";
    FilterType["IS_UNDERWORLD"] = "is_underworld";
    FilterType["IS_NOT_UNDERWORLD"] = "is_not_underworld";
    FilterType["GREATER_THAN"] = "greater_than";
    FilterType["LESS_THAN"] = "less_than";
    FilterType["STARTS_WITH"] = "starts_with";
    FilterType["ENDS_WITH"] = "ends_with";
    FilterType["CONTAINS_RESOURCE"] = "contains_resource";
})(FilterType || (FilterType = {}));
var OperationSpeed;
(function (OperationSpeed) {
    OperationSpeed["VERY_FAST"] = "very_fast";
    OperationSpeed["FAST"] = "fast";
    OperationSpeed["NORMAL"] = "normal";
    OperationSpeed["SLOW"] = "slow";
})(OperationSpeed || (OperationSpeed = {}));
var UnderworldCostume;
(function (UnderworldCostume) {
    UnderworldCostume["DIS_PATER_NORMAL"] = "dis_pater_normal";
    UnderworldCostume["DIS_PATER_MEDIUM"] = "dis_pater_medium";
    UnderworldCostume["DIS_PATER_HARD"] = "dis_pater_hard";
    UnderworldCostume["VULCANUS_FORGE"] = "vulcanus_forge";
    UnderworldCostume["FERONIAS_EARTHEN_SHIELD"] = "feronias_earthen_shield";
    UnderworldCostume["NEPTUNES_FLUID_MIGHT"] = "neptunes_fluid_might";
    UnderworldCostume["AELOUS_AERIAL_FREEDOM"] = "aelous_aerial_freedom";
    UnderworldCostume["PLUTOS_DEADLY_MIST"] = "plutos_deadly_mist";
    UnderworldCostume["JUNOS_BREATH_OF_LIFE"] = "junos_breath_of_life";
    UnderworldCostume["WRATH_MOUNTAIN_SCALE_ARMOUR"] = "wrath_mountain_scale_armour";
    UnderworldCostume["EAGLE_EYES"] = "eagle_eyes";
    UnderworldCostume["SATURNS_WINTER_GARMENT"] = "saturns_winter_garment";
    UnderworldCostume["BUBONA_BULL_ARMOUR"] = "bubona_bull_armour";
    UnderworldCostume["MERCERIUS_ROBBERS_GARMENTS"] = "mercerius_robbers_garments";
    UnderworldCostume["RA_LIGHT_ROBE"] = "ra_light_robe";
})(UnderworldCostume || (UnderworldCostume = {}));
var CooldownRemovalOption;
(function (CooldownRemovalOption) {
    CooldownRemovalOption["HOURGLASS"] = "hourglass";
    CooldownRemovalOption["HOURGLASS_OR_RUBY"] = "hourglass_or_ruby";
})(CooldownRemovalOption || (CooldownRemovalOption = {}));
var UnderworldMode;
(function (UnderworldMode) {
    UnderworldMode["FARM_QUEST"] = "farm_quest";
    UnderworldMode["FARM_MOB"] = "farm_mob";
    UnderworldMode["NORMAL"] = "normal";
})(UnderworldMode || (UnderworldMode = {}));
var UnderworldDifficulty;
(function (UnderworldDifficulty) {
    UnderworldDifficulty["NORMAL"] = "normal";
    UnderworldDifficulty["MEDIUM"] = "medium";
    UnderworldDifficulty["HARD"] = "hard";
})(UnderworldDifficulty || (UnderworldDifficulty = {}));


/***/ }),

/***/ "./src/types/ui.ts":
/*!*************************!*\
  !*** ./src/types/ui.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ButtonStyle: () => (/* binding */ ButtonStyle),
/* harmony export */   FieldType: () => (/* binding */ FieldType),
/* harmony export */   ModalSize: () => (/* binding */ ModalSize),
/* harmony export */   NotificationType: () => (/* binding */ NotificationType),
/* harmony export */   ValidationType: () => (/* binding */ ValidationType)
/* harmony export */ });
// UI and interface types
var NotificationType;
(function (NotificationType) {
    NotificationType["INFO"] = "info";
    NotificationType["SUCCESS"] = "success";
    NotificationType["WARNING"] = "warning";
    NotificationType["ERROR"] = "error";
    NotificationType["GOLD_EXPIRY"] = "gold_expiry";
    NotificationType["ITEM_FOUND"] = "item_found";
    NotificationType["ITEM_BOUGHT"] = "item_bought";
})(NotificationType || (NotificationType = {}));
var ModalSize;
(function (ModalSize) {
    ModalSize["SMALL"] = "small";
    ModalSize["MEDIUM"] = "medium";
    ModalSize["LARGE"] = "large";
    ModalSize["EXTRA_LARGE"] = "extra_large";
})(ModalSize || (ModalSize = {}));
var ButtonStyle;
(function (ButtonStyle) {
    ButtonStyle["PRIMARY"] = "primary";
    ButtonStyle["SECONDARY"] = "secondary";
    ButtonStyle["SUCCESS"] = "success";
    ButtonStyle["DANGER"] = "danger";
    ButtonStyle["WARNING"] = "warning";
    ButtonStyle["INFO"] = "info";
})(ButtonStyle || (ButtonStyle = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["NUMBER"] = "number";
    FieldType["SELECT"] = "select";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["RADIO"] = "radio";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["RANGE"] = "range";
    FieldType["COLOR"] = "color";
    FieldType["DATE"] = "date";
    FieldType["TIME"] = "time";
})(FieldType || (FieldType = {}));
var ValidationType;
(function (ValidationType) {
    ValidationType["REQUIRED"] = "required";
    ValidationType["MIN"] = "min";
    ValidationType["MAX"] = "max";
    ValidationType["MIN_LENGTH"] = "min_length";
    ValidationType["MAX_LENGTH"] = "max_length";
    ValidationType["PATTERN"] = "pattern";
    ValidationType["EMAIL"] = "email";
    ValidationType["URL"] = "url";
})(ValidationType || (ValidationType = {}));


/***/ }),

/***/ "./src/utils/default-settings.ts":
/*!***************************************!*\
  !*** ./src/utils/default-settings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DefaultSettingsFactory: () => (/* binding */ DefaultSettingsFactory)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
// Default Settings Factory - Provides default values for all bot settings

class DefaultSettingsFactory {
    static createDefaultSettings() {
        return {
            general: {
                selectedBackpack: 1,
                maxRandomDelay: 10,
                loginDelay: 5,
                autoCollectDaily: true,
                autoCollectGodOils: true,
                showBiddenBox: true,
                showShopBox: true,
                highlightUnderworldItems: true,
                shopMinQuality: 1
            },
            heal: {
                enabled: false,
                minHealth: 80,
                healCervisia: true,
                healEggs: true,
                selectedEggs: [1, 2, 3],
                buyFood: false,
                buyFoodNeeded: false,
                renewShop: _types__WEBPACK_IMPORTED_MODULE_0__.RenewShopOption.NOT_ALLOW,
                autoStopAttacks: true
            },
            hideGold: {
                primary: {
                    enabled: false,
                    location: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.GUILD,
                    settings: {}
                },
                backup: {
                    enabled: false,
                    location: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.SHOP,
                    settings: {}
                },
                minGoldBackup: 500,
                soulBoundTo: '',
                filterBy: _types__WEBPACK_IMPORTED_MODULE_0__.PackFilterType.LOWEST_PRICE,
                allowMultiple: false,
                recheckDuration: 3600,
                pocketMoney: 1000,
                hideIn: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.GUILD,
                duration: _types__WEBPACK_IMPORTED_MODULE_0__.PackDuration.TWO_HOURS,
                skillsToTrain: [],
                minItemPrice: 100,
                minPack: 1,
                maxPack: 10,
                buyFromPlayers: []
            },
            packages: {
                rotateItems: {
                    enabled: false,
                    selectedItems: [],
                    colors: [],
                    underworldItems: false,
                    itemsCooldownDays: 7,
                    collectResourceHourly: false
                },
                sellItems: {
                    enabled: false,
                    rules: [],
                    autoSellOnRenew: false
                },
                pickGold: {
                    enabled: false,
                    goldToPick: 0
                },
                operationSpeed: _types__WEBPACK_IMPORTED_MODULE_0__.OperationSpeed.NORMAL
            },
            costumes: {
                wearUnderworld: false,
                preventWearUnderworldOnPause: false,
                dontPauseIfUnderworldActive: false,
                underworldCostume: 'dis_pater_normal',
                standardProfile: {
                    enabled: false
                },
                turmaProfile: {
                    enabled: false
                },
                eventProfile: {
                    enabled: false
                }
            },
            turma: {
                enabled: false,
                levelRestriction: 0,
                autoStop: 0,
                attackIfQuest: false,
                runUntilGetChest: false,
                goldRaided: 0,
                attackPlayers: [],
                autoIgnorePlayers: [],
                ignorePlayers: [],
                attackSameServer: true
            },
            arena: {
                enabled: false,
                allowSim: true,
                prioritizeChance: 80,
                skipLow: false,
                loseLimit: 3,
                attackTargetPlayers: false,
                allowMaxAttacks: false,
                autoIgnoreGuildPlayers: true,
                attackScore: false,
                selectScorePage: 1,
                progressLeague: false,
                stopAfterLimit: false,
                attackGoldWhale: false,
                doRevenge: false,
                totalWin: false,
                limitAttacks: 0,
                dailyAttacks: 0,
                autoStop: 0,
                attackIfQuest: false,
                attackIfCombatQuest: false,
                goldRaided: 0,
                attackPlayers: [],
                autoIgnorePlayers: [],
                ignorePlayers: [],
                attackSameServer: true,
                attackScoreSingle: false
            },
            expeditions: {
                enabled: false,
                minDungeonPoints: 0,
                location: '',
                mob: '',
                autoCollectBonuses: true,
                travelForDungeon: false,
                removeCooldown: _types__WEBPACK_IMPORTED_MODULE_0__.CooldownRemovalOption.HOURGLASS,
                cooldownLimit: 10,
                cooldownUsed: 0
            },
            dungeon: {
                enabled: false,
                location: '',
                useGateKey: false,
                isAdvanced: false,
                skipBoss: false,
                bossName: '',
                restartAfterFail: 0
            },
            event: {
                enabled: false,
                mob: '',
                autoCollectBonuses: true,
                autoRenewEvent: false,
                followLeader: '',
                autoStop: false
            },
            underworld: {
                enabled: false,
                noWeapon: false,
                sendMail: false,
                mailContent: '',
                maxMedics: 5,
                usedMedics: 0,
                maxRuby: 2,
                usedRuby: 0,
                buffs: true,
                costumes: true,
                reinforcements: [],
                mode: _types__WEBPACK_IMPORTED_MODULE_0__.UnderworldMode.NORMAL,
                difficulty: _types__WEBPACK_IMPORTED_MODULE_0__.UnderworldDifficulty.NORMAL,
                healGuild: true,
                healPotions: true,
                healSacrifice: false,
                allowMobilisation: false,
                allowRuby: false,
                attackDisPaterAsap: true,
                stopArenaInUnder: true,
                healCostumes: true,
                allowReinforcements: false,
                allowUpgrades: false,
                autoBuffGods: true,
                autoBuffOils: true,
                exitUnder: false,
                stayHours: 24
            },
            forge: {
                enabled: false,
                autoForge: false,
                autoSmelt: false,
                maxGoldSpend: 10000,
                maxCostPerItem: 1000,
                dailyForgeLimit: 10,
                qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN],
                minLevel: 1,
                maxLevel: 50,
                itemTypes: [],
                minSmeltValue: 10,
                smeltQualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE]
            },
            repair: {
                enabled: false,
                repairThreshold: 50,
                maxCostPerItem: 500,
                maxGoldSpend: 5000,
                itemFilter: []
            },
            quest: {
                enabled: false,
                autoComplete: false,
                dailyLimit: 10,
                rules: []
            },
            market: {
                enabled: false,
                checkInterval: 300,
                dailyBuyLimit: 20,
                autoBuy: {
                    enabled: false,
                    maxPrice: 10000,
                    maxGoldSpend: 50000,
                    qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE],
                    minLevel: 1,
                    maxLevel: 100,
                    itemNames: [],
                    blacklistedSellers: []
                },
                autoSell: {
                    enabled: false,
                    rules: []
                },
                auction: {
                    enabled: false,
                    autoBid: false,
                    autoBuyout: false,
                    maxBid: 10000,
                    maxBuyout: 20000,
                    bidIncrement: 100,
                    minTimeRemaining: 300,
                    qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE]
                }
            },
            shop: {
                enabled: false,
                searchRules: [],
                maxSearches: 10,
                usedSearches: 0,
                allowRuby: false,
                autoStart: false
            },
            bank: {
                enabled: false,
                isBank: false,
                selectedBackpack: 1,
                clientSettings: {
                    requestThreshold: 1000,
                    packGoldAmount: 10000,
                    duration: '2h'
                }
            },
            guild: {
                enabled: false,
                attackList: [],
                ignoreList: []
            }
        };
    }
    static mergeWithDefaults(userSettings) {
        const defaultSettings = this.createDefaultSettings();
        return this.deepMerge(defaultSettings, userSettings);
    }
    static deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            }
            else {
                result[key] = source[key];
            }
        }
        return result;
    }
}


/***/ }),

/***/ "./src/utils/gladiator-url-helper.ts":
/*!*******************************************!*\
  !*** ./src/utils/gladiator-url-helper.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GladiatorUrlHelper: () => (/* binding */ GladiatorUrlHelper)
/* harmony export */ });
// Gladiatus URL parsing and validation utilities
class GladiatorUrlHelper {
    constructor() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
    /**
     * Check if URL is a Gladiatus domain
     */
    isGladiatus(url) {
        return /gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is a Gladiatus game page (not lobby)
     */
    isPlaying(url) {
        return /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is the Gladiatus lobby
     */
    isLobby(url) {
        return /lobby\.gladiatus\.gameforge\.com/.test(url) && url.indexOf('loading') < 0;
    }
    /**
     * Parse query string into object
     */
    resolveQueries(queryString) {
        const queries = {};
        const params = queryString.split('&');
        for (let i = params.length - 1; i >= 0; i--) {
            const [key, value] = params[i].split('=');
            if (key && value) {
                queries[key] = decodeURIComponent(value);
            }
        }
        return queries;
    }
    /**
     * Resolve URL information from Gladiatus game URL
     */
    resolve(url, force = false) {
        if (this.resolved && !force) {
            return this.urlInfo;
        }
        const match = url.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main)\.php(?:\?(.*))?/i);
        if (!match) {
            return null;
        }
        this.urlInfo = {
            server: match[1],
            country: match[2],
            domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,
            queries: this.resolveQueries(match[3] || ''),
            resolved: true
        };
        this.resolved = true;
        return this.urlInfo;
    }
    /**
     * Build Gladiatus URL with parameters
     */
    buildUrl(params, page = 'index.php') {
        const queryParts = [];
        for (const key in params) {
            queryParts.push(`${key}=${encodeURIComponent(params[key])}`);
        }
        // Add security hash if available
        if (this.urlInfo.queries.sh) {
            queryParts.push(`sh=${this.urlInfo.queries.sh}`);
        }
        return `${page}?${queryParts.join('&')}`;
    }
    /**
     * Build AJAX URL with parameters
     */
    buildAjaxUrl(params) {
        return this.buildUrl(params, 'ajax.php');
    }
    /**
     * Get current URL info
     */
    getUrlInfo() {
        return this.urlInfo;
    }
    /**
     * Reset resolved state
     */
    reset() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
}


/***/ }),

/***/ "./src/utils/storage-manager.ts":
/*!**************************************!*\
  !*** ./src/utils/storage-manager.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StorageManager: () => (/* binding */ StorageManager)
/* harmony export */ });
/* harmony import */ var _default_settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./default-settings */ "./src/utils/default-settings.ts");
// Chrome extension storage management utilities

class StorageManager {
    constructor() {
        this.STORAGE_KEYS = {
            SETTINGS: 'gh_settings',
            STATISTICS: 'gh_statistics',
            LICENSE: 'gh_license',
            PAUSE_SCHEDULE: 'gh_pause_schedule',
            QUEST_RULES: 'gh_quest_rules',
            MARKET_RULES: 'gh_market_rules',
            BANK_DATA: 'gh_bank_data',
            BOT_STATUS: 'gh_bot_status'
        };
    }
    /**
     * Check if Chrome storage API is available
     */
    isChromeStorageAvailable() {
        return typeof chrome !== 'undefined' &&
            chrome.storage &&
            chrome.storage.local &&
            typeof chrome.storage.local.get === 'function';
    }
    /**
     * Fallback method to get settings from localStorage
     */
    getSettingsFromLocalStorage() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);
            return stored ? JSON.parse(stored) : null;
        }
        catch (error) {
            console.error('Failed to get settings from localStorage:', error);
            return null;
        }
    }
    /**
     * Fallback method to save settings to localStorage
     */
    saveSettingsToLocalStorage(settings) {
        try {
            const currentSettings = this.getSettingsFromLocalStorage() || {};
            const updatedSettings = this.deepMerge(currentSettings, settings);
            localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));
        }
        catch (error) {
            console.error('Failed to save settings to localStorage:', error);
        }
    }
    /**
     * Get bot settings from storage
     */
    async getSettings() {
        try {
            // Check if Chrome storage API is available
            if (!this.isChromeStorageAvailable()) {
                console.warn('Chrome storage API not available, using localStorage fallback');
                const storedSettings = this.getSettingsFromLocalStorage();
                return storedSettings ? _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings) : _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
            }
            const result = await chrome.storage.local.get(this.STORAGE_KEYS.SETTINGS);
            const storedSettings = result[this.STORAGE_KEYS.SETTINGS];
            if (!storedSettings) {
                // Return default settings if none are stored
                return _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
            }
            // Merge stored settings with defaults to ensure all properties exist
            return _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings);
        }
        catch (error) {
            console.error('Failed to get settings:', error);
            // Fallback to localStorage
            const storedSettings = this.getSettingsFromLocalStorage();
            return storedSettings ? _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings) : _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
        }
    }
    /**
     * Save bot settings to storage
     */
    async saveSettings(settings) {
        try {
            // Check if Chrome storage API is available
            if (!this.isChromeStorageAvailable()) {
                console.warn('Chrome storage API not available, using localStorage fallback');
                this.saveSettingsToLocalStorage(settings);
                return;
            }
            const currentSettings = await this.getSettings() || {};
            const updatedSettings = this.deepMerge(currentSettings, settings);
            await chrome.storage.local.set({
                [this.STORAGE_KEYS.SETTINGS]: updatedSettings
            });
        }
        catch (error) {
            console.error('Failed to save settings:', error);
            // Fallback to localStorage
            this.saveSettingsToLocalStorage(settings);
        }
    }
    /**
     * Get bot status from storage
     */
    async getBotStatus() {
        try {
            if (!this.isChromeStorageAvailable()) {
                const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
                return stored ? JSON.parse(stored) : null;
            }
            const result = await chrome.storage.local.get(this.STORAGE_KEYS.BOT_STATUS);
            return result[this.STORAGE_KEYS.BOT_STATUS] || null;
        }
        catch (error) {
            console.error('Failed to get bot status:', error);
            try {
                const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
                return stored ? JSON.parse(stored) : null;
            }
            catch {
                return null;
            }
        }
    }
    /**
     * Save bot status to storage
     */
    async saveBotStatus(status) {
        try {
            if (!this.isChromeStorageAvailable()) {
                localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
                return;
            }
            await chrome.storage.local.set({
                [this.STORAGE_KEYS.BOT_STATUS]: status
            });
        }
        catch (error) {
            console.error('Failed to save bot status:', error);
            // Fallback to localStorage
            try {
                localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
            }
            catch (fallbackError) {
                console.error('Failed to save bot status to localStorage:', fallbackError);
                throw error;
            }
        }
    }
    /**
     * Get all extension data from storage
     */
    async getAllData() {
        try {
            const keys = Object.values(this.STORAGE_KEYS);
            const result = await chrome.storage.local.get(keys);
            return {
                settings: result[this.STORAGE_KEYS.SETTINGS],
                statistics: result[this.STORAGE_KEYS.STATISTICS],
                licenseInfo: result[this.STORAGE_KEYS.LICENSE],
                pauseSchedule: result[this.STORAGE_KEYS.PAUSE_SCHEDULE],
                questRules: result[this.STORAGE_KEYS.QUEST_RULES],
                marketRules: result[this.STORAGE_KEYS.MARKET_RULES],
                bankData: result[this.STORAGE_KEYS.BANK_DATA]
            };
        }
        catch (error) {
            console.error('Failed to get all data:', error);
            return {};
        }
    }
    /**
     * Clear all extension data from storage
     */
    async clearAllData() {
        try {
            const keys = Object.values(this.STORAGE_KEYS);
            await chrome.storage.local.remove(keys);
        }
        catch (error) {
            console.error('Failed to clear all data:', error);
            throw error;
        }
    }
    /**
     * Get storage usage information
     */
    async getStorageUsage() {
        try {
            const bytesInUse = await chrome.storage.local.getBytesInUse();
            const quota = chrome.storage.local.QUOTA_BYTES;
            return { bytesInUse, quota };
        }
        catch (error) {
            console.error('Failed to get storage usage:', error);
            return { bytesInUse: 0, quota: 0 };
        }
    }
    /**
     * Deep merge two objects
     */
    deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            }
            else {
                result[key] = source[key];
            }
        }
        return result;
    }
    /**
     * Export settings as JSON string
     */
    async exportSettings() {
        const data = await this.getAllData();
        return JSON.stringify(data, null, 2);
    }
    /**
     * Import settings from JSON string
     */
    async importSettings(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (data.settings) {
                await this.saveSettings(data.settings);
            }
            // Import other data types as needed
            const keys = Object.values(this.STORAGE_KEYS);
            const importData = {};
            for (const key of keys) {
                const dataKey = key.replace('gh_', '');
                if (data[dataKey]) {
                    importData[key] = data[dataKey];
                }
            }
            if (Object.keys(importData).length > 0) {
                await chrome.storage.local.set(importData);
            }
        }
        catch (error) {
            console.error('Failed to import settings:', error);
            throw new Error('Invalid settings data');
        }
    }
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!***************************!*\
  !*** ./src/main/index.ts ***!
  \***************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
/* harmony import */ var _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/gladiator-url-helper */ "./src/utils/gladiator-url-helper.ts");
/* harmony import */ var _utils_storage_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/storage-manager */ "./src/utils/storage-manager.ts");
/* harmony import */ var _bot_engine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bot-engine */ "./src/main/bot-engine.ts");
/* harmony import */ var _login_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./login-manager */ "./src/main/login-manager.ts");
/* harmony import */ var _ui_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui-manager */ "./src/main/ui-manager.ts");
// Main application logic for Gladiatus Helper Bot






class GladiatusHelperBot {
    constructor() {
        this.isInitialized = false;
        this.settings = null;
        this.urlHelper = new _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__.GladiatorUrlHelper();
        this.storageManager = new _utils_storage_manager__WEBPACK_IMPORTED_MODULE_2__.StorageManager();
        this.botEngine = new _bot_engine__WEBPACK_IMPORTED_MODULE_3__.BotEngine(this.storageManager, this.urlHelper);
        this.loginManager = new _login_manager__WEBPACK_IMPORTED_MODULE_4__.LoginManager(this.urlHelper, this.storageManager);
        this.uiManager = new _ui_manager__WEBPACK_IMPORTED_MODULE_5__.UIManager();
        this.botStatus = {
            isActive: false,
            isPaused: false,
            currentAction: _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE,
            statistics: {
                startTime: new Date(),
                totalRunTime: 0,
                actionsPerformed: 0,
                errorsEncountered: 0,
                goldEarned: 0,
                experienceGained: 0,
                itemsFound: 0,
                combatsWon: 0,
                combatsLost: 0
            }
        };
        this.initialize();
    }
    async initialize() {
        try {
            console.log('Gladiatus Helper Bot: Initializing main application');
            // Load settings
            await this.loadSettings();
            // Initialize URL info
            const currentUrl = window.location.href;
            this.urlHelper.resolve(currentUrl);
            // Set up event listeners
            this.setupEventListeners();
            // Check if we're on lobby page
            if (this.urlHelper.isLobby(currentUrl)) {
                await this.handleLobbyPage();
            }
            else if (this.urlHelper.isPlaying(currentUrl)) {
                await this.handleGamePage();
            }
            this.isInitialized = true;
            console.log('Gladiatus Helper Bot: Initialization complete');
        }
        catch (error) {
            console.error('Gladiatus Helper Bot: Initialization failed:', error);
            this.botStatus.error = error.message;
        }
    }
    async loadSettings() {
        this.settings = await this.storageManager.getSettings();
        if (!this.settings) {
            console.log('No settings found, using defaults');
            // Initialize with default settings if none exist
            this.settings = this.getDefaultSettings();
            await this.storageManager.saveSettings(this.settings);
        }
    }
    getDefaultSettings() {
        return {
            general: {
                selectedBackpack: 1,
                maxRandomDelay: 10,
                loginDelay: 5,
                highlightUnderworldItems: true,
                showShopBox: true,
                shopMinQuality: 1,
                showBiddenBox: true,
                autoCollectGodOils: true,
                autoCollectDaily: true
            },
            heal: {
                enabled: false,
                minHealth: 50,
                healCervisia: true,
                healEggs: true,
                buyFoodNeeded: true,
                selectedEggs: [1, 2, 3],
                renewShop: 'not_allow',
                autoStopAttacks: true,
                buyFood: true
            },
            hideGold: {
                primary: {
                    enabled: true,
                    location: 'guild',
                    settings: {}
                },
                backup: {
                    enabled: false,
                    location: 'shop',
                    settings: {}
                },
                minGoldBackup: 1000,
                soulBoundTo: '',
                filterBy: 'd',
                allowMultiple: false,
                recheckDuration: 30,
                pocketMoney: 500,
                hideIn: 'guild',
                duration: '24h',
                skillsToTrain: [],
                minItemPrice: 100,
                minPack: 1,
                maxPack: 10,
                buyFromPlayers: []
            },
            packages: {
                rotateItems: {
                    enabled: true,
                    selectedItems: [],
                    colors: [],
                    underworldItems: false,
                    itemsCooldownDays: 7,
                    collectResourceHourly: true
                },
                sellItems: {
                    enabled: true,
                    rules: [],
                    autoSellOnRenew: false
                },
                pickGold: {
                    enabled: true,
                    goldToPick: 1000
                },
                operationSpeed: 'normal'
            },
            costumes: {
                wearUnderworld: true,
                preventWearUnderworldOnPause: false,
                dontPauseIfUnderworldActive: false,
                underworldCostume: 'dis_pater_normal',
                standardProfile: {
                    enabled: true
                },
                turmaProfile: {
                    enabled: false
                },
                eventProfile: {
                    enabled: false
                }
            }
        };
    }
    setupEventListeners() {
        // Listen for custom events from content script
        window.addEventListener('ghToggleBot', () => {
            this.toggleBot();
        });
        window.addEventListener('ghOpenStatistics', () => {
            this.uiManager.showStatistics(this.botStatus.statistics);
        });
        window.addEventListener('ghSettingsUpdated', (event) => {
            this.updateSettings(event.detail);
        });
        // Listen for page navigation
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        // Listen for storage changes to sync status across tabs
        window.addEventListener('storage', (event) => {
            if (event.key === 'gh_bot_status') {
                this.syncBotStatus();
            }
        });
    }
    async handleLobbyPage() {
        console.log('Gladiatus Helper Bot: Handling lobby page');
        await this.loginManager.handleLobby();
    }
    async handleGamePage() {
        console.log('Gladiatus Helper Bot: Handling game page');
        // Load and restore bot status from storage
        await this.loadBotStatus();
        // Update UI with current status (this will show the persisted state)
        this.uiManager.updateStatus(this.botStatus);
        // Auto-start only if explicitly enabled in settings, not based on previous state
        if (this.settings?.general?.autoStart) {
            console.log('Auto-start enabled, starting bot...');
            await this.startBot();
        }
        else {
            console.log('Bot status loaded, current state:', this.botStatus.isActive ? 'Active' : 'Inactive');
        }
    }
    async loadBotStatus() {
        try {
            const storedStatus = await this.storageManager.getBotStatus();
            if (storedStatus) {
                console.log('Loaded bot status from storage:', storedStatus);
                // Preserve the stored status for UI display but reset active state for safety
                this.botStatus = {
                    ...this.botStatus,
                    ...storedStatus,
                    // Keep stored state for UI display but don't auto-resume
                    wasActive: storedStatus.isActive, // Remember if it was active
                    isActive: false, // Always start inactive for safety
                    isPaused: false, // Reset pause state
                    currentAction: _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE,
                    // Preserve statistics from stored status
                    statistics: {
                        ...this.botStatus.statistics,
                        ...storedStatus.statistics
                    }
                };
                console.log('Bot status after merge:', this.botStatus);
            }
            else {
                console.log('No stored bot status found, using defaults');
            }
        }
        catch (error) {
            console.error('Failed to load bot status:', error);
        }
    }
    async syncBotStatus() {
        try {
            const storedStatus = await this.storageManager.getBotStatus();
            if (storedStatus) {
                this.botStatus = { ...this.botStatus, ...storedStatus };
                this.uiManager.updateStatus(this.botStatus);
            }
        }
        catch (error) {
            console.error('Failed to sync bot status:', error);
        }
    }
    async toggleBot() {
        if (this.botStatus.isActive) {
            await this.stopBot();
        }
        else {
            await this.startBot();
        }
    }
    async startBot() {
        if (!this.isInitialized || !this.settings) {
            console.error('Bot not initialized or settings not loaded');
            return;
        }
        try {
            console.log('Gladiatus Helper Bot: Starting bot');
            this.botStatus.isActive = true;
            this.botStatus.isPaused = false;
            this.botStatus.currentAction = _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
            this.botStatus.statistics.startTime = new Date();
            // Save status
            await this.storageManager.saveBotStatus(this.botStatus);
            console.log('Bot status saved to storage:', this.botStatus);
            // Update UI
            this.uiManager.updateStatus(this.botStatus);
            // Start bot engine
            await this.botEngine.start(this.settings);
            console.log('Gladiatus Helper Bot: Bot started successfully');
        }
        catch (error) {
            console.error('Gladiatus Helper Bot: Failed to start bot:', error);
            this.botStatus.error = error.message;
            this.botStatus.isActive = false;
            this.uiManager.updateStatus(this.botStatus);
        }
    }
    async stopBot() {
        try {
            console.log('Gladiatus Helper Bot: Stopping bot');
            this.botStatus.isActive = false;
            this.botStatus.currentAction = _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
            // Stop bot engine
            await this.botEngine.stop();
            // Save status
            await this.storageManager.saveBotStatus(this.botStatus);
            console.log('Bot status saved to storage:', this.botStatus);
            // Update UI
            this.uiManager.updateStatus(this.botStatus);
            console.log('Gladiatus Helper Bot: Bot stopped successfully');
        }
        catch (error) {
            console.error('Gladiatus Helper Bot: Failed to stop bot:', error);
            this.botStatus.error = error.message;
            this.uiManager.updateStatus(this.botStatus);
        }
    }
    async pauseBot() {
        if (!this.botStatus.isActive)
            return;
        this.botStatus.isPaused = true;
        this.botStatus.currentAction = _types__WEBPACK_IMPORTED_MODULE_0__.BotAction.IDLE;
        await this.botEngine.pause();
        await this.storageManager.saveBotStatus(this.botStatus);
        this.uiManager.updateStatus(this.botStatus);
        console.log('Gladiatus Helper Bot: Bot paused');
    }
    async resumeBot() {
        if (!this.botStatus.isActive || !this.botStatus.isPaused)
            return;
        this.botStatus.isPaused = false;
        await this.botEngine.resume();
        await this.storageManager.saveBotStatus(this.botStatus);
        this.uiManager.updateStatus(this.botStatus);
        console.log('Gladiatus Helper Bot: Bot resumed');
    }
    getBotStatus() {
        return { ...this.botStatus };
    }
    async updateSettings(newSettings) {
        if (!this.settings)
            return;
        // Merge with existing settings
        this.settings = { ...this.settings, ...newSettings };
        // Save to storage
        await this.storageManager.saveSettings(this.settings);
        // Update bot engine if running
        if (this.botStatus.isActive) {
            await this.botEngine.updateSettings(this.settings);
        }
        console.log('Gladiatus Helper Bot: Settings updated');
    }
    cleanup() {
        if (this.botStatus.isActive) {
            this.botEngine.stop();
        }
    }
}
// Initialize the bot when script loads
const gladiatusBot = new GladiatusHelperBot();
// Make bot available globally for debugging
window.gladiatusBot = gladiatusBot;

})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=index.iife.js.map