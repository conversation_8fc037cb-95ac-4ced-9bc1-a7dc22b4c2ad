// Quest Management Module - Handles automatic quest selection and completion

import { QuestManagementSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface Quest {
  id: string;
  name: string;
  description: string;
  reward: string;
  difficulty: 'easy' | 'medium' | 'hard';
  isCompleted: boolean;
  isAvailable: boolean;
  requirements: string[];
}

export interface QuestManagementRule {
  priority: number;
  questType: string;
  minReward: number;
  maxDifficulty: 'easy' | 'medium' | 'hard';
  autoComplete: boolean;
}

export class QuestManagementModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private questsCompleted: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: QuestManagementSettings): Promise<void> {
    if (this.isRunning || !settings.enabled) return;
    
    this.isRunning = true;
    this.questsCompleted = 0;
    
    console.log('Quest Management: Starting quest automation');
    
    try {
      await this.performQuestLoop(settings);
    } catch (error) {
      console.error('Quest Management: Error in quest automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Quest Management: Stopped quest automation');
  }

  private async performQuestLoop(settings: QuestManagementSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueQuests(settings)) {
      try {
        await this.navigateToQuests();
        
        // Get available quests
        const availableQuests = await this.getAvailableQuests();
        
        if (availableQuests.length === 0) {
          console.log('Quest Management: No available quests found');
          await this.delay(30000);
          continue;
        }

        // Select best quest based on rules
        const selectedQuest = this.selectBestQuest(availableQuests, settings.rules);
        
        if (!selectedQuest) {
          console.log('Quest Management: No suitable quest found');
          await this.delay(30000);
          continue;
        }

        // Accept and complete quest
        await this.acceptQuest(selectedQuest);
        
        if (settings.autoComplete) {
          await this.completeQuest(selectedQuest, settings);
        }

        this.questsCompleted++;
        await this.delay(10000);
        
      } catch (error) {
        console.error('Quest Management: Error in quest loop:', error);
        await this.delay(15000);
      }
    }
  }

  private async navigateToQuests(): Promise<void> {
    if (!window.location.href.includes('mod=quest')) {
      console.log('Quest Management: Navigating to quests');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=quest`;
      
      await this.waitForPageLoad();
    }
  }

  private async getAvailableQuests(): Promise<Quest[]> {
    const quests: Quest[] = [];
    const questElements = document.querySelectorAll('.quest_item, .available-quest');
    
    for (let i = 0; i < questElements.length; i++) {
      const element = questElements[i] as HTMLElement;
      
      try {
        const quest = await this.parseQuestElement(element, i);
        if (quest && quest.isAvailable && !quest.isCompleted) {
          quests.push(quest);
        }
      } catch (error) {
        console.error('Quest Management: Error parsing quest:', error);
      }
    }
    
    return quests;
  }

  private async parseQuestElement(element: HTMLElement, index: number): Promise<Quest | null> {
    const nameElement = element.querySelector('.quest_name, .name');
    const descriptionElement = element.querySelector('.quest_description, .description');
    const rewardElement = element.querySelector('.quest_reward, .reward');
    const acceptButton = element.querySelector('.accept_quest, [data-action="accept"]');
    const completedIndicator = element.querySelector('.quest_completed, .completed');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const description = descriptionElement?.textContent?.trim() || '';
    const reward = rewardElement?.textContent?.trim() || '';
    
    // Determine difficulty based on quest name or description
    let difficulty: 'easy' | 'medium' | 'hard' = 'medium';
    const text = (name + ' ' + description).toLowerCase();
    if (text.includes('easy') || text.includes('simple')) {
      difficulty = 'easy';
    } else if (text.includes('hard') || text.includes('difficult') || text.includes('challenging')) {
      difficulty = 'hard';
    }
    
    // Parse requirements
    const requirements: string[] = [];
    const reqElements = element.querySelectorAll('.quest_requirement, .requirement');
    reqElements.forEach(req => {
      const reqText = req.textContent?.trim();
      if (reqText) requirements.push(reqText);
    });
    
    return {
      id: index.toString(),
      name,
      description,
      reward,
      difficulty,
      isCompleted: !!completedIndicator,
      isAvailable: !!(acceptButton && !acceptButton.hasAttribute('disabled')),
      requirements
    };
  }

  private selectBestQuest(quests: Quest[], rules: QuestManagementRule[]): Quest | null {
    if (quests.length === 0) return null;
    
    // Sort rules by priority (highest first)
    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);
    
    for (const rule of sortedRules) {
      const matchingQuests = quests.filter(quest => this.questMatchesRule(quest, rule));
      
      if (matchingQuests.length > 0) {
        // Return the first matching quest for the highest priority rule
        return matchingQuests[0];
      }
    }
    
    // If no rules match, return the first available quest
    return quests[0];
  }

  private questMatchesRule(quest: Quest, rule: QuestManagementRule): boolean {
    // Check quest type
    if (rule.questType && !quest.name.toLowerCase().includes(rule.questType.toLowerCase())) {
      return false;
    }
    
    // Check difficulty
    const difficultyOrder = { easy: 1, medium: 2, hard: 3 };
    if (difficultyOrder[quest.difficulty] > difficultyOrder[rule.maxDifficulty]) {
      return false;
    }
    
    // Check minimum reward (simplified - would need better reward parsing)
    const rewardValue = this.parseRewardValue(quest.reward);
    if (rewardValue < rule.minReward) {
      return false;
    }
    
    return true;
  }

  private parseRewardValue(reward: string): number {
    // Simple reward parsing - extract numbers from reward string
    const goldMatch = reward.match(/(\d+)\s*gold/i);
    const xpMatch = reward.match(/(\d+)\s*xp/i);
    
    let value = 0;
    if (goldMatch) value += parseInt(goldMatch[1]);
    if (xpMatch) value += parseInt(xpMatch[1]) * 0.1; // XP worth less than gold
    
    return value;
  }

  private async acceptQuest(quest: Quest): Promise<void> {
    console.log(`Quest Management: Accepting quest "${quest.name}"`);
    
    const questElement = document.querySelector(`.quest_item:nth-child(${parseInt(quest.id) + 1})`);
    if (!questElement) return;
    
    const acceptButton = questElement.querySelector('.accept_quest, [data-action="accept"]');
    if (acceptButton && !acceptButton.hasAttribute('disabled')) {
      (acceptButton as HTMLElement).click();
      await this.delay(2000);
    }
  }

  private async completeQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {
    console.log(`Quest Management: Attempting to complete quest "${quest.name}"`);
    
    // This would contain logic to actually complete the quest
    // For example, if it's a combat quest, go fight monsters
    // If it's a collection quest, collect items, etc.
    
    if (quest.name.toLowerCase().includes('combat') || quest.name.toLowerCase().includes('fight')) {
      await this.completeCombatQuest(quest, settings);
    } else if (quest.name.toLowerCase().includes('collect') || quest.name.toLowerCase().includes('gather')) {
      await this.completeCollectionQuest(quest, settings);
    } else if (quest.name.toLowerCase().includes('travel') || quest.name.toLowerCase().includes('visit')) {
      await this.completeTravelQuest(quest, settings);
    }
    
    // Check if quest is completed and claim reward
    await this.claimQuestReward(quest);
  }

  private async completeCombatQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {
    // Navigate to appropriate combat area and fight required enemies
    console.log(`Quest Management: Completing combat quest "${quest.name}"`);
    
    // This would integrate with arena/turma combat modules
    // For now, just simulate completion
    await this.delay(5000);
  }

  private async completeCollectionQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {
    // Navigate to appropriate location and collect required items
    console.log(`Quest Management: Completing collection quest "${quest.name}"`);
    
    // This would integrate with expedition/dungeon modules
    await this.delay(5000);
  }

  private async completeTravelQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {
    // Navigate to required locations
    console.log(`Quest Management: Completing travel quest "${quest.name}"`);
    
    // Parse quest requirements to find locations to visit
    for (const requirement of quest.requirements) {
      if (requirement.toLowerCase().includes('visit') || requirement.toLowerCase().includes('go to')) {
        // Extract location and navigate there
        await this.delay(2000);
      }
    }
  }

  private async claimQuestReward(quest: Quest): Promise<void> {
    // Look for quest completion and claim reward
    const claimButton = document.querySelector('.claim_reward, [data-action="claim"]');
    if (claimButton && !claimButton.hasAttribute('disabled')) {
      console.log(`Quest Management: Claiming reward for quest "${quest.name}"`);
      (claimButton as HTMLElement).click();
      await this.delay(2000);
    }
  }

  private shouldContinueQuests(settings: QuestManagementSettings): boolean {
    // Check daily quest limit
    if (settings.dailyLimit > 0 && this.questsCompleted >= settings.dailyLimit) {
      console.log('Quest Management: Daily quest limit reached');
      return false;
    }
    
    return true;
  }

  async getQuestStatus(): Promise<any> {
    return {
      isRunning: this.isRunning,
      questsCompleted: this.questsCompleted,
      lastUpdate: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
