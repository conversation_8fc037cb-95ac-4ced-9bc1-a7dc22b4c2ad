import { ItemQuality } from './gladiatus';
import { QuestRule, MarketRule } from './api';
import { FilterCondition } from './settings';
export interface BotStatus {
    isActive: boolean;
    isPaused: boolean;
    wasActive?: boolean;
    currentAction: BotAction;
    nextActionTime?: Date;
    error?: string;
    statistics: GameBotStatistics;
}
export declare enum BotAction {
    IDLE = "idle",
    HEALING = "healing",
    ATTACKING = "attacking",
    TRAVELING = "traveling",
    BUYING_PACK = "buying_pack",
    PICKING_ITEMS = "picking_items",
    UNDERWORLD = "underworld",
    EVENT = "event",
    SELLING_PACK = "selling_pack",
    QUEST_MANAGEMENT = "quest_management",
    MARKET_OPERATIONS = "market_operations",
    FORGE_REPAIR = "forge_repair",
    ROTATING_ITEMS = "rotating_items",
    CHECKING_QUESTS = "checking_quests",
    FORGING = "forging",
    SMELTING = "smelting",
    REPAIRING = "repairing",
    CHECKING_MARKET = "checking_market",
    CHECKING_AUCTION = "checking_auction",
    ACTIVATING_PACTS = "activating_pacts",
    PROCESSING_PACK = "processing_pack",
    REQUESTING_PACK = "requesting_pack"
}
export interface GameBotStatistics {
    startTime: Date;
    totalRunTime: number;
    actionsPerformed: number;
    errorsEncountered: number;
    goldEarned: number;
    experienceGained: number;
    itemsFound: number;
    combatsWon: number;
    combatsLost: number;
}
export interface WorkSettings {
    enabled: boolean;
    workIfNoPoints: boolean;
    selectedWork: WorkType;
    duration: number;
}
export declare enum WorkType {
    SENATOR = "senator",
    JEWELLER = "jeweller",
    STABLE_BOY = "stable_boy",
    FARMER = "farmer",
    BUTCHER = "butcher",
    FISHERMAN = "fisherman",
    BAKER = "baker",
    BLACKSMITH = "blacksmith",
    MASTER_BLACKSMITH = "master_blacksmith"
}
export interface ForgeSettings {
    enabled: boolean;
    acceptedColor: ItemQuality[];
    prefix: string;
    item: string;
    suffix: string;
    resourceQuality: ItemQuality;
    stopSuccess: number;
    toolsQuality: ToolQuality;
    stopResources: boolean;
    stopTools: boolean;
    slots: number;
    sendTo: ForgeDestination;
    useHammer: boolean;
}
export declare enum ToolQuality {
    BRONZE = "bronze",
    SILVER = "silver",
    GOLD = "gold"
}
export declare enum ForgeDestination {
    NONE = "none",
    INVENTORY = "inventory",
    PACKAGES = "packages"
}
export interface RepairSettings {
    standardProfile: RepairProfile;
    turmaProfile: RepairProfile;
}
export interface RepairProfile {
    enabled: boolean;
    minConditioning: number;
    maxQuality: ItemQuality;
    ignoreMaterials: boolean;
}
export interface QuestSettings {
    enabled: boolean;
    rules: QuestRule[];
    underworldRules: QuestRule[];
}
export interface MarketSettings {
    enabled: boolean;
    sellConfig: MarketSellConfig;
    buyConfig: MarketBuyConfig;
}
export interface MarketSellConfig {
    enabled: boolean;
    stackSize: number;
    pricePerUnit: number;
    maxListedItems: number;
    goldRatio: number;
    pickVAT: boolean;
    forgingGoods: string[];
}
export interface MarketBuyConfig {
    enabled: boolean;
    rules: MarketRule[];
    autoBuy: boolean;
}
export interface ShopSettings {
    enabled: boolean;
    searchRules: ShopRule[];
    maxSearches: number;
    usedSearches: number;
    allowRuby: boolean;
    autoStart: boolean;
}
export interface ShopRule {
    id: string;
    conditions: FilterCondition[];
    autoBuy: boolean;
    enabled: boolean;
}
export interface BankSettings {
    enabled: boolean;
    isBank: boolean;
    bankId?: string;
    selectedBackpack: number;
    clientSettings: BankClientSettings;
}
export interface BankClientSettings {
    requestThreshold: number;
    packGoldAmount: number;
    duration: string;
}
export interface GuildSettings {
    enabled: boolean;
    attackList: string[];
    ignoreList: string[];
}
//# sourceMappingURL=game.d.ts.map