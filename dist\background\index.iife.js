(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* binding */ BankRequestState),
/* harmony export */   LicenseType: () => (/* binding */ LicenseType)
/* harmony export */ });
// API and network request types
var LicenseType;
(function (LicenseType) {
    LicenseType["FREE_TRIAL"] = "free_trial";
    LicenseType["ESSENTIAL"] = "essential";
    LicenseType["PREMIUM"] = "premium";
    LicenseType["INACTIVE"] = "inactive";
})(LicenseType || (LicenseType = {}));
var BankRequestState;
(function (BankRequestState) {
    BankRequestState["PENDING"] = "pending";
    BankRequestState["PROGRESS"] = "progress";
    BankRequestState["READY"] = "ready";
    BankRequestState["COMPLETE"] = "complete";
})(BankRequestState || (BankRequestState = {}));


/***/ }),

/***/ "./src/types/extension.ts":
/*!********************************!*\
  !*** ./src/types/extension.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideGoldLocation: () => (/* binding */ HideGoldLocation),
/* harmony export */   MessageType: () => (/* binding */ MessageType),
/* harmony export */   PackDuration: () => (/* binding */ PackDuration),
/* harmony export */   PackFilterType: () => (/* binding */ PackFilterType),
/* harmony export */   RenewShopOption: () => (/* binding */ RenewShopOption)
/* harmony export */ });
// Chrome extension specific type definitions
var MessageType;
(function (MessageType) {
    MessageType["LOGIN_REQUEST"] = "LOGIN_REQUEST";
    MessageType["LOGIN_RESPONSE"] = "LOGIN_RESPONSE";
    MessageType["LOGIN_FAILED"] = "LOGIN_FAILED";
    MessageType["SCRIPT_STATUS"] = "SCRIPT_STATUS";
    MessageType["SETTINGS_UPDATE"] = "SETTINGS_UPDATE";
    MessageType["NOTIFICATION"] = "NOTIFICATION";
    MessageType["ERROR"] = "ERROR";
})(MessageType || (MessageType = {}));
var RenewShopOption;
(function (RenewShopOption) {
    RenewShopOption["NOT_ALLOW"] = "not_allow";
    RenewShopOption["CLOTHE_ONLY"] = "clothe_only";
    RenewShopOption["RUBY_OR_CLOTHE"] = "ruby_or_clothe";
})(RenewShopOption || (RenewShopOption = {}));
var HideGoldLocation;
(function (HideGoldLocation) {
    HideGoldLocation["GUILD"] = "guild";
    HideGoldLocation["SHOP"] = "shop";
    HideGoldLocation["AUCTION"] = "auction";
    HideGoldLocation["TRAINING"] = "training";
    HideGoldLocation["MARKET"] = "market";
    HideGoldLocation["DONATE"] = "donate";
})(HideGoldLocation || (HideGoldLocation = {}));
var PackDuration;
(function (PackDuration) {
    PackDuration["TWO_HOURS"] = "2h";
    PackDuration["EIGHT_HOURS"] = "8h";
    PackDuration["TWENTY_FOUR_HOURS"] = "24h";
    PackDuration["FORTY_EIGHT_HOURS"] = "48h";
})(PackDuration || (PackDuration = {}));
var PackFilterType;
(function (PackFilterType) {
    PackFilterType["LOWEST_DURATION"] = "d";
    PackFilterType["HIGHEST_DURATION"] = "dd";
    PackFilterType["LOWEST_PRICE"] = "p";
    PackFilterType["HIGHEST_PRICE"] = "pd";
})(PackFilterType || (PackFilterType = {}));


/***/ }),

/***/ "./src/types/game.ts":
/*!***************************!*\
  !*** ./src/types/game.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BotAction: () => (/* binding */ BotAction),
/* harmony export */   ForgeDestination: () => (/* binding */ ForgeDestination),
/* harmony export */   ToolQuality: () => (/* binding */ ToolQuality),
/* harmony export */   WorkType: () => (/* binding */ WorkType)
/* harmony export */ });
// Game mechanics and bot logic types
var BotAction;
(function (BotAction) {
    BotAction["IDLE"] = "idle";
    BotAction["HEALING"] = "healing";
    BotAction["ATTACKING"] = "attacking";
    BotAction["TRAVELING"] = "traveling";
    BotAction["BUYING_PACK"] = "buying_pack";
    BotAction["PICKING_ITEMS"] = "picking_items";
    BotAction["UNDERWORLD"] = "underworld";
    BotAction["EVENT"] = "event";
    BotAction["SELLING_PACK"] = "selling_pack";
    BotAction["QUEST_MANAGEMENT"] = "quest_management";
    BotAction["MARKET_OPERATIONS"] = "market_operations";
    BotAction["FORGE_REPAIR"] = "forge_repair";
    BotAction["ROTATING_ITEMS"] = "rotating_items";
    BotAction["CHECKING_QUESTS"] = "checking_quests";
    BotAction["FORGING"] = "forging";
    BotAction["SMELTING"] = "smelting";
    BotAction["REPAIRING"] = "repairing";
    BotAction["CHECKING_MARKET"] = "checking_market";
    BotAction["CHECKING_AUCTION"] = "checking_auction";
    BotAction["ACTIVATING_PACTS"] = "activating_pacts";
    BotAction["PROCESSING_PACK"] = "processing_pack";
    BotAction["REQUESTING_PACK"] = "requesting_pack";
})(BotAction || (BotAction = {}));
var WorkType;
(function (WorkType) {
    WorkType["SENATOR"] = "senator";
    WorkType["JEWELLER"] = "jeweller";
    WorkType["STABLE_BOY"] = "stable_boy";
    WorkType["FARMER"] = "farmer";
    WorkType["BUTCHER"] = "butcher";
    WorkType["FISHERMAN"] = "fisherman";
    WorkType["BAKER"] = "baker";
    WorkType["BLACKSMITH"] = "blacksmith";
    WorkType["MASTER_BLACKSMITH"] = "master_blacksmith";
})(WorkType || (WorkType = {}));
var ToolQuality;
(function (ToolQuality) {
    ToolQuality["BRONZE"] = "bronze";
    ToolQuality["SILVER"] = "silver";
    ToolQuality["GOLD"] = "gold";
})(ToolQuality || (ToolQuality = {}));
var ForgeDestination;
(function (ForgeDestination) {
    ForgeDestination["NONE"] = "none";
    ForgeDestination["INVENTORY"] = "inventory";
    ForgeDestination["PACKAGES"] = "packages";
})(ForgeDestination || (ForgeDestination = {}));


/***/ }),

/***/ "./src/types/gladiatus.ts":
/*!********************************!*\
  !*** ./src/types/gladiatus.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GodType: () => (/* binding */ GodType),
/* harmony export */   ItemQuality: () => (/* binding */ ItemQuality),
/* harmony export */   ItemType: () => (/* binding */ ItemType),
/* harmony export */   LocationType: () => (/* binding */ LocationType),
/* harmony export */   QuestType: () => (/* binding */ QuestType)
/* harmony export */ });
// Gladiatus game-specific type definitions
var ItemQuality;
(function (ItemQuality) {
    ItemQuality[ItemQuality["WHITE"] = 0] = "WHITE";
    ItemQuality[ItemQuality["GREEN"] = 1] = "GREEN";
    ItemQuality[ItemQuality["BLUE"] = 2] = "BLUE";
    ItemQuality[ItemQuality["PURPLE"] = 3] = "PURPLE";
    ItemQuality[ItemQuality["ORANGE"] = 4] = "ORANGE";
    ItemQuality[ItemQuality["RED"] = 5] = "RED";
})(ItemQuality || (ItemQuality = {}));
var ItemType;
(function (ItemType) {
    ItemType["WEAPON"] = "weapon";
    ItemType["SHIELD"] = "shield";
    ItemType["HELMET"] = "helmet";
    ItemType["CHEST"] = "chest";
    ItemType["GLOVES"] = "gloves";
    ItemType["SHOES"] = "shoes";
    ItemType["RING"] = "ring";
    ItemType["AMULET"] = "amulet";
    ItemType["USABLE"] = "usable";
    ItemType["REINFORCEMENT"] = "reinforcement";
    ItemType["UPGRADE"] = "upgrade";
    ItemType["RECIPE"] = "recipe";
    ItemType["MERCENARY"] = "mercenary";
    ItemType["FORGING_GOODS"] = "forging_goods";
    ItemType["TOOLS"] = "tools";
    ItemType["SCROLL"] = "scroll";
    ItemType["EVENT_ITEMS"] = "event_items";
})(ItemType || (ItemType = {}));
var QuestType;
(function (QuestType) {
    QuestType["ARENA"] = "arena";
    QuestType["GROUP_ARENA"] = "group_arena";
    QuestType["EXPEDITION"] = "expedition";
    QuestType["DUNGEON"] = "dungeon";
    QuestType["WORK"] = "work";
    QuestType["FIND_ITEM"] = "find_item";
    QuestType["COMBAT"] = "combat";
    QuestType["ANY_QUEST"] = "any_quest";
})(QuestType || (QuestType = {}));
var GodType;
(function (GodType) {
    GodType["APOLLO"] = "apollo";
    GodType["VULCAN"] = "vulcan";
    GodType["MARS"] = "mars";
    GodType["MERCURY"] = "mercury";
    GodType["DIANA"] = "diana";
    GodType["MINERVA"] = "minerva";
})(GodType || (GodType = {}));
var LocationType;
(function (LocationType) {
    LocationType["EXPEDITION"] = "expedition";
    LocationType["DUNGEON"] = "dungeon";
    LocationType["ARENA"] = "arena";
    LocationType["TURMA"] = "turma";
    LocationType["UNDERWORLD"] = "underworld";
    LocationType["MARKET"] = "market";
    LocationType["GUILD"] = "guild";
})(LocationType || (LocationType = {}));


/***/ }),

/***/ "./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.BankRequestState),
/* harmony export */   BotAction: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.BotAction),
/* harmony export */   ButtonStyle: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ButtonStyle),
/* harmony export */   CooldownRemovalOption: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.CooldownRemovalOption),
/* harmony export */   FieldType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.FieldType),
/* harmony export */   FilterType: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.FilterType),
/* harmony export */   ForgeDestination: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ForgeDestination),
/* harmony export */   GodType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.GodType),
/* harmony export */   HideGoldLocation: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.HideGoldLocation),
/* harmony export */   ItemQuality: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemQuality),
/* harmony export */   ItemType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemType),
/* harmony export */   LicenseType: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.LicenseType),
/* harmony export */   LocationType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.LocationType),
/* harmony export */   MessageType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.MessageType),
/* harmony export */   ModalSize: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ModalSize),
/* harmony export */   NotificationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.NotificationType),
/* harmony export */   OperationSpeed: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.OperationSpeed),
/* harmony export */   PackDuration: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackDuration),
/* harmony export */   PackFilterType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackFilterType),
/* harmony export */   QuestType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.QuestType),
/* harmony export */   RenewShopOption: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.RenewShopOption),
/* harmony export */   ToolQuality: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ToolQuality),
/* harmony export */   UnderworldCostume: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldMode),
/* harmony export */   ValidationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ValidationType),
/* harmony export */   WorkType: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.WorkType)
/* harmony export */ });
/* harmony import */ var _gladiatus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gladiatus */ "./src/types/gladiatus.ts");
/* harmony import */ var _extension__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extension */ "./src/types/extension.ts");
/* harmony import */ var _localization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./localization */ "./src/types/localization.ts");
/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./settings */ "./src/types/settings.ts");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api */ "./src/types/api.ts");
/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ "./src/types/ui.ts");
/* harmony import */ var _game__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./game */ "./src/types/game.ts");
// Main type definitions for Gladiatus Helper Bot









/***/ }),

/***/ "./src/types/localization.ts":
/*!***********************************!*\
  !*** ./src/types/localization.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// Localization and internationalization types



/***/ }),

/***/ "./src/types/settings.ts":
/*!*******************************!*\
  !*** ./src/types/settings.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CooldownRemovalOption: () => (/* binding */ CooldownRemovalOption),
/* harmony export */   FilterType: () => (/* binding */ FilterType),
/* harmony export */   OperationSpeed: () => (/* binding */ OperationSpeed),
/* harmony export */   UnderworldCostume: () => (/* binding */ UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* binding */ UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* binding */ UnderworldMode)
/* harmony export */ });
// Settings and configuration type definitions
var FilterType;
(function (FilterType) {
    FilterType["CONTAINS"] = "contains";
    FilterType["NOT_CONTAINS"] = "not_contains";
    FilterType["CONTAINS_ANY"] = "contains_any";
    FilterType["NOT_CONTAINS_ANY"] = "not_contains_any";
    FilterType["CONTAINS_WORD"] = "contains_word";
    FilterType["IS_UNDERWORLD"] = "is_underworld";
    FilterType["IS_NOT_UNDERWORLD"] = "is_not_underworld";
    FilterType["GREATER_THAN"] = "greater_than";
    FilterType["LESS_THAN"] = "less_than";
    FilterType["STARTS_WITH"] = "starts_with";
    FilterType["ENDS_WITH"] = "ends_with";
    FilterType["CONTAINS_RESOURCE"] = "contains_resource";
})(FilterType || (FilterType = {}));
var OperationSpeed;
(function (OperationSpeed) {
    OperationSpeed["VERY_FAST"] = "very_fast";
    OperationSpeed["FAST"] = "fast";
    OperationSpeed["NORMAL"] = "normal";
    OperationSpeed["SLOW"] = "slow";
})(OperationSpeed || (OperationSpeed = {}));
var UnderworldCostume;
(function (UnderworldCostume) {
    UnderworldCostume["DIS_PATER_NORMAL"] = "dis_pater_normal";
    UnderworldCostume["DIS_PATER_MEDIUM"] = "dis_pater_medium";
    UnderworldCostume["DIS_PATER_HARD"] = "dis_pater_hard";
    UnderworldCostume["VULCANUS_FORGE"] = "vulcanus_forge";
    UnderworldCostume["FERONIAS_EARTHEN_SHIELD"] = "feronias_earthen_shield";
    UnderworldCostume["NEPTUNES_FLUID_MIGHT"] = "neptunes_fluid_might";
    UnderworldCostume["AELOUS_AERIAL_FREEDOM"] = "aelous_aerial_freedom";
    UnderworldCostume["PLUTOS_DEADLY_MIST"] = "plutos_deadly_mist";
    UnderworldCostume["JUNOS_BREATH_OF_LIFE"] = "junos_breath_of_life";
    UnderworldCostume["WRATH_MOUNTAIN_SCALE_ARMOUR"] = "wrath_mountain_scale_armour";
    UnderworldCostume["EAGLE_EYES"] = "eagle_eyes";
    UnderworldCostume["SATURNS_WINTER_GARMENT"] = "saturns_winter_garment";
    UnderworldCostume["BUBONA_BULL_ARMOUR"] = "bubona_bull_armour";
    UnderworldCostume["MERCERIUS_ROBBERS_GARMENTS"] = "mercerius_robbers_garments";
    UnderworldCostume["RA_LIGHT_ROBE"] = "ra_light_robe";
})(UnderworldCostume || (UnderworldCostume = {}));
var CooldownRemovalOption;
(function (CooldownRemovalOption) {
    CooldownRemovalOption["HOURGLASS"] = "hourglass";
    CooldownRemovalOption["HOURGLASS_OR_RUBY"] = "hourglass_or_ruby";
})(CooldownRemovalOption || (CooldownRemovalOption = {}));
var UnderworldMode;
(function (UnderworldMode) {
    UnderworldMode["FARM_QUEST"] = "farm_quest";
    UnderworldMode["FARM_MOB"] = "farm_mob";
    UnderworldMode["NORMAL"] = "normal";
})(UnderworldMode || (UnderworldMode = {}));
var UnderworldDifficulty;
(function (UnderworldDifficulty) {
    UnderworldDifficulty["NORMAL"] = "normal";
    UnderworldDifficulty["MEDIUM"] = "medium";
    UnderworldDifficulty["HARD"] = "hard";
})(UnderworldDifficulty || (UnderworldDifficulty = {}));


/***/ }),

/***/ "./src/types/ui.ts":
/*!*************************!*\
  !*** ./src/types/ui.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ButtonStyle: () => (/* binding */ ButtonStyle),
/* harmony export */   FieldType: () => (/* binding */ FieldType),
/* harmony export */   ModalSize: () => (/* binding */ ModalSize),
/* harmony export */   NotificationType: () => (/* binding */ NotificationType),
/* harmony export */   ValidationType: () => (/* binding */ ValidationType)
/* harmony export */ });
// UI and interface types
var NotificationType;
(function (NotificationType) {
    NotificationType["INFO"] = "info";
    NotificationType["SUCCESS"] = "success";
    NotificationType["WARNING"] = "warning";
    NotificationType["ERROR"] = "error";
    NotificationType["GOLD_EXPIRY"] = "gold_expiry";
    NotificationType["ITEM_FOUND"] = "item_found";
    NotificationType["ITEM_BOUGHT"] = "item_bought";
})(NotificationType || (NotificationType = {}));
var ModalSize;
(function (ModalSize) {
    ModalSize["SMALL"] = "small";
    ModalSize["MEDIUM"] = "medium";
    ModalSize["LARGE"] = "large";
    ModalSize["EXTRA_LARGE"] = "extra_large";
})(ModalSize || (ModalSize = {}));
var ButtonStyle;
(function (ButtonStyle) {
    ButtonStyle["PRIMARY"] = "primary";
    ButtonStyle["SECONDARY"] = "secondary";
    ButtonStyle["SUCCESS"] = "success";
    ButtonStyle["DANGER"] = "danger";
    ButtonStyle["WARNING"] = "warning";
    ButtonStyle["INFO"] = "info";
})(ButtonStyle || (ButtonStyle = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["NUMBER"] = "number";
    FieldType["SELECT"] = "select";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["RADIO"] = "radio";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["RANGE"] = "range";
    FieldType["COLOR"] = "color";
    FieldType["DATE"] = "date";
    FieldType["TIME"] = "time";
})(FieldType || (FieldType = {}));
var ValidationType;
(function (ValidationType) {
    ValidationType["REQUIRED"] = "required";
    ValidationType["MIN"] = "min";
    ValidationType["MAX"] = "max";
    ValidationType["MIN_LENGTH"] = "min_length";
    ValidationType["MAX_LENGTH"] = "max_length";
    ValidationType["PATTERN"] = "pattern";
    ValidationType["EMAIL"] = "email";
    ValidationType["URL"] = "url";
})(ValidationType || (ValidationType = {}));


/***/ }),

/***/ "./src/utils/default-settings.ts":
/*!***************************************!*\
  !*** ./src/utils/default-settings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DefaultSettingsFactory: () => (/* binding */ DefaultSettingsFactory)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
// Default Settings Factory - Provides default values for all bot settings

class DefaultSettingsFactory {
    static createDefaultSettings() {
        return {
            general: {
                selectedBackpack: 1,
                maxRandomDelay: 10,
                loginDelay: 5,
                autoCollectDaily: true,
                autoCollectGodOils: true,
                showBiddenBox: true,
                showShopBox: true,
                highlightUnderworldItems: true,
                shopMinQuality: 1
            },
            heal: {
                enabled: false,
                minHealth: 80,
                healCervisia: true,
                healEggs: true,
                selectedEggs: [1, 2, 3],
                buyFood: false,
                buyFoodNeeded: false,
                renewShop: _types__WEBPACK_IMPORTED_MODULE_0__.RenewShopOption.NOT_ALLOW,
                autoStopAttacks: true
            },
            hideGold: {
                primary: {
                    enabled: false,
                    location: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.GUILD,
                    settings: {}
                },
                backup: {
                    enabled: false,
                    location: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.SHOP,
                    settings: {}
                },
                minGoldBackup: 500,
                soulBoundTo: '',
                filterBy: _types__WEBPACK_IMPORTED_MODULE_0__.PackFilterType.LOWEST_PRICE,
                allowMultiple: false,
                recheckDuration: 3600,
                pocketMoney: 1000,
                hideIn: _types__WEBPACK_IMPORTED_MODULE_0__.HideGoldLocation.GUILD,
                duration: _types__WEBPACK_IMPORTED_MODULE_0__.PackDuration.TWO_HOURS,
                skillsToTrain: [],
                minItemPrice: 100,
                minPack: 1,
                maxPack: 10,
                buyFromPlayers: []
            },
            packages: {
                rotateItems: {
                    enabled: false,
                    selectedItems: [],
                    colors: [],
                    underworldItems: false,
                    itemsCooldownDays: 7,
                    collectResourceHourly: false
                },
                sellItems: {
                    enabled: false,
                    rules: [],
                    autoSellOnRenew: false
                },
                pickGold: {
                    enabled: false,
                    goldToPick: 0
                },
                operationSpeed: _types__WEBPACK_IMPORTED_MODULE_0__.OperationSpeed.NORMAL
            },
            costumes: {
                wearUnderworld: false,
                preventWearUnderworldOnPause: false,
                dontPauseIfUnderworldActive: false,
                underworldCostume: 'dis_pater_normal',
                standardProfile: {
                    enabled: false
                },
                turmaProfile: {
                    enabled: false
                },
                eventProfile: {
                    enabled: false
                }
            },
            turma: {
                enabled: false,
                levelRestriction: 0,
                autoStop: 0,
                attackIfQuest: false,
                runUntilGetChest: false,
                goldRaided: 0,
                attackPlayers: [],
                autoIgnorePlayers: [],
                ignorePlayers: [],
                attackSameServer: true
            },
            arena: {
                enabled: false,
                allowSim: true,
                prioritizeChance: 80,
                skipLow: false,
                loseLimit: 3,
                attackTargetPlayers: false,
                allowMaxAttacks: false,
                autoIgnoreGuildPlayers: true,
                attackScore: false,
                selectScorePage: 1,
                progressLeague: false,
                stopAfterLimit: false,
                attackGoldWhale: false,
                doRevenge: false,
                totalWin: false,
                limitAttacks: 0,
                dailyAttacks: 0,
                autoStop: 0,
                attackIfQuest: false,
                attackIfCombatQuest: false,
                goldRaided: 0,
                attackPlayers: [],
                autoIgnorePlayers: [],
                ignorePlayers: [],
                attackSameServer: true,
                attackScoreSingle: false
            },
            expeditions: {
                enabled: false,
                minDungeonPoints: 0,
                location: '',
                mob: '',
                autoCollectBonuses: true,
                travelForDungeon: false,
                removeCooldown: _types__WEBPACK_IMPORTED_MODULE_0__.CooldownRemovalOption.HOURGLASS,
                cooldownLimit: 10,
                cooldownUsed: 0
            },
            dungeon: {
                enabled: false,
                location: '',
                useGateKey: false,
                isAdvanced: false,
                skipBoss: false,
                bossName: '',
                restartAfterFail: 0
            },
            event: {
                enabled: false,
                mob: '',
                autoCollectBonuses: true,
                autoRenewEvent: false,
                followLeader: '',
                autoStop: false
            },
            underworld: {
                enabled: false,
                noWeapon: false,
                sendMail: false,
                mailContent: '',
                maxMedics: 5,
                usedMedics: 0,
                maxRuby: 2,
                usedRuby: 0,
                buffs: true,
                costumes: true,
                reinforcements: [],
                mode: _types__WEBPACK_IMPORTED_MODULE_0__.UnderworldMode.NORMAL,
                difficulty: _types__WEBPACK_IMPORTED_MODULE_0__.UnderworldDifficulty.NORMAL,
                healGuild: true,
                healPotions: true,
                healSacrifice: false,
                allowMobilisation: false,
                allowRuby: false,
                attackDisPaterAsap: true,
                stopArenaInUnder: true,
                healCostumes: true,
                allowReinforcements: false,
                allowUpgrades: false,
                autoBuffGods: true,
                autoBuffOils: true,
                exitUnder: false,
                stayHours: 24
            },
            forge: {
                enabled: false,
                autoForge: false,
                autoSmelt: false,
                maxGoldSpend: 10000,
                maxCostPerItem: 1000,
                dailyForgeLimit: 10,
                qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.GREEN],
                minLevel: 1,
                maxLevel: 50,
                itemTypes: [],
                minSmeltValue: 10,
                smeltQualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.WHITE]
            },
            repair: {
                enabled: false,
                repairThreshold: 50,
                maxCostPerItem: 500,
                maxGoldSpend: 5000,
                itemFilter: []
            },
            quest: {
                enabled: false,
                autoComplete: false,
                dailyLimit: 10,
                rules: []
            },
            market: {
                enabled: false,
                checkInterval: 300,
                dailyBuyLimit: 20,
                autoBuy: {
                    enabled: false,
                    maxPrice: 10000,
                    maxGoldSpend: 50000,
                    qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.BLUE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE],
                    minLevel: 1,
                    maxLevel: 100,
                    itemNames: [],
                    blacklistedSellers: []
                },
                autoSell: {
                    enabled: false,
                    rules: []
                },
                auction: {
                    enabled: false,
                    autoBid: false,
                    autoBuyout: false,
                    maxBid: 10000,
                    maxBuyout: 20000,
                    bidIncrement: 100,
                    minTimeRemaining: 300,
                    qualityFilter: [_types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.PURPLE, _types__WEBPACK_IMPORTED_MODULE_0__.ItemQuality.ORANGE]
                }
            },
            shop: {
                enabled: false,
                searchRules: [],
                maxSearches: 10,
                usedSearches: 0,
                allowRuby: false,
                autoStart: false
            },
            bank: {
                enabled: false,
                isBank: false,
                selectedBackpack: 1,
                clientSettings: {
                    requestThreshold: 1000,
                    packGoldAmount: 10000,
                    duration: '2h'
                }
            },
            guild: {
                enabled: false,
                attackList: [],
                ignoreList: []
            }
        };
    }
    static mergeWithDefaults(userSettings) {
        const defaultSettings = this.createDefaultSettings();
        return this.deepMerge(defaultSettings, userSettings);
    }
    static deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            }
            else {
                result[key] = source[key];
            }
        }
        return result;
    }
}


/***/ }),

/***/ "./src/utils/gladiator-url-helper.ts":
/*!*******************************************!*\
  !*** ./src/utils/gladiator-url-helper.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GladiatorUrlHelper: () => (/* binding */ GladiatorUrlHelper)
/* harmony export */ });
// Gladiatus URL parsing and validation utilities
class GladiatorUrlHelper {
    constructor() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
    /**
     * Check if URL is a Gladiatus domain
     */
    isGladiatus(url) {
        return /gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is a Gladiatus game page (not lobby)
     */
    isPlaying(url) {
        return /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is the Gladiatus lobby
     */
    isLobby(url) {
        return /lobby\.gladiatus\.gameforge\.com/.test(url) && url.indexOf('loading') < 0;
    }
    /**
     * Parse query string into object
     */
    resolveQueries(queryString) {
        const queries = {};
        const params = queryString.split('&');
        for (let i = params.length - 1; i >= 0; i--) {
            const [key, value] = params[i].split('=');
            if (key && value) {
                queries[key] = decodeURIComponent(value);
            }
        }
        return queries;
    }
    /**
     * Resolve URL information from Gladiatus game URL
     */
    resolve(url, force = false) {
        if (this.resolved && !force) {
            return this.urlInfo;
        }
        const match = url.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main)\.php(?:\?(.*))?/i);
        if (!match) {
            return null;
        }
        this.urlInfo = {
            server: match[1],
            country: match[2],
            domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,
            queries: this.resolveQueries(match[3] || ''),
            resolved: true
        };
        this.resolved = true;
        return this.urlInfo;
    }
    /**
     * Build Gladiatus URL with parameters
     */
    buildUrl(params, page = 'index.php') {
        const queryParts = [];
        for (const key in params) {
            queryParts.push(`${key}=${encodeURIComponent(params[key])}`);
        }
        // Add security hash if available
        if (this.urlInfo.queries.sh) {
            queryParts.push(`sh=${this.urlInfo.queries.sh}`);
        }
        return `${page}?${queryParts.join('&')}`;
    }
    /**
     * Build AJAX URL with parameters
     */
    buildAjaxUrl(params) {
        return this.buildUrl(params, 'ajax.php');
    }
    /**
     * Get current URL info
     */
    getUrlInfo() {
        return this.urlInfo;
    }
    /**
     * Reset resolved state
     */
    reset() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
}


/***/ }),

/***/ "./src/utils/storage-manager.ts":
/*!**************************************!*\
  !*** ./src/utils/storage-manager.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StorageManager: () => (/* binding */ StorageManager)
/* harmony export */ });
/* harmony import */ var _default_settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./default-settings */ "./src/utils/default-settings.ts");
// Chrome extension storage management utilities

class StorageManager {
    constructor() {
        this.STORAGE_KEYS = {
            SETTINGS: 'gh_settings',
            STATISTICS: 'gh_statistics',
            LICENSE: 'gh_license',
            PAUSE_SCHEDULE: 'gh_pause_schedule',
            QUEST_RULES: 'gh_quest_rules',
            MARKET_RULES: 'gh_market_rules',
            BANK_DATA: 'gh_bank_data',
            BOT_STATUS: 'gh_bot_status'
        };
    }
    /**
     * Check if Chrome storage API is available
     */
    isChromeStorageAvailable() {
        return typeof chrome !== 'undefined' &&
            chrome.storage &&
            chrome.storage.local &&
            typeof chrome.storage.local.get === 'function';
    }
    /**
     * Fallback method to get settings from localStorage
     */
    getSettingsFromLocalStorage() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);
            return stored ? JSON.parse(stored) : null;
        }
        catch (error) {
            console.error('Failed to get settings from localStorage:', error);
            return null;
        }
    }
    /**
     * Fallback method to save settings to localStorage
     */
    saveSettingsToLocalStorage(settings) {
        try {
            const currentSettings = this.getSettingsFromLocalStorage() || {};
            const updatedSettings = this.deepMerge(currentSettings, settings);
            localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));
        }
        catch (error) {
            console.error('Failed to save settings to localStorage:', error);
        }
    }
    /**
     * Get bot settings from storage
     */
    async getSettings() {
        try {
            // Check if Chrome storage API is available
            if (!this.isChromeStorageAvailable()) {
                console.warn('Chrome storage API not available, using localStorage fallback');
                const storedSettings = this.getSettingsFromLocalStorage();
                return storedSettings ? _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings) : _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
            }
            const result = await chrome.storage.local.get(this.STORAGE_KEYS.SETTINGS);
            const storedSettings = result[this.STORAGE_KEYS.SETTINGS];
            if (!storedSettings) {
                // Return default settings if none are stored
                return _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
            }
            // Merge stored settings with defaults to ensure all properties exist
            return _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings);
        }
        catch (error) {
            console.error('Failed to get settings:', error);
            // Fallback to localStorage
            const storedSettings = this.getSettingsFromLocalStorage();
            return storedSettings ? _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.mergeWithDefaults(storedSettings) : _default_settings__WEBPACK_IMPORTED_MODULE_0__.DefaultSettingsFactory.createDefaultSettings();
        }
    }
    /**
     * Save bot settings to storage
     */
    async saveSettings(settings) {
        try {
            // Check if Chrome storage API is available
            if (!this.isChromeStorageAvailable()) {
                console.warn('Chrome storage API not available, using localStorage fallback');
                this.saveSettingsToLocalStorage(settings);
                return;
            }
            const currentSettings = await this.getSettings() || {};
            const updatedSettings = this.deepMerge(currentSettings, settings);
            await chrome.storage.local.set({
                [this.STORAGE_KEYS.SETTINGS]: updatedSettings
            });
        }
        catch (error) {
            console.error('Failed to save settings:', error);
            // Fallback to localStorage
            this.saveSettingsToLocalStorage(settings);
        }
    }
    /**
     * Get bot status from storage
     */
    async getBotStatus() {
        try {
            if (!this.isChromeStorageAvailable()) {
                const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
                return stored ? JSON.parse(stored) : null;
            }
            const result = await chrome.storage.local.get(this.STORAGE_KEYS.BOT_STATUS);
            return result[this.STORAGE_KEYS.BOT_STATUS] || null;
        }
        catch (error) {
            console.error('Failed to get bot status:', error);
            try {
                const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
                return stored ? JSON.parse(stored) : null;
            }
            catch {
                return null;
            }
        }
    }
    /**
     * Save bot status to storage
     */
    async saveBotStatus(status) {
        try {
            if (!this.isChromeStorageAvailable()) {
                localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
                return;
            }
            await chrome.storage.local.set({
                [this.STORAGE_KEYS.BOT_STATUS]: status
            });
        }
        catch (error) {
            console.error('Failed to save bot status:', error);
            // Fallback to localStorage
            try {
                localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
            }
            catch (fallbackError) {
                console.error('Failed to save bot status to localStorage:', fallbackError);
                throw error;
            }
        }
    }
    /**
     * Get all extension data from storage
     */
    async getAllData() {
        try {
            const keys = Object.values(this.STORAGE_KEYS);
            const result = await chrome.storage.local.get(keys);
            return {
                settings: result[this.STORAGE_KEYS.SETTINGS],
                statistics: result[this.STORAGE_KEYS.STATISTICS],
                licenseInfo: result[this.STORAGE_KEYS.LICENSE],
                pauseSchedule: result[this.STORAGE_KEYS.PAUSE_SCHEDULE],
                questRules: result[this.STORAGE_KEYS.QUEST_RULES],
                marketRules: result[this.STORAGE_KEYS.MARKET_RULES],
                bankData: result[this.STORAGE_KEYS.BANK_DATA]
            };
        }
        catch (error) {
            console.error('Failed to get all data:', error);
            return {};
        }
    }
    /**
     * Clear all extension data from storage
     */
    async clearAllData() {
        try {
            const keys = Object.values(this.STORAGE_KEYS);
            await chrome.storage.local.remove(keys);
        }
        catch (error) {
            console.error('Failed to clear all data:', error);
            throw error;
        }
    }
    /**
     * Get storage usage information
     */
    async getStorageUsage() {
        try {
            const bytesInUse = await chrome.storage.local.getBytesInUse();
            const quota = chrome.storage.local.QUOTA_BYTES;
            return { bytesInUse, quota };
        }
        catch (error) {
            console.error('Failed to get storage usage:', error);
            return { bytesInUse: 0, quota: 0 };
        }
    }
    /**
     * Deep merge two objects
     */
    deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            }
            else {
                result[key] = source[key];
            }
        }
        return result;
    }
    /**
     * Export settings as JSON string
     */
    async exportSettings() {
        const data = await this.getAllData();
        return JSON.stringify(data, null, 2);
    }
    /**
     * Import settings from JSON string
     */
    async importSettings(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (data.settings) {
                await this.saveSettings(data.settings);
            }
            // Import other data types as needed
            const keys = Object.values(this.STORAGE_KEYS);
            const importData = {};
            for (const key of keys) {
                const dataKey = key.replace('gh_', '');
                if (data[dataKey]) {
                    importData[key] = data[dataKey];
                }
            }
            if (Object.keys(importData).length > 0) {
                await chrome.storage.local.set(importData);
            }
        }
        catch (error) {
            console.error('Failed to import settings:', error);
            throw new Error('Invalid settings data');
        }
    }
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*********************************!*\
  !*** ./src/background/index.ts ***!
  \*********************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
/* harmony import */ var _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/gladiator-url-helper */ "./src/utils/gladiator-url-helper.ts");
/* harmony import */ var _utils_storage_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/storage-manager */ "./src/utils/storage-manager.ts");
// Background service worker for Gladiatus Helper Bot



class BackgroundService {
    constructor() {
        this.urlHelper = new _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__.GladiatorUrlHelper();
        this.storageManager = new _utils_storage_manager__WEBPACK_IMPORTED_MODULE_2__.StorageManager();
        this.initialize();
    }
    initialize() {
        // Listen for extension messages
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
        // Listen for tab updates to detect Gladiatus pages
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tabId, tab.url);
            }
        });
        // Listen for extension installation/startup
        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });
    }
    async handleMessage(message, _sender, sendResponse) {
        try {
            switch (message.type) {
                case _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.LOGIN_REQUEST:
                    await this.handleLoginRequest(message, sendResponse);
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.SETTINGS_UPDATE:
                    await this.handleSettingsUpdate(message, sendResponse);
                    break;
                case _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.SCRIPT_STATUS:
                    await this.handleScriptStatus(message, sendResponse);
                    break;
                default:
                    console.warn('Unknown message type:', message.type);
                    sendResponse({ error: 'Unknown message type' });
            }
        }
        catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }
    async handleLoginRequest(message, sendResponse) {
        if (message.isLobby) {
            const shouldLogin = await this.shouldAutoLogin();
            const loginDelay = await this.getLoginDelay();
            sendResponse({
                shouldLogin,
                loginDelay: shouldLogin ? Date.now() + loginDelay : 0,
                server: message.server,
                playerId: message.playerId,
                language: message.language
            });
        }
    }
    async handleSettingsUpdate(message, sendResponse) {
        try {
            await this.storageManager.saveSettings(message.data);
            sendResponse({ success: true });
        }
        catch (error) {
            sendResponse({ error: error.message });
        }
    }
    async handleScriptStatus(_message, sendResponse) {
        const status = await this.storageManager.getBotStatus();
        sendResponse(status);
    }
    async handleTabUpdate(tabId, url) {
        if (this.urlHelper.isGladiatus(url)) {
            // Inject content script if it's a Gladiatus page
            try {
                await chrome.scripting.executeScript({
                    target: { tabId },
                    files: ['content-ui/index.iife.js']
                });
            }
            catch (error) {
                console.error('Failed to inject content script:', error);
            }
        }
    }
    handleStartup() {
        console.log('Gladiatus Helper Bot started');
    }
    handleInstalled(details) {
        if (details.reason === 'install') {
            console.log('Gladiatus Helper Bot installed');
            this.initializeDefaultSettings();
        }
        else if (details.reason === 'update') {
            console.log('Gladiatus Helper Bot updated');
            this.handleUpdate(details.previousVersion);
        }
    }
    async shouldAutoLogin() {
        const settings = await this.storageManager.getSettings();
        return settings?.general?.autoLogin ?? false;
    }
    async getLoginDelay() {
        const settings = await this.storageManager.getSettings();
        return (settings?.general?.loginDelay ?? 5) * 1000; // Convert to milliseconds
    }
    async initializeDefaultSettings() {
        const defaultSettings = {
            general: {
                selectedBackpack: 1,
                maxRandomDelay: 10,
                loginDelay: 5,
                highlightUnderworldItems: true,
                showShopBox: true,
                shopMinQuality: 1,
                showBiddenBox: true,
                autoCollectGodOils: true,
                autoCollectDaily: true
            }
        };
        await this.storageManager.saveSettings(defaultSettings);
    }
    async handleUpdate(previousVersion) {
        // Handle version-specific updates
        console.log(`Updated from version ${previousVersion} to ${chrome.runtime.getManifest().version}`);
    }
}
// Initialize the background service
new BackgroundService();

})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=index.iife.js.map