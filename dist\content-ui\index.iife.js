(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/styles/content.css":
/*!********************************!*\
  !*** ./src/styles/content.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* binding */ BankRequestState),
/* harmony export */   LicenseType: () => (/* binding */ LicenseType)
/* harmony export */ });
// API and network request types
var LicenseType;
(function (LicenseType) {
    LicenseType["FREE_TRIAL"] = "free_trial";
    LicenseType["ESSENTIAL"] = "essential";
    LicenseType["PREMIUM"] = "premium";
    LicenseType["INACTIVE"] = "inactive";
})(LicenseType || (LicenseType = {}));
var BankRequestState;
(function (BankRequestState) {
    BankRequestState["PENDING"] = "pending";
    BankRequestState["PROGRESS"] = "progress";
    BankRequestState["READY"] = "ready";
    BankRequestState["COMPLETE"] = "complete";
})(BankRequestState || (BankRequestState = {}));


/***/ }),

/***/ "./src/types/extension.ts":
/*!********************************!*\
  !*** ./src/types/extension.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideGoldLocation: () => (/* binding */ HideGoldLocation),
/* harmony export */   MessageType: () => (/* binding */ MessageType),
/* harmony export */   PackDuration: () => (/* binding */ PackDuration),
/* harmony export */   PackFilterType: () => (/* binding */ PackFilterType),
/* harmony export */   RenewShopOption: () => (/* binding */ RenewShopOption)
/* harmony export */ });
// Chrome extension specific type definitions
var MessageType;
(function (MessageType) {
    MessageType["LOGIN_REQUEST"] = "LOGIN_REQUEST";
    MessageType["LOGIN_RESPONSE"] = "LOGIN_RESPONSE";
    MessageType["LOGIN_FAILED"] = "LOGIN_FAILED";
    MessageType["SCRIPT_STATUS"] = "SCRIPT_STATUS";
    MessageType["SETTINGS_UPDATE"] = "SETTINGS_UPDATE";
    MessageType["NOTIFICATION"] = "NOTIFICATION";
    MessageType["ERROR"] = "ERROR";
})(MessageType || (MessageType = {}));
var RenewShopOption;
(function (RenewShopOption) {
    RenewShopOption["NOT_ALLOW"] = "not_allow";
    RenewShopOption["CLOTHE_ONLY"] = "clothe_only";
    RenewShopOption["RUBY_OR_CLOTHE"] = "ruby_or_clothe";
})(RenewShopOption || (RenewShopOption = {}));
var HideGoldLocation;
(function (HideGoldLocation) {
    HideGoldLocation["GUILD"] = "guild";
    HideGoldLocation["SHOP"] = "shop";
    HideGoldLocation["AUCTION"] = "auction";
    HideGoldLocation["TRAINING"] = "training";
    HideGoldLocation["MARKET"] = "market";
    HideGoldLocation["DONATE"] = "donate";
})(HideGoldLocation || (HideGoldLocation = {}));
var PackDuration;
(function (PackDuration) {
    PackDuration["TWO_HOURS"] = "2h";
    PackDuration["EIGHT_HOURS"] = "8h";
    PackDuration["TWENTY_FOUR_HOURS"] = "24h";
    PackDuration["FORTY_EIGHT_HOURS"] = "48h";
})(PackDuration || (PackDuration = {}));
var PackFilterType;
(function (PackFilterType) {
    PackFilterType["LOWEST_DURATION"] = "d";
    PackFilterType["HIGHEST_DURATION"] = "dd";
    PackFilterType["LOWEST_PRICE"] = "p";
    PackFilterType["HIGHEST_PRICE"] = "pd";
})(PackFilterType || (PackFilterType = {}));


/***/ }),

/***/ "./src/types/game.ts":
/*!***************************!*\
  !*** ./src/types/game.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BotAction: () => (/* binding */ BotAction),
/* harmony export */   ForgeDestination: () => (/* binding */ ForgeDestination),
/* harmony export */   ToolQuality: () => (/* binding */ ToolQuality),
/* harmony export */   WorkType: () => (/* binding */ WorkType)
/* harmony export */ });
// Game mechanics and bot logic types
var BotAction;
(function (BotAction) {
    BotAction["IDLE"] = "idle";
    BotAction["HEALING"] = "healing";
    BotAction["ATTACKING"] = "attacking";
    BotAction["TRAVELING"] = "traveling";
    BotAction["BUYING_PACK"] = "buying_pack";
    BotAction["PICKING_ITEMS"] = "picking_items";
    BotAction["UNDERWORLD"] = "underworld";
    BotAction["EVENT"] = "event";
    BotAction["SELLING_PACK"] = "selling_pack";
    BotAction["QUEST_MANAGEMENT"] = "quest_management";
    BotAction["MARKET_OPERATIONS"] = "market_operations";
    BotAction["FORGE_REPAIR"] = "forge_repair";
    BotAction["ROTATING_ITEMS"] = "rotating_items";
    BotAction["CHECKING_QUESTS"] = "checking_quests";
    BotAction["FORGING"] = "forging";
    BotAction["SMELTING"] = "smelting";
    BotAction["REPAIRING"] = "repairing";
    BotAction["CHECKING_MARKET"] = "checking_market";
    BotAction["CHECKING_AUCTION"] = "checking_auction";
    BotAction["ACTIVATING_PACTS"] = "activating_pacts";
    BotAction["PROCESSING_PACK"] = "processing_pack";
    BotAction["REQUESTING_PACK"] = "requesting_pack";
})(BotAction || (BotAction = {}));
var WorkType;
(function (WorkType) {
    WorkType["SENATOR"] = "senator";
    WorkType["JEWELLER"] = "jeweller";
    WorkType["STABLE_BOY"] = "stable_boy";
    WorkType["FARMER"] = "farmer";
    WorkType["BUTCHER"] = "butcher";
    WorkType["FISHERMAN"] = "fisherman";
    WorkType["BAKER"] = "baker";
    WorkType["BLACKSMITH"] = "blacksmith";
    WorkType["MASTER_BLACKSMITH"] = "master_blacksmith";
})(WorkType || (WorkType = {}));
var ToolQuality;
(function (ToolQuality) {
    ToolQuality["BRONZE"] = "bronze";
    ToolQuality["SILVER"] = "silver";
    ToolQuality["GOLD"] = "gold";
})(ToolQuality || (ToolQuality = {}));
var ForgeDestination;
(function (ForgeDestination) {
    ForgeDestination["NONE"] = "none";
    ForgeDestination["INVENTORY"] = "inventory";
    ForgeDestination["PACKAGES"] = "packages";
})(ForgeDestination || (ForgeDestination = {}));


/***/ }),

/***/ "./src/types/gladiatus.ts":
/*!********************************!*\
  !*** ./src/types/gladiatus.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GodType: () => (/* binding */ GodType),
/* harmony export */   ItemQuality: () => (/* binding */ ItemQuality),
/* harmony export */   ItemType: () => (/* binding */ ItemType),
/* harmony export */   LocationType: () => (/* binding */ LocationType),
/* harmony export */   QuestType: () => (/* binding */ QuestType)
/* harmony export */ });
// Gladiatus game-specific type definitions
var ItemQuality;
(function (ItemQuality) {
    ItemQuality[ItemQuality["WHITE"] = 0] = "WHITE";
    ItemQuality[ItemQuality["GREEN"] = 1] = "GREEN";
    ItemQuality[ItemQuality["BLUE"] = 2] = "BLUE";
    ItemQuality[ItemQuality["PURPLE"] = 3] = "PURPLE";
    ItemQuality[ItemQuality["ORANGE"] = 4] = "ORANGE";
    ItemQuality[ItemQuality["RED"] = 5] = "RED";
})(ItemQuality || (ItemQuality = {}));
var ItemType;
(function (ItemType) {
    ItemType["WEAPON"] = "weapon";
    ItemType["SHIELD"] = "shield";
    ItemType["HELMET"] = "helmet";
    ItemType["CHEST"] = "chest";
    ItemType["GLOVES"] = "gloves";
    ItemType["SHOES"] = "shoes";
    ItemType["RING"] = "ring";
    ItemType["AMULET"] = "amulet";
    ItemType["USABLE"] = "usable";
    ItemType["REINFORCEMENT"] = "reinforcement";
    ItemType["UPGRADE"] = "upgrade";
    ItemType["RECIPE"] = "recipe";
    ItemType["MERCENARY"] = "mercenary";
    ItemType["FORGING_GOODS"] = "forging_goods";
    ItemType["TOOLS"] = "tools";
    ItemType["SCROLL"] = "scroll";
    ItemType["EVENT_ITEMS"] = "event_items";
})(ItemType || (ItemType = {}));
var QuestType;
(function (QuestType) {
    QuestType["ARENA"] = "arena";
    QuestType["GROUP_ARENA"] = "group_arena";
    QuestType["EXPEDITION"] = "expedition";
    QuestType["DUNGEON"] = "dungeon";
    QuestType["WORK"] = "work";
    QuestType["FIND_ITEM"] = "find_item";
    QuestType["COMBAT"] = "combat";
    QuestType["ANY_QUEST"] = "any_quest";
})(QuestType || (QuestType = {}));
var GodType;
(function (GodType) {
    GodType["APOLLO"] = "apollo";
    GodType["VULCAN"] = "vulcan";
    GodType["MARS"] = "mars";
    GodType["MERCURY"] = "mercury";
    GodType["DIANA"] = "diana";
    GodType["MINERVA"] = "minerva";
})(GodType || (GodType = {}));
var LocationType;
(function (LocationType) {
    LocationType["EXPEDITION"] = "expedition";
    LocationType["DUNGEON"] = "dungeon";
    LocationType["ARENA"] = "arena";
    LocationType["TURMA"] = "turma";
    LocationType["UNDERWORLD"] = "underworld";
    LocationType["MARKET"] = "market";
    LocationType["GUILD"] = "guild";
})(LocationType || (LocationType = {}));


/***/ }),

/***/ "./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BankRequestState: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.BankRequestState),
/* harmony export */   BotAction: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.BotAction),
/* harmony export */   ButtonStyle: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ButtonStyle),
/* harmony export */   CooldownRemovalOption: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.CooldownRemovalOption),
/* harmony export */   FieldType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.FieldType),
/* harmony export */   FilterType: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.FilterType),
/* harmony export */   ForgeDestination: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ForgeDestination),
/* harmony export */   GodType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.GodType),
/* harmony export */   HideGoldLocation: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.HideGoldLocation),
/* harmony export */   ItemQuality: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemQuality),
/* harmony export */   ItemType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.ItemType),
/* harmony export */   LicenseType: () => (/* reexport safe */ _api__WEBPACK_IMPORTED_MODULE_4__.LicenseType),
/* harmony export */   LocationType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.LocationType),
/* harmony export */   MessageType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.MessageType),
/* harmony export */   ModalSize: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ModalSize),
/* harmony export */   NotificationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.NotificationType),
/* harmony export */   OperationSpeed: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.OperationSpeed),
/* harmony export */   PackDuration: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackDuration),
/* harmony export */   PackFilterType: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.PackFilterType),
/* harmony export */   QuestType: () => (/* reexport safe */ _gladiatus__WEBPACK_IMPORTED_MODULE_0__.QuestType),
/* harmony export */   RenewShopOption: () => (/* reexport safe */ _extension__WEBPACK_IMPORTED_MODULE_1__.RenewShopOption),
/* harmony export */   ToolQuality: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.ToolQuality),
/* harmony export */   UnderworldCostume: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* reexport safe */ _settings__WEBPACK_IMPORTED_MODULE_3__.UnderworldMode),
/* harmony export */   ValidationType: () => (/* reexport safe */ _ui__WEBPACK_IMPORTED_MODULE_5__.ValidationType),
/* harmony export */   WorkType: () => (/* reexport safe */ _game__WEBPACK_IMPORTED_MODULE_6__.WorkType)
/* harmony export */ });
/* harmony import */ var _gladiatus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gladiatus */ "./src/types/gladiatus.ts");
/* harmony import */ var _extension__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extension */ "./src/types/extension.ts");
/* harmony import */ var _localization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./localization */ "./src/types/localization.ts");
/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./settings */ "./src/types/settings.ts");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api */ "./src/types/api.ts");
/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ "./src/types/ui.ts");
/* harmony import */ var _game__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./game */ "./src/types/game.ts");
// Main type definitions for Gladiatus Helper Bot









/***/ }),

/***/ "./src/types/localization.ts":
/*!***********************************!*\
  !*** ./src/types/localization.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// Localization and internationalization types



/***/ }),

/***/ "./src/types/settings.ts":
/*!*******************************!*\
  !*** ./src/types/settings.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CooldownRemovalOption: () => (/* binding */ CooldownRemovalOption),
/* harmony export */   FilterType: () => (/* binding */ FilterType),
/* harmony export */   OperationSpeed: () => (/* binding */ OperationSpeed),
/* harmony export */   UnderworldCostume: () => (/* binding */ UnderworldCostume),
/* harmony export */   UnderworldDifficulty: () => (/* binding */ UnderworldDifficulty),
/* harmony export */   UnderworldMode: () => (/* binding */ UnderworldMode)
/* harmony export */ });
// Settings and configuration type definitions
var FilterType;
(function (FilterType) {
    FilterType["CONTAINS"] = "contains";
    FilterType["NOT_CONTAINS"] = "not_contains";
    FilterType["CONTAINS_ANY"] = "contains_any";
    FilterType["NOT_CONTAINS_ANY"] = "not_contains_any";
    FilterType["CONTAINS_WORD"] = "contains_word";
    FilterType["IS_UNDERWORLD"] = "is_underworld";
    FilterType["IS_NOT_UNDERWORLD"] = "is_not_underworld";
    FilterType["GREATER_THAN"] = "greater_than";
    FilterType["LESS_THAN"] = "less_than";
    FilterType["STARTS_WITH"] = "starts_with";
    FilterType["ENDS_WITH"] = "ends_with";
    FilterType["CONTAINS_RESOURCE"] = "contains_resource";
})(FilterType || (FilterType = {}));
var OperationSpeed;
(function (OperationSpeed) {
    OperationSpeed["VERY_FAST"] = "very_fast";
    OperationSpeed["FAST"] = "fast";
    OperationSpeed["NORMAL"] = "normal";
    OperationSpeed["SLOW"] = "slow";
})(OperationSpeed || (OperationSpeed = {}));
var UnderworldCostume;
(function (UnderworldCostume) {
    UnderworldCostume["DIS_PATER_NORMAL"] = "dis_pater_normal";
    UnderworldCostume["DIS_PATER_MEDIUM"] = "dis_pater_medium";
    UnderworldCostume["DIS_PATER_HARD"] = "dis_pater_hard";
    UnderworldCostume["VULCANUS_FORGE"] = "vulcanus_forge";
    UnderworldCostume["FERONIAS_EARTHEN_SHIELD"] = "feronias_earthen_shield";
    UnderworldCostume["NEPTUNES_FLUID_MIGHT"] = "neptunes_fluid_might";
    UnderworldCostume["AELOUS_AERIAL_FREEDOM"] = "aelous_aerial_freedom";
    UnderworldCostume["PLUTOS_DEADLY_MIST"] = "plutos_deadly_mist";
    UnderworldCostume["JUNOS_BREATH_OF_LIFE"] = "junos_breath_of_life";
    UnderworldCostume["WRATH_MOUNTAIN_SCALE_ARMOUR"] = "wrath_mountain_scale_armour";
    UnderworldCostume["EAGLE_EYES"] = "eagle_eyes";
    UnderworldCostume["SATURNS_WINTER_GARMENT"] = "saturns_winter_garment";
    UnderworldCostume["BUBONA_BULL_ARMOUR"] = "bubona_bull_armour";
    UnderworldCostume["MERCERIUS_ROBBERS_GARMENTS"] = "mercerius_robbers_garments";
    UnderworldCostume["RA_LIGHT_ROBE"] = "ra_light_robe";
})(UnderworldCostume || (UnderworldCostume = {}));
var CooldownRemovalOption;
(function (CooldownRemovalOption) {
    CooldownRemovalOption["HOURGLASS"] = "hourglass";
    CooldownRemovalOption["HOURGLASS_OR_RUBY"] = "hourglass_or_ruby";
})(CooldownRemovalOption || (CooldownRemovalOption = {}));
var UnderworldMode;
(function (UnderworldMode) {
    UnderworldMode["FARM_QUEST"] = "farm_quest";
    UnderworldMode["FARM_MOB"] = "farm_mob";
    UnderworldMode["NORMAL"] = "normal";
})(UnderworldMode || (UnderworldMode = {}));
var UnderworldDifficulty;
(function (UnderworldDifficulty) {
    UnderworldDifficulty["NORMAL"] = "normal";
    UnderworldDifficulty["MEDIUM"] = "medium";
    UnderworldDifficulty["HARD"] = "hard";
})(UnderworldDifficulty || (UnderworldDifficulty = {}));


/***/ }),

/***/ "./src/types/ui.ts":
/*!*************************!*\
  !*** ./src/types/ui.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ButtonStyle: () => (/* binding */ ButtonStyle),
/* harmony export */   FieldType: () => (/* binding */ FieldType),
/* harmony export */   ModalSize: () => (/* binding */ ModalSize),
/* harmony export */   NotificationType: () => (/* binding */ NotificationType),
/* harmony export */   ValidationType: () => (/* binding */ ValidationType)
/* harmony export */ });
// UI and interface types
var NotificationType;
(function (NotificationType) {
    NotificationType["INFO"] = "info";
    NotificationType["SUCCESS"] = "success";
    NotificationType["WARNING"] = "warning";
    NotificationType["ERROR"] = "error";
    NotificationType["GOLD_EXPIRY"] = "gold_expiry";
    NotificationType["ITEM_FOUND"] = "item_found";
    NotificationType["ITEM_BOUGHT"] = "item_bought";
})(NotificationType || (NotificationType = {}));
var ModalSize;
(function (ModalSize) {
    ModalSize["SMALL"] = "small";
    ModalSize["MEDIUM"] = "medium";
    ModalSize["LARGE"] = "large";
    ModalSize["EXTRA_LARGE"] = "extra_large";
})(ModalSize || (ModalSize = {}));
var ButtonStyle;
(function (ButtonStyle) {
    ButtonStyle["PRIMARY"] = "primary";
    ButtonStyle["SECONDARY"] = "secondary";
    ButtonStyle["SUCCESS"] = "success";
    ButtonStyle["DANGER"] = "danger";
    ButtonStyle["WARNING"] = "warning";
    ButtonStyle["INFO"] = "info";
})(ButtonStyle || (ButtonStyle = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "text";
    FieldType["NUMBER"] = "number";
    FieldType["SELECT"] = "select";
    FieldType["CHECKBOX"] = "checkbox";
    FieldType["RADIO"] = "radio";
    FieldType["TEXTAREA"] = "textarea";
    FieldType["RANGE"] = "range";
    FieldType["COLOR"] = "color";
    FieldType["DATE"] = "date";
    FieldType["TIME"] = "time";
})(FieldType || (FieldType = {}));
var ValidationType;
(function (ValidationType) {
    ValidationType["REQUIRED"] = "required";
    ValidationType["MIN"] = "min";
    ValidationType["MAX"] = "max";
    ValidationType["MIN_LENGTH"] = "min_length";
    ValidationType["MAX_LENGTH"] = "max_length";
    ValidationType["PATTERN"] = "pattern";
    ValidationType["EMAIL"] = "email";
    ValidationType["URL"] = "url";
})(ValidationType || (ValidationType = {}));


/***/ }),

/***/ "./src/utils/gladiator-url-helper.ts":
/*!*******************************************!*\
  !*** ./src/utils/gladiator-url-helper.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GladiatorUrlHelper: () => (/* binding */ GladiatorUrlHelper)
/* harmony export */ });
// Gladiatus URL parsing and validation utilities
class GladiatorUrlHelper {
    constructor() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
    /**
     * Check if URL is a Gladiatus domain
     */
    isGladiatus(url) {
        return /gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is a Gladiatus game page (not lobby)
     */
    isPlaying(url) {
        return /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is the Gladiatus lobby
     */
    isLobby(url) {
        return /lobby\.gladiatus\.gameforge\.com/.test(url) && url.indexOf('loading') < 0;
    }
    /**
     * Parse query string into object
     */
    resolveQueries(queryString) {
        const queries = {};
        const params = queryString.split('&');
        for (let i = params.length - 1; i >= 0; i--) {
            const [key, value] = params[i].split('=');
            if (key && value) {
                queries[key] = decodeURIComponent(value);
            }
        }
        return queries;
    }
    /**
     * Resolve URL information from Gladiatus game URL
     */
    resolve(url, force = false) {
        if (this.resolved && !force) {
            return this.urlInfo;
        }
        const match = url.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main)\.php(?:\?(.*))?/i);
        if (!match) {
            return null;
        }
        this.urlInfo = {
            server: match[1],
            country: match[2],
            domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,
            queries: this.resolveQueries(match[3] || ''),
            resolved: true
        };
        this.resolved = true;
        return this.urlInfo;
    }
    /**
     * Build Gladiatus URL with parameters
     */
    buildUrl(params, page = 'index.php') {
        const queryParts = [];
        for (const key in params) {
            queryParts.push(`${key}=${encodeURIComponent(params[key])}`);
        }
        // Add security hash if available
        if (this.urlInfo.queries.sh) {
            queryParts.push(`sh=${this.urlInfo.queries.sh}`);
        }
        return `${page}?${queryParts.join('&')}`;
    }
    /**
     * Build AJAX URL with parameters
     */
    buildAjaxUrl(params) {
        return this.buildUrl(params, 'ajax.php');
    }
    /**
     * Get current URL info
     */
    getUrlInfo() {
        return this.urlInfo;
    }
    /**
     * Reset resolved state
     */
    reset() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!***********************************!*\
  !*** ./src/content/content-ui.ts ***!
  \***********************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
/* harmony import */ var _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/gladiator-url-helper */ "./src/utils/gladiator-url-helper.ts");
/* harmony import */ var _styles_content_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/content.css */ "./src/styles/content.css");
// Content script for Gladiatus Helper Bot UI injection



class ContentUIManager {
    constructor() {
        this.isInjected = false;
        this.botStatus = null;
        this.urlHelper = new _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_1__.GladiatorUrlHelper();
        this.extensionId = chrome.runtime.id;
        this.initialize();
    }
    initialize() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        }
        else {
            this.onDOMReady();
        }
        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }
    onDOMReady() {
        const currentUrl = window.location.href;
        if (this.urlHelper.isLobby(currentUrl)) {
            this.handleLobbyPage();
        }
        else if (this.urlHelper.isPlaying(currentUrl)) {
            this.handleGamePage(currentUrl);
        }
    }
    handleLobbyPage() {
        console.log('Gladiatus Helper Bot: Lobby page detected');
        // Send login request to background script
        const message = {
            type: _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.LOGIN_REQUEST,
            isLobby: true,
            server: this.extractServerFromUrl(),
            playerId: this.extractPlayerIdFromUrl(),
            language: this.extractLanguageFromUrl()
        };
        chrome.runtime.sendMessage(message, (response) => {
            if (response?.shouldLogin && response.loginDelay) {
                this.scheduleAutoLogin(response.loginDelay);
            }
        });
    }
    handleGamePage(url) {
        console.log('Gladiatus Helper Bot: Game page detected');
        const urlInfo = this.urlHelper.resolve(url);
        if (!urlInfo) {
            console.error('Failed to resolve game URL');
            return;
        }
        // Set global extension ID for main script
        window.ghExtensionId = this.extensionId;
        // Inject UI elements first
        this.injectUI();
        // Inject main bot script after UI is ready
        setTimeout(() => {
            this.injectMainScript();
        }, 100);
        // Load and restore bot status
        this.loadBotStatus();
    }
    injectMainScript() {
        if (this.isInjected)
            return;
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('main/index.iife.js');
        script.onload = () => {
            console.log('Gladiatus Helper Bot: Main script injected');
            this.isInjected = true;
        };
        script.onerror = () => {
            console.error('Gladiatus Helper Bot: Failed to inject main script');
        };
        (document.head || document.documentElement).appendChild(script);
    }
    injectUI() {
        // Create bot control panel
        this.createControlPanel();
        // Create notification system
        this.createNotificationSystem();
        // Highlight underworld items if enabled
        this.highlightUnderworldItems();
    }
    createControlPanel() {
        // Check if panel already exists
        if (document.getElementById('gh-control-panel'))
            return;
        const panel = document.createElement('div');
        panel.id = 'gh-control-panel';
        panel.className = 'gh-panel';
        panel.innerHTML = `
      <div class="gh-panel-header">
        <h3>Gladiatus Helper Bot</h3>
        <button id="gh-toggle-panel" class="gh-btn gh-btn-sm">−</button>
      </div>
      <div class="gh-panel-content">
        <div class="gh-status-section">
          <div class="gh-status-indicator" id="gh-status-indicator">
            <span class="gh-status-dot"></span>
            <span id="gh-status-text">Inactive</span>
          </div>
          <button id="gh-toggle-bot" class="gh-btn gh-btn-primary">Start Bot</button>
        </div>
        <div class="gh-stats-section">
          <div class="gh-stat-item">
            <span class="gh-stat-label">Runtime:</span>
            <span id="gh-runtime" class="gh-stat-value">00:00:00</span>
          </div>
          <div class="gh-stat-item">
            <span class="gh-stat-label">Actions:</span>
            <span id="gh-actions" class="gh-stat-value">0</span>
          </div>
        </div>
        <div class="gh-actions-section">
          <button id="gh-settings" class="gh-btn gh-btn-secondary">Settings</button>
          <button id="gh-statistics" class="gh-btn gh-btn-secondary">Statistics</button>
        </div>
      </div>
    `;
        // Position panel
        panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      width: 280px;
      background: #2c3e50;
      border: 1px solid #34495e;
      border-radius: 8px;
      color: white;
      font-family: Arial, sans-serif;
      font-size: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
        document.body.appendChild(panel);
        // Add event listeners
        this.attachPanelEventListeners();
    }
    attachPanelEventListeners() {
        const togglePanel = document.getElementById('gh-toggle-panel');
        const toggleBot = document.getElementById('gh-toggle-bot');
        const settingsBtn = document.getElementById('gh-settings');
        const statisticsBtn = document.getElementById('gh-statistics');
        togglePanel?.addEventListener('click', () => {
            const content = document.querySelector('.gh-panel-content');
            const isCollapsed = content.style.display === 'none';
            content.style.display = isCollapsed ? 'block' : 'none';
            togglePanel.textContent = isCollapsed ? '−' : '+';
        });
        toggleBot?.addEventListener('click', () => {
            this.toggleBot();
        });
        settingsBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Settings button clicked');
            this.openSettings();
        });
        statisticsBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Statistics button clicked');
            this.openStatistics();
        });
    }
    createNotificationSystem() {
        if (document.getElementById('gh-notifications'))
            return;
        const container = document.createElement('div');
        container.id = 'gh-notifications';
        container.className = 'gh-notifications';
        container.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      z-index: 9999;
      max-width: 400px;
    `;
        document.body.appendChild(container);
    }
    highlightUnderworldItems() {
        // This would be implemented based on game-specific selectors
        const items = document.querySelectorAll('[data-item-type="underworld"]');
        items.forEach(item => {
            item.style.border = '2px solid #e74c3c';
            item.style.boxShadow = '0 0 10px rgba(231, 76, 60, 0.5)';
        });
    }
    scheduleAutoLogin(delay) {
        const remainingTime = delay - Date.now();
        if (remainingTime > 0) {
            setTimeout(() => {
                this.performAutoLogin();
            }, remainingTime);
        }
    }
    performAutoLogin() {
        // Look for login button and click it
        const loginButton = document.querySelector('input[type="submit"][value*="Login"], button[type="submit"]');
        if (loginButton) {
            loginButton.click();
        }
    }
    async loadBotStatus() {
        try {
            // Get bot status from storage
            const result = await chrome.storage.local.get('gh_bot_status');
            this.botStatus = result.gh_bot_status || null;
            console.log('Content script loaded bot status:', this.botStatus);
            if (this.botStatus) {
                // Update UI with stored status
                this.updateStatusUI(this.botStatus);
            }
            // Set up periodic status sync
            this.setupStatusSync();
        }
        catch (error) {
            console.error('Failed to load bot status:', error);
        }
    }
    setupStatusSync() {
        // Listen for storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local' && changes.gh_bot_status) {
                console.log('Bot status changed in storage:', changes.gh_bot_status.newValue);
                this.botStatus = changes.gh_bot_status.newValue;
                if (this.botStatus) {
                    this.updateStatusUI(this.botStatus);
                }
            }
        });
        // Periodic sync every 5 seconds
        setInterval(async () => {
            try {
                const result = await chrome.storage.local.get('gh_bot_status');
                const newStatus = result.gh_bot_status;
                if (newStatus && JSON.stringify(newStatus) !== JSON.stringify(this.botStatus)) {
                    console.log('Syncing bot status from storage');
                    this.botStatus = newStatus;
                    this.updateStatusUI(this.botStatus);
                }
            }
            catch (error) {
                // Ignore sync errors
            }
        }, 5000);
    }
    updateStatusUI(status) {
        const statusText = document.getElementById('gh-status-text');
        const toggleButton = document.getElementById('gh-toggle-bot');
        const statusDot = document.querySelector('.gh-status-dot');
        if (statusText && toggleButton && statusDot) {
            if (status.isActive) {
                if (status.isPaused) {
                    statusDot.style.backgroundColor = '#f39c12'; // Orange for paused
                    statusText.textContent = 'Paused';
                    toggleButton.textContent = 'Resume Bot';
                }
                else {
                    statusDot.style.backgroundColor = '#27ae60'; // Green for active
                    statusText.textContent = 'Active';
                    toggleButton.textContent = 'Stop Bot';
                }
            }
            else {
                statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive
                statusText.textContent = status.error ? 'Error' : 'Inactive';
                toggleButton.textContent = 'Start Bot';
            }
        }
    }
    toggleBot() {
        // Send message to main script to toggle bot
        const event = new CustomEvent('ghToggleBot');
        window.dispatchEvent(event);
    }
    openSettings() {
        console.log('Opening settings modal...');
        // Prevent any immediate event propagation
        setTimeout(() => {
            this.createSettingsModal();
        }, 10);
    }
    createSettingsModal() {
        console.log('Creating settings modal...');
        // Check if modal already exists
        const existingModal = document.getElementById('gh-settings-modal');
        if (existingModal) {
            console.log('Modal already exists, removing...');
            existingModal.remove();
        }
        const modal = document.createElement('div');
        modal.id = 'gh-settings-modal';
        modal.className = 'gh-modal';
        modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
        const modalContent = document.createElement('div');
        modalContent.className = 'gh-modal-content';
        modalContent.style.cssText = `
      background: #2c3e50;
      color: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    `;
        modalContent.innerHTML = `
      <div class="gh-modal-header">
        <h2>Bot Settings</h2>
        <button class="gh-modal-close" style="float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
      </div>
      <div class="gh-modal-body">
        <div class="gh-settings-section">
          <h3>General Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-auto-start"> Auto-start bot on page load
          </label>
          <label>
            <input type="number" id="gh-setting-login-delay" min="1" max="60" value="5"> Login delay (seconds)
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Combat Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-arena-enabled"> Enable Arena Combat
          </label>
          <label>
            <input type="checkbox" id="gh-setting-turma-enabled"> Enable Turma Combat
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Healing Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-heal-enabled"> Enable Auto-Healing
          </label>
          <label>
            <input type="number" id="gh-setting-min-health" min="1" max="100" value="50"> Minimum Health %
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Quest & Market Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-quest-enabled"> Enable Quest Management
          </label>
          <label>
            <input type="checkbox" id="gh-setting-market-enabled"> Enable Market Operations
          </label>
        </div>
        <div class="gh-settings-section">
          <h3>Forge & Repair Settings</h3>
          <label>
            <input type="checkbox" id="gh-setting-forge-enabled"> Enable Auto-Forging
          </label>
          <label>
            <input type="checkbox" id="gh-setting-repair-enabled"> Enable Auto-Repair
          </label>
        </div>
      </div>
      <div class="gh-modal-footer" style="margin-top: 20px; text-align: right;">
        <button id="gh-settings-save" class="gh-btn gh-btn-primary">Save Settings</button>
        <button id="gh-settings-cancel" class="gh-btn gh-btn-secondary" style="margin-left: 10px;">Cancel</button>
      </div>
    `;
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        console.log('Modal added to DOM, modal element:', modal);
        console.log('Modal display style:', modal.style.display);
        // Load current settings
        this.loadSettingsIntoModal();
        // Add event listeners
        this.attachSettingsEventListeners(modal);
        console.log('Settings modal fully initialized');
    }
    async loadSettingsIntoModal() {
        try {
            const result = await chrome.storage.local.get('gh_settings');
            const settings = result.gh_settings || {};
            // Populate form fields with current settings
            const autoStart = document.getElementById('gh-setting-auto-start');
            const loginDelay = document.getElementById('gh-setting-login-delay');
            const arenaEnabled = document.getElementById('gh-setting-arena-enabled');
            const turmaEnabled = document.getElementById('gh-setting-turma-enabled');
            const healEnabled = document.getElementById('gh-setting-heal-enabled');
            const minHealth = document.getElementById('gh-setting-min-health');
            const questEnabled = document.getElementById('gh-setting-quest-enabled');
            const marketEnabled = document.getElementById('gh-setting-market-enabled');
            const forgeEnabled = document.getElementById('gh-setting-forge-enabled');
            const repairEnabled = document.getElementById('gh-setting-repair-enabled');
            if (autoStart)
                autoStart.checked = settings.general?.autoStart || false;
            if (loginDelay)
                loginDelay.value = settings.general?.loginDelay || 5;
            if (arenaEnabled)
                arenaEnabled.checked = settings.arena?.enabled || false;
            if (turmaEnabled)
                turmaEnabled.checked = settings.turma?.enabled || false;
            if (healEnabled)
                healEnabled.checked = settings.heal?.enabled || false;
            if (minHealth)
                minHealth.value = settings.heal?.minHealth || 50;
            if (questEnabled)
                questEnabled.checked = settings.quest?.enabled || false;
            if (marketEnabled)
                marketEnabled.checked = settings.market?.enabled || false;
            if (forgeEnabled)
                forgeEnabled.checked = settings.forge?.enabled || false;
            if (repairEnabled)
                repairEnabled.checked = settings.repair?.enabled || false;
        }
        catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
    attachSettingsEventListeners(modal) {
        console.log('Attaching settings event listeners...');
        const closeBtn = modal.querySelector('.gh-modal-close');
        const saveBtn = document.getElementById('gh-settings-save');
        const cancelBtn = document.getElementById('gh-settings-cancel');
        const modalContent = modal.querySelector('.gh-modal-content');
        console.log('Found elements:', { closeBtn, saveBtn, cancelBtn, modalContent });
        closeBtn?.addEventListener('click', (e) => {
            console.log('Close button clicked');
            e.preventDefault();
            e.stopPropagation();
            modal.remove();
        });
        cancelBtn?.addEventListener('click', (e) => {
            console.log('Cancel button clicked');
            e.preventDefault();
            e.stopPropagation();
            modal.remove();
        });
        saveBtn?.addEventListener('click', (e) => {
            console.log('Save button clicked');
            e.preventDefault();
            e.stopPropagation();
            this.saveSettings();
            modal.remove();
        });
        // Prevent modal content clicks from closing modal
        modalContent?.addEventListener('click', (e) => {
            console.log('Modal content clicked');
            e.stopPropagation();
        });
        // Close on background click only - with delay to prevent immediate closure
        setTimeout(() => {
            modal.addEventListener('click', (e) => {
                console.log('Modal background clicked, target:', e.target, 'modal:', modal);
                if (e.target === modal) {
                    console.log('Closing modal via background click');
                    e.preventDefault();
                    e.stopPropagation();
                    modal.remove();
                }
            });
        }, 100);
        // Close on Escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                console.log('Escape key pressed, closing modal');
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
        console.log('Event listeners attached successfully');
    }
    async saveSettings() {
        try {
            // Get current settings
            const result = await chrome.storage.local.get('gh_settings');
            const settings = result.gh_settings || {};
            // Update settings from form
            const autoStart = document.getElementById('gh-setting-auto-start')?.checked;
            const loginDelay = parseInt(document.getElementById('gh-setting-login-delay')?.value) || 5;
            const arenaEnabled = document.getElementById('gh-setting-arena-enabled')?.checked;
            const turmaEnabled = document.getElementById('gh-setting-turma-enabled')?.checked;
            const healEnabled = document.getElementById('gh-setting-heal-enabled')?.checked;
            const minHealth = parseInt(document.getElementById('gh-setting-min-health')?.value) || 50;
            const questEnabled = document.getElementById('gh-setting-quest-enabled')?.checked;
            const marketEnabled = document.getElementById('gh-setting-market-enabled')?.checked;
            const forgeEnabled = document.getElementById('gh-setting-forge-enabled')?.checked;
            const repairEnabled = document.getElementById('gh-setting-repair-enabled')?.checked;
            // Update settings object
            settings.general = settings.general || {};
            settings.general.autoStart = autoStart;
            settings.general.loginDelay = loginDelay;
            settings.arena = settings.arena || {};
            settings.arena.enabled = arenaEnabled;
            settings.turma = settings.turma || {};
            settings.turma.enabled = turmaEnabled;
            settings.heal = settings.heal || {};
            settings.heal.enabled = healEnabled;
            settings.heal.minHealth = minHealth;
            settings.quest = settings.quest || {};
            settings.quest.enabled = questEnabled;
            settings.market = settings.market || {};
            settings.market.enabled = marketEnabled;
            settings.forge = settings.forge || {};
            settings.forge.enabled = forgeEnabled;
            settings.repair = settings.repair || {};
            settings.repair.enabled = repairEnabled;
            // Save to storage
            await chrome.storage.local.set({ 'gh_settings': settings });
            // Notify main script of settings update
            const event = new CustomEvent('ghSettingsUpdated', { detail: settings });
            window.dispatchEvent(event);
            this.showNotification({
                title: 'Settings Saved',
                message: 'Your settings have been saved successfully.'
            });
        }
        catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification({
                title: 'Error',
                message: 'Failed to save settings. Please try again.'
            });
        }
    }
    openStatistics() {
        // Open statistics modal
        const event = new CustomEvent('ghOpenStatistics');
        window.dispatchEvent(event);
    }
    handleMessage(message, _sender, _sendResponse) {
        switch (message.type) {
            case _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.NOTIFICATION:
                this.showNotification(message.data);
                break;
            case _types__WEBPACK_IMPORTED_MODULE_0__.MessageType.ERROR:
                this.showError(message.data);
                break;
            default:
                break;
        }
    }
    showNotification(data) {
        const container = document.getElementById('gh-notifications');
        if (!container)
            return;
        const notification = document.createElement('div');
        notification.className = 'gh-notification gh-notification-info';
        notification.innerHTML = `
      <div class="gh-notification-content">
        <strong>${data.title || 'Notification'}</strong>
        <p>${data.message}</p>
      </div>
      <button class="gh-notification-close">×</button>
    `;
        container.appendChild(notification);
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
        // Add close button functionality
        notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {
            notification.remove();
        });
    }
    showError(data) {
        console.error('Gladiatus Helper Bot Error:', data);
        this.showNotification({
            title: 'Error',
            message: data.message || 'An error occurred'
        });
    }
    extractServerFromUrl() {
        const match = window.location.href.match(/s(\d+)-(\w+)/);
        return match ? `${match[1]}-${match[2]}` : '';
    }
    extractPlayerIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('playerId') || '';
    }
    extractLanguageFromUrl() {
        const match = window.location.href.match(/s\d+-(\w+)/);
        return match ? match[1] : 'en';
    }
}
// Initialize content UI manager
new ContentUIManager();

})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=index.iife.js.map