import { QuestManagementSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface Quest {
    id: string;
    name: string;
    description: string;
    reward: string;
    difficulty: 'easy' | 'medium' | 'hard';
    isCompleted: boolean;
    isAvailable: boolean;
    requirements: string[];
}
export interface QuestManagementRule {
    priority: number;
    questType: string;
    minReward: number;
    maxDifficulty: 'easy' | 'medium' | 'hard';
    autoComplete: boolean;
}
export declare class QuestManagementModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private questsCompleted;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: QuestManagementSettings): Promise<void>;
    stop(): Promise<void>;
    private performQuestLoop;
    private navigateToQuests;
    private getAvailableQuests;
    private parseQuestElement;
    private selectBestQuest;
    private questMatchesRule;
    private parseRewardValue;
    private acceptQuest;
    private completeQuest;
    private completeCombatQuest;
    private completeCollectionQuest;
    private completeTravelQuest;
    private claimQuestReward;
    private shouldContinueQuests;
    getQuestStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=quest-management.d.ts.map