// Arena Combat Module - Handles all arena-related combat functionality

import { ArenaSettings, BotAction, GameBotStatistics } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface ArenaTarget {
  id: string;
  name: string;
  level: number;
  points: number;
  winChance: number;
  isGuildMember: boolean;
  canAttack: boolean;
}

export class ArenaCombatModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private attackCount: number = 0;
  private loseCount: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: ArenaSettings): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.attackCount = 0;
    this.loseCount = 0;
    
    console.log('Arena Combat: Starting arena automation');
    
    try {
      await this.navigateToArena();
      await this.performArenaLoop(settings);
    } catch (error) {
      console.error('Arena Combat: Error in arena automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Arena Combat: Stopped arena automation');
  }

  private async navigateToArena(): Promise<void> {
    const currentUrl = window.location.href;
    
    if (!currentUrl.includes('mod=arena')) {
      console.log('Arena Combat: Navigating to arena');
      window.location.href = currentUrl.replace(/mod=\w+/, 'mod=arena');
      
      // Wait for page load
      await this.waitForPageLoad();
    }
  }

  private async performArenaLoop(settings: ArenaSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueAttacking(settings)) {
      try {
        const targets = await this.getAvailableTargets(settings);
        
        if (targets.length === 0) {
          console.log('Arena Combat: No suitable targets found');
          await this.delay(5000);
          continue;
        }

        const target = this.selectBestTarget(targets, settings);
        if (!target) {
          console.log('Arena Combat: No target selected');
          await this.delay(5000);
          continue;
        }

        const result = await this.attackTarget(target, settings);
        await this.processAttackResult(result);
        
        // Random delay between attacks
        const delay = Math.random() * 10000 + 5000; // 5-15 seconds
        await this.delay(delay);
        
      } catch (error) {
        console.error('Arena Combat: Error in attack loop:', error);
        await this.delay(10000);
      }
    }
  }

  private async getAvailableTargets(settings: ArenaSettings): Promise<ArenaTarget[]> {
    const targets: ArenaTarget[] = [];
    
    // Parse arena page for targets
    const targetElements = document.querySelectorAll('.arena_opponent, .opponent_row');
    
    for (const element of targetElements) {
      try {
        const target = await this.parseTargetElement(element as HTMLElement, settings);
        if (target && this.isValidTarget(target, settings)) {
          targets.push(target);
        }
      } catch (error) {
        console.error('Arena Combat: Error parsing target:', error);
      }
    }
    
    return targets;
  }

  private async parseTargetElement(element: HTMLElement, settings: ArenaSettings): Promise<ArenaTarget | null> {
    const nameElement = element.querySelector('.opponent_name, .player_name');
    const levelElement = element.querySelector('.opponent_level, .level');
    const pointsElement = element.querySelector('.opponent_points, .points');
    const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
    
    if (!nameElement || !attackButton) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '0');
    const points = parseInt(pointsElement?.textContent?.trim() || '0');
    const id = attackButton.getAttribute('data-target-id') || 
               attackButton.getAttribute('href')?.match(/target=(\d+)/)?.[1] || '';
    
    // Calculate win chance (simplified)
    const winChance = this.calculateWinChance(level, points);
    
    // Check if guild member
    const isGuildMember = this.isGuildMember(name, settings);
    
    return {
      id,
      name,
      level,
      points,
      winChance,
      isGuildMember,
      canAttack: !attackButton.hasAttribute('disabled')
    };
  }

  private isValidTarget(target: ArenaTarget, settings: ArenaSettings): boolean {
    // Skip if can't attack
    if (!target.canAttack) return false;
    
    // Skip guild members if auto-ignore is enabled
    if (settings.autoIgnoreGuildPlayers && target.isGuildMember) return false;
    
    // Skip if in ignore list
    if (settings.ignorePlayers.includes(target.name)) return false;
    
    // Skip if win chance is too low and skipLow is enabled
    if (settings.skipLow && target.winChance < (settings.prioritizeChance || 80)) return false;
    
    // Check if targeting specific players
    if (settings.attackTargetPlayers && settings.attackPlayers.length > 0) {
      return settings.attackPlayers.includes(target.name);
    }
    
    return true;
  }

  private selectBestTarget(targets: ArenaTarget[], settings: ArenaSettings): ArenaTarget | null {
    if (targets.length === 0) return null;
    
    // Sort by win chance (highest first) and then by points
    targets.sort((a, b) => {
      if (Math.abs(a.winChance - b.winChance) < 5) {
        return b.points - a.points; // Higher points first if win chances are similar
      }
      return b.winChance - a.winChance; // Higher win chance first
    });
    
    return targets[0];
  }

  private async attackTarget(target: ArenaTarget, settings: ArenaSettings): Promise<any> {
    console.log(`Arena Combat: Attacking ${target.name} (Level ${target.level}, Win Chance: ${target.winChance}%)`);
    
    // Find and click attack button
    const attackButton = document.querySelector(`[data-target-id="${target.id}"], [href*="target=${target.id}"]`);
    if (!attackButton) {
      throw new Error('Attack button not found');
    }
    
    // Simulate attack if enabled
    if (settings.allowSim) {
      const simButton = document.querySelector('.simulate_button, [data-action="simulate"]');
      if (simButton) {
        (simButton as HTMLElement).click();
      } else {
        (attackButton as HTMLElement).click();
      }
    } else {
      (attackButton as HTMLElement).click();
    }
    
    // Wait for attack result
    await this.waitForAttackResult();
    
    return this.parseAttackResult();
  }

  private async processAttackResult(result: any): Promise<void> {
    this.attackCount++;
    
    if (result.won) {
      console.log(`Arena Combat: Victory! Gold: ${result.gold}, XP: ${result.xp}`);
      this.loseCount = 0; // Reset lose count on win
    } else {
      console.log('Arena Combat: Defeat');
      this.loseCount++;
    }
    
    // Update statistics
    await this.updateStatistics(result);
  }

  private shouldContinueAttacking(settings: ArenaSettings): boolean {
    // Check lose limit
    if (settings.loseLimit > 0 && this.loseCount >= settings.loseLimit) {
      console.log('Arena Combat: Lose limit reached');
      return false;
    }
    
    // Check daily attack limit
    if (settings.dailyAttacks > 0 && this.attackCount >= settings.dailyAttacks) {
      console.log('Arena Combat: Daily attack limit reached');
      return false;
    }
    
    // Check total attack limit
    if (settings.limitAttacks > 0 && this.attackCount >= settings.limitAttacks) {
      console.log('Arena Combat: Attack limit reached');
      return false;
    }
    
    return true;
  }

  private calculateWinChance(level: number, points: number): number {
    // Simplified win chance calculation
    // In reality, this would be more complex based on player stats
    const myLevel = this.getPlayerLevel();
    const myPoints = this.getPlayerPoints();
    
    const levelDiff = myLevel - level;
    const pointsDiff = myPoints - points;
    
    let winChance = 50; // Base 50%
    winChance += levelDiff * 5; // +/- 5% per level difference
    winChance += pointsDiff * 0.01; // +/- 0.01% per point difference
    
    return Math.max(0, Math.min(100, winChance));
  }

  private isGuildMember(playerName: string, settings: ArenaSettings): boolean {
    // Check if player is in the same guild
    // This would need to be implemented based on guild member list
    return false; // Simplified for now
  }

  private getPlayerLevel(): number {
    const levelElement = document.querySelector('.player_level, #player_level');
    return parseInt(levelElement?.textContent?.trim() || '1');
  }

  private getPlayerPoints(): number {
    const pointsElement = document.querySelector('.player_points, #player_points');
    return parseInt(pointsElement?.textContent?.trim() || '0');
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000); // Additional delay for dynamic content
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async waitForAttackResult(): Promise<void> {
    return new Promise((resolve) => {
      const checkResult = () => {
        const resultElement = document.querySelector('.combat_result, .attack_result');
        if (resultElement) {
          resolve();
        } else {
          setTimeout(checkResult, 500);
        }
      };
      setTimeout(checkResult, 2000); // Wait at least 2 seconds
    });
  }

  private parseAttackResult(): any {
    const resultElement = document.querySelector('.combat_result, .attack_result');
    if (!resultElement) return { won: false, gold: 0, xp: 0 };
    
    const won = resultElement.textContent?.includes('Victory') || 
                resultElement.classList.contains('victory') || false;
    
    const goldMatch = resultElement.textContent?.match(/(\d+)\s*gold/i);
    const xpMatch = resultElement.textContent?.match(/(\d+)\s*xp/i);
    
    return {
      won,
      gold: goldMatch ? parseInt(goldMatch[1]) : 0,
      xp: xpMatch ? parseInt(xpMatch[1]) : 0
    };
  }

  private async updateStatistics(result: any): Promise<void> {
    // Update bot statistics
    const stats = await this.storageManager.getBotStatus();
    if (stats?.statistics) {
      stats.statistics.actionsPerformed++;
      if (result.won) {
        stats.statistics.combatsWon++;
        stats.statistics.goldEarned += result.gold;
        stats.statistics.experienceGained += result.xp;
      } else {
        stats.statistics.combatsLost++;
      }
      
      await this.storageManager.saveBotStatus(stats);
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
