{"version": 3, "file": "main/index.iife.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;;;;;;;;;;;ACVA,qCAAqC;AAEa;AAGU;AACA;AACI;AACQ;AACF;AACA;AACJ;AACN;AACQ;AACE;AACV;AAErD,MAAM,SAAS;IAmBpB,YAAoB,cAA8B,EAAU,SAA6B;QAArE,mBAAc,GAAd,cAAc,CAAgB;QAAU,cAAS,GAAT,SAAS,CAAoB;QAlBjF,cAAS,GAAY,KAAK,CAAC;QAC3B,aAAQ,GAAY,KAAK,CAAC;QAC1B,oBAAe,GAAuB,IAAI,CAAC;QAC3C,mBAAc,GAA0B,IAAI,CAAC;QAgBnD,yBAAyB;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,oEAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,WAAW,GAAG,IAAI,oEAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,IAAI,wEAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACxE,IAAI,CAAC,iBAAiB,GAAG,IAAI,gFAAuB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAChF,IAAI,CAAC,gBAAgB,GAAG,IAAI,8EAAsB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC9E,IAAI,CAAC,gBAAgB,GAAG,IAAI,8EAAsB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,GAAG,IAAI,0EAAoB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,oEAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,eAAe,GAAG,IAAI,4EAAqB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,IAAI,+EAAsB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC9E,IAAI,CAAC,WAAW,GAAG,IAAI,qEAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAqB;QAC/B,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAE/C,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,mBAAmB;QACnB,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAE9B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAqB;QACxC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC9D,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,wBAAwB;IACpC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO;QAElC,IAAI,CAAC;YACH,iEAAiE;YACjE,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE9C,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,6CAAS,CAAC,OAAO;oBACpB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC5B,MAAM;gBACR,KAAK,6CAAS,CAAC,SAAS;oBACtB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC9B,MAAM;gBACR,KAAK,6CAAS,CAAC,aAAa;oBAC1B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM;gBACR,KAAK,6CAAS,CAAC,WAAW;oBACxB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC/B,MAAM;gBACR,KAAK,6CAAS,CAAC,SAAS;oBACtB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC9B,MAAM;gBACR,KAAK,6CAAS,CAAC,UAAU;oBACvB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC/B,MAAM;gBACR,KAAK,6CAAS,CAAC,KAAK;oBAClB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,6CAAS,CAAC,gBAAgB;oBAC7B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,6CAAS,CAAC,iBAAiB;oBAC9B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBACrC,MAAM;gBACR,KAAK,6CAAS,CAAC,YAAY;oBACzB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM;gBACR;oBACE,oBAAoB;oBACpB,MAAM;YACV,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAChE,4CAA4C;QAC9C,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO,6CAAS,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC;YACH,sCAAsC;YAEtC,gCAAgC;YAChC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC9D,OAAO,6CAAS,CAAC,OAAO,CAAC;YAC3B,CAAC;YAED,4CAA4C;YAC5C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBACjE,OAAO,6CAAS,CAAC,SAAS,CAAC;YAC7B,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBACjE,OAAO,6CAAS,CAAC,SAAS,CAAC;YAC7B,CAAC;YAED,2CAA2C;YAC3C,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;gBACtF,OAAO,6CAAS,CAAC,aAAa,CAAC;YACjC,CAAC;YAED,sCAAsC;YACtC,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;gBACxE,OAAO,6CAAS,CAAC,SAAS,CAAC;YAC7B,CAAC;YAED,+CAA+C;YAC/C,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC1E,OAAO,6CAAS,CAAC,UAAU,CAAC;YAC9B,CAAC;YAED,iCAAiC;YACjC,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;gBAClE,OAAO,6CAAS,CAAC,KAAK,CAAC;YACzB,CAAC;YAED,0CAA0C;YAC1C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBACrE,OAAO,6CAAS,CAAC,gBAAgB,CAAC;YACpC,CAAC;YAED,4CAA4C;YAC5C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;gBACjF,OAAO,6CAAS,CAAC,iBAAiB,CAAC;YACrC,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrH,OAAO,6CAAS,CAAC,YAAY,CAAC;YAChC,CAAC;YAED,OAAO,6CAAS,CAAC,IAAI,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,6CAAS,CAAC,IAAI,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,iDAAiD;QACjD,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,IAAI,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAErD,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,gBAAgB,GAAG,CAAC,aAAa,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;YAE3D,OAAO,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc;QACpB,8DAA8D;QAC9D,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC1F,CAAC;IAEO,cAAc;QACpB,8DAA8D;QAC9D,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC1F,CAAC;IAEO,sBAAsB;QAC5B,mCAAmC;QACnC,OAAO,IAAI,CAAC,CAAC,qBAAqB;IACpC,CAAC;IAEO,eAAe;QACrB,qCAAqC;QACrC,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/F,CAAC;IAEO,kBAAkB;QACxB,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;QAC1F,OAAO,CAAC,CAAC,iBAAiB,CAAC;IAC7B,CAAC;IAEO,eAAe;QACrB,mCAAmC;QACnC,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;QAC5E,OAAO,CAAC,CAAC,YAAY,CAAC;IACxB,CAAC;IAEO,kBAAkB;QACxB,sCAAsC;QACtC,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;QAClF,OAAO,CAAC,CAAC,YAAY,CAAC;IACxB,CAAC;IAEO,6BAA6B;QACnC,iDAAiD;QACjD,+DAA+D;QAC/D,OAAO,IAAI,CAAC,CAAC,qBAAqB;IACpC,CAAC;IAEO,wBAAwB;QAC9B,uDAAuD;QACvD,kEAAkE;QAClE,OAAO,IAAI,CAAC,CAAC,qBAAqB;IACpC,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI;YAAE,OAAO;QAExC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO;QAElC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAEhD,qCAAqC;YACrC,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBACjE,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC3D,CAAC;iBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBACxE,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ;YAAE,OAAO;QAE5C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ;YAAE,OAAO;QAE5C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO;YAAE,OAAO;QAEjF,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU;YAAE,OAAO;QAE9C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK;YAAE,OAAO;QAEzC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK;YAAE,OAAO;QAEzC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM;YAAE,OAAO;QAE1C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM;YAAE,OAAO;QAE1E,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;;;;;;;;;;;;;;;ACzZD,wDAAwD;AAKjD,MAAM,YAAY;IACvB,YAAoB,UAA8B,EAAU,eAA+B;QAAvE,eAAU,GAAV,UAAU,CAAoB;QAAU,oBAAe,GAAf,eAAe,CAAgB;QACzF,0EAA0E;IAC5E,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,+CAA+C;QAC/C,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;QAEnC,MAAM,CAAC,KAAK,GAAG,KAAK,EAAE,GAAG,IAA8B,EAAE,EAAE;YACzD,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;YAC3B,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAE,KAAiB,CAAC,GAAG,CAAC;YAEvE,gCAAgC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;gBACxD,0BAA0B;YAC5B,CAAC;YAED,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC;IACJ,CAAC;CACF;;;;;;;;;;;;;;;ACjCD,uDAAuD;AAIhD,MAAM,SAAS;IACpB;QACE,yBAAyB;IAC3B,CAAC;IAED,YAAY,CAAC,MAAiB;QAC5B,0BAA0B;QAC1B,MAAM,eAAe,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QACvE,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAsB,CAAC;QAEnF,IAAI,eAAe,IAAI,UAAU,IAAI,YAAY,EAAE,CAAC;YAClD,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAgB,CAAC;YAEjF,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACpB,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,oBAAoB;oBACjE,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAClC,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,mBAAmB;oBAChE,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAClC,YAAY,CAAC,WAAW,GAAG,UAAU,CAAC;gBACxC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,kEAAkE;gBAClE,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACrB,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,oCAAoC;oBACjF,UAAU,CAAC,WAAW,GAAG,oBAAoB,CAAC;oBAC9C,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,mBAAmB;oBAChE,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;oBAC7D,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEtC,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC9D,CAAC;IAEO,aAAa,CAAC,UAA6B;QACjD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAI,CAAC,cAAc;YAAE,OAAO;QAE5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QAEpD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAE3D,cAAc,CAAC,WAAW,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IACpJ,CAAC;IAEO,kBAAkB,CAAC,gBAAwB;QACjD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,WAAW,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,cAAc,CAAC,UAA6B;QAC1C,mCAAmC;QACnC,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QACrE,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,MAAM,EAAE,CAAC;QACzB,CAAC;QAED,mCAAmC;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACrD,KAAK,CAAC,EAAE,GAAG,qBAAqB,CAAC;QACjC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEjC,qDAAqD;QACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAE9D,QAAQ,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACxC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,kDAAkD;QAClD,YAAY,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAC5C,CAAC,CAAC,eAAe,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,2EAA2E;QAC3E,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;oBACvB,CAAC,CAAC,cAAc,EAAE,CAAC;oBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;oBACpB,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,sBAAsB;QACtB,MAAM,YAAY,GAAG,CAAC,CAAgB,EAAE,EAAE;YACxC,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACvB,KAAK,CAAC,MAAM,EAAE,CAAC;gBACf,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC;QACF,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAEO,qBAAqB,CAAC,UAA6B;QACzD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;QAC7B,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;;;KAWrB,CAAC;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,CAAC,SAAS,GAAG,kBAAkB,CAAC;QAC5C,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;KAS5B,CAAC;QAEF,YAAY,CAAC,SAAS,GAAG;;;;;;;;2BAQF,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE;8BAC5C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC;kCACxC,UAAU,CAAC,gBAAgB;mCAC1B,UAAU,CAAC,iBAAiB;;;;4BAInC,UAAU,CAAC,UAAU;6BACpB,UAAU,CAAC,WAAW;yBAC1B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;4BAIjF,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE;kCAChC,UAAU,CAAC,gBAAgB,CAAC,cAAc,EAAE;4BAClD,UAAU,CAAC,UAAU;;;KAG5C,CAAC;QAEF,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc,CAAC,YAAoB;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAEhE,OAAO,GAAG,KAAK,KAAK,OAAO,KAAK,OAAO,GAAG,CAAC;IAC7C,CAAC;IAED,gBAAgB,CAAC,KAAa,EAAE,OAAe,EAAE,OAAiD,MAAM;QACtG,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,CAAC,SAAS,GAAG,mCAAmC,IAAI,EAAE,CAAC;QACnE,YAAY,CAAC,SAAS,GAAG;;kBAEX,KAAK;aACV,OAAO;;;KAGf,CAAC;QAEF,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEpC,8BAA8B;QAC9B,UAAU,CAAC,GAAG,EAAE;YACd,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,iCAAiC;QACjC,YAAY,CAAC,aAAa,CAAC,wBAAwB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACnF,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;;;;;;;;;;;;;;;ACvND,uEAAuE;AAgBhE,MAAM,iBAAiB;IAO5B,YAAY,SAA6B,EAAE,cAA8B;QAJjE,cAAS,GAAY,KAAK,CAAC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC;QAG5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAuB;QACjC,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAExC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAElE,qBAAqB;YACrB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAuB;QACpD,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACzB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;oBACvD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvB,SAAS;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACxD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;oBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvB,SAAS;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAEvC,+BAA+B;gBAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,eAAe;gBAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC5D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAuB;QACvD,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,+BAA+B;QAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QAEnF,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAsB,EAAE,QAAQ,CAAC,CAAC;gBAC/E,IAAI,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;oBACnD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAoB,EAAE,QAAuB;QAC5E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;QAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QACzE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QAErF,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACnE,MAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,gBAAgB,CAAC;YAC3C,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAE/E,oCAAoC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEzD,wBAAwB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAEzD,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK;YACL,MAAM;YACN,SAAS;YACT,aAAa;YACb,SAAS,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,MAAmB,EAAE,QAAuB;QAChE,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAEpC,+CAA+C;QAC/C,IAAI,QAAQ,CAAC,sBAAsB,IAAI,MAAM,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAE1E,yBAAyB;QACzB,IAAI,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAE/D,uDAAuD;QACvD,IAAI,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,gBAAgB,IAAI,EAAE,CAAC;YAAE,OAAO,KAAK,CAAC;QAE3F,sCAAsC;QACtC,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,OAAsB,EAAE,QAAuB;QACtE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtC,wDAAwD;QACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACpB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,iDAAiD;YAC/E,CAAC;YACD,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,0BAA0B;QAC9D,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAmB,EAAE,QAAuB;QACrE,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,KAAK,iBAAiB,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAEhH,+BAA+B;QAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,MAAM,CAAC,EAAE,sBAAsB,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9G,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,4CAA4C,CAAC,CAAC;YACvF,IAAI,SAAS,EAAE,CAAC;gBACb,SAAyB,CAAC,KAAK,EAAE,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACL,YAA4B,CAAC,KAAK,EAAE,CAAC;YACxC,CAAC;QACH,CAAC;aAAM,CAAC;YACL,YAA4B,CAAC,KAAK,EAAE,CAAC;QACxC,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAW;QAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,0BAA0B;QAChD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;QAED,oBAAoB;QACpB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAEO,uBAAuB,CAAC,QAAuB;QACrD,mBAAmB;QACnB,IAAI,QAAQ,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,KAAa,EAAE,MAAc;QACtD,oCAAoC;QACpC,+DAA+D;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,MAAM,SAAS,GAAG,OAAO,GAAG,KAAK,CAAC;QAClC,MAAM,UAAU,GAAG,QAAQ,GAAG,MAAM,CAAC;QAErC,IAAI,SAAS,GAAG,EAAE,CAAC,CAAC,WAAW;QAC/B,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,8BAA8B;QAC1D,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,iCAAiC;QAEjE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEO,aAAa,CAAC,UAAkB,EAAE,QAAuB;QAC/D,uCAAuC;QACvC,+DAA+D;QAC/D,OAAO,KAAK,CAAC,CAAC,qBAAqB;IACrC,CAAC;IAEO,cAAc;QACpB,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;QAC5E,OAAO,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;IAC5D,CAAC;IAEO,eAAe;QACrB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;QAC/E,OAAO,QAAQ,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC;gBACpE,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,GAAG,EAAE;gBACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;gBAC/E,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC;YACF,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,0BAA0B;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa;YAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QAE1D,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC9C,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;QAEjE,MAAM,SAAS,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACpE,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO;YACL,GAAG;YACH,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAW;QACxC,wBAAwB;QACxB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACvD,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACpC,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC;gBAC3C,KAAK,CAAC,UAAU,CAAC,gBAAgB,IAAI,MAAM,CAAC,EAAE,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACjC,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;ACrVD,oEAAoE;AAuB7D,MAAM,iBAAiB;IAM5B,YAAY,SAA6B,EAAE,cAA8B;QAHjE,cAAS,GAAY,KAAK,CAAC;QAC3B,eAAU,GAAW,CAAC,CAAC;QAG7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAuB;QACjC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAuB;QACpD,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAElD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBACpD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,4BAA4B;oBACrD,SAAS;gBACX,CAAC;gBAED,4BAA4B;gBAC5B,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,SAAS;wBAAE,MAAM;oBAE3B,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;oBACzC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACnC,CAAC;gBAED,8BAA8B;gBAC9B,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;oBAC5B,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC3B,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,4BAA4B;YAEvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,wBAAwB;QACxB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAE9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAgB,CAAC;YAEhD,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAoB,EAAE,KAAa;QACjE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAChE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,kDAAkD,CAAC,CAAC;QACpG,MAAM,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAEzE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,SAAS,GAAG,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QACjE,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC;YAC/B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3F,8CAA8C;QAC9C,IAAI,IAAI,GAAwC,SAAS,CAAC;QAC1D,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAClF,IAAI,GAAG,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3F,IAAI,GAAG,YAAY,CAAC;QACtB,CAAC;QAED,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,IAAI;YACJ,QAAQ,EAAE,aAAa,GAAG,CAAC;YAC3B,aAAa;YACb,UAAU,EAAE,CAAC,CAAC,cAAc;YAC5B,cAAc,EAAE,CAAC,CAAC,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACrF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAgB,EAAE,QAAuB;QAClE,OAAO,CAAC,GAAG,CAAC,kCAAkC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAE5D,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAChD,MAAM;QACV,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,QAAuB;QACxE,IAAI,CAAC,KAAK,CAAC,cAAc;YAAE,OAAO;QAElC,OAAO,CAAC,GAAG,CAAC,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzE,gCAAgC;QAChC,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAExC,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAErD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAgB,EAAE,QAAuB;QAC5E,OAAO,CAAC,GAAG,CAAC,6CAA6C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAEvE,oCAAoC;QACpC,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;QAE5C,0BAA0B;QAC1B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAgB,EAAE,QAAuB;QACzE,OAAO,CAAC,GAAG,CAAC,0CAA0C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAEpE,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,yBAAyB,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChG,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,iBAAiB,GAAG,YAAY,CAAC,aAAa,CAAC,kDAAkD,CAAC,CAAC;gBACzG,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBACpE,iBAAiC,CAAC,KAAK,EAAE,CAAC;oBAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAuB;QACnD,MAAM,OAAO,GAAU,EAAE,CAAC;QAC1B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;QAEhF,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;YACrC,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;YAErF,IAAI,WAAW,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1E,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAEnD,2CAA2C;gBAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;oBAC7E,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI;wBACJ,OAAO;wBACP,MAAM,EAAE,YAAY;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAW;QACzC,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAErD,MAAM,CAAC,MAAsB,CAAC,KAAK,EAAE,CAAC;QAEvC,yBAAyB;QACzB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,0BAA0B;QAC1B,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;QAC9E,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,iCAAiC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,0CAA0C,CAAC,CAAC;QAE7F,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;gBAClD,MAAsB,CAAC,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,oCAAoC;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,8DAA8D,CAAC,CAAC;QAE/G,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAC7C,MAAsB,CAAC,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;QAEtF,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAC3C,MAAsB,CAAC,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,aAAa,CAAC;YAC/C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAgB;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,mCAAmC,KAAK,CAAC,EAAE,EAAE,CAAC;QAC/E,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAgB;QACtD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,oCAAoC,KAAK,CAAC,EAAE,EAAE,CAAC;QAChF,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,CAAC;IAEO,oBAAoB,CAAC,QAAuB;QAClD,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,CAAC,CAAC,kBAAkB;YAClE,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,yCAAyC;YACzC,OAAO,IAAI,CAAC,CAAC,aAAa;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAElD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,YAAY,EAAE,YAAY,CAAC,MAAM;YACjC,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;ACzWD,uEAAuE;AAwBhE,MAAM,sBAAsB;IAOjC,YAAY,SAA6B,EAAE,cAA8B;QAJjE,cAAS,GAAY,KAAK,CAAC;QAC3B,oBAAe,GAAW,CAAC,CAAC;QAC5B,iBAAY,GAAW,CAAC,CAAC;QAG/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,kBAAsC,EAAE,eAAgC;QAClF,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QAEjE,IAAI,CAAC;YACH,IAAI,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAA4B;QAC3D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElC,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBAE7D,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3B,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;oBACpE,MAAM;gBACR,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;oBAC7D,MAAM;gBACR,CAAC;gBAED,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC/C,IAAI,CAAC,eAAe,EAAE,CAAC;gBAEvB,iCAAiC;gBACjC,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAEzC,6BAA6B;gBAC7B,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACxC,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAyB;QACrD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAEhD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;oBACnE,MAAM;gBACR,CAAC;gBAED,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAC/C,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAElD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEpB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;gBAElE,yBAAyB;gBACzB,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,gBAAgB,EAAE,CAAC;oBACnF,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;oBACnE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,SAAS;gBACX,CAAC;qBAAM,CAAC;oBACN,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,iBAAiB,CAAC;YAEnD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9F,OAAO,CAAC,GAAG,CAAC,+CAA+C,QAAQ,EAAE,CAAC,CAAC;YACvE,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,oBAAoB,QAAQ,EAAE,CAAC;YAEhE,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,QAA4B;QAC9D,MAAM,SAAS,GAAyB,EAAE,CAAC;QAE3C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC;QAE3F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,MAAM,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAgB,CAAC;YAEnD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAChE,IAAI,QAAQ,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;oBACnE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,OAAoB,EAAE,KAAa;QACvE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACxE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;QACtF,MAAM,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAEnE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QAC1E,MAAM,WAAW,GAAG,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEzE,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC/D,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC1D,IAAI,SAAS,EAAE,CAAC;gBACd,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3G,CAAC;QACH,CAAC;QAED,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,KAAK;YACL,aAAa;YACb,WAAW,EAAE,CAAC,CAAC,WAAW;YAC1B,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,QAA4B,EAAE,QAA4B;QAC1F,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QAExC,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAC;QAErE,iCAAiC;QACjC,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YAChG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,SAA+B,EAAE,QAA4B;QACtF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAExC,yCAAyC;QACzC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;QAE5D,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAA4B,EAAE,QAA4B;QACtF,OAAO,CAAC,GAAG,CAAC,6CAA6C,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1E,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/G,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,MAAM,WAAW,GAAG,eAAe,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;QAC9F,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC;YAAE,OAAO;QAEjE,qCAAqC;QACrC,IAAI,QAAQ,CAAC,cAAc,KAAK,WAAW,IAAI,QAAQ,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAC3C,CAAC;QAEA,WAA2B,CAAC,KAAK,EAAE,CAAC;QACrC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,2BAA2B;QACvC,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,+CAA+C,CAAC,CAAC;QAChG,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;YACpE,eAA+B,CAAC,KAAK,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B;QACvC,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QAEpE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,eAAe,GAAG,GAAG,EAAE;gBAC3B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;gBACrF,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,2CAA2C,CAAC,CAAC;gBAE1F,IAAI,iBAAiB,IAAI,aAAa,EAAE,CAAC;oBACvC,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC;YAEF,UAAU,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,kCAAkC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,2CAA2C,CAAC,CAAC;QAC1F,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAC/D,aAA6B,CAAC,KAAK,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QACrF,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAChE,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAEtE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,OAAO;YACL,IAAI,EAAE,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;YAC3C,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,QAAQ;YAC9D,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK;YACzE,OAAO,EAAE,CAAC,CAAC,WAAW;YACtB,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE;YAC1C,WAAW,EAAE,CAAC,CAAC,UAAU;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,WAAwB,EAAE,QAAyB;QAC5E,OAAO,CAAC,GAAG,CAAC,uCAAuC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAEvE,uCAAuC;QACvC,IAAI,WAAW,CAAC,WAAW,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;YACnF,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACpD,SAAyB,CAAC,KAAK,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;QACpF,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,WAA2B,CAAC,KAAK,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,WAAwB,EAAE,QAAyB;QAC/E,OAAO,CAAC,GAAG,CAAC,yCAAyC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzE,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,QAAQ,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5E,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;gBAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;gBAC9E,IAAI,UAAU,EAAE,CAAC;oBACd,UAA0B,CAAC,KAAK,EAAE,CAAC;oBACpC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,wBAAwB;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,eAAe,GAAG,GAAG,EAAE;gBAC3B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;gBAClF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,qCAAqC,CAAC,CAAC;gBAEjF,IAAI,iBAAiB,IAAI,UAAU,EAAE,CAAC;oBACpC,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CAAC;YAEF,UAAU,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,gCAAgC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,QAA4B;QAC5D,4CAA4C;QAC5C,OAAO,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC,mBAAmB;IACvD,CAAC;IAEO,sBAAsB,CAAC,QAAyB;QACtD,yCAAyC;QACzC,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,mBAAmB;IACnD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;;AC1YD,+EAA+E;AAEW;AAgCnF,MAAM,iBAAiB;IAS5B,YAAY,SAA6B,EAAE,cAA8B;QANjE,cAAS,GAAY,KAAK,CAAC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QACxB,kBAAa,GAAW,CAAC,CAAC;QAC1B,iBAAY,GAAW,CAAC,CAAC;QACzB,cAAS,GAAW,CAAC,CAAC;QAG5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,aAAsC,EAAE,cAAwC;QAC1F,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,QAAiC;QACpE,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC;gBACH,mCAAmC;gBACnC,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC;gBAED,iBAAiB;gBACjB,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACtC,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBACnE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAAkC;QACtE,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxD,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;gBAE3E,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,SAAS;wBAAE,MAAM;oBAE3B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;wBAC9D,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;wBAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;wBAElC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACzB,CAAC;gBACH,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;gBACpE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAiC;QAC7D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE3E,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC3B,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAiC;QAC5D,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE1E,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;gBAEjC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,YAAY,CAAC;YAE9C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,aAAa,CAAC;YAE/C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,KAAK,GAAoB,EAAE,CAAC;QAClC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QAEjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAoB,EAAE,KAAa;QAClE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QAClF,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAEpF,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAEhF,kBAAkB;QAClB,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACxC,IAAI,OAAO;gBAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,OAAO,GAAgB,+CAAW,CAAC,KAAK,CAAC;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,KAAK,CAAC;aAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,IAAI,CAAC;aAC9D,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,GAAG,CAAC;QAEjE,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,OAAO;YACP,KAAK;YACL,SAAS;YACT,QAAQ,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAClE,SAAS;SACV,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,KAAK,GAAqB,EAAE,CAAC;QACnC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;QAElF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACxD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAoB,EAAE,KAAa;QACnE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAC3E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QAErF,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAEjF,mBAAmB;QACnB,MAAM,cAAc,GAAG,iBAAiB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC3E,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC7D,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAE3E,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,UAAU;YACV,aAAa;YACb,UAAU;YACV,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,KAAK,GAAoB,EAAE,CAAC;QAClC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC;QAEnF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAoB,EAAE,KAAa;QAClE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QAElF,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAElF,qCAAqC;QACrC,IAAI,OAAO,GAAgB,+CAAW,CAAC,KAAK,CAAC;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,KAAK,CAAC;aAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,IAAI,CAAC;aAC9D,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,GAAG,CAAC;QAEjE,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,OAAO;YACP,KAAK;YACL,UAAU;YACV,QAAQ,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACnE,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,KAAsB,EAAE,QAAiC;QACrF,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEjC,mBAAmB;YACnB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,cAAc;gBAAE,OAAO,KAAK,CAAC;YAE3D,uBAAuB;YACvB,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,oBAAoB;YACpB,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YACtE,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEtE,yBAAyB;YACzB,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACrD,CAAC;gBACF,IAAI,CAAC,SAAS;oBAAE,OAAO,KAAK,CAAC;YAC/B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,KAAuB,EAAE,QAAkC;QACtF,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,sBAAsB;YACtB,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAElC,6BAA6B;YAC7B,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;YACvE,IAAI,iBAAiB,GAAG,QAAQ,CAAC,eAAe;gBAAE,OAAO,KAAK,CAAC;YAE/D,mBAAmB;YACnB,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,cAAc;gBAAE,OAAO,KAAK,CAAC;YAE5D,yBAAyB;YACzB,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAClD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CACvD,CAAC;gBACF,IAAI,CAAC,SAAS;oBAAE,OAAO,KAAK,CAAC;YAC/B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,KAAsB,EAAE,QAAiC;QACtF,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEjC,4BAA4B;YAC5B,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa;gBAAE,OAAO,KAAK,CAAC;YAE3D,oDAAoD;YACpD,IAAI,QAAQ,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClG,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAmB;QACzC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,SAAS,OAAO,CAAC,CAAC;QAE/E,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,6BAA6B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QACtF,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,WAA2B,CAAC,KAAK,EAAE,CAAC;YAErC,iCAAiC;YACjC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,yCAAyC,CAAC,CAAC;YACxF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAoB;QAC3C,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC;QAElF,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,8BAA8B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACzF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,YAA4B,CAAC,KAAK,EAAE,CAAC;YAEtC,iCAAiC;YACjC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;YACzF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAmB;QACzC,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,UAAU,YAAY,CAAC,CAAC;QAEtF,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,6BAA6B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QACtF,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,WAA2B,CAAC,KAAK,EAAE,CAAC;YAErC,iCAAiC;YACjC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,yCAAyC,CAAC,CAAC;YACxF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,QAAiC;QAC7D,4BAA4B;QAC5B,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,QAAQ,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YACjF,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,uBAAuB,CAAC,QAAkC;QAChE,4BAA4B;QAC5B,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;AClhBD,iFAAiF;AAuB1E,MAAM,oBAAoB;IAM/B,YAAY,SAA6B,EAAE,cAA8B;QAHjE,iBAAY,GAAY,KAAK,CAAC;QAC9B,gBAAW,GAAW,CAAC,CAAC;QAG9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAA0B;QACzC,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAE9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,YAAY,GAAG;YACnB,qBAAqB;YACrB,cAAc;YACd,cAAc;YACd,eAAe;SAChB,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC;gBAChE,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,cAAc,CAAC,QAA0B;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QAC3D,OAAO,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAA0B;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QAE3D,OAAO,CAAC,GAAG,CAAC,2BAA2B,UAAU,kBAAkB,QAAQ,CAAC,WAAW,gBAAgB,CAAC,CAAC;QAEzG,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACtF,IAAI,OAAO;gBAAE,OAAO;QACtB,CAAC;QAED,wCAAwC;QACxC,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,cAAmB,EAAE,MAAc,EAAE,QAA0B;QAC9F,QAAQ,cAAc,CAAC,QAAQ,EAAE,CAAC;YAChC,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtD,KAAK,MAAM;gBACT,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACrD,KAAK,QAAQ;gBACX,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACvD,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzD;gBACE,OAAO,CAAC,GAAG,CAAC,2CAA2C,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAClF,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,QAA0B;QACtE,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,8BAA8B;QAC9B,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAqB,CAAC;QACtG,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QAEtF,IAAI,CAAC,aAAa,IAAI,CAAC,YAAY,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACnE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sBAAsB;QACtB,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACxC,aAAa,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEnE,sBAAsB;QACrB,YAA4B,CAAC,KAAK,EAAE,CAAC;QAEtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,gBAAgB,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,QAA0B;QACrE,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QAEjE,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5B,IAAI,eAAe,GAAG,MAAM,CAAC;QAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEhD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,eAAe,IAAI,CAAC;gBAAE,MAAM;YAEhC,IAAI,IAAI,CAAC,KAAK,IAAI,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC7B,eAAe,IAAI,IAAI,CAAC,KAAK,CAAC;gBAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,GAAG,eAAe,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,0BAA0B,WAAW,qBAAqB,CAAC,CAAC;QACxE,OAAO,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,QAA0B;QACvE,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,IAAI,eAAe,GAAG,MAAM,CAAC;QAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAElD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,eAAe,IAAI,CAAC;gBAAE,MAAM;YAEhC,IAAI,IAAI,CAAC,KAAK,IAAI,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/B,eAAe,IAAI,IAAI,CAAC,KAAK,CAAC;gBAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,GAAG,eAAe,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,0BAA0B,WAAW,uBAAuB,CAAC,CAAC;QAC1E,OAAO,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,QAA0B;QACzE,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAEhC,IAAI,eAAe,GAAG,MAAM,CAAC;QAE7B,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC3C,IAAI,eAAe,IAAI,CAAC;gBAAE,MAAM;YAEhC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;YAC9D,eAAe,IAAI,OAAO,CAAC;QAC7B,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,GAAG,eAAe,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,0BAA0B,WAAW,yBAAyB,CAAC,CAAC;QAC5E,OAAO,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAA0B;QACnD,MAAM,KAAK,GAAiB,EAAE,CAAC;QAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAE3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAC/C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAElD,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACjD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAA0B;QACrD,MAAM,KAAK,GAAiB,EAAE,CAAC;QAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;QAE9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAC/C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAEpD,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACjD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAoB,EAAE,KAAa;QAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAE5E,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAE5E,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,KAAK;YACL,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAC7D,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAoB,EAAE,KAAa;QAC/D,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACrE,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAE5E,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAC5E,MAAM,MAAM,GAAG,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAE/D,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,KAAK;YACL,OAAO,EAAE,SAAS;YAClB,MAAM;YACN,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAC7D,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,IAAgB,EAAE,QAA0B;QAClE,sBAAsB;QACtB,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY;YAAE,OAAO,KAAK,CAAC;QAErD,wCAAwC;QACxC,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAED,8BAA8B;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAgB;QACxC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC;QAE3E,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,wBAAwB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7F,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAChF,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,SAAyB,CAAC,KAAK,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAgB;QAC1C,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC;QAE/F,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/F,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAChF,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,SAAyB,CAAC,KAAK,EAAE,CAAC;YAEnC,iCAAiC;YACjC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;YACtF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,SAAiB;QAC3D,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,eAAe,SAAS,OAAO,CAAC,CAAC;QAEnF,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;QAEhF,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;YAChE,IAAI,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAC9E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;gBAClF,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;gBAEnE,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBACzD,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;oBAC3E,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;wBACrB,WAA2B,CAAC,KAAK,EAAE,CAAC;wBACrC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACvB,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,YAAY,CAAC;YAC9C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC1D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,sBAAsB,CAAC;YACxD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,aAAa,CAAC;YAC/C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,eAAe,CAAC;YACjD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO;YACL,WAAW,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE;YACxC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;AC9ZD,wEAAwE;AAoBjE,MAAM,mBAAmB;IAK9B,YAAY,SAA6B,EAAE,cAA8B;QAFjE,cAAS,GAAY,KAAK,CAAC;QAGjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAsB;QACvC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAEtD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAE9B,IAAI,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,6BAA6B,UAAU,CAAC,UAAU,6BAA6B,CAAC,CAAC;YAC7F,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAgB;QACtB,4CAA4C;QAC5C,MAAM,eAAe,GAAG;YACtB,2BAA2B;YAC3B,sBAAsB;YACtB,gBAAgB;YAChB,yBAAyB;SAC1B,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAE/C,+BAA+B;gBAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB;gBAC1D,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,OAAO;wBACL,OAAO;wBACP,OAAO;wBACP,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;qBAClD,CAAC;gBACJ,CAAC;gBAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe;gBAC7C,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtC,OAAO;wBACL,OAAO,EAAE,UAAU;wBACnB,OAAO,EAAE,GAAG;wBACZ,UAAU;qBACX,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAsB,EAAE,UAAsB;QACzE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,IAAI,CAAC;YACH,6CAA6C;YAC7C,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,cAAc,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC1C,IAAI,cAAc,EAAE,CAAC;oBACnB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBACpD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,mCAAmC;YACnC,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1D,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAC3D,IAAI,cAAc,EAAE,CAAC;oBACnB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;oBAChD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,wCAAwC;YACxC,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAC/C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAChD,IAAI,UAAU,EAAE,CAAC;oBACf,2CAA2C;oBAC3C,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBAC3D,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;wBAC1D,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,0CAA0C;QAC1C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC;QAED,2BAA2B;QAC3B,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,kDAAkD,CAAC,CAAC;QAClG,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wBAAwB;QACvB,cAA8B,CAAC,KAAK,EAAE,CAAC;QAExC,+BAA+B;QAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,2BAA2B;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1C,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,YAAsB;QAC1C,4CAA4C;QAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9C,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClE,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAgB;QACtB,MAAM,SAAS,GAAe,EAAE,CAAC;QAEjC,sCAAsC;QACtC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;QAE5E,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACtC,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;YAE5E,MAAM,IAAI,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,QAAQ,KAAK,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;YACjE,MAAM,WAAW,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAErE,0CAA0C;YAC1C,IAAI,UAAU,GAAG,EAAE,CAAC,CAAC,UAAU;YAC/B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,UAAU,GAAG,EAAE,CAAC;YACxD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAAE,UAAU,GAAG,EAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,UAAU,GAAG,EAAE,CAAC;YAEzD,SAAS,CAAC,IAAI,CAAC;gBACb,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAC1B,IAAI;gBACJ,UAAU;gBACV,KAAK;gBACL,WAAW,EAAE,CAAC,CAAC,WAAW;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAc;QACtC,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,kBAAkB,IAAI,CAAC,EAAE,wCAAwC,IAAI,CAAC,EAAE,uBAAuB,CAAC,CAAC;QAC1I,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjD,SAAyB,CAAC,KAAK,EAAE,CAAC;QAEnC,uBAAuB;QACvB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEvB,2BAA2B;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1C,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,QAAsB;QAC1C,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QACvD,CAAC;QAED,6BAA6B;QAC7B,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;QAE5E,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;YACzE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAE/D,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC;gBAAE,SAAS;YAE/E,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;YAE7E,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;gBAC9F,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;gBAC/D,SAAyB,CAAC,KAAK,EAAE,CAAC;gBAEnC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvB,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,iCAAiC,WAAW,EAAE,CAAC,CAAC;QAC5D,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,IAAI,WAAW,EAAE,CAAC;QAEnD,qBAAqB;QACrB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC;gBACpE,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAsB;QAC5C,IAAI,CAAC,QAAQ,CAAC,eAAe;YAAE,OAAO,KAAK,CAAC;QAE5C,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAE9B,2EAA2E;QAC3E,IAAI,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;gBACpF,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAsB;QACpD,iCAAiC;QACjC,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,+CAA+C;YAC/C,OAAO,IAAI,CAAC,CAAC,aAAa;QAC5B,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9C,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjD,IAAI,CAAC,WAAW;gBAChB,IAAI,CAAC,KAAK,GAAG,CAAC,CACf,CAAC;YAEF,IAAI,aAAa;gBAAE,OAAO,IAAI,CAAC;QACjC,CAAC;QAED,wBAAwB;QACxB,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC/C,6DAA6D;YAC7D,OAAO,IAAI,CAAC,CAAC,aAAa;QAC5B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;;AC3UD,uFAAuF;AAEtB;AA6B1D,MAAM,sBAAsB;IASjC,YAAY,SAA6B,EAAE,cAA8B;QANjE,cAAS,GAAY,KAAK,CAAC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC;QACtB,cAAS,GAAW,CAAC,CAAC;QACtB,eAAU,GAAW,CAAC,CAAC;QAG7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAkC;QAC5C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAkC;QAChE,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,IAAI,CAAC;gBACH,2BAA2B;gBAC3B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC7B,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACvD,CAAC;gBAED,4BAA4B;gBAC5B,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC9B,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACzD,CAAC;gBAED,4BAA4B;gBAC5B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC7B,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACxD,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;YAElD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;gBACjE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,WAAgB;QACpD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1E,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;gBAC5D,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC;gBAE7B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,YAAiB;QACtD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,mCAAmC;QACnC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAE7E,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,EAAE,CAAC;YAEjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,eAAoB;QACzD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAE7E,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,MAAM;YAE3B,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,aAAa,CAAC;YAE/C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,cAAc,CAAC;YAEhD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,KAAK,GAAiB,EAAE,CAAC;QAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAEhF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACpD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAoB,EAAE,KAAa;QAC/D,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAErE,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAC5E,MAAM,MAAM,GAAG,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACxD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QAEjE,qCAAqC;QACrC,IAAI,OAAO,GAAgB,+CAAW,CAAC,KAAK,CAAC;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,KAAK,CAAC;aAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,IAAI,CAAC;aAC9D,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,GAAG,CAAC;QAEjE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC/D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC;YAC/B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3F,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,OAAO;YACP,KAAK;YACL,KAAK;YACL,MAAM;YACN,aAAa;YACb,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC5D,MAAM,EAAE,KAAK,CAAC,mCAAmC;SAClD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;QAElF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACrD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAoB,EAAE,KAAa;QAChE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QACxE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACrF,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAErE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QACvF,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QACpF,MAAM,MAAM,GAAG,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACxD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QAEjE,qCAAqC;QACrC,IAAI,OAAO,GAAgB,+CAAW,CAAC,KAAK,CAAC;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,KAAK,CAAC;aAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,IAAI,CAAC;aAC9D,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,GAAG,CAAC;QAEjE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC/D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC;YAC/B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3F,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,OAAO;YACP,KAAK;YACL,UAAU;YACV,WAAW;YACX,aAAa;YACb,MAAM;YACN,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC5D,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,KAAmB,EAAE,QAAa;QAC7D,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,oBAAoB;YACpB,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEjD,uBAAuB;YACvB,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,oBAAoB;YACpB,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YACtE,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAEtE,yBAAyB;YACzB,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAY,EAAE,EAAE,CACzD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACrD,CAAC;gBACF,IAAI,CAAC,SAAS;oBAAE,OAAO,KAAK,CAAC;YAC/B,CAAC;YAED,yBAAyB;YACzB,IAAI,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAAE,OAAO,KAAK,CAAC;YAEpE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,KAAY,EAAE,QAAa;QACvD,gDAAgD;QAChD,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,oDAAoD;YACpD,OAAO,IAAI,CAAC,CAAC,aAAa;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,KAAoB,EAAE,QAAa;QAC/D,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,4CAA4C;YAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;gBAAE,OAAO,KAAK,CAAC;YAExC,oBAAoB;YACpB,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,MAAM;gBAAE,OAAO,KAAK,CAAC;YAErD,4DAA4D;YAC5D,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,gBAAgB;gBAAE,OAAO,KAAK,CAAC;YAEjE,oDAAoD;YACpD,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,IAAgB;QACpC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC;QAE7E,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/F,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAChF,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,SAAyB,CAAC,KAAK,EAAE,CAAC;YAEnC,iCAAiC;YACjC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;YACtF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAS,EAAE,QAAa;QAC7C,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEvD,8CAA8C;QAC9C,8DAA8D;IAChE,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAiB,EAAE,QAAa;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAErF,OAAO,CAAC,GAAG,CAAC,8BAA8B,SAAS,YAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAE5E,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,QAAQ,GAAG,WAAW,CAAC,aAAa,CAAC,+BAA+B,CAAqB,CAAC;QAChG,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QAEhF,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YACtC,QAAQ,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAE7D,SAAyB,CAAC,KAAK,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAiB,EAAE,QAAa;QACvD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;YAAE,OAAO;QAElD,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;QAEvF,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACzF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,YAA4B,CAAC,KAAK,EAAE,CAAC;YAEtC,sBAAsB;YACtB,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;YACzF,IAAI,aAAa,EAAE,CAAC;gBACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,mDAAmD;QACnD,2DAA2D;QAC3D,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,wBAAwB,CAAC,QAAkC;QACjE,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAChF,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,QAAQ,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC7E,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;;ACneD,+EAA+E;AAEb;AAiB3D,MAAM,uBAAuB;IAKlC,YAAY,SAA6B,EAAE,cAA8B;QAFjE,iBAAY,GAAY,KAAK,CAAC;QAGpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAyB;QAC7C,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAE9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,uBAAuB;YACvB,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACpD,CAAC;YAED,0BAA0B;YAC1B,IAAI,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;YAED,wBAAwB;YACxB,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAElE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEzC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,eAAe,CAAC;YAEjD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB;QACvC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO;QAE5B,OAAO,CAAC,GAAG,CAAC,+BAA+B,UAAU,OAAO,CAAC,CAAC;QAE9D,4CAA4C;QAC5C,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAqB,CAAC;QAC1F,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,8CAA8C,CAAC,CAAC;QAE1F,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,kBAAkB;QAClB,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAExC,sBAAsB;QACtB,SAAS,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAE/D,oBAAoB;QACnB,UAA0B,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,cAAmB;QAC3C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAEzE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,CAAC,MAAM,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,YAAiB;QACvC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEpE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4BAA4B,WAAW,CAAC,MAAM,QAAQ,CAAC,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,KAAK,GAAkB,EAAE,CAAC;QAEhC,iCAAiC;QACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QAEjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAgB,CAAC;YAE/C,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACrD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAoB,EAAE,KAAa;QAChE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACxE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAElE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,YAAY,GAAG,cAAc,EAAE,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;QACpE,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAE7E,qCAAqC;QACrC,IAAI,OAAO,GAAgB,+CAAW,CAAC,KAAK,CAAC;QAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,KAAK,CAAC;aAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,IAAI,CAAC;aAC9D,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,MAAM,CAAC;aAClE,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,GAAG,+CAAW,CAAC,GAAG,CAAC;QAEjE,6BAA6B;QAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;YACzC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YACzC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC;QAEvE,4BAA4B;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC;QAErE,mBAAmB;QACnB,MAAM,mBAAmB,GAAG,OAAO,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC;QAEjF,oCAAoC;QACpC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QAErF,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,OAAO;YACP,KAAK;YACL,KAAK;YACL,YAAY;YACZ,WAAW;YACX,YAAY;YACZ,OAAO,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC/D,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,KAAoB,EAAE,QAAa;QAChE,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAElC,8CAA8C;YAC9C,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,gBAAgB;gBAAE,OAAO,KAAK,CAAC;YAEjE,iCAAiC;YACjC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,eAAe;gBAAE,OAAO,KAAK,CAAC;YAEjE,4BAA4B;YAC5B,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,YAAoB,EAAE,EAAE,CACzE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAC7D,CAAC;gBACF,IAAI,CAAC,aAAa;oBAAE,OAAO,KAAK,CAAC;YACnC,CAAC;YAED,uBAAuB;YACvB,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5D,IAAI,CAAC,YAAY;oBAAE,OAAO,KAAK,CAAC;YAClC,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,KAAoB,EAAE,QAAa;QAC/D,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACzB,uBAAuB;YACvB,IAAI,CAAC,IAAI,CAAC,OAAO;gBAAE,OAAO,KAAK,CAAC;YAEhC,6BAA6B;YAC7B,IAAI,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAC;YAEnC,sBAAsB;YACtB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAClC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;oBACrC,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,IAAiB,EAAE,IAAS;QAClD,kBAAkB;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACpF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAiB;QACxC,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzD,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACzF,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC;YAAE,OAAO;QAElE,YAA4B,CAAC,KAAK,EAAE,CAAC;QACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAiB;QACtC,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC;QAE/E,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;QACnF,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC;YAAE,OAAO;QAE9D,UAA0B,CAAC,KAAK,EAAE,CAAC;QAEpC,wCAAwC;QACxC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACvF,IAAI,aAAa,EAAE,CAAC;YACjB,aAA6B,CAAC,KAAK,EAAE,CAAC;QACzC,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAyB;QACnD,6CAA6C;QAC7C,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO;YACzB,QAAQ,CAAC,WAAW,CAAC,OAAO;YAC5B,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAChC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAChE,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAEnE,OAAO;YACL,UAAU;YACV,aAAa;YACb,cAAc;YACd,aAAa,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;ACnXD,6EAA6E;AAyBtE,MAAM,qBAAqB;IAMhC,YAAY,SAA6B,EAAE,cAA8B;QAHjE,cAAS,GAAY,KAAK,CAAC;QAC3B,oBAAe,GAAW,CAAC,CAAC;QAGlC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAiC;QAC3C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAiC;QAC9D,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAE9B,uBAAuB;gBACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAExD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;oBAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,SAAS;gBACX,CAAC;gBAED,mCAAmC;gBACnC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAE5E,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;oBACzD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,SAAS;gBACX,CAAC;gBAED,4BAA4B;gBAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBAEtC,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;oBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACpD,CAAC;gBAED,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC/D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,YAAY,CAAC;YAE9C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAEjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAgB,CAAC;YAEhD,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;oBACrD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAoB,EAAE,KAAa;QACjE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAChE,MAAM,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;QACrF,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QACtE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;QACpF,MAAM,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;QAEjF,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,kBAAkB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAClE,MAAM,MAAM,GAAG,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAExD,0DAA0D;QAC1D,IAAI,UAAU,GAA+B,QAAQ,CAAC;QACtD,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACtD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrD,UAAU,GAAG,MAAM,CAAC;QACtB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC/F,UAAU,GAAG,MAAM,CAAC;QACtB,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC;QACjF,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACxC,IAAI,OAAO;gBAAE,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;YACpB,IAAI;YACJ,WAAW;YACX,MAAM;YACN,UAAU;YACV,WAAW,EAAE,CAAC,CAAC,kBAAkB;YACjC,WAAW,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACvE,YAAY;SACb,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,MAAe,EAAE,KAA4B;QACnE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAErC,yCAAyC;QACzC,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;YAElF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,gEAAgE;gBAChE,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,sDAAsD;QACtD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAY,EAAE,IAAyB;QAC9D,mBAAmB;QACnB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACvF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,MAAM,eAAe,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QACxD,IAAI,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uEAAuE;QACvE,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,MAAc;QACrC,6DAA6D;QAC7D,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE5C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,SAAS;YAAE,KAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,OAAO;YAAE,KAAK,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,0BAA0B;QAE5E,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAY;QACpC,OAAO,CAAC,GAAG,CAAC,sCAAsC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAEjE,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,yBAAyB,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,MAAM,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;QACzF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,YAA4B,CAAC,KAAK,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAY,EAAE,QAAiC;QACzE,OAAO,CAAC,GAAG,CAAC,mDAAmD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAE9E,0DAA0D;QAC1D,yDAAyD;QACzD,kDAAkD;QAElD,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9F,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,+CAA+C;QAC/C,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAY,EAAE,QAAiC;QAC/E,iEAAiE;QACjE,OAAO,CAAC,GAAG,CAAC,8CAA8C,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAEzE,uDAAuD;QACvD,oCAAoC;QACpC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAY,EAAE,QAAiC;QACnF,8DAA8D;QAC9D,OAAO,CAAC,GAAG,CAAC,kDAAkD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAE7E,uDAAuD;QACvD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAY,EAAE,QAAiC;QAC/E,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,8CAA8C,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAEzE,sDAAsD;QACtD,KAAK,MAAM,WAAW,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YAC7C,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/F,sCAAsC;gBACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAY;QACzC,6CAA6C;QAC7C,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QACnF,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,gDAAgD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;YAC1E,WAA2B,CAAC,KAAK,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,QAAiC;QAC5D,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;ACxUD,uEAAuE;AAiBhE,MAAM,iBAAiB;IAO5B,YAAY,SAA6B,EAAE,cAA8B;QAJjE,cAAS,GAAY,KAAK,CAAC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QACxB,eAAU,GAAW,CAAC,CAAC;QAG7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAuB;QACjC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAExC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,YAAY,CAAC;YAE9C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAuB;QACpD,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACzB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;oBACvD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,SAAS;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACxD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;oBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,SAAS;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAE/C,mDAAmD;gBACnD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,gBAAgB;gBAC7D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC5D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAuB;QACvD,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,+BAA+B;QAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QAEnF,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAsB,EAAE,QAAQ,CAAC,CAAC;gBAC7E,IAAI,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;oBACnD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAoB,EAAE,QAAuB;QAC1E,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACpE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;QACrF,MAAM,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAEzE,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3E,MAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,gBAAgB,CAAC;YAC3C,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAE/E,MAAM,QAAQ,GAAG,eAAe,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC9C,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;QAE1E,OAAO;YACL,EAAE;YACF,IAAI;YACJ,KAAK;YACL,KAAK;YACL,IAAI;YACJ,QAAQ;YACR,SAAS,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,MAAmB,EAAE,QAAuB;QAChE,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAEpC,yBAAyB;QACzB,IAAI,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAE/D,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAEnE,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,gBAAgB,GAAG,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAC;QAE5F,sCAAsC;QACtC,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAC/B,yCAAyC;YACzC,OAAO,IAAI,CAAC,CAAC,qBAAqB;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,OAAsB,EAAE,QAAuB;QACtE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtC,uEAAuE;QACvE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACpB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;gBACrC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,uCAAuC;YACnE,CAAC;YACD,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,oBAAoB;QAC9C,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAmB,EAAE,QAAuB;QACrE,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,KAAK,WAAW,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAEpG,+BAA+B;QAC/B,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,MAAM,CAAC,EAAE,sBAAsB,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9G,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAEA,YAA4B,CAAC,KAAK,EAAE,CAAC;QAEtC,yBAAyB;QACzB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAW,EAAE,MAAmB;QAChE,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,IAAI,cAAc,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC;YAE/B,wCAAwC;YACxC,IAAI,MAAM,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,iBAAiB;gBACxC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAEO,eAAe,CAAC,UAAkB;QACxC,iDAAiD;QACjD,OAAO,CAAC,GAAG,CAAC,wBAAwB,UAAU,sBAAsB,CAAC,CAAC;IACxE,CAAC;IAEO,uBAAuB,CAAC,QAAuB;QACrD,wBAAwB;QACxB,IAAI,QAAQ,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iCAAiC;QACjC,IAAI,QAAQ,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC1D,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB;QACzB,iDAAiD;QACjD,MAAM,aAAa,GAAG,QAAQ,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;QAE9E,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YACzD,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC5F,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,GAAG,EAAE;gBACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+CAA+C,CAAC,CAAC;gBAC9F,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC;YACF,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,oCAAoC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,+CAA+C,CAAC,CAAC;QAC9F,IAAI,CAAC,aAAa;YAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QAE1D,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC9C,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC;YAC1C,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;QAEjE,MAAM,SAAS,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACpE,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO;YACL,GAAG;YACH,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAW;QACxC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACvD,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACpC,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC;gBAC3C,KAAK,CAAC,UAAU,CAAC,gBAAgB,IAAI,MAAM,CAAC,EAAE,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACjC,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;AClVD,sEAAsE;AAyB/D,MAAM,sBAAsB;IAOjC,YAAY,SAA6B,EAAE,cAA8B;QAJjE,cAAS,GAAY,KAAK,CAAC;QAC3B,eAAU,GAAW,CAAC,CAAC;QACvB,aAAQ,GAAW,CAAC,CAAC;QAG3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAA4B;QACtC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAElB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QAEjE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,OAAO,iBAAiB,CAAC;YAEnD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,QAA4B;QAC9D,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAEhD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;oBACxD,MAAM;gBACR,CAAC;gBAED,iBAAiB;gBACjB,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAErD,eAAe;gBACf,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBACnB,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;gBAED,kBAAkB;gBAClB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACtB,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACtC,CAAC;gBAED,gBAAgB;gBAChB,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAEpD,wBAAwB;gBACxB,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBAC5C,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAE5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QACxE,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAC9E,MAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;QAEjF,MAAM,QAAQ,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC/D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC;YAC/B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3F,MAAM,QAAQ,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,MAAM,CAAC;QAC5D,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE3D,MAAM,eAAe,GAAG,kBAAkB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC7E,MAAM,gBAAgB,GAAG,eAAe,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3E,MAAM,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAE9E,MAAM,gBAAgB,GAAG,mBAAmB,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QAC/E,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9E,MAAM,eAAe,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAEjF,OAAO;YACL,QAAQ,EAAE,aAAa,GAAG,CAAC;YAC3B,aAAa;YACb,WAAW;YACX,UAAU;YACV,WAAW;YACX,cAAc;YACd,YAAY;YACZ,eAAe;SAChB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAA4B,EAAE,MAAwB;QAC1F,mCAAmC;QACnC,IAAI,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAC3E,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;QAED,wBAAwB;QACxB,MAAM,mBAAmB,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC;QACjF,IAAI,mBAAmB,GAAG,EAAE,EAAE,CAAC;YAC7B,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjC,CAAC;YAED,IAAI,QAAQ,CAAC,aAAa,IAAI,mBAAmB,GAAG,EAAE,EAAE,CAAC;gBACvD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAA4B;QAClD,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,uCAAuC,CAAC,CAAC;YACpF,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;gBAC3D,WAA2B,CAAC,KAAK,EAAE,CAAC;gBACrC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvB,OAAO;YACT,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,qCAAqC,CAAC,CAAC;YACjF,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAC1D,UAA0B,CAAC,KAAK,EAAE,CAAC;gBACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,yCAAyC,CAAC,CAAC;QACvF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACtD,YAA4B,CAAC,KAAK,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,4CAA4C,CAAC,CAAC;QAC7F,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YACzD,eAA+B,CAAC,KAAK,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAA4B;QACnD,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;YACxF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBACpC,MAAsB,CAAC,KAAK,EAAE,CAAC;oBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;YACxF,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBACpC,MAAsB,CAAC,KAAK,EAAE,CAAC;oBAChC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAA4B;QACvD,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,6CAA6C,CAAC,CAAC;YAChG,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrE,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;gBAClD,iBAAiC,CAAC,KAAK,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,QAA4B,EAAE,MAAwB;QACzF,iDAAiD;QACjD,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3C,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAC3D,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAChC,OAAO;YACT,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxD,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QAE1E,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAC/D,MAAM,IAAI,GAAG,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YAElE,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5D,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAsB,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAqB,EAAE,CAAC;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QAE1E,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAsB,CAAC,CAAC;YACjE,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAoB;QACjD,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;QAEnF,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;QACnE,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5D,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAE/D,OAAO;YACL,IAAI;YACJ,MAAM;YACN,SAAS;YACT,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACpD,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAoB;QAC3C,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzD,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QAC1E,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;YAC/D,IAAI,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBACnD,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;gBACnF,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC1D,YAA4B,CAAC,KAAK,EAAE,CAAC;oBACtC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAA4B;QAC7D,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEjD,KAAK,MAAM,aAAa,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YACpD,MAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,wBAAwB,aAAa,IAAI,CAAC,CAAC;YAC9F,IAAI,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzE,OAAO,CAAC,GAAG,CAAC,6CAA6C,aAAa,EAAE,CAAC,CAAC;gBACzE,mBAAmC,CAAC,KAAK,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,QAA4B;QAC3D,kCAAkC;QAClC,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC1C,IAAI,WAAW,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;gBACzE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEhD,OAAO;YACL,GAAG,MAAM;YACT,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;;;;;;;;;;;;;;;;AC3YD,gCAAgC;AAsBhC,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,wCAAyB;IACzB,sCAAuB;IACvB,kCAAmB;IACnB,oCAAqB;AACvB,CAAC,EALW,WAAW,KAAX,WAAW,QAKtB;AA2BD,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,uCAAmB;IACnB,yCAAqB;IACrB,mCAAe;IACf,yCAAqB;AACvB,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,QAK3B;;;;;;;;;;;;;;;;;;;AC3DD,6CAA6C;AAmC7C,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,8CAA+B;IAC/B,gDAAiC;IACjC,4CAA6B;IAC7B,8CAA+B;IAC/B,kDAAmC;IACnC,4CAA6B;IAC7B,8BAAe;AACjB,CAAC,EARW,WAAW,KAAX,WAAW,QAQtB;AA2DD,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,0CAAuB;IACvB,8CAA2B;IAC3B,oDAAiC;AACnC,CAAC,EAJW,eAAe,KAAf,eAAe,QAI1B;AAoBD,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,mCAAe;IACf,iCAAa;IACb,uCAAmB;IACnB,yCAAqB;IACrB,qCAAiB;IACjB,qCAAiB;AACnB,CAAC,EAPW,gBAAgB,KAAhB,gBAAgB,QAO3B;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,gCAAgB;IAChB,kCAAkB;IAClB,yCAAyB;IACzB,yCAAyB;AAC3B,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AAED,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,uCAAqB;IACrB,yCAAuB;IACvB,oCAAkB;IAClB,sCAAoB;AACtB,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;;;;;;;;;;;;;;;;;;ACnJD,qCAAqC;AAgBrC,IAAY,SAuBX;AAvBD,WAAY,SAAS;IACnB,0BAAa;IACb,gCAAmB;IACnB,oCAAuB;IACvB,oCAAuB;IACvB,wCAA2B;IAC3B,4CAA+B;IAC/B,sCAAyB;IACzB,4BAAe;IACf,0CAA6B;IAC7B,kDAAqC;IACrC,oDAAuC;IACvC,0CAA6B;IAC7B,8CAAiC;IACjC,gDAAmC;IACnC,gCAAmB;IACnB,kCAAqB;IACrB,oCAAuB;IACvB,gDAAmC;IACnC,kDAAqC;IACrC,kDAAqC;IACrC,gDAAmC;IACnC,gDAAmC;AACrC,CAAC,EAvBW,SAAS,KAAT,SAAS,QAuBpB;AAqBD,IAAY,QAUX;AAVD,WAAY,QAAQ;IAClB,+BAAmB;IACnB,iCAAqB;IACrB,qCAAyB;IACzB,6BAAiB;IACjB,+BAAmB;IACnB,mCAAuB;IACvB,2BAAe;IACf,qCAAyB;IACzB,mDAAuC;AACzC,CAAC,EAVW,QAAQ,KAAR,QAAQ,QAUnB;AAkBD,IAAY,WAIX;AAJD,WAAY,WAAW;IACrB,gCAAiB;IACjB,gCAAiB;IACjB,4BAAa;AACf,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,2CAAuB;IACvB,yCAAqB;AACvB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;;;;;;;;;;;;;;;;;;;AClGD,2CAA2C;AAoD3C,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,+CAAS;IACT,+CAAS;IACT,6CAAQ;IACR,iDAAU;IACV,iDAAU;IACV,2CAAO;AACT,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAED,IAAY,QAkBX;AAlBD,WAAY,QAAQ;IAClB,6BAAiB;IACjB,6BAAiB;IACjB,6BAAiB;IACjB,2BAAe;IACf,6BAAiB;IACjB,2BAAe;IACf,yBAAa;IACb,6BAAiB;IACjB,6BAAiB;IACjB,2CAA+B;IAC/B,+BAAmB;IACnB,6BAAiB;IACjB,mCAAuB;IACvB,2CAA+B;IAC/B,2BAAe;IACf,6BAAiB;IACjB,uCAA2B;AAC7B,CAAC,EAlBW,QAAQ,KAAR,QAAQ,QAkBnB;AAkCD,IAAY,SASX;AATD,WAAY,SAAS;IACnB,4BAAe;IACf,wCAA2B;IAC3B,sCAAyB;IACzB,gCAAmB;IACnB,0BAAa;IACb,oCAAuB;IACvB,8BAAiB;IACjB,oCAAuB;AACzB,CAAC,EATW,SAAS,KAAT,SAAS,QASpB;AAgBD,IAAY,OAOX;AAPD,WAAY,OAAO;IACjB,4BAAiB;IACjB,4BAAiB;IACjB,wBAAa;IACb,8BAAmB;IACnB,0BAAe;IACf,8BAAmB;AACrB,CAAC,EAPW,OAAO,KAAP,OAAO,QAOlB;AASD,IAAY,YAQX;AARD,WAAY,YAAY;IACtB,yCAAyB;IACzB,mCAAmB;IACnB,+BAAe;IACf,+BAAe;IACf,yCAAyB;IACzB,iCAAiB;IACjB,+BAAe;AACjB,CAAC,EARW,YAAY,KAAZ,YAAY,QAQvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClKD,iDAAiD;AAErB;AACA;AACG;AACJ;AACL;AACD;AACE;;;;;;;;;;;;ACRvB,8CAA8C;;;;;;;;;;;;;;;;;;;;;ACA9C,8CAA8C;AAsC9C,IAAY,UAaX;AAbD,WAAY,UAAU;IACpB,mCAAqB;IACrB,2CAA6B;IAC7B,2CAA6B;IAC7B,mDAAqC;IACrC,6CAA+B;IAC/B,6CAA+B;IAC/B,qDAAuC;IACvC,2CAA6B;IAC7B,qCAAuB;IACvB,yCAA2B;IAC3B,qCAAuB;IACvB,qDAAuC;AACzC,CAAC,EAbW,UAAU,KAAV,UAAU,QAarB;AAOD,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,yCAAuB;IACvB,+BAAa;IACb,mCAAiB;IACjB,+BAAa;AACf,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;AAYD,IAAY,iBAgBX;AAhBD,WAAY,iBAAiB;IAC3B,0DAAqC;IACrC,0DAAqC;IACrC,sDAAiC;IACjC,sDAAiC;IACjC,wEAAmD;IACnD,kEAA6C;IAC7C,oEAA+C;IAC/C,8DAAyC;IACzC,kEAA6C;IAC7C,gFAA2D;IAC3D,8CAAyB;IACzB,sEAAiD;IACjD,8DAAyC;IACzC,8EAAyD;IACzD,oDAA+B;AACjC,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,QAgB5B;AA6DD,IAAY,qBAGX;AAHD,WAAY,qBAAqB;IAC/B,gDAAuB;IACvB,gEAAuC;AACzC,CAAC,EAHW,qBAAqB,KAArB,qBAAqB,QAGhC;AAmDD,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,2CAAyB;IACzB,uCAAqB;IACrB,mCAAiB;AACnB,CAAC,EAJW,cAAc,KAAd,cAAc,QAIzB;AAED,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,yCAAiB;IACjB,yCAAiB;IACjB,qCAAa;AACf,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,QAI/B;;;;;;;;;;;;;;;;;;;ACxND,yBAAyB;AAYzB,IAAY,gBAQX;AARD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,uCAAmB;IACnB,uCAAmB;IACnB,mCAAe;IACf,+CAA2B;IAC3B,6CAAyB;IACzB,+CAA2B;AAC7B,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,QAQ3B;AAiCD,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,4BAAe;IACf,8BAAiB;IACjB,4BAAe;IACf,wCAA2B;AAC7B,CAAC,EALW,SAAS,KAAT,SAAS,QAKpB;AAED,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,kCAAmB;IACnB,sCAAuB;IACvB,kCAAmB;IACnB,gCAAiB;IACjB,kCAAmB;IACnB,4BAAa;AACf,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAcD,IAAY,SAWX;AAXD,WAAY,SAAS;IACnB,0BAAa;IACb,8BAAiB;IACjB,8BAAiB;IACjB,kCAAqB;IACrB,4BAAe;IACf,kCAAqB;IACrB,4BAAe;IACf,4BAAe;IACf,0BAAa;IACb,0BAAa;AACf,CAAC,EAXW,SAAS,KAAT,SAAS,QAWpB;AAeD,IAAY,cASX;AATD,WAAY,cAAc;IACxB,uCAAqB;IACrB,6BAAW;IACX,6BAAW;IACX,2CAAyB;IACzB,2CAAyB;IACzB,qCAAmB;IACnB,iCAAe;IACf,6BAAW;AACb,CAAC,EATW,cAAc,KAAd,cAAc,QASzB;;;;;;;;;;;;;;;;ACpHD,0EAA0E;AAaxD;AAEX,MAAM,sBAAsB;IACjC,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,CAAC;gBACb,gBAAgB,EAAE,IAAI;gBACtB,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;gBACjB,wBAAwB,EAAE,IAAI;gBAC9B,cAAc,EAAE,CAAC;aAClB;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACvB,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,mDAAe,CAAC,SAAS;gBACpC,eAAe,EAAE,IAAI;aACtB;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,oDAAgB,CAAC,KAAK;oBAChC,QAAQ,EAAE,EAAE;iBACb;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,oDAAgB,CAAC,IAAI;oBAC/B,QAAQ,EAAE,EAAE;iBACb;gBACD,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,kDAAc,CAAC,YAAY;gBACrC,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,IAAI;gBACrB,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,oDAAgB,CAAC,KAAK;gBAC9B,QAAQ,EAAE,gDAAY,CAAC,SAAS;gBAChC,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,GAAG;gBACjB,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,EAAE;gBACX,cAAc,EAAE,EAAE;aACnB;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE;oBACX,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,eAAe,EAAE,KAAK;oBACtB,iBAAiB,EAAE,CAAC;oBACpB,qBAAqB,EAAE,KAAK;iBAC7B;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,EAAE;oBACT,eAAe,EAAE,KAAK;iBACvB;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,CAAC;iBACd;gBACD,cAAc,EAAE,kDAAc,CAAC,MAAM;aACtC;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,KAAK;gBACrB,4BAA4B,EAAE,KAAK;gBACnC,2BAA2B,EAAE,KAAK;gBAClC,iBAAiB,EAAE,kBAAyB;gBAC5C,eAAe,EAAE;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;aACF;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,KAAK;gBACpB,gBAAgB,EAAE,KAAK;gBACvB,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,EAAE;gBACrB,aAAa,EAAE,EAAE;gBACjB,gBAAgB,EAAE,IAAI;aACvB;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,IAAI;gBACd,gBAAgB,EAAE,EAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,CAAC;gBACZ,mBAAmB,EAAE,KAAK;gBAC1B,eAAe,EAAE,KAAK;gBACtB,sBAAsB,EAAE,IAAI;gBAC5B,WAAW,EAAE,KAAK;gBAClB,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,KAAK;gBACrB,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,KAAK;gBACtB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,KAAK;gBACpB,mBAAmB,EAAE,KAAK;gBAC1B,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,EAAE;gBACrB,aAAa,EAAE,EAAE;gBACjB,gBAAgB,EAAE,IAAI;gBACtB,iBAAiB,EAAE,KAAK;aACzB;YACD,WAAW,EAAE;gBACX,OAAO,EAAE,KAAK;gBACd,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,EAAE;gBACZ,GAAG,EAAE,EAAE;gBACP,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,yDAAqB,CAAC,SAAS;gBAC/C,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,CAAC;aAChB;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,CAAC;aACpB;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,EAAE;gBACP,kBAAkB,EAAE,IAAI;gBACxB,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,KAAK;aAChB;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;gBACV,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE,EAAE;gBAClB,IAAI,EAAE,kDAAc,CAAC,MAAM;gBAC3B,UAAU,EAAE,wDAAoB,CAAC,MAAM;gBACvC,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,KAAK;gBACpB,iBAAiB,EAAE,KAAK;gBACxB,SAAS,EAAE,KAAK;gBAChB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;gBACtB,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,KAAK;gBAC1B,aAAa,EAAE,KAAK;gBACpB,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,EAAE;aACd;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,KAAK;gBACnB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,CAAC,+CAAW,CAAC,KAAK,EAAE,+CAAW,CAAC,KAAK,CAAC;gBACrD,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,EAAE;gBACb,aAAa,EAAE,EAAE;gBACjB,kBAAkB,EAAE,CAAC,+CAAW,CAAC,KAAK,CAAC;aACxC;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,GAAG;gBACnB,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,EAAE;aACf;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,EAAE;aACV;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,GAAG;gBAClB,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,KAAK;oBACnB,aAAa,EAAE,CAAC,+CAAW,CAAC,IAAI,EAAE,+CAAW,CAAC,MAAM,CAAC;oBACrD,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,GAAG;oBACb,SAAS,EAAE,EAAE;oBACb,kBAAkB,EAAE,EAAE;iBACvB;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,EAAE;iBACV;gBACD,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,KAAK;oBAChB,YAAY,EAAE,GAAG;oBACjB,gBAAgB,EAAE,GAAG;oBACrB,aAAa,EAAE,CAAC,+CAAW,CAAC,MAAM,EAAE,+CAAW,CAAC,MAAM,CAAC;iBACxD;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;aACjB;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE;oBACd,gBAAgB,EAAE,IAAI;oBACtB,cAAc,EAAE,KAAK;oBACrB,QAAQ,EAAE,IAAI;iBACf;aACF;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACf;SACF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,YAAkC;QACzD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,MAAW,EAAE,MAAW;QAC/C,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3F,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;;;;;;;;;;;;;;;ACzSD,iDAAiD;AAI1C,MAAM,kBAAkB;IAA/B;QACU,aAAQ,GAAY,KAAK,CAAC;QAC1B,YAAO,GAAqB;YAClC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IA8GJ,CAAC;IA5GC;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,mDAAmD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW;QACjB,OAAO,kCAAkC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW,EAAE,QAAiB,KAAK;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QAEF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,0BAA0B;YAC1D,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5C,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAuC,EAAE,OAAe,WAAW;QAC1E,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAuC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;CACF;;;;;;;;;;;;;;;;ACvHD,gDAAgD;AAGY;AAErD,MAAM,cAAc;IAA3B;QACmB,iBAAY,GAAG;YAC9B,QAAQ,EAAE,aAAa;YACvB,UAAU,EAAE,eAAe;YAC3B,OAAO,EAAE,YAAY;YACrB,cAAc,EAAE,mBAAmB;YACnC,WAAW,EAAE,gBAAgB;YAC7B,YAAY,EAAE,iBAAiB;YAC/B,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE,eAAe;SACnB,CAAC;IAuPb,CAAC;IArPC;;OAEG;IACK,wBAAwB;QAC9B,OAAO,OAAO,MAAM,KAAK,WAAW;YAC7B,MAAM,CAAC,OAAO;YACd,MAAM,CAAC,OAAO,CAAC,KAAK;YACpB,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,UAAU,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAChE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,QAA8B;QAC/D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,IAAI,EAAE,CAAC;YACjE,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAClE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC9E,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC1D,OAAO,cAAc,CAAC,CAAC,CAAC,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;YACpI,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC1E,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE1D,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,6CAA6C;gBAC7C,OAAO,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;YACxD,CAAC;YAED,qEAAqE;YACrE,OAAO,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,2BAA2B;YAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC1D,OAAO,cAAc,CAAC,CAAC,CAAC,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;QACpI,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAA8B;QAC/C,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC9E,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAElE,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBAC7B,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,eAAe;aAC9C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,2BAA2B;YAC3B,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC5E,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5C,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAiB;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBAC7B,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,MAAM;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,2BAA2B;YAC3B,IAAI,CAAC;gBACH,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7E,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,aAAa,CAAC,CAAC;gBAC3E,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEpD,OAAO;gBACL,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC5C,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAChD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBAC9C,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;gBACvD,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;gBACjD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;gBACnD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;YAE/C,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,MAAW,EAAE,MAAW;QACxC,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAClF,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;YAED,oCAAoC;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAwB,EAAE,CAAC;YAE3C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACvC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClB,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvC,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;CACF;;;;;;;UCtQD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;ACNA,kDAAkD;AAEW;AACM;AACT;AACjB;AACM;AACN;AAEzC,MAAM,kBAAkB;IAWtB;QAJQ,kBAAa,GAAY,KAAK,CAAC;QAE/B,aAAQ,GAAuB,IAAI,CAAC;QAG1C,IAAI,CAAC,SAAS,GAAG,IAAI,2EAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,kEAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,kDAAS,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,wDAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,IAAI,kDAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,SAAS,GAAG;YACf,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,6CAAS,CAAC,IAAI;YAC7B,UAAU,EAAE;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,CAAC;gBACnB,iBAAiB,EAAE,CAAC;gBACpB,UAAU,EAAE,CAAC;gBACb,gBAAgB,EAAE,CAAC;gBACnB,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,CAAC;aACf;SACF,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YAEnE,gBAAgB;YAChB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAE1B,sBAAsB;YACtB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAEnC,yBAAyB;YACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,+BAA+B;YAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/B,CAAC;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACrE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAI,KAAe,CAAC,OAAO,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,iDAAiD;YACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,OAAO;YACL,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,CAAC;gBACb,wBAAwB,EAAE,IAAI;gBAC9B,WAAW,EAAE,IAAI;gBACjB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,IAAI;gBACnB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;aACvB;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,aAAa,EAAE,IAAI;gBACnB,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACvB,SAAS,EAAE,WAAkB;gBAC7B,eAAe,EAAE,IAAI;gBACrB,OAAO,EAAE,IAAI;aACd;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,OAAc;oBACxB,QAAQ,EAAE,EAAE;iBACb;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,MAAa;oBACvB,QAAQ,EAAE,EAAE;iBACb;gBACD,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,GAAU;gBACpB,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,EAAE;gBACnB,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,OAAc;gBACtB,QAAQ,EAAE,KAAY;gBACtB,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,GAAG;gBACjB,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,EAAE;gBACX,cAAc,EAAE,EAAE;aACnB;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE;oBACX,OAAO,EAAE,IAAI;oBACb,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,eAAe,EAAE,KAAK;oBACtB,iBAAiB,EAAE,CAAC;oBACpB,qBAAqB,EAAE,IAAI;iBAC5B;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,IAAI;oBACb,KAAK,EAAE,EAAE;oBACT,eAAe,EAAE,KAAK;iBACvB;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD,cAAc,EAAE,QAAe;aAChC;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,IAAI;gBACpB,4BAA4B,EAAE,KAAK;gBACnC,2BAA2B,EAAE,KAAK;gBAClC,iBAAiB,EAAE,kBAAyB;gBAC5C,eAAe,EAAE;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;aACF;SACK,CAAC;IACX,CAAC;IAEO,mBAAmB;QACzB,+CAA+C;QAC/C,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,GAAG,EAAE;YAC1C,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC/C,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,KAAU,EAAE,EAAE;YAC1D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,GAAG,EAAE;YAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,wDAAwD;QACxD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAC3C,IAAI,KAAK,CAAC,GAAG,KAAK,eAAe,EAAE,CAAC;gBAClC,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,2CAA2C;QAC3C,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3B,qEAAqE;QACrE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE5C,iFAAiF;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAC9D,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,YAAY,CAAC,CAAC;gBAC7D,8EAA8E;gBAC9E,IAAI,CAAC,SAAS,GAAG;oBACf,GAAG,IAAI,CAAC,SAAS;oBACjB,GAAG,YAAY;oBACf,yDAAyD;oBACzD,SAAS,EAAE,YAAY,CAAC,QAAQ,EAAE,4BAA4B;oBAC9D,QAAQ,EAAE,KAAK,EAAE,mCAAmC;oBACpD,QAAQ,EAAE,KAAK,EAAE,oBAAoB;oBACrC,aAAa,EAAE,6CAAS,CAAC,IAAI;oBAC7B,yCAAyC;oBACzC,UAAU,EAAE;wBACV,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU;wBAC5B,GAAG,YAAY,CAAC,UAAU;qBAC3B;iBACF,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAC9D,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,YAAY,EAAE,CAAC;gBACxD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,SAAS;QACpB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,QAAQ;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1C,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAElD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,6CAAS,CAAC,IAAI,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjD,cAAc;YACd,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5D,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5C,mBAAmB;YACnB,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAI,KAAe,CAAC,OAAO,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAElD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,6CAAS,CAAC,IAAI,CAAC;YAE9C,kBAAkB;YAClB,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAE5B,cAAc;YACd,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5D,YAAY;YACZ,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAI,KAAe,CAAC,OAAO,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,QAAQ;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;YAAE,OAAO;QAErC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,6CAAS,CAAC,IAAI,CAAC;QAE9C,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE5C,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;YAAE,OAAO;QAEjE,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEhC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE5C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;IAEM,YAAY;QACjB,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,WAAiC;QAC3D,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;QAErD,kBAAkB;QAClB,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtD,+BAA+B;QAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAEO,OAAO;QACb,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;CACF;AAED,uCAAuC;AACvC,MAAM,YAAY,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAE9C,4CAA4C;AAC3C,MAAc,CAAC,YAAY,GAAG,YAAY,CAAC", "sources": ["webpack://gladiatus-helper-bot-ts/webpack/universalModuleDefinition", "webpack://gladiatus-helper-bot-ts/./src/main/bot-engine.ts", "webpack://gladiatus-helper-bot-ts/./src/main/login-manager.ts", "webpack://gladiatus-helper-bot-ts/./src/main/ui-manager.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/arena-combat.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/event-system.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/expedition-system.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/forge-repair.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/gold-management.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/healing-system.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/market-operations.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/package-management.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/quest-management.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/turma-combat.ts", "webpack://gladiatus-helper-bot-ts/./src/modules/underworld-system.ts", "webpack://gladiatus-helper-bot-ts/./src/types/api.ts", "webpack://gladiatus-helper-bot-ts/./src/types/extension.ts", "webpack://gladiatus-helper-bot-ts/./src/types/game.ts", "webpack://gladiatus-helper-bot-ts/./src/types/gladiatus.ts", "webpack://gladiatus-helper-bot-ts/./src/types/index.ts", "webpack://gladiatus-helper-bot-ts/./src/types/localization.ts", "webpack://gladiatus-helper-bot-ts/./src/types/settings.ts", "webpack://gladiatus-helper-bot-ts/./src/types/ui.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/default-settings.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/gladiator-url-helper.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/storage-manager.ts", "webpack://gladiatus-helper-bot-ts/webpack/bootstrap", "webpack://gladiatus-helper-bot-ts/webpack/runtime/define property getters", "webpack://gladiatus-helper-bot-ts/webpack/runtime/hasOwnProperty shorthand", "webpack://gladiatus-helper-bot-ts/webpack/runtime/make namespace object", "webpack://gladiatus-helper-bot-ts/./src/main/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "// Bot engine - core automation logic\r\n\r\nimport { BotSettings, BotAction } from '../types';\r\nimport { StorageManager } from '../utils/storage-manager';\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\nimport { ArenaCombatModule } from '../modules/arena-combat';\r\nimport { TurmaCombatModule } from '../modules/turma-combat';\r\nimport { HealingSystemModule } from '../modules/healing-system';\r\nimport { PackageManagementModule } from '../modules/package-management';\r\nimport { ExpeditionSystemModule } from '../modules/expedition-system';\r\nimport { UnderworldSystemModule } from '../modules/underworld-system';\r\nimport { GoldManagementModule } from '../modules/gold-management';\r\nimport { EventSystemModule } from '../modules/event-system';\r\nimport { QuestManagementModule } from '../modules/quest-management';\r\nimport { MarketOperationsModule } from '../modules/market-operations';\r\nimport { ForgeRepairModule } from '../modules/forge-repair';\r\n\r\nexport class BotEngine {\r\n  private isRunning: boolean = false;\r\n  private isPaused: boolean = false;\r\n  private currentSettings: BotSettings | null = null;\r\n  private actionInterval: NodeJS.Timeout | null = null;\r\n\r\n  // Module instances\r\n  private arenaCombat: ArenaCombatModule;\r\n  private turmaCombat: TurmaCombatModule;\r\n  private healingSystem: HealingSystemModule;\r\n  private packageManagement: PackageManagementModule;\r\n  private expeditionSystem: ExpeditionSystemModule;\r\n  private underworldSystem: UnderworldSystemModule;\r\n  private goldManagement: GoldManagementModule;\r\n  private eventSystem: EventSystemModule;\r\n  private questManagement: QuestManagementModule;\r\n  private marketOperations: MarketOperationsModule;\r\n  private forgeRepair: ForgeRepairModule;\r\n\r\n  constructor(private storageManager: StorageManager, private urlHelper: GladiatorUrlHelper) {\r\n    // Initialize all modules\r\n    this.arenaCombat = new ArenaCombatModule(urlHelper, storageManager);\r\n    this.turmaCombat = new TurmaCombatModule(urlHelper, storageManager);\r\n    this.healingSystem = new HealingSystemModule(urlHelper, storageManager);\r\n    this.packageManagement = new PackageManagementModule(urlHelper, storageManager);\r\n    this.expeditionSystem = new ExpeditionSystemModule(urlHelper, storageManager);\r\n    this.underworldSystem = new UnderworldSystemModule(urlHelper, storageManager);\r\n    this.goldManagement = new GoldManagementModule(urlHelper, storageManager);\r\n    this.eventSystem = new EventSystemModule(urlHelper, storageManager);\r\n    this.questManagement = new QuestManagementModule(urlHelper, storageManager);\r\n    this.marketOperations = new MarketOperationsModule(urlHelper, storageManager);\r\n    this.forgeRepair = new ForgeRepairModule(urlHelper, storageManager);\r\n  }\r\n\r\n  async start(settings: BotSettings): Promise<void> {\r\n    if (this.isRunning) return;\r\n\r\n    this.currentSettings = settings;\r\n    this.isRunning = true;\r\n    this.isPaused = false;\r\n\r\n    console.log('Bot Engine: Starting automation');\r\n\r\n    // Start main automation loop\r\n    this.startAutomationLoop();\r\n  }\r\n\r\n  async stop(): Promise<void> {\r\n    this.isRunning = false;\r\n    this.isPaused = false;\r\n\r\n    if (this.actionInterval) {\r\n      clearInterval(this.actionInterval);\r\n      this.actionInterval = null;\r\n    }\r\n\r\n    // Stop all modules\r\n    await this.arenaCombat.stop();\r\n    await this.turmaCombat.stop();\r\n    await this.expeditionSystem.stop();\r\n    await this.underworldSystem.stop();\r\n    await this.eventSystem.stop();\r\n    await this.questManagement.stop();\r\n    await this.marketOperations.stop();\r\n    await this.forgeRepair.stop();\r\n\r\n    console.log('Bot Engine: Stopped automation');\r\n  }\r\n\r\n  async pause(): Promise<void> {\r\n    this.isPaused = true;\r\n    console.log('Bot Engine: Paused automation');\r\n  }\r\n\r\n  async resume(): Promise<void> {\r\n    this.isPaused = false;\r\n    console.log('Bot Engine: Resumed automation');\r\n  }\r\n\r\n  async updateSettings(settings: BotSettings): Promise<void> {\r\n    this.currentSettings = settings;\r\n    console.log('Bot Engine: Settings updated');\r\n  }\r\n\r\n  private startAutomationLoop(): void {\r\n    this.actionInterval = setInterval(async () => {\r\n      if (!this.isRunning || this.isPaused || !this.currentSettings) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.performNextAction();\r\n      } catch (error) {\r\n        console.error('Bot Engine: Error in automation loop:', error);\r\n      }\r\n    }, 5000); // Check every 5 seconds\r\n  }\r\n\r\n  private async performNextAction(): Promise<void> {\r\n    if (!this.currentSettings) return;\r\n\r\n    try {\r\n      // Determine next action based on settings and current game state\r\n      const nextAction = this.determineNextAction();\r\n\r\n      switch (nextAction) {\r\n        case BotAction.HEALING:\r\n          await this.performHealing();\r\n          break;\r\n        case BotAction.ATTACKING:\r\n          await this.performAttacking();\r\n          break;\r\n        case BotAction.PICKING_ITEMS:\r\n          await this.performItemPicking();\r\n          break;\r\n        case BotAction.BUYING_PACK:\r\n          await this.performPackBuying();\r\n          break;\r\n        case BotAction.TRAVELING:\r\n          await this.performTraveling();\r\n          break;\r\n        case BotAction.UNDERWORLD:\r\n          await this.performUnderworld();\r\n          break;\r\n        case BotAction.EVENT:\r\n          await this.performEvents();\r\n          break;\r\n        case BotAction.QUEST_MANAGEMENT:\r\n          await this.performQuests();\r\n          break;\r\n        case BotAction.MARKET_OPERATIONS:\r\n          await this.performMarketOperations();\r\n          break;\r\n        case BotAction.FORGE_REPAIR:\r\n          await this.performForgeRepair();\r\n          break;\r\n        default:\r\n          // Idle - do nothing\r\n          break;\r\n      }\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in performNextAction:', error);\r\n      // Continue running even if one action fails\r\n    }\r\n  }\r\n\r\n  private determineNextAction(): BotAction {\r\n    if (!this.currentSettings) return BotAction.IDLE;\r\n\r\n    try {\r\n      // Priority-based action determination\r\n\r\n      // 1. Check if healing is needed\r\n      if (this.currentSettings.heal?.enabled && this.needsHealing()) {\r\n        return BotAction.HEALING;\r\n      }\r\n\r\n      // 2. Check if arena/turma combat is enabled\r\n      if (this.currentSettings.arena?.enabled && this.canAttackArena()) {\r\n        return BotAction.ATTACKING;\r\n      }\r\n\r\n      if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {\r\n        return BotAction.ATTACKING;\r\n      }\r\n\r\n      // 3. Check if package management is needed\r\n      if (this.currentSettings.packages?.pickGold?.enabled && this.needsPackageManagement()) {\r\n        return BotAction.PICKING_ITEMS;\r\n      }\r\n\r\n      // 4. Check if expeditions are enabled\r\n      if (this.currentSettings.expeditions?.enabled && this.canDoExpedition()) {\r\n        return BotAction.TRAVELING;\r\n      }\r\n\r\n      // 5. Check if underworld is active and enabled\r\n      if (this.currentSettings.underworld?.enabled && this.isUnderworldActive()) {\r\n        return BotAction.UNDERWORLD;\r\n      }\r\n\r\n      // 6. Check if events are enabled\r\n      if (this.currentSettings.event?.enabled && this.hasActiveEvents()) {\r\n        return BotAction.EVENT;\r\n      }\r\n\r\n      // 7. Check if quest management is enabled\r\n      if (this.currentSettings.quest?.enabled && this.hasAvailableQuests()) {\r\n        return BotAction.QUEST_MANAGEMENT;\r\n      }\r\n\r\n      // 8. Check if market operations are enabled\r\n      if (this.currentSettings.market?.enabled && this.shouldPerformMarketOperations()) {\r\n        return BotAction.MARKET_OPERATIONS;\r\n      }\r\n\r\n      // 9. Check if forge/repair is enabled\r\n      if ((this.currentSettings.forge?.enabled || this.currentSettings.repair?.enabled) && this.shouldPerformForgeRepair()) {\r\n        return BotAction.FORGE_REPAIR;\r\n      }\r\n\r\n      return BotAction.IDLE;\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in determineNextAction:', error);\r\n      return BotAction.IDLE;\r\n    }\r\n  }\r\n\r\n  private needsHealing(): boolean {\r\n    // Check current health vs minimum health setting\r\n    const healthElement = document.querySelector('.health-bar, #health_points');\r\n    if (!healthElement) return false;\r\n\r\n    const healthText = healthElement.textContent || '';\r\n    const healthMatch = healthText.match(/(\\d+)\\/(\\d+)/);\r\n\r\n    if (healthMatch) {\r\n      const currentHealth = parseInt(healthMatch[1]);\r\n      const maxHealth = parseInt(healthMatch[2]);\r\n      const healthPercentage = (currentHealth / maxHealth) * 100;\r\n\r\n      return healthPercentage < (this.currentSettings?.heal.minHealth || 50);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private canAttackArena(): boolean {\r\n    // Check if we're on the right page and have attacks available\r\n    return window.location.href.includes('arena') || window.location.href.includes('index');\r\n  }\r\n\r\n  private canAttackTurma(): boolean {\r\n    // Check if we're on the right page and have attacks available\r\n    return window.location.href.includes('turma') || window.location.href.includes('index');\r\n  }\r\n\r\n  private needsPackageManagement(): boolean {\r\n    // Check if packages need attention\r\n    return true; // Simplified for now\r\n  }\r\n\r\n  private canDoExpedition(): boolean {\r\n    // Check if expeditions are available\r\n    return window.location.href.includes('expedition') || window.location.href.includes('index');\r\n  }\r\n\r\n  private isUnderworldActive(): boolean {\r\n    // Check if underworld is currently active\r\n    const underworldElement = document.querySelector('.underworld_active, .underworld-timer');\r\n    return !!underworldElement;\r\n  }\r\n\r\n  private hasActiveEvents(): boolean {\r\n    // Check if there are active events\r\n    const eventElement = document.querySelector('.event_active, .active-event');\r\n    return !!eventElement;\r\n  }\r\n\r\n  private hasAvailableQuests(): boolean {\r\n    // Check if there are available quests\r\n    const questElement = document.querySelector('.available_quest, .quest-available');\r\n    return !!questElement;\r\n  }\r\n\r\n  private shouldPerformMarketOperations(): boolean {\r\n    // Check if market operations should be performed\r\n    // This could be based on time intervals, gold thresholds, etc.\r\n    return true; // Simplified for now\r\n  }\r\n\r\n  private shouldPerformForgeRepair(): boolean {\r\n    // Check if forge/repair operations should be performed\r\n    // This could be based on damaged items, available materials, etc.\r\n    return true; // Simplified for now\r\n  }\r\n\r\n  private async performHealing(): Promise<void> {\r\n    if (!this.currentSettings?.heal) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing healing');\r\n      await this.healingSystem.checkAndHeal(this.currentSettings.heal);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in healing:', error);\r\n    }\r\n  }\r\n\r\n  private async performAttacking(): Promise<void> {\r\n    if (!this.currentSettings) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing attacking');\r\n\r\n      // Check which combat type to perform\r\n      if (this.currentSettings.arena?.enabled && this.canAttackArena()) {\r\n        await this.arenaCombat.start(this.currentSettings.arena);\r\n      } else if (this.currentSettings.turma?.enabled && this.canAttackTurma()) {\r\n        await this.turmaCombat.start(this.currentSettings.turma);\r\n      }\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in attacking:', error);\r\n    }\r\n  }\r\n\r\n  private async performItemPicking(): Promise<void> {\r\n    if (!this.currentSettings?.packages) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing item management');\r\n      await this.packageManagement.processPackages(this.currentSettings.packages);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in item management:', error);\r\n    }\r\n  }\r\n\r\n  private async performPackBuying(): Promise<void> {\r\n    if (!this.currentSettings?.hideGold) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing gold management');\r\n      await this.goldManagement.manageGold(this.currentSettings.hideGold);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in gold management:', error);\r\n    }\r\n  }\r\n\r\n  private async performTraveling(): Promise<void> {\r\n    if (!this.currentSettings?.expeditions || !this.currentSettings?.dungeon) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing expeditions');\r\n      await this.expeditionSystem.start(this.currentSettings.expeditions, this.currentSettings.dungeon);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in expeditions:', error);\r\n    }\r\n  }\r\n\r\n  private async performUnderworld(): Promise<void> {\r\n    if (!this.currentSettings?.underworld) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing underworld activities');\r\n      await this.underworldSystem.start(this.currentSettings.underworld);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in underworld:', error);\r\n    }\r\n  }\r\n\r\n  private async performEvents(): Promise<void> {\r\n    if (!this.currentSettings?.event) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing event activities');\r\n      await this.eventSystem.start(this.currentSettings.event);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in events:', error);\r\n    }\r\n  }\r\n\r\n  private async performQuests(): Promise<void> {\r\n    if (!this.currentSettings?.quest) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing quest management');\r\n      await this.questManagement.start(this.currentSettings.quest);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in quest management:', error);\r\n    }\r\n  }\r\n\r\n  private async performMarketOperations(): Promise<void> {\r\n    if (!this.currentSettings?.market) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing market operations');\r\n      await this.marketOperations.start(this.currentSettings.market);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in market operations:', error);\r\n    }\r\n  }\r\n\r\n  private async performForgeRepair(): Promise<void> {\r\n    if (!this.currentSettings?.forge || !this.currentSettings?.repair) return;\r\n\r\n    try {\r\n      console.log('Bot Engine: Performing forge and repair operations');\r\n      await this.forgeRepair.start(this.currentSettings.forge, this.currentSettings.repair);\r\n    } catch (error) {\r\n      console.error('Bot Engine: Error in forge/repair:', error);\r\n    }\r\n  }\r\n}", "// Login manager - handles automatic login functionality\r\n\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\nimport { StorageManager } from '../utils/storage-manager';\r\n\r\nexport class LoginManager {\r\n  constructor(private _urlHelper: GladiatorUrlHelper, private _storageManager: StorageManager) {\r\n    // Constructor parameters are automatically assigned to private properties\r\n  }\r\n\r\n  async handleLobby(): Promise<void> {\r\n    console.log('Login Manager: Handling lobby page');\r\n\r\n    // Set up fetch interception for login requests\r\n    this.interceptFetch();\r\n  }\r\n\r\n  private interceptFetch(): void {\r\n    const originalFetch = window.fetch;\r\n\r\n    window.fetch = async (...args: Parameters<typeof fetch>) => {\r\n      const [input, init] = args;\r\n      const url = typeof input === 'string' ? input : (input as Request).url;\r\n\r\n      // Intercept login link requests\r\n      if (url && url.includes('loginLink')) {\r\n        console.log('Login Manager: Intercepted login request');\r\n        // Handle login logic here\r\n      }\r\n\r\n      return originalFetch(input, init);\r\n    };\r\n  }\r\n}", "// UI manager - handles bot UI updates and interactions\r\n\r\nimport { BotStatus, GameBotStatistics } from '../types';\r\n\r\nexport class UIManager {\r\n  constructor() {\r\n    // UI Manager initialized\r\n  }\r\n\r\n  updateStatus(status: BotStatus): void {\r\n    // Update status indicator\r\n    const statusIndicator = document.getElementById('gh-status-indicator');\r\n    const statusText = document.getElementById('gh-status-text');\r\n    const toggleButton = document.getElementById('gh-toggle-bot') as HTMLButtonElement;\r\n\r\n    if (statusIndicator && statusText && toggleButton) {\r\n      const statusDot = statusIndicator.querySelector('.gh-status-dot') as HTMLElement;\r\n\r\n      if (status.isActive) {\r\n        if (status.isPaused) {\r\n          statusDot.style.backgroundColor = '#f39c12'; // Orange for paused\r\n          statusText.textContent = 'Paused';\r\n          toggleButton.textContent = 'Resume Bot';\r\n        } else {\r\n          statusDot.style.backgroundColor = '#27ae60'; // Green for active\r\n          statusText.textContent = 'Active';\r\n          toggleButton.textContent = 'Stop Bot';\r\n        }\r\n      } else {\r\n        // Show different status based on whether it was previously active\r\n        if (status.wasActive) {\r\n          statusDot.style.backgroundColor = '#f39c12'; // Orange for was active but stopped\r\n          statusText.textContent = 'Ready (was active)';\r\n          toggleButton.textContent = 'Start Bot';\r\n        } else {\r\n          statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive\r\n          statusText.textContent = status.error ? 'Error' : 'Inactive';\r\n          toggleButton.textContent = 'Start Bot';\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update runtime\r\n    this.updateRuntime(status.statistics);\r\n\r\n    // Update actions count\r\n    this.updateActionsCount(status.statistics.actionsPerformed);\r\n  }\r\n\r\n  private updateRuntime(statistics: GameBotStatistics): void {\r\n    const runtimeElement = document.getElementById('gh-runtime');\r\n    if (!runtimeElement) return;\r\n\r\n    const now = new Date();\r\n    const startTime = new Date(statistics.startTime);\r\n    const runtime = now.getTime() - startTime.getTime();\r\n\r\n    const hours = Math.floor(runtime / (1000 * 60 * 60));\r\n    const minutes = Math.floor((runtime % (1000 * 60 * 60)) / (1000 * 60));\r\n    const seconds = Math.floor((runtime % (1000 * 60)) / 1000);\r\n\r\n    runtimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  private updateActionsCount(actionsPerformed: number): void {\r\n    const actionsElement = document.getElementById('gh-actions');\r\n    if (actionsElement) {\r\n      actionsElement.textContent = actionsPerformed.toString();\r\n    }\r\n  }\r\n\r\n  showStatistics(statistics: GameBotStatistics): void {\r\n    // Remove existing modal if present\r\n    const existingModal = document.getElementById('gh-statistics-modal');\r\n    if (existingModal) {\r\n      existingModal.remove();\r\n    }\r\n\r\n    // Create and show statistics modal\r\n    const modal = this.createStatisticsModal(statistics);\r\n    modal.id = 'gh-statistics-modal';\r\n    document.body.appendChild(modal);\r\n\r\n    // Add close functionality with proper event handling\r\n    const closeBtn = modal.querySelector('.gh-modal-close');\r\n    const modalContent = modal.querySelector('.gh-modal-content');\r\n\r\n    closeBtn?.addEventListener('click', (e) => {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      modal.remove();\r\n    });\r\n\r\n    // Prevent modal content clicks from closing modal\r\n    modalContent?.addEventListener('click', (e) => {\r\n      e.stopPropagation();\r\n    });\r\n\r\n    // Close on background click only - with delay to prevent immediate closure\r\n    setTimeout(() => {\r\n      modal.addEventListener('click', (e) => {\r\n        if (e.target === modal) {\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          modal.remove();\r\n        }\r\n      });\r\n    }, 100);\r\n\r\n    // Close on Escape key\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape') {\r\n        modal.remove();\r\n        document.removeEventListener('keydown', handleEscape);\r\n      }\r\n    };\r\n    document.addEventListener('keydown', handleEscape);\r\n  }\r\n\r\n  private createStatisticsModal(statistics: GameBotStatistics): HTMLElement {\r\n    const modal = document.createElement('div');\r\n    modal.className = 'gh-modal';\r\n    modal.style.cssText = `\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: rgba(0,0,0,0.5);\r\n      z-index: 10001;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    `;\r\n\r\n    const modalContent = document.createElement('div');\r\n    modalContent.className = 'gh-modal-content';\r\n    modalContent.style.cssText = `\r\n      background: #2c3e50;\r\n      color: white;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n      max-width: 500px;\r\n      width: 90%;\r\n      max-height: 80%;\r\n      overflow-y: auto;\r\n    `;\r\n\r\n    modalContent.innerHTML = `\r\n      <div class=\"gh-modal-header\">\r\n        <h2>Bot Statistics</h2>\r\n        <button class=\"gh-modal-close\" style=\"float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;\">×</button>\r\n      </div>\r\n      <div class=\"gh-modal-body\">\r\n        <div class=\"gh-stat-group\">\r\n          <h3>General</h3>\r\n          <p>Start Time: ${new Date(statistics.startTime).toLocaleString()}</p>\r\n          <p>Total Runtime: ${this.formatDuration(statistics.totalRunTime)}</p>\r\n          <p>Actions Performed: ${statistics.actionsPerformed}</p>\r\n          <p>Errors Encountered: ${statistics.errorsEncountered}</p>\r\n        </div>\r\n        <div class=\"gh-stat-group\">\r\n          <h3>Combat</h3>\r\n          <p>Combats Won: ${statistics.combatsWon}</p>\r\n          <p>Combats Lost: ${statistics.combatsLost}</p>\r\n          <p>Win Rate: ${statistics.combatsWon + statistics.combatsLost > 0 ?\r\n            ((statistics.combatsWon / (statistics.combatsWon + statistics.combatsLost)) * 100).toFixed(1) : 0}%</p>\r\n        </div>\r\n        <div class=\"gh-stat-group\">\r\n          <h3>Rewards</h3>\r\n          <p>Gold Earned: ${statistics.goldEarned.toLocaleString()}</p>\r\n          <p>Experience Gained: ${statistics.experienceGained.toLocaleString()}</p>\r\n          <p>Items Found: ${statistics.itemsFound}</p>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    modal.appendChild(modalContent);\r\n    return modal;\r\n  }\r\n\r\n  private formatDuration(milliseconds: number): string {\r\n    const hours = Math.floor(milliseconds / (1000 * 60 * 60));\r\n    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));\r\n    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);\r\n\r\n    return `${hours}h ${minutes}m ${seconds}s`;\r\n  }\r\n\r\n  showNotification(title: string, message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {\r\n    const container = document.getElementById('gh-notifications');\r\n    if (!container) return;\r\n\r\n    const notification = document.createElement('div');\r\n    notification.className = `gh-notification gh-notification-${type}`;\r\n    notification.innerHTML = `\r\n      <div class=\"gh-notification-content\">\r\n        <strong>${title}</strong>\r\n        <p>${message}</p>\r\n      </div>\r\n      <button class=\"gh-notification-close\">×</button>\r\n    `;\r\n\r\n    container.appendChild(notification);\r\n\r\n    // Auto-remove after 5 seconds\r\n    setTimeout(() => {\r\n      notification.remove();\r\n    }, 5000);\r\n\r\n    // Add close button functionality\r\n    notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {\r\n      notification.remove();\r\n    });\r\n  }\r\n}", "// Arena Combat Module - Handles all arena-related combat functionality\n\nimport { ArenaSettings, BotAction, GameBotStatistics } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface ArenaTarget {\n  id: string;\n  name: string;\n  level: number;\n  points: number;\n  winChance: number;\n  isGuildMember: boolean;\n  canAttack: boolean;\n}\n\nexport class ArenaCombatModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private attackCount: number = 0;\n  private loseCount: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: ArenaSettings): Promise<void> {\n    if (this.isRunning) return;\n    \n    this.isRunning = true;\n    this.attackCount = 0;\n    this.loseCount = 0;\n    \n    console.log('Arena Combat: Starting arena automation');\n    \n    try {\n      await this.navigateToArena();\n      await this.performArenaLoop(settings);\n    } catch (error) {\n      console.error('Arena Combat: Error in arena automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Arena Combat: Stopped arena automation');\n  }\n\n  private async navigateToArena(): Promise<void> {\n    const currentUrl = window.location.href;\n    \n    if (!currentUrl.includes('mod=arena')) {\n      console.log('Arena Combat: Navigating to arena');\n      window.location.href = currentUrl.replace(/mod=\\w+/, 'mod=arena');\n      \n      // Wait for page load\n      await this.waitForPageLoad();\n    }\n  }\n\n  private async performArenaLoop(settings: ArenaSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueAttacking(settings)) {\n      try {\n        const targets = await this.getAvailableTargets(settings);\n        \n        if (targets.length === 0) {\n          console.log('Arena Combat: No suitable targets found');\n          await this.delay(5000);\n          continue;\n        }\n\n        const target = this.selectBestTarget(targets, settings);\n        if (!target) {\n          console.log('Arena Combat: No target selected');\n          await this.delay(5000);\n          continue;\n        }\n\n        const result = await this.attackTarget(target, settings);\n        await this.processAttackResult(result);\n        \n        // Random delay between attacks\n        const delay = Math.random() * 10000 + 5000; // 5-15 seconds\n        await this.delay(delay);\n        \n      } catch (error) {\n        console.error('Arena Combat: Error in attack loop:', error);\n        await this.delay(10000);\n      }\n    }\n  }\n\n  private async getAvailableTargets(settings: ArenaSettings): Promise<ArenaTarget[]> {\n    const targets: ArenaTarget[] = [];\n    \n    // Parse arena page for targets\n    const targetElements = document.querySelectorAll('.arena_opponent, .opponent_row');\n    \n    for (const element of targetElements) {\n      try {\n        const target = await this.parseTargetElement(element as HTMLElement, settings);\n        if (target && this.isValidTarget(target, settings)) {\n          targets.push(target);\n        }\n      } catch (error) {\n        console.error('Arena Combat: Error parsing target:', error);\n      }\n    }\n    \n    return targets;\n  }\n\n  private async parseTargetElement(element: HTMLElement, settings: ArenaSettings): Promise<ArenaTarget | null> {\n    const nameElement = element.querySelector('.opponent_name, .player_name');\n    const levelElement = element.querySelector('.opponent_level, .level');\n    const pointsElement = element.querySelector('.opponent_points, .points');\n    const attackButton = element.querySelector('.attack_button, [data-action=\"attack\"]');\n    \n    if (!nameElement || !attackButton) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '0');\n    const points = parseInt(pointsElement?.textContent?.trim() || '0');\n    const id = attackButton.getAttribute('data-target-id') || \n               attackButton.getAttribute('href')?.match(/target=(\\d+)/)?.[1] || '';\n    \n    // Calculate win chance (simplified)\n    const winChance = this.calculateWinChance(level, points);\n    \n    // Check if guild member\n    const isGuildMember = this.isGuildMember(name, settings);\n    \n    return {\n      id,\n      name,\n      level,\n      points,\n      winChance,\n      isGuildMember,\n      canAttack: !attackButton.hasAttribute('disabled')\n    };\n  }\n\n  private isValidTarget(target: ArenaTarget, settings: ArenaSettings): boolean {\n    // Skip if can't attack\n    if (!target.canAttack) return false;\n    \n    // Skip guild members if auto-ignore is enabled\n    if (settings.autoIgnoreGuildPlayers && target.isGuildMember) return false;\n    \n    // Skip if in ignore list\n    if (settings.ignorePlayers.includes(target.name)) return false;\n    \n    // Skip if win chance is too low and skipLow is enabled\n    if (settings.skipLow && target.winChance < (settings.prioritizeChance || 80)) return false;\n    \n    // Check if targeting specific players\n    if (settings.attackTargetPlayers && settings.attackPlayers.length > 0) {\n      return settings.attackPlayers.includes(target.name);\n    }\n    \n    return true;\n  }\n\n  private selectBestTarget(targets: ArenaTarget[], settings: ArenaSettings): ArenaTarget | null {\n    if (targets.length === 0) return null;\n    \n    // Sort by win chance (highest first) and then by points\n    targets.sort((a, b) => {\n      if (Math.abs(a.winChance - b.winChance) < 5) {\n        return b.points - a.points; // Higher points first if win chances are similar\n      }\n      return b.winChance - a.winChance; // Higher win chance first\n    });\n    \n    return targets[0];\n  }\n\n  private async attackTarget(target: ArenaTarget, settings: ArenaSettings): Promise<any> {\n    console.log(`Arena Combat: Attacking ${target.name} (Level ${target.level}, Win Chance: ${target.winChance}%)`);\n    \n    // Find and click attack button\n    const attackButton = document.querySelector(`[data-target-id=\"${target.id}\"], [href*=\"target=${target.id}\"]`);\n    if (!attackButton) {\n      throw new Error('Attack button not found');\n    }\n    \n    // Simulate attack if enabled\n    if (settings.allowSim) {\n      const simButton = document.querySelector('.simulate_button, [data-action=\"simulate\"]');\n      if (simButton) {\n        (simButton as HTMLElement).click();\n      } else {\n        (attackButton as HTMLElement).click();\n      }\n    } else {\n      (attackButton as HTMLElement).click();\n    }\n    \n    // Wait for attack result\n    await this.waitForAttackResult();\n    \n    return this.parseAttackResult();\n  }\n\n  private async processAttackResult(result: any): Promise<void> {\n    this.attackCount++;\n    \n    if (result.won) {\n      console.log(`Arena Combat: Victory! Gold: ${result.gold}, XP: ${result.xp}`);\n      this.loseCount = 0; // Reset lose count on win\n    } else {\n      console.log('Arena Combat: Defeat');\n      this.loseCount++;\n    }\n    \n    // Update statistics\n    await this.updateStatistics(result);\n  }\n\n  private shouldContinueAttacking(settings: ArenaSettings): boolean {\n    // Check lose limit\n    if (settings.loseLimit > 0 && this.loseCount >= settings.loseLimit) {\n      console.log('Arena Combat: Lose limit reached');\n      return false;\n    }\n    \n    // Check daily attack limit\n    if (settings.dailyAttacks > 0 && this.attackCount >= settings.dailyAttacks) {\n      console.log('Arena Combat: Daily attack limit reached');\n      return false;\n    }\n    \n    // Check total attack limit\n    if (settings.limitAttacks > 0 && this.attackCount >= settings.limitAttacks) {\n      console.log('Arena Combat: Attack limit reached');\n      return false;\n    }\n    \n    return true;\n  }\n\n  private calculateWinChance(level: number, points: number): number {\n    // Simplified win chance calculation\n    // In reality, this would be more complex based on player stats\n    const myLevel = this.getPlayerLevel();\n    const myPoints = this.getPlayerPoints();\n    \n    const levelDiff = myLevel - level;\n    const pointsDiff = myPoints - points;\n    \n    let winChance = 50; // Base 50%\n    winChance += levelDiff * 5; // +/- 5% per level difference\n    winChance += pointsDiff * 0.01; // +/- 0.01% per point difference\n    \n    return Math.max(0, Math.min(100, winChance));\n  }\n\n  private isGuildMember(playerName: string, settings: ArenaSettings): boolean {\n    // Check if player is in the same guild\n    // This would need to be implemented based on guild member list\n    return false; // Simplified for now\n  }\n\n  private getPlayerLevel(): number {\n    const levelElement = document.querySelector('.player_level, #player_level');\n    return parseInt(levelElement?.textContent?.trim() || '1');\n  }\n\n  private getPlayerPoints(): number {\n    const pointsElement = document.querySelector('.player_points, #player_points');\n    return parseInt(pointsElement?.textContent?.trim() || '0');\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000); // Additional delay for dynamic content\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async waitForAttackResult(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkResult = () => {\n        const resultElement = document.querySelector('.combat_result, .attack_result');\n        if (resultElement) {\n          resolve();\n        } else {\n          setTimeout(checkResult, 500);\n        }\n      };\n      setTimeout(checkResult, 2000); // Wait at least 2 seconds\n    });\n  }\n\n  private parseAttackResult(): any {\n    const resultElement = document.querySelector('.combat_result, .attack_result');\n    if (!resultElement) return { won: false, gold: 0, xp: 0 };\n    \n    const won = resultElement.textContent?.includes('Victory') || \n                resultElement.classList.contains('victory') || false;\n    \n    const goldMatch = resultElement.textContent?.match(/(\\d+)\\s*gold/i);\n    const xpMatch = resultElement.textContent?.match(/(\\d+)\\s*xp/i);\n    \n    return {\n      won,\n      gold: goldMatch ? parseInt(goldMatch[1]) : 0,\n      xp: xpMatch ? parseInt(xpMatch[1]) : 0\n    };\n  }\n\n  private async updateStatistics(result: any): Promise<void> {\n    // Update bot statistics\n    const stats = await this.storageManager.getBotStatus();\n    if (stats?.statistics) {\n      stats.statistics.actionsPerformed++;\n      if (result.won) {\n        stats.statistics.combatsWon++;\n        stats.statistics.goldEarned += result.gold;\n        stats.statistics.experienceGained += result.xp;\n      } else {\n        stats.statistics.combatsLost++;\n      }\n      \n      await this.storageManager.saveBotStatus(stats);\n    }\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Event System Module - Handles special events and bonus collection\n\nimport { EventSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface GameEvent {\n  id: string;\n  name: string;\n  type: 'combat' | 'collection' | 'special';\n  isActive: boolean;\n  timeRemaining: number;\n  hasRewards: boolean;\n  canParticipate: boolean;\n}\n\nexport interface EventReward {\n  type: 'gold' | 'xp' | 'item' | 'points';\n  amount: number;\n  description: string;\n  isCollected: boolean;\n}\n\nexport class EventSystemModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private eventCount: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: EventSettings): Promise<void> {\n    if (this.isRunning || !settings.enabled) return;\n    \n    this.isRunning = true;\n    this.eventCount = 0;\n    \n    console.log('Event System: Starting event automation');\n    \n    try {\n      await this.performEventLoop(settings);\n    } catch (error) {\n      console.error('Event System: Error in event automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Event System: Stopped event automation');\n  }\n\n  private async performEventLoop(settings: EventSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueEvents(settings)) {\n      try {\n        // Check for active events\n        const activeEvents = await this.getActiveEvents();\n        \n        if (activeEvents.length === 0) {\n          console.log('Event System: No active events found');\n          await this.delay(30000); // Check again in 30 seconds\n          continue;\n        }\n\n        // Process each active event\n        for (const event of activeEvents) {\n          if (!this.isRunning) break;\n          \n          await this.processEvent(event, settings);\n          await this.delay(5000);\n        }\n\n        // Collect bonuses if enabled\n        if (settings.autoCollectBonuses) {\n          await this.collectEventBonuses();\n        }\n\n        // Auto renew event if enabled\n        if (settings.autoRenewEvent) {\n          await this.renewEvents();\n        }\n\n        await this.delay(60000); // Check events every minute\n        \n      } catch (error) {\n        console.error('Event System: Error in event loop:', error);\n        await this.delay(30000);\n      }\n    }\n  }\n\n  private async getActiveEvents(): Promise<GameEvent[]> {\n    const events: GameEvent[] = [];\n    \n    // Check main event page\n    await this.navigateToEvents();\n    \n    const eventElements = document.querySelectorAll('.event_item, .active-event');\n    \n    for (let i = 0; i < eventElements.length; i++) {\n      const element = eventElements[i] as HTMLElement;\n      \n      try {\n        const event = await this.parseEventElement(element, i);\n        if (event && event.isActive) {\n          events.push(event);\n        }\n      } catch (error) {\n        console.error('Event System: Error parsing event:', error);\n      }\n    }\n    \n    return events;\n  }\n\n  private async parseEventElement(element: HTMLElement, index: number): Promise<GameEvent | null> {\n    const nameElement = element.querySelector('.event_name, .name');\n    const timerElement = element.querySelector('.event_timer, .timer');\n    const participateButton = element.querySelector('.participate_button, [data-action=\"participate\"]');\n    const rewardsElement = element.querySelector('.event_rewards, .rewards');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const timerText = timerElement?.textContent?.trim() || '0:00:00';\n    const timeMatch = timerText.match(/(\\d+):(\\d+):(\\d+)/);\n    const timeRemaining = timeMatch ? \n      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;\n    \n    // Determine event type based on name or class\n    let type: 'combat' | 'collection' | 'special' = 'special';\n    if (name.toLowerCase().includes('combat') || name.toLowerCase().includes('fight')) {\n      type = 'combat';\n    } else if (name.toLowerCase().includes('collect') || name.toLowerCase().includes('gather')) {\n      type = 'collection';\n    }\n    \n    return {\n      id: index.toString(),\n      name,\n      type,\n      isActive: timeRemaining > 0,\n      timeRemaining,\n      hasRewards: !!rewardsElement,\n      canParticipate: !!(participateButton && !participateButton.hasAttribute('disabled'))\n    };\n  }\n\n  private async processEvent(event: GameEvent, settings: EventSettings): Promise<void> {\n    console.log(`Event System: Processing event ${event.name}`);\n    \n    switch (event.type) {\n      case 'combat':\n        await this.processCombatEvent(event, settings);\n        break;\n      case 'collection':\n        await this.processCollectionEvent(event, settings);\n        break;\n      case 'special':\n        await this.processSpecialEvent(event, settings);\n        break;\n    }\n    \n    this.eventCount++;\n  }\n\n  private async processCombatEvent(event: GameEvent, settings: EventSettings): Promise<void> {\n    if (!event.canParticipate) return;\n    \n    console.log(`Event System: Participating in combat event ${event.name}`);\n    \n    // Navigate to event combat page\n    await this.navigateToEventCombat(event);\n    \n    // Look for combat targets\n    const targets = await this.getEventTargets(settings);\n    \n    for (const target of targets) {\n      if (!this.isRunning) break;\n      \n      await this.attackEventTarget(target);\n      await this.delay(5000);\n    }\n  }\n\n  private async processCollectionEvent(event: GameEvent, settings: EventSettings): Promise<void> {\n    console.log(`Event System: Processing collection event ${event.name}`);\n    \n    // Navigate to event collection page\n    await this.navigateToEventCollection(event);\n    \n    // Collect available items\n    await this.collectEventItems();\n  }\n\n  private async processSpecialEvent(event: GameEvent, settings: EventSettings): Promise<void> {\n    console.log(`Event System: Processing special event ${event.name}`);\n    \n    if (event.canParticipate) {\n      const eventElement = document.querySelector(`.event_item:nth-child(${parseInt(event.id) + 1})`);\n      if (eventElement) {\n        const participateButton = eventElement.querySelector('.participate_button, [data-action=\"participate\"]');\n        if (participateButton && !participateButton.hasAttribute('disabled')) {\n          (participateButton as HTMLElement).click();\n          await this.delay(3000);\n        }\n      }\n    }\n  }\n\n  private async getEventTargets(settings: EventSettings): Promise<any[]> {\n    const targets: any[] = [];\n    const targetElements = document.querySelectorAll('.event_target, .event-enemy');\n    \n    for (const element of targetElements) {\n      const nameElement = element.querySelector('.target_name, .name');\n      const attackButton = element.querySelector('.attack_button, [data-action=\"attack\"]');\n      \n      if (nameElement && attackButton && !attackButton.hasAttribute('disabled')) {\n        const name = nameElement.textContent?.trim() || '';\n        \n        // Check if this matches the configured mob\n        if (!settings.mob || name.toLowerCase().includes(settings.mob.toLowerCase())) {\n          targets.push({\n            name,\n            element,\n            button: attackButton\n          });\n        }\n      }\n    }\n    \n    return targets;\n  }\n\n  private async attackEventTarget(target: any): Promise<void> {\n    console.log(`Event System: Attacking ${target.name}`);\n    \n    (target.button as HTMLElement).click();\n    \n    // Wait for combat result\n    await this.delay(3000);\n    \n    // Check for combat result\n    const resultElement = document.querySelector('.combat_result, .event_result');\n    if (resultElement) {\n      const won = resultElement.textContent?.includes('Victory') || false;\n      console.log(`Event System: Combat result - ${won ? 'Victory' : 'Defeat'}`);\n    }\n  }\n\n  private async collectEventItems(): Promise<void> {\n    const collectButtons = document.querySelectorAll('.collect_button, [data-action=\"collect\"]');\n    \n    for (const button of collectButtons) {\n      if (!button.hasAttribute('disabled')) {\n        console.log('Event System: Collecting event item');\n        (button as HTMLElement).click();\n        await this.delay(1500);\n      }\n    }\n  }\n\n  private async collectEventBonuses(): Promise<void> {\n    console.log('Event System: Collecting event bonuses');\n    \n    // Look for bonus collection buttons\n    const bonusButtons = document.querySelectorAll('.collect_bonus, .bonus_button, [data-action=\"collect-bonus\"]');\n    \n    for (const button of bonusButtons) {\n      if (!button.hasAttribute('disabled')) {\n        console.log('Event System: Collecting bonus');\n        (button as HTMLElement).click();\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async renewEvents(): Promise<void> {\n    console.log('Event System: Auto-renewing events');\n    \n    const renewButtons = document.querySelectorAll('.renew_event, [data-action=\"renew\"]');\n    \n    for (const button of renewButtons) {\n      if (!button.hasAttribute('disabled')) {\n        console.log('Event System: Renewing event');\n        (button as HTMLElement).click();\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async navigateToEvents(): Promise<void> {\n    if (!window.location.href.includes('mod=events')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=events`;\n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToEventCombat(event: GameEvent): Promise<void> {\n    const currentUrl = window.location.href;\n    const baseUrl = currentUrl.split('?')[0];\n    window.location.href = `${baseUrl}?mod=events&action=combat&event=${event.id}`;\n    await this.waitForPageLoad();\n  }\n\n  private async navigateToEventCollection(event: GameEvent): Promise<void> {\n    const currentUrl = window.location.href;\n    const baseUrl = currentUrl.split('?')[0];\n    window.location.href = `${baseUrl}?mod=events&action=collect&event=${event.id}`;\n    await this.waitForPageLoad();\n  }\n\n  private shouldContinueEvents(settings: EventSettings): boolean {\n    // Check auto stop condition\n    if (settings.autoStop && this.eventCount >= 50) { // Arbitrary limit\n      console.log('Event System: Event limit reached');\n      return false;\n    }\n    \n    // Check if following a leader\n    if (settings.followLeader) {\n      // This would need leader detection logic\n      return true; // Simplified\n    }\n    \n    return true;\n  }\n\n  async getEventStatus(): Promise<any> {\n    const activeEvents = await this.getActiveEvents();\n    \n    return {\n      isRunning: this.isRunning,\n      eventCount: this.eventCount,\n      activeEvents: activeEvents.length,\n      lastUpdate: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Expedition System Module - Handles expedition and dungeon automation\n\nimport { ExpeditionSettings, DungeonSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface ExpeditionLocation {\n  id: string;\n  name: string;\n  level: number;\n  dungeonPoints: number;\n  isAvailable: boolean;\n  cooldownRemaining: number;\n}\n\nexport interface DungeonInfo {\n  name: string;\n  difficulty: string;\n  isAdvanced: boolean;\n  hasBoss: boolean;\n  bossName?: string;\n  requiresKey: boolean;\n}\n\nexport class ExpeditionSystemModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private expeditionCount: number = 0;\n  private dungeonCount: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(expeditionSettings: ExpeditionSettings, dungeonSettings: DungeonSettings): Promise<void> {\n    if (this.isRunning) return;\n    \n    this.isRunning = true;\n    this.expeditionCount = 0;\n    this.dungeonCount = 0;\n    \n    console.log('Expedition System: Starting expedition automation');\n    \n    try {\n      if (expeditionSettings.enabled) {\n        await this.performExpeditions(expeditionSettings);\n      }\n      \n      if (dungeonSettings.enabled) {\n        await this.performDungeons(dungeonSettings);\n      }\n    } catch (error) {\n      console.error('Expedition System: Error in expedition automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Expedition System: Stopped expedition automation');\n  }\n\n  private async performExpeditions(settings: ExpeditionSettings): Promise<void> {\n    console.log('Expedition System: Starting expeditions');\n    \n    await this.navigateToExpedition();\n    \n    while (this.isRunning && this.shouldContinueExpeditions(settings)) {\n      try {\n        const locations = await this.getAvailableLocations(settings);\n        \n        if (locations.length === 0) {\n          console.log('Expedition System: No available expedition locations');\n          break;\n        }\n\n        const location = this.selectBestLocation(locations, settings);\n        if (!location) {\n          console.log('Expedition System: No suitable location found');\n          break;\n        }\n\n        await this.startExpedition(location, settings);\n        this.expeditionCount++;\n        \n        // Wait for expedition completion\n        await this.waitForExpeditionCompletion();\n        \n        // Collect bonuses if enabled\n        if (settings.autoCollectBonuses) {\n          await this.collectExpeditionBonuses();\n        }\n        \n        await this.delay(5000);\n        \n      } catch (error) {\n        console.error('Expedition System: Error in expedition loop:', error);\n        await this.delay(10000);\n      }\n    }\n  }\n\n  private async performDungeons(settings: DungeonSettings): Promise<void> {\n    console.log('Expedition System: Starting dungeons');\n    \n    await this.navigateToDungeon(settings.location);\n    \n    while (this.isRunning && this.shouldContinueDungeons(settings)) {\n      try {\n        const dungeonInfo = await this.getDungeonInfo();\n        \n        if (!dungeonInfo) {\n          console.log('Expedition System: No dungeon information available');\n          break;\n        }\n\n        await this.enterDungeon(dungeonInfo, settings);\n        await this.completeDungeon(dungeonInfo, settings);\n        \n        this.dungeonCount++;\n        \n        await this.delay(10000);\n        \n      } catch (error) {\n        console.error('Expedition System: Error in dungeon loop:', error);\n        \n        // Handle dungeon failure\n        if (settings.restartAfterFail > 0 && this.dungeonCount < settings.restartAfterFail) {\n          console.log('Expedition System: Restarting dungeon after failure');\n          await this.delay(15000);\n          continue;\n        } else {\n          break;\n        }\n      }\n    }\n  }\n\n  private async navigateToExpedition(): Promise<void> {\n    if (!window.location.href.includes('mod=expedition')) {\n      console.log('Expedition System: Navigating to expedition');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=expedition`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToDungeon(location: string): Promise<void> {\n    if (!window.location.href.includes('mod=dungeon') || !window.location.href.includes(location)) {\n      console.log(`Expedition System: Navigating to dungeon at ${location}`);\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=dungeon&loc=${location}`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async getAvailableLocations(settings: ExpeditionSettings): Promise<ExpeditionLocation[]> {\n    const locations: ExpeditionLocation[] = [];\n    \n    const locationElements = document.querySelectorAll('.expedition_location, .location-item');\n    \n    for (let i = 0; i < locationElements.length; i++) {\n      const element = locationElements[i] as HTMLElement;\n      \n      try {\n        const location = await this.parseExpeditionLocation(element, i);\n        if (location && this.isValidExpeditionLocation(location, settings)) {\n          locations.push(location);\n        }\n      } catch (error) {\n        console.error('Expedition System: Error parsing location:', error);\n      }\n    }\n    \n    return locations;\n  }\n\n  private async parseExpeditionLocation(element: HTMLElement, index: number): Promise<ExpeditionLocation | null> {\n    const nameElement = element.querySelector('.location_name, .name');\n    const levelElement = element.querySelector('.location_level, .level');\n    const pointsElement = element.querySelector('.dungeon_points, .points');\n    const startButton = element.querySelector('.start_expedition, [data-action=\"start\"]');\n    const cooldownElement = element.querySelector('.cooldown, .timer');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    const dungeonPoints = parseInt(pointsElement?.textContent?.trim() || '0');\n    const isAvailable = startButton && !startButton.hasAttribute('disabled');\n    \n    let cooldownRemaining = 0;\n    if (cooldownElement) {\n      const cooldownText = cooldownElement.textContent?.trim() || '';\n      const timeMatch = cooldownText.match(/(\\d+):(\\d+):(\\d+)/);\n      if (timeMatch) {\n        cooldownRemaining = parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]);\n      }\n    }\n    \n    return {\n      id: index.toString(),\n      name,\n      level,\n      dungeonPoints,\n      isAvailable: !!isAvailable,\n      cooldownRemaining\n    };\n  }\n\n  private isValidExpeditionLocation(location: ExpeditionLocation, settings: ExpeditionSettings): boolean {\n    // Must be available\n    if (!location.isAvailable) return false;\n    \n    // Check minimum dungeon points\n    if (location.dungeonPoints < settings.minDungeonPoints) return false;\n    \n    // Check specific location if set\n    if (settings.location && !location.name.toLowerCase().includes(settings.location.toLowerCase())) {\n      return false;\n    }\n    \n    return true;\n  }\n\n  private selectBestLocation(locations: ExpeditionLocation[], settings: ExpeditionSettings): ExpeditionLocation | null {\n    if (locations.length === 0) return null;\n    \n    // Sort by dungeon points (highest first)\n    locations.sort((a, b) => b.dungeonPoints - a.dungeonPoints);\n    \n    return locations[0];\n  }\n\n  private async startExpedition(location: ExpeditionLocation, settings: ExpeditionSettings): Promise<void> {\n    console.log(`Expedition System: Starting expedition to ${location.name}`);\n    \n    const locationElement = document.querySelector(`.expedition_location:nth-child(${parseInt(location.id) + 1})`);\n    if (!locationElement) return;\n    \n    const startButton = locationElement.querySelector('.start_expedition, [data-action=\"start\"]');\n    if (!startButton || startButton.hasAttribute('disabled')) return;\n    \n    // Handle cooldown removal if enabled\n    if (settings.removeCooldown === 'hourglass' && location.cooldownRemaining > 0) {\n      await this.removeCooldownWithHourglass();\n    }\n    \n    (startButton as HTMLElement).click();\n    await this.delay(2000);\n  }\n\n  private async removeCooldownWithHourglass(): Promise<void> {\n    const hourglassButton = document.querySelector('.use_hourglass, [data-action=\"use-hourglass\"]');\n    if (hourglassButton && !hourglassButton.hasAttribute('disabled')) {\n      console.log('Expedition System: Using hourglass to remove cooldown');\n      (hourglassButton as HTMLElement).click();\n      await this.delay(1000);\n    }\n  }\n\n  private async waitForExpeditionCompletion(): Promise<void> {\n    console.log('Expedition System: Waiting for expedition completion');\n    \n    return new Promise((resolve) => {\n      const checkCompletion = () => {\n        const completionElement = document.querySelector('.expedition_complete, .completed');\n        const collectButton = document.querySelector('.collect_rewards, [data-action=\"collect\"]');\n        \n        if (completionElement || collectButton) {\n          resolve();\n        } else {\n          setTimeout(checkCompletion, 5000);\n        }\n      };\n      \n      setTimeout(checkCompletion, 30000); // Start checking after 30 seconds\n    });\n  }\n\n  private async collectExpeditionBonuses(): Promise<void> {\n    const collectButton = document.querySelector('.collect_rewards, [data-action=\"collect\"]');\n    if (collectButton && !collectButton.hasAttribute('disabled')) {\n      console.log('Expedition System: Collecting expedition bonuses');\n      (collectButton as HTMLElement).click();\n      await this.delay(2000);\n    }\n  }\n\n  private async getDungeonInfo(): Promise<DungeonInfo | null> {\n    const nameElement = document.querySelector('.dungeon_name, .name');\n    const difficultyElement = document.querySelector('.dungeon_difficulty, .difficulty');\n    const bossElement = document.querySelector('.boss_name, .boss');\n    const keyElement = document.querySelector('.gate_key, .key-required');\n    \n    if (!nameElement) return null;\n    \n    return {\n      name: nameElement.textContent?.trim() || '',\n      difficulty: difficultyElement?.textContent?.trim() || 'normal',\n      isAdvanced: difficultyElement?.textContent?.includes('advanced') || false,\n      hasBoss: !!bossElement,\n      bossName: bossElement?.textContent?.trim(),\n      requiresKey: !!keyElement\n    };\n  }\n\n  private async enterDungeon(dungeonInfo: DungeonInfo, settings: DungeonSettings): Promise<void> {\n    console.log(`Expedition System: Entering dungeon ${dungeonInfo.name}`);\n    \n    // Use gate key if required and enabled\n    if (dungeonInfo.requiresKey && settings.useGateKey) {\n      const keyButton = document.querySelector('.use_gate_key, [data-action=\"use-key\"]');\n      if (keyButton && !keyButton.hasAttribute('disabled')) {\n        (keyButton as HTMLElement).click();\n        await this.delay(2000);\n      }\n    }\n    \n    const enterButton = document.querySelector('.enter_dungeon, [data-action=\"enter\"]');\n    if (enterButton && !enterButton.hasAttribute('disabled')) {\n      (enterButton as HTMLElement).click();\n      await this.delay(3000);\n    }\n  }\n\n  private async completeDungeon(dungeonInfo: DungeonInfo, settings: DungeonSettings): Promise<void> {\n    console.log(`Expedition System: Completing dungeon ${dungeonInfo.name}`);\n    \n    // Handle boss skip if enabled\n    if (settings.skipBoss && dungeonInfo.hasBoss) {\n      if (!settings.bossName || dungeonInfo.bossName?.includes(settings.bossName)) {\n        console.log('Expedition System: Skipping boss as configured');\n        const skipButton = document.querySelector('.skip_boss, [data-action=\"skip\"]');\n        if (skipButton) {\n          (skipButton as HTMLElement).click();\n          await this.delay(2000);\n        }\n      }\n    }\n    \n    // Wait for dungeon completion\n    await this.waitForDungeonCompletion();\n  }\n\n  private async waitForDungeonCompletion(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkCompletion = () => {\n        const completionElement = document.querySelector('.dungeon_complete, .completed');\n        const exitButton = document.querySelector('.exit_dungeon, [data-action=\"exit\"]');\n        \n        if (completionElement || exitButton) {\n          resolve();\n        } else {\n          setTimeout(checkCompletion, 10000);\n        }\n      };\n      \n      setTimeout(checkCompletion, 60000); // Start checking after 1 minute\n    });\n  }\n\n  private shouldContinueExpeditions(settings: ExpeditionSettings): boolean {\n    // Add logic for expedition limits if needed\n    return this.expeditionCount < 10; // Simplified limit\n  }\n\n  private shouldContinueDungeons(settings: DungeonSettings): boolean {\n    // Add logic for dungeon limits if needed\n    return this.dungeonCount < 5; // Simplified limit\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Forge & Repair Module - Handles automatic forging, smelting, and item repair\n\nimport { ForgeAutomationSettings, RepairAutomationSettings, ItemQuality } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface ForgeableItem {\n  id: string;\n  name: string;\n  quality: ItemQuality;\n  level: number;\n  materials: string[];\n  canForge: boolean;\n  forgeCost: number;\n}\n\nexport interface RepairableItem {\n  id: string;\n  name: string;\n  durability: number;\n  maxDurability: number;\n  repairCost: number;\n  canRepair: boolean;\n}\n\nexport interface SmeltableItem {\n  id: string;\n  name: string;\n  quality: ItemQuality;\n  level: number;\n  smeltValue: number;\n  canSmelt: boolean;\n}\n\nexport class ForgeRepairModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private itemsForged: number = 0;\n  private itemsRepaired: number = 0;\n  private itemsSmelted: number = 0;\n  private goldSpent: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(forgeSettings: ForgeAutomationSettings, repairSettings: RepairAutomationSettings): Promise<void> {\n    if (this.isRunning) return;\n    \n    this.isRunning = true;\n    this.itemsForged = 0;\n    this.itemsRepaired = 0;\n    this.itemsSmelted = 0;\n    this.goldSpent = 0;\n    \n    console.log('Forge & Repair: Starting forge and repair automation');\n    \n    try {\n      if (forgeSettings.enabled) {\n        await this.performForgeOperations(forgeSettings);\n      }\n      \n      if (repairSettings.enabled) {\n        await this.performRepairOperations(repairSettings);\n      }\n    } catch (error) {\n      console.error('Forge & Repair: Error in automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Forge & Repair: Stopped automation');\n  }\n\n  private async performForgeOperations(settings: ForgeAutomationSettings): Promise<void> {\n    console.log('Forge & Repair: Starting forge operations');\n    \n    await this.navigateToForge();\n    \n    while (this.isRunning && this.shouldContinueForging(settings)) {\n      try {\n        // Handle smelting first if enabled\n        if (settings.autoSmelt) {\n          await this.performSmelting(settings);\n        }\n        \n        // Handle forging\n        if (settings.autoForge) {\n          await this.performForging(settings);\n        }\n        \n        await this.delay(10000);\n        \n      } catch (error) {\n        console.error('Forge & Repair: Error in forge operations:', error);\n        await this.delay(15000);\n      }\n    }\n  }\n\n  private async performRepairOperations(settings: RepairAutomationSettings): Promise<void> {\n    console.log('Forge & Repair: Starting repair operations');\n    \n    await this.navigateToRepair();\n    \n    while (this.isRunning && this.shouldContinueRepairing(settings)) {\n      try {\n        const repairableItems = await this.getRepairableItems();\n        const itemsToRepair = this.filterItemsForRepair(repairableItems, settings);\n        \n        for (const item of itemsToRepair) {\n          if (!this.isRunning) break;\n          \n          if (this.goldSpent + item.repairCost <= settings.maxGoldSpend) {\n            await this.repairItem(item);\n            this.itemsRepaired++;\n            this.goldSpent += item.repairCost;\n            \n            await this.delay(2000);\n          }\n        }\n        \n        await this.delay(30000);\n        \n      } catch (error) {\n        console.error('Forge & Repair: Error in repair operations:', error);\n        await this.delay(15000);\n      }\n    }\n  }\n\n  private async performSmelting(settings: ForgeAutomationSettings): Promise<void> {\n    console.log('Forge & Repair: Performing smelting operations');\n    \n    const smeltableItems = await this.getSmeltableItems();\n    const itemsToSmelt = this.filterItemsForSmelting(smeltableItems, settings);\n    \n    for (const item of itemsToSmelt) {\n      if (!this.isRunning) break;\n      \n      await this.smeltItem(item);\n      this.itemsSmelted++;\n      \n      await this.delay(1500);\n    }\n  }\n\n  private async performForging(settings: ForgeAutomationSettings): Promise<void> {\n    console.log('Forge & Repair: Performing forging operations');\n    \n    const forgeableItems = await this.getForgeableItems();\n    const itemsToForge = this.filterItemsForForging(forgeableItems, settings);\n    \n    for (const item of itemsToForge) {\n      if (!this.isRunning) break;\n      \n      if (this.goldSpent + item.forgeCost <= settings.maxGoldSpend) {\n        await this.forgeItem(item);\n        this.itemsForged++;\n        this.goldSpent += item.forgeCost;\n        \n        await this.delay(3000);\n      }\n    }\n  }\n\n  private async navigateToForge(): Promise<void> {\n    if (!window.location.href.includes('mod=forge')) {\n      console.log('Forge & Repair: Navigating to forge');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=forge`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToRepair(): Promise<void> {\n    if (!window.location.href.includes('mod=repair')) {\n      console.log('Forge & Repair: Navigating to repair shop');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=repair`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async getForgeableItems(): Promise<ForgeableItem[]> {\n    const items: ForgeableItem[] = [];\n    const itemElements = document.querySelectorAll('.forgeable_item, .forge-recipe');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parseForgeableItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Forge & Repair: Error parsing forgeable item:', error);\n      }\n    }\n    \n    return items;\n  }\n\n  private async parseForgeableItem(element: HTMLElement, index: number): Promise<ForgeableItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const levelElement = element.querySelector('.item_level, .level');\n    const costElement = element.querySelector('.forge_cost, .cost');\n    const forgeButton = element.querySelector('.forge_button, [data-action=\"forge\"]');\n    const materialsElements = element.querySelectorAll('.material, .required-material');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    const forgeCost = parseInt(costElement?.textContent?.replace(/\\D/g, '') || '0');\n    \n    // Parse materials\n    const materials: string[] = [];\n    materialsElements.forEach(mat => {\n      const matText = mat.textContent?.trim();\n      if (matText) materials.push(matText);\n    });\n    \n    // Determine quality from CSS classes\n    let quality: ItemQuality = ItemQuality.WHITE;\n    const qualityClass = element.className;\n    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;\n    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;\n    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;\n    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;\n    else if (qualityClass.includes('red')) quality = ItemQuality.RED;\n    \n    return {\n      id: index.toString(),\n      name,\n      quality,\n      level,\n      materials,\n      canForge: !!(forgeButton && !forgeButton.hasAttribute('disabled')),\n      forgeCost\n    };\n  }\n\n  private async getRepairableItems(): Promise<RepairableItem[]> {\n    const items: RepairableItem[] = [];\n    const itemElements = document.querySelectorAll('.repairable_item, .damaged-item');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parseRepairableItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Forge & Repair: Error parsing repairable item:', error);\n      }\n    }\n    \n    return items;\n  }\n\n  private async parseRepairableItem(element: HTMLElement, index: number): Promise<RepairableItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const durabilityElement = element.querySelector('.durability, .condition');\n    const costElement = element.querySelector('.repair_cost, .cost');\n    const repairButton = element.querySelector('.repair_button, [data-action=\"repair\"]');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const repairCost = parseInt(costElement?.textContent?.replace(/\\D/g, '') || '0');\n    \n    // Parse durability\n    const durabilityText = durabilityElement?.textContent?.trim() || '100/100';\n    const durabilityMatch = durabilityText.match(/(\\d+)\\/(\\d+)/);\n    const durability = durabilityMatch ? parseInt(durabilityMatch[1]) : 100;\n    const maxDurability = durabilityMatch ? parseInt(durabilityMatch[2]) : 100;\n    \n    return {\n      id: index.toString(),\n      name,\n      durability,\n      maxDurability,\n      repairCost,\n      canRepair: !!(repairButton && !repairButton.hasAttribute('disabled'))\n    };\n  }\n\n  private async getSmeltableItems(): Promise<SmeltableItem[]> {\n    const items: SmeltableItem[] = [];\n    const itemElements = document.querySelectorAll('.smeltable_item, .inventory-item');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parseSmeltableItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Forge & Repair: Error parsing smeltable item:', error);\n      }\n    }\n    \n    return items;\n  }\n\n  private async parseSmeltableItem(element: HTMLElement, index: number): Promise<SmeltableItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const levelElement = element.querySelector('.item_level, .level');\n    const valueElement = element.querySelector('.smelt_value, .value');\n    const smeltButton = element.querySelector('.smelt_button, [data-action=\"smelt\"]');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    const smeltValue = parseInt(valueElement?.textContent?.replace(/\\D/g, '') || '0');\n    \n    // Determine quality from CSS classes\n    let quality: ItemQuality = ItemQuality.WHITE;\n    const qualityClass = element.className;\n    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;\n    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;\n    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;\n    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;\n    else if (qualityClass.includes('red')) quality = ItemQuality.RED;\n    \n    return {\n      id: index.toString(),\n      name,\n      quality,\n      level,\n      smeltValue,\n      canSmelt: !!(smeltButton && !smeltButton.hasAttribute('disabled'))\n    };\n  }\n\n  private filterItemsForForging(items: ForgeableItem[], settings: ForgeAutomationSettings): ForgeableItem[] {\n    return items.filter(item => {\n      // Check if can forge\n      if (!item.canForge) return false;\n      \n      // Check cost limit\n      if (item.forgeCost > settings.maxCostPerItem) return false;\n      \n      // Check quality filter\n      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {\n        return false;\n      }\n      \n      // Check level range\n      if (settings.minLevel && item.level < settings.minLevel) return false;\n      if (settings.maxLevel && item.level > settings.maxLevel) return false;\n      \n      // Check item type filter\n      if (settings.itemTypes.length > 0) {\n        const typeMatch = settings.itemTypes.some(type => \n          item.name.toLowerCase().includes(type.toLowerCase())\n        );\n        if (!typeMatch) return false;\n      }\n      \n      return true;\n    });\n  }\n\n  private filterItemsForRepair(items: RepairableItem[], settings: RepairAutomationSettings): RepairableItem[] {\n    return items.filter(item => {\n      // Check if can repair\n      if (!item.canRepair) return false;\n      \n      // Check durability threshold\n      const durabilityPercent = (item.durability / item.maxDurability) * 100;\n      if (durabilityPercent > settings.repairThreshold) return false;\n      \n      // Check cost limit\n      if (item.repairCost > settings.maxCostPerItem) return false;\n      \n      // Check item name filter\n      if (settings.itemFilter.length > 0) {\n        const nameMatch = settings.itemFilter.some(filter => \n          item.name.toLowerCase().includes(filter.toLowerCase())\n        );\n        if (!nameMatch) return false;\n      }\n      \n      return true;\n    });\n  }\n\n  private filterItemsForSmelting(items: SmeltableItem[], settings: ForgeAutomationSettings): SmeltableItem[] {\n    return items.filter(item => {\n      // Check if can smelt\n      if (!item.canSmelt) return false;\n      \n      // Check minimum smelt value\n      if (item.smeltValue < settings.minSmeltValue) return false;\n      \n      // Check quality - usually smelt lower quality items\n      if (settings.smeltQualityFilter.length > 0 && !settings.smeltQualityFilter.includes(item.quality)) {\n        return false;\n      }\n      \n      return true;\n    });\n  }\n\n  private async forgeItem(item: ForgeableItem): Promise<void> {\n    console.log(`Forge & Repair: Forging ${item.name} for ${item.forgeCost} gold`);\n    \n    const itemElement = document.querySelector(`.forgeable_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const forgeButton = itemElement.querySelector('.forge_button, [data-action=\"forge\"]');\n    if (forgeButton && !forgeButton.hasAttribute('disabled')) {\n      (forgeButton as HTMLElement).click();\n      \n      // Handle confirmation if present\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_forge, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private async repairItem(item: RepairableItem): Promise<void> {\n    console.log(`Forge & Repair: Repairing ${item.name} for ${item.repairCost} gold`);\n    \n    const itemElement = document.querySelector(`.repairable_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const repairButton = itemElement.querySelector('.repair_button, [data-action=\"repair\"]');\n    if (repairButton && !repairButton.hasAttribute('disabled')) {\n      (repairButton as HTMLElement).click();\n      \n      // Handle confirmation if present\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_repair, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private async smeltItem(item: SmeltableItem): Promise<void> {\n    console.log(`Forge & Repair: Smelting ${item.name} for ${item.smeltValue} materials`);\n    \n    const itemElement = document.querySelector(`.smeltable_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const smeltButton = itemElement.querySelector('.smelt_button, [data-action=\"smelt\"]');\n    if (smeltButton && !smeltButton.hasAttribute('disabled')) {\n      (smeltButton as HTMLElement).click();\n      \n      // Handle confirmation if present\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_smelt, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private shouldContinueForging(settings: ForgeAutomationSettings): boolean {\n    // Check gold spending limit\n    if (this.goldSpent >= settings.maxGoldSpend) {\n      console.log('Forge & Repair: Gold spending limit reached for forging');\n      return false;\n    }\n    \n    // Check daily limits\n    if (settings.dailyForgeLimit > 0 && this.itemsForged >= settings.dailyForgeLimit) {\n      console.log('Forge & Repair: Daily forge limit reached');\n      return false;\n    }\n    \n    return true;\n  }\n\n  private shouldContinueRepairing(settings: RepairAutomationSettings): boolean {\n    // Check gold spending limit\n    if (this.goldSpent >= settings.maxGoldSpend) {\n      console.log('Forge & Repair: Gold spending limit reached for repairs');\n      return false;\n    }\n    \n    return true;\n  }\n\n  async getForgeRepairStatus(): Promise<any> {\n    return {\n      isRunning: this.isRunning,\n      itemsForged: this.itemsForged,\n      itemsRepaired: this.itemsRepaired,\n      itemsSmelted: this.itemsSmelted,\n      goldSpent: this.goldSpent,\n      lastUpdate: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Gold Management Module - Handles gold hiding, donations, and market operations\n\nimport { HideGoldSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface GoldHidingLocation {\n  type: 'guild' | 'shop' | 'market' | 'training';\n  name: string;\n  isAvailable: boolean;\n  capacity: number;\n  currentAmount: number;\n}\n\nexport interface MarketItem {\n  id: string;\n  name: string;\n  price: number;\n  quality: string;\n  seller: string;\n  canBuy: boolean;\n}\n\nexport class GoldManagementModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isProcessing: boolean = false;\n  private currentGold: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async manageGold(settings: HideGoldSettings): Promise<void> {\n    if (this.isProcessing) return;\n    \n    this.isProcessing = true;\n    console.log('Gold Management: Starting gold management');\n\n    try {\n      this.currentGold = await this.getCurrentGold();\n      \n      if (this.shouldHideGold(settings)) {\n        await this.hideGold(settings);\n      }\n      \n      console.log('Gold Management: Gold management completed');\n\n    } catch (error) {\n      console.error('Gold Management: Error managing gold:', error);\n      throw error;\n    } finally {\n      this.isProcessing = false;\n    }\n  }\n\n  private async getCurrentGold(): Promise<number> {\n    const goldElements = [\n      '#header_values_gold',\n      '.player_gold',\n      '.gold-amount',\n      '#gold_display'\n    ];\n\n    for (const selector of goldElements) {\n      const element = document.querySelector(selector);\n      if (element) {\n        const goldText = element.textContent?.replace(/\\D/g, '') || '0';\n        return parseInt(goldText);\n      }\n    }\n\n    return 0;\n  }\n\n  private shouldHideGold(settings: HideGoldSettings): boolean {\n    const excessGold = this.currentGold - settings.pocketMoney;\n    return excessGold > settings.minGoldBackup;\n  }\n\n  private async hideGold(settings: HideGoldSettings): Promise<void> {\n    const excessGold = this.currentGold - settings.pocketMoney;\n    \n    console.log(`Gold Management: Hiding ${excessGold} gold (keeping ${settings.pocketMoney} pocket money)`);\n\n    // Try primary location first\n    if (settings.primary.enabled) {\n      const success = await this.hideGoldInLocation(settings.primary, excessGold, settings);\n      if (success) return;\n    }\n\n    // Try backup location if primary failed\n    if (settings.backup.enabled) {\n      await this.hideGoldInLocation(settings.backup, excessGold, settings);\n    }\n  }\n\n  private async hideGoldInLocation(locationConfig: any, amount: number, settings: HideGoldSettings): Promise<boolean> {\n    switch (locationConfig.location) {\n      case 'guild':\n        return await this.hideGoldInGuild(amount, settings);\n      case 'shop':\n        return await this.hideGoldInShop(amount, settings);\n      case 'market':\n        return await this.hideGoldInMarket(amount, settings);\n      case 'training':\n        return await this.hideGoldInTraining(amount, settings);\n      default:\n        console.log(`Gold Management: Unknown location type: ${locationConfig.location}`);\n        return false;\n    }\n  }\n\n  private async hideGoldInGuild(amount: number, settings: HideGoldSettings): Promise<boolean> {\n    console.log('Gold Management: Hiding gold in guild');\n    \n    await this.navigateToGuild();\n    \n    // Look for donation interface\n    const donationInput = document.querySelector('#donation_amount, .donation-input') as HTMLInputElement;\n    const donateButton = document.querySelector('.donate_button, [data-action=\"donate\"]');\n    \n    if (!donationInput || !donateButton) {\n      console.log('Gold Management: Guild donation interface not found');\n      return false;\n    }\n\n    // Set donation amount\n    donationInput.value = amount.toString();\n    donationInput.dispatchEvent(new Event('input', { bubbles: true }));\n    \n    // Click donate button\n    (donateButton as HTMLElement).click();\n    \n    await this.delay(2000);\n    console.log(`Gold Management: Donated ${amount} gold to guild`);\n    return true;\n  }\n\n  private async hideGoldInShop(amount: number, settings: HideGoldSettings): Promise<boolean> {\n    console.log('Gold Management: Hiding gold by buying shop items');\n    \n    await this.navigateToShop();\n    \n    let remainingAmount = amount;\n    const items = await this.getShopItems(settings);\n    \n    for (const item of items) {\n      if (remainingAmount <= 0) break;\n      \n      if (item.price <= remainingAmount && item.canBuy) {\n        await this.buyShopItem(item);\n        remainingAmount -= item.price;\n        await this.delay(1000);\n      }\n    }\n    \n    const spentAmount = amount - remainingAmount;\n    console.log(`Gold Management: Spent ${spentAmount} gold on shop items`);\n    return spentAmount > 0;\n  }\n\n  private async hideGoldInMarket(amount: number, settings: HideGoldSettings): Promise<boolean> {\n    console.log('Gold Management: Hiding gold by buying market items');\n    \n    await this.navigateToMarket();\n    \n    let remainingAmount = amount;\n    const items = await this.getMarketItems(settings);\n    \n    for (const item of items) {\n      if (remainingAmount <= 0) break;\n      \n      if (item.price <= remainingAmount && item.canBuy) {\n        await this.buyMarketItem(item);\n        remainingAmount -= item.price;\n        await this.delay(1500);\n      }\n    }\n    \n    const spentAmount = amount - remainingAmount;\n    console.log(`Gold Management: Spent ${spentAmount} gold on market items`);\n    return spentAmount > 0;\n  }\n\n  private async hideGoldInTraining(amount: number, settings: HideGoldSettings): Promise<boolean> {\n    console.log('Gold Management: Hiding gold in skill training');\n    \n    await this.navigateToTraining();\n    \n    let remainingAmount = amount;\n    \n    for (const skill of settings.skillsToTrain) {\n      if (remainingAmount <= 0) break;\n      \n      const trained = await this.trainSkill(skill, remainingAmount);\n      remainingAmount -= trained;\n    }\n    \n    const spentAmount = amount - remainingAmount;\n    console.log(`Gold Management: Spent ${spentAmount} gold on skill training`);\n    return spentAmount > 0;\n  }\n\n  private async getShopItems(settings: HideGoldSettings): Promise<MarketItem[]> {\n    const items: MarketItem[] = [];\n    const itemElements = document.querySelectorAll('.shop_item, .market-item');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      const item = await this.parseShopItem(element, i);\n      \n      if (item && this.isValidPurchase(item, settings)) {\n        items.push(item);\n      }\n    }\n    \n    // Sort by price (ascending) for efficient spending\n    items.sort((a, b) => a.price - b.price);\n    \n    return items;\n  }\n\n  private async getMarketItems(settings: HideGoldSettings): Promise<MarketItem[]> {\n    const items: MarketItem[] = [];\n    const itemElements = document.querySelectorAll('.market_item, .auction-item');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      const item = await this.parseMarketItem(element, i);\n      \n      if (item && this.isValidPurchase(item, settings)) {\n        items.push(item);\n      }\n    }\n    \n    // Sort by price (ascending)\n    items.sort((a, b) => a.price - b.price);\n    \n    return items;\n  }\n\n  private async parseShopItem(element: HTMLElement, index: number): Promise<MarketItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const priceElement = element.querySelector('.item_price, .price');\n    const buyButton = element.querySelector('.buy_button, [data-action=\"buy\"]');\n    \n    if (!nameElement || !priceElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const price = parseInt(priceElement.textContent?.replace(/\\D/g, '') || '0');\n    \n    return {\n      id: index.toString(),\n      name,\n      price,\n      quality: 'unknown',\n      seller: 'shop',\n      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))\n    };\n  }\n\n  private async parseMarketItem(element: HTMLElement, index: number): Promise<MarketItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const priceElement = element.querySelector('.item_price, .price');\n    const sellerElement = element.querySelector('.seller_name, .seller');\n    const buyButton = element.querySelector('.buy_button, [data-action=\"buy\"]');\n    \n    if (!nameElement || !priceElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const price = parseInt(priceElement.textContent?.replace(/\\D/g, '') || '0');\n    const seller = sellerElement?.textContent?.trim() || 'unknown';\n    \n    return {\n      id: index.toString(),\n      name,\n      price,\n      quality: 'unknown',\n      seller,\n      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled'))\n    };\n  }\n\n  private isValidPurchase(item: MarketItem, settings: HideGoldSettings): boolean {\n    // Check minimum price\n    if (item.price < settings.minItemPrice) return false;\n    \n    // Check if buying from specific players\n    if (settings.buyFromPlayers.length > 0) {\n      return settings.buyFromPlayers.includes(item.seller);\n    }\n    \n    // Check if item can be bought\n    return item.canBuy;\n  }\n\n  private async buyShopItem(item: MarketItem): Promise<void> {\n    console.log(`Gold Management: Buying ${item.name} for ${item.price} gold`);\n    \n    const itemElement = document.querySelector(`.shop_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const buyButton = itemElement.querySelector('.buy_button, [data-action=\"buy\"]');\n    if (buyButton && !buyButton.hasAttribute('disabled')) {\n      (buyButton as HTMLElement).click();\n    }\n  }\n\n  private async buyMarketItem(item: MarketItem): Promise<void> {\n    console.log(`Gold Management: Buying ${item.name} from ${item.seller} for ${item.price} gold`);\n    \n    const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const buyButton = itemElement.querySelector('.buy_button, [data-action=\"buy\"]');\n    if (buyButton && !buyButton.hasAttribute('disabled')) {\n      (buyButton as HTMLElement).click();\n      \n      // Handle confirmation if present\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_buy, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private async trainSkill(skillName: string, maxAmount: number): Promise<number> {\n    console.log(`Gold Management: Training ${skillName} with up to ${maxAmount} gold`);\n    \n    const skillElements = document.querySelectorAll('.skill_item, .training-skill');\n    \n    for (const element of skillElements) {\n      const nameElement = element.querySelector('.skill_name, .name');\n      if (nameElement?.textContent?.toLowerCase().includes(skillName.toLowerCase())) {\n        const trainButton = element.querySelector('.train_button, [data-action=\"train\"]');\n        const costElement = element.querySelector('.training_cost, .cost');\n        \n        if (trainButton && !trainButton.hasAttribute('disabled')) {\n          const cost = parseInt(costElement?.textContent?.replace(/\\D/g, '') || '0');\n          if (cost <= maxAmount) {\n            (trainButton as HTMLElement).click();\n            await this.delay(2000);\n            return cost;\n          }\n        }\n      }\n    }\n    \n    return 0;\n  }\n\n  private async navigateToGuild(): Promise<void> {\n    if (!window.location.href.includes('mod=guild')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=guild`;\n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToShop(): Promise<void> {\n    if (!window.location.href.includes('mod=location&loc=26')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=location&loc=26`;\n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToMarket(): Promise<void> {\n    if (!window.location.href.includes('mod=market')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=market`;\n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToTraining(): Promise<void> {\n    if (!window.location.href.includes('mod=training')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=training`;\n      await this.waitForPageLoad();\n    }\n  }\n\n  async getGoldStatus(): Promise<any> {\n    return {\n      currentGold: await this.getCurrentGold(),\n      isProcessing: this.isProcessing,\n      lastProcessed: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Healing System Module - Handles automatic healing and food management\n\nimport { HealSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface HealthInfo {\n  current: number;\n  maximum: number;\n  percentage: number;\n}\n\nexport interface FoodItem {\n  id: string;\n  name: string;\n  healAmount: number;\n  count: number;\n  isAvailable: boolean;\n}\n\nexport class HealingSystemModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isHealing: boolean = false;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async checkAndHeal(settings: HealSettings): Promise<boolean> {\n    if (this.isHealing || !settings.enabled) return false;\n\n    const healthInfo = this.getCurrentHealth();\n    if (!healthInfo) return false;\n\n    if (healthInfo.percentage < settings.minHealth) {\n      console.log(`Healing System: Health at ${healthInfo.percentage}%, starting healing process`);\n      return await this.performHealing(settings, healthInfo);\n    }\n\n    return false;\n  }\n\n  private getCurrentHealth(): HealthInfo | null {\n    // Try multiple selectors for health display\n    const healthSelectors = [\n      '#header_values_hp_percent',\n      '.health-bar .current',\n      '#health_points',\n      '.player-health .current'\n    ];\n\n    for (const selector of healthSelectors) {\n      const element = document.querySelector(selector);\n      if (element) {\n        const text = element.textContent?.trim() || '';\n        \n        // Try different health formats\n        let match = text.match(/(\\d+)\\/(\\d+)/); // \"50/100\" format\n        if (match) {\n          const current = parseInt(match[1]);\n          const maximum = parseInt(match[2]);\n          return {\n            current,\n            maximum,\n            percentage: Math.round((current / maximum) * 100)\n          };\n        }\n\n        match = text.match(/(\\d+)%/); // \"75%\" format\n        if (match) {\n          const percentage = parseInt(match[1]);\n          return {\n            current: percentage,\n            maximum: 100,\n            percentage\n          };\n        }\n      }\n    }\n\n    return null;\n  }\n\n  private async performHealing(settings: HealSettings, healthInfo: HealthInfo): Promise<boolean> {\n    this.isHealing = true;\n    let healingSuccess = false;\n\n    try {\n      // Try healing with cervisia first if enabled\n      if (settings.healCervisia) {\n        healingSuccess = await this.useCervisia();\n        if (healingSuccess) {\n          console.log('Healing System: Healed with cervisia');\n          return true;\n        }\n      }\n\n      // Try healing with eggs if enabled\n      if (settings.healEggs && settings.selectedEggs.length > 0) {\n        healingSuccess = await this.useEggs(settings.selectedEggs);\n        if (healingSuccess) {\n          console.log('Healing System: Healed with eggs');\n          return true;\n        }\n      }\n\n      // Try buying food if enabled and needed\n      if (settings.buyFood && settings.buyFoodNeeded) {\n        const foodBought = await this.buyFood(settings);\n        if (foodBought) {\n          // Try healing again with newly bought food\n          healingSuccess = await this.useEggs(settings.selectedEggs);\n          if (healingSuccess) {\n            console.log('Healing System: Healed with purchased food');\n            return true;\n          }\n        }\n      }\n\n      console.log('Healing System: No healing options available');\n      return false;\n\n    } catch (error) {\n      console.error('Healing System: Error during healing:', error);\n      return false;\n    } finally {\n      this.isHealing = false;\n    }\n  }\n\n  private async useCervisia(): Promise<boolean> {\n    // Navigate to tavern if not already there\n    if (!window.location.href.includes('mod=location&loc=2')) {\n      await this.navigateToLocation('mod=location&loc=2');\n    }\n\n    // Look for cervisia button\n    const cervisiaButton = document.querySelector('.cervisia_button, [data-action=\"drink-cervisia\"]');\n    if (!cervisiaButton || cervisiaButton.hasAttribute('disabled')) {\n      return false;\n    }\n\n    // Click cervisia button\n    (cervisiaButton as HTMLElement).click();\n    \n    // Wait for healing to complete\n    await this.delay(2000);\n    \n    // Check if health improved\n    const newHealth = this.getCurrentHealth();\n    return newHealth ? newHealth.percentage > 90 : false;\n  }\n\n  private async useEggs(selectedEggs: number[]): Promise<boolean> {\n    // Navigate to packages if not already there\n    if (!window.location.href.includes('mod=packages')) {\n      await this.navigateToLocation('mod=packages');\n    }\n\n    const availableFood = this.getAvailableFood();\n    \n    for (const eggType of selectedEggs) {\n      const food = availableFood.find(f => f.id === eggType.toString());\n      if (food && food.isAvailable && food.count > 0) {\n        const success = await this.consumeFood(food);\n        if (success) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  private getAvailableFood(): FoodItem[] {\n    const foodItems: FoodItem[] = [];\n    \n    // Parse food items from packages page\n    const foodElements = document.querySelectorAll('.package_food, .food-item');\n    \n    foodElements.forEach((element, index) => {\n      const nameElement = element.querySelector('.item_name, .name');\n      const countElement = element.querySelector('.item_count, .count');\n      const useButton = element.querySelector('.use_button, [data-action=\"use\"]');\n      \n      const name = nameElement?.textContent?.trim() || `Food ${index}`;\n      const count = parseInt(countElement?.textContent?.trim() || '0');\n      const isAvailable = useButton && !useButton.hasAttribute('disabled');\n      \n      // Estimate heal amount based on food type\n      let healAmount = 10; // Default\n      if (name.toLowerCase().includes('egg')) healAmount = 25;\n      if (name.toLowerCase().includes('bread')) healAmount = 15;\n      if (name.toLowerCase().includes('meat')) healAmount = 30;\n      \n      foodItems.push({\n        id: (index + 1).toString(),\n        name,\n        healAmount,\n        count,\n        isAvailable: !!isAvailable\n      });\n    });\n\n    return foodItems;\n  }\n\n  private async consumeFood(food: FoodItem): Promise<boolean> {\n    const useButton = document.querySelector(`[data-food-id=\"${food.id}\"] .use_button, .food-item:nth-child(${food.id}) [data-action=\"use\"]`);\n    if (!useButton || useButton.hasAttribute('disabled')) {\n      return false;\n    }\n\n    console.log(`Healing System: Using ${food.name}`);\n    (useButton as HTMLElement).click();\n    \n    // Wait for consumption\n    await this.delay(1500);\n    \n    // Check if health improved\n    const newHealth = this.getCurrentHealth();\n    return newHealth ? newHealth.percentage >= 90 : false;\n  }\n\n  private async buyFood(settings: HealSettings): Promise<boolean> {\n    // Navigate to shop\n    if (!window.location.href.includes('mod=location&loc=26')) {\n      await this.navigateToLocation('mod=location&loc=26');\n    }\n\n    // Look for food items to buy\n    const foodShopItems = document.querySelectorAll('.shop_item, .market-item');\n    \n    for (const item of foodShopItems) {\n      const nameElement = item.querySelector('.item_name, .name');\n      const buyButton = item.querySelector('.buy_button, [data-action=\"buy\"]');\n      const priceElement = item.querySelector('.item_price, .price');\n      \n      if (!nameElement || !buyButton || buyButton.hasAttribute('disabled')) continue;\n      \n      const name = nameElement.textContent?.trim().toLowerCase() || '';\n      const price = parseInt(priceElement?.textContent?.replace(/\\D/g, '') || '0');\n      \n      // Check if it's a food item we want to buy\n      if ((name.includes('egg') || name.includes('bread') || name.includes('meat')) && price < 1000) {\n        console.log(`Healing System: Buying ${name} for ${price} gold`);\n        (buyButton as HTMLElement).click();\n        \n        await this.delay(2000);\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  private async navigateToLocation(locationUrl: string): Promise<void> {\n    const currentUrl = window.location.href;\n    const baseUrl = currentUrl.split('?')[0];\n    \n    console.log(`Healing System: Navigating to ${locationUrl}`);\n    window.location.href = `${baseUrl}?${locationUrl}`;\n    \n    // Wait for page load\n    await this.waitForPageLoad();\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000); // Additional delay for dynamic content\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  async shouldStopAttacks(settings: HealSettings): Promise<boolean> {\n    if (!settings.autoStopAttacks) return false;\n    \n    const healthInfo = this.getCurrentHealth();\n    if (!healthInfo) return false;\n    \n    // Stop attacks if health is below minimum and no healing options available\n    if (healthInfo.percentage < settings.minHealth) {\n      const canHeal = await this.canPerformHealing(settings);\n      if (!canHeal) {\n        console.log('Healing System: Stopping attacks - low health and no healing options');\n        return true;\n      }\n    }\n    \n    return false;\n  }\n\n  private async canPerformHealing(settings: HealSettings): Promise<boolean> {\n    // Check if cervisia is available\n    if (settings.healCervisia) {\n      // This would need to check tavern availability\n      return true; // Simplified\n    }\n    \n    // Check if food is available\n    if (settings.healEggs) {\n      const availableFood = this.getAvailableFood();\n      const hasUsableFood = availableFood.some(food => \n        settings.selectedEggs.includes(parseInt(food.id)) && \n        food.isAvailable && \n        food.count > 0\n      );\n      \n      if (hasUsableFood) return true;\n    }\n    \n    // Check if can buy food\n    if (settings.buyFood && settings.buyFoodNeeded) {\n      // This would need to check gold availability and shop access\n      return true; // Simplified\n    }\n    \n    return false;\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Market Operations Module - Handles automated buying, selling, and auction management\n\nimport { MarketOperationsSettings, ItemQuality } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface MarketItem {\n  id: string;\n  name: string;\n  quality: ItemQuality;\n  level: number;\n  price: number;\n  seller: string;\n  timeRemaining: number;\n  canBuy: boolean;\n  canBid: boolean;\n}\n\nexport interface AuctionItem {\n  id: string;\n  name: string;\n  quality: ItemQuality;\n  level: number;\n  currentBid: number;\n  buyoutPrice: number;\n  timeRemaining: number;\n  bidder: string;\n  canBid: boolean;\n  canBuyout: boolean;\n}\n\nexport class MarketOperationsModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private itemsBought: number = 0;\n  private itemsSold: number = 0;\n  private goldSpent: number = 0;\n  private goldEarned: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: MarketOperationsSettings): Promise<void> {\n    if (this.isRunning || !settings.enabled) return;\n    \n    this.isRunning = true;\n    this.itemsBought = 0;\n    this.itemsSold = 0;\n    this.goldSpent = 0;\n    this.goldEarned = 0;\n    \n    console.log('Market Operations: Starting market automation');\n    \n    try {\n      await this.performMarketLoop(settings);\n    } catch (error) {\n      console.error('Market Operations: Error in market automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Market Operations: Stopped market automation');\n  }\n\n  private async performMarketLoop(settings: MarketOperationsSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueOperations(settings)) {\n      try {\n        // Handle buying operations\n        if (settings.autoBuy.enabled) {\n          await this.performBuyingOperations(settings.autoBuy);\n        }\n        \n        // Handle selling operations\n        if (settings.autoSell.enabled) {\n          await this.performSellingOperations(settings.autoSell);\n        }\n        \n        // Handle auction operations\n        if (settings.auction.enabled) {\n          await this.performAuctionOperations(settings.auction);\n        }\n        \n        await this.delay(settings.checkInterval * 1000);\n        \n      } catch (error) {\n        console.error('Market Operations: Error in market loop:', error);\n        await this.delay(30000);\n      }\n    }\n  }\n\n  private async performBuyingOperations(buySettings: any): Promise<void> {\n    console.log('Market Operations: Performing buying operations');\n    \n    await this.navigateToMarket();\n    \n    const availableItems = await this.getMarketItems();\n    const itemsToBuy = this.filterItemsForBuying(availableItems, buySettings);\n    \n    for (const item of itemsToBuy) {\n      if (!this.isRunning) break;\n      \n      if (this.goldSpent + item.price <= buySettings.maxGoldSpend) {\n        await this.buyItem(item);\n        this.itemsBought++;\n        this.goldSpent += item.price;\n        \n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async performSellingOperations(sellSettings: any): Promise<void> {\n    console.log('Market Operations: Performing selling operations');\n    \n    await this.navigateToMarket();\n    \n    // Get items from inventory to sell\n    const inventoryItems = await this.getInventoryItems();\n    const itemsToSell = this.filterItemsForSelling(inventoryItems, sellSettings);\n    \n    for (const item of itemsToSell) {\n      if (!this.isRunning) break;\n      \n      await this.sellItem(item, sellSettings);\n      this.itemsSold++;\n      \n      await this.delay(2000);\n    }\n  }\n\n  private async performAuctionOperations(auctionSettings: any): Promise<void> {\n    console.log('Market Operations: Performing auction operations');\n    \n    await this.navigateToAuction();\n    \n    const auctionItems = await this.getAuctionItems();\n    const itemsToBid = this.filterItemsForBidding(auctionItems, auctionSettings);\n    \n    for (const item of itemsToBid) {\n      if (!this.isRunning) break;\n      \n      if (auctionSettings.autoBid) {\n        await this.bidOnItem(item, auctionSettings);\n      }\n      \n      if (auctionSettings.autoBuyout && item.canBuyout) {\n        await this.buyoutItem(item, auctionSettings);\n      }\n      \n      await this.delay(3000);\n    }\n  }\n\n  private async navigateToMarket(): Promise<void> {\n    if (!window.location.href.includes('mod=market')) {\n      console.log('Market Operations: Navigating to market');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=market`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async navigateToAuction(): Promise<void> {\n    if (!window.location.href.includes('mod=auction')) {\n      console.log('Market Operations: Navigating to auction');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=auction`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async getMarketItems(): Promise<MarketItem[]> {\n    const items: MarketItem[] = [];\n    const itemElements = document.querySelectorAll('.market_item, .market-listing');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parseMarketItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Market Operations: Error parsing market item:', error);\n      }\n    }\n    \n    return items;\n  }\n\n  private async parseMarketItem(element: HTMLElement, index: number): Promise<MarketItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const priceElement = element.querySelector('.item_price, .price');\n    const sellerElement = element.querySelector('.seller_name, .seller');\n    const levelElement = element.querySelector('.item_level, .level');\n    const buyButton = element.querySelector('.buy_button, [data-action=\"buy\"]');\n    const timeElement = element.querySelector('.time_remaining, .timer');\n    \n    if (!nameElement || !priceElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const price = parseInt(priceElement.textContent?.replace(/\\D/g, '') || '0');\n    const seller = sellerElement?.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    \n    // Determine quality from CSS classes\n    let quality: ItemQuality = ItemQuality.WHITE;\n    const qualityClass = element.className;\n    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;\n    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;\n    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;\n    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;\n    else if (qualityClass.includes('red')) quality = ItemQuality.RED;\n    \n    // Parse time remaining\n    const timeText = timeElement?.textContent?.trim() || '0:00:00';\n    const timeMatch = timeText.match(/(\\d+):(\\d+):(\\d+)/);\n    const timeRemaining = timeMatch ? \n      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;\n    \n    return {\n      id: index.toString(),\n      name,\n      quality,\n      level,\n      price,\n      seller,\n      timeRemaining,\n      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled')),\n      canBid: false // Market items are direct purchase\n    };\n  }\n\n  private async getAuctionItems(): Promise<AuctionItem[]> {\n    const items: AuctionItem[] = [];\n    const itemElements = document.querySelectorAll('.auction_item, .auction-listing');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parseAuctionItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Market Operations: Error parsing auction item:', error);\n      }\n    }\n    \n    return items;\n  }\n\n  private async parseAuctionItem(element: HTMLElement, index: number): Promise<AuctionItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const currentBidElement = element.querySelector('.current_bid, .bid');\n    const buyoutElement = element.querySelector('.buyout_price, .buyout');\n    const bidderElement = element.querySelector('.current_bidder, .bidder');\n    const levelElement = element.querySelector('.item_level, .level');\n    const bidButton = element.querySelector('.bid_button, [data-action=\"bid\"]');\n    const buyoutButton = element.querySelector('.buyout_button, [data-action=\"buyout\"]');\n    const timeElement = element.querySelector('.time_remaining, .timer');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const currentBid = parseInt(currentBidElement?.textContent?.replace(/\\D/g, '') || '0');\n    const buyoutPrice = parseInt(buyoutElement?.textContent?.replace(/\\D/g, '') || '0');\n    const bidder = bidderElement?.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    \n    // Determine quality from CSS classes\n    let quality: ItemQuality = ItemQuality.WHITE;\n    const qualityClass = element.className;\n    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;\n    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;\n    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;\n    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;\n    else if (qualityClass.includes('red')) quality = ItemQuality.RED;\n    \n    // Parse time remaining\n    const timeText = timeElement?.textContent?.trim() || '0:00:00';\n    const timeMatch = timeText.match(/(\\d+):(\\d+):(\\d+)/);\n    const timeRemaining = timeMatch ? \n      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;\n    \n    return {\n      id: index.toString(),\n      name,\n      quality,\n      level,\n      currentBid,\n      buyoutPrice,\n      timeRemaining,\n      bidder,\n      canBid: !!(bidButton && !bidButton.hasAttribute('disabled')),\n      canBuyout: !!(buyoutButton && !buyoutButton.hasAttribute('disabled'))\n    };\n  }\n\n  private filterItemsForBuying(items: MarketItem[], settings: any): MarketItem[] {\n    return items.filter(item => {\n      // Check price limit\n      if (item.price > settings.maxPrice) return false;\n      \n      // Check quality filter\n      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {\n        return false;\n      }\n      \n      // Check level range\n      if (settings.minLevel && item.level < settings.minLevel) return false;\n      if (settings.maxLevel && item.level > settings.maxLevel) return false;\n      \n      // Check item name filter\n      if (settings.itemNames.length > 0) {\n        const nameMatch = settings.itemNames.some((name: string) => \n          item.name.toLowerCase().includes(name.toLowerCase())\n        );\n        if (!nameMatch) return false;\n      }\n      \n      // Check seller blacklist\n      if (settings.blacklistedSellers.includes(item.seller)) return false;\n      \n      return item.canBuy;\n    });\n  }\n\n  private filterItemsForSelling(items: any[], settings: any): any[] {\n    // This would filter inventory items for selling\n    return items.filter(item => {\n      // Apply selling rules similar to package management\n      return true; // Simplified\n    });\n  }\n\n  private filterItemsForBidding(items: AuctionItem[], settings: any): AuctionItem[] {\n    return items.filter(item => {\n      // Check if we're already the highest bidder\n      if (item.bidder === 'You') return false;\n      \n      // Check maximum bid\n      if (item.currentBid >= settings.maxBid) return false;\n      \n      // Check time remaining (don't bid on items ending too soon)\n      if (item.timeRemaining < settings.minTimeRemaining) return false;\n      \n      // Check quality and other filters similar to buying\n      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {\n        return false;\n      }\n      \n      return item.canBid;\n    });\n  }\n\n  private async buyItem(item: MarketItem): Promise<void> {\n    console.log(`Market Operations: Buying ${item.name} for ${item.price} gold`);\n    \n    const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const buyButton = itemElement.querySelector('.buy_button, [data-action=\"buy\"]');\n    if (buyButton && !buyButton.hasAttribute('disabled')) {\n      (buyButton as HTMLElement).click();\n      \n      // Handle confirmation if present\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_buy, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private async sellItem(item: any, settings: any): Promise<void> {\n    console.log(`Market Operations: Selling ${item.name}`);\n    \n    // This would handle putting items up for sale\n    // Implementation would depend on the game's selling interface\n  }\n\n  private async bidOnItem(item: AuctionItem, settings: any): Promise<void> {\n    const bidAmount = Math.min(item.currentBid + settings.bidIncrement, settings.maxBid);\n    \n    console.log(`Market Operations: Bidding ${bidAmount} gold on ${item.name}`);\n    \n    const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const bidInput = itemElement.querySelector('.bid_input, input[name=\"bid\"]') as HTMLInputElement;\n    const bidButton = itemElement.querySelector('.bid_button, [data-action=\"bid\"]');\n    \n    if (bidInput && bidButton && !bidButton.hasAttribute('disabled')) {\n      bidInput.value = bidAmount.toString();\n      bidInput.dispatchEvent(new Event('input', { bubbles: true }));\n      \n      (bidButton as HTMLElement).click();\n    }\n  }\n\n  private async buyoutItem(item: AuctionItem, settings: any): Promise<void> {\n    if (item.buyoutPrice > settings.maxBuyout) return;\n    \n    console.log(`Market Operations: Buying out ${item.name} for ${item.buyoutPrice} gold`);\n    \n    const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n    \n    const buyoutButton = itemElement.querySelector('.buyout_button, [data-action=\"buyout\"]');\n    if (buyoutButton && !buyoutButton.hasAttribute('disabled')) {\n      (buyoutButton as HTMLElement).click();\n      \n      // Handle confirmation\n      await this.delay(500);\n      const confirmButton = document.querySelector('.confirm_buyout, [data-action=\"confirm\"]');\n      if (confirmButton) {\n        (confirmButton as HTMLElement).click();\n      }\n    }\n  }\n\n  private async getInventoryItems(): Promise<any[]> {\n    // This would get items from the player's inventory\n    // Implementation would depend on how inventory is accessed\n    return [];\n  }\n\n  private shouldContinueOperations(settings: MarketOperationsSettings): boolean {\n    // Check gold spending limit\n    if (settings.autoBuy.enabled && this.goldSpent >= settings.autoBuy.maxGoldSpend) {\n      console.log('Market Operations: Gold spending limit reached');\n      return false;\n    }\n    \n    // Check daily limits\n    if (settings.dailyBuyLimit > 0 && this.itemsBought >= settings.dailyBuyLimit) {\n      console.log('Market Operations: Daily buy limit reached');\n      return false;\n    }\n    \n    return true;\n  }\n\n  async getMarketStatus(): Promise<any> {\n    return {\n      isRunning: this.isRunning,\n      itemsBought: this.itemsBought,\n      itemsSold: this.itemsSold,\n      goldSpent: this.goldSpent,\n      goldEarned: this.goldEarned,\n      lastUpdate: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Package Management Module - Handles item rotation, selling, and gold picking\n\nimport { PackageSettings, GameItem, ItemQuality } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface PackageItem {\n  id: string;\n  name: string;\n  quality: ItemQuality;\n  level: number;\n  price: number;\n  isUnderworld: boolean;\n  isSoulbound: boolean;\n  conditioning: number;\n  canSell: boolean;\n  canRotate: boolean;\n}\n\nexport class PackageManagementModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isProcessing: boolean = false;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async processPackages(settings: PackageSettings): Promise<void> {\n    if (this.isProcessing) return;\n    \n    this.isProcessing = true;\n    console.log('Package Management: Starting package processing');\n\n    try {\n      // Navigate to packages if not already there\n      await this.navigateToPackages();\n\n      // Pick gold if enabled\n      if (settings.pickGold.enabled) {\n        await this.pickGold(settings.pickGold.goldToPick);\n      }\n\n      // Rotate items if enabled\n      if (settings.rotateItems.enabled) {\n        await this.rotateItems(settings.rotateItems);\n      }\n\n      // Sell items if enabled\n      if (settings.sellItems.enabled) {\n        await this.sellItems(settings.sellItems);\n      }\n\n      console.log('Package Management: Package processing completed');\n\n    } catch (error) {\n      console.error('Package Management: Error processing packages:', error);\n      throw error;\n    } finally {\n      this.isProcessing = false;\n    }\n  }\n\n  private async navigateToPackages(): Promise<void> {\n    if (!window.location.href.includes('mod=packages')) {\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      \n      console.log('Package Management: Navigating to packages');\n      window.location.href = `${baseUrl}?mod=packages`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async pickGold(goldToPick: number): Promise<void> {\n    if (goldToPick <= 0) return;\n\n    console.log(`Package Management: Picking ${goldToPick} gold`);\n\n    // Look for gold input field and pick button\n    const goldInput = document.querySelector('#gold_amount, .gold-input') as HTMLInputElement;\n    const pickButton = document.querySelector('.pick_gold_button, [data-action=\"pick-gold\"]');\n\n    if (!goldInput || !pickButton) {\n      console.log('Package Management: Gold picking interface not found');\n      return;\n    }\n\n    // Set gold amount\n    goldInput.value = goldToPick.toString();\n    \n    // Trigger input event\n    goldInput.dispatchEvent(new Event('input', { bubbles: true }));\n    \n    // Click pick button\n    (pickButton as HTMLElement).click();\n    \n    await this.delay(2000);\n    console.log('Package Management: Gold picked successfully');\n  }\n\n  private async rotateItems(rotateSettings: any): Promise<void> {\n    console.log('Package Management: Starting item rotation');\n\n    const items = await this.getPackageItems();\n    const itemsToRotate = this.filterItemsForRotation(items, rotateSettings);\n\n    for (const item of itemsToRotate) {\n      try {\n        await this.rotateItem(item);\n        await this.delay(1000); // Delay between rotations\n      } catch (error) {\n        console.error(`Package Management: Error rotating item ${item.name}:`, error);\n      }\n    }\n\n    console.log(`Package Management: Rotated ${itemsToRotate.length} items`);\n  }\n\n  private async sellItems(sellSettings: any): Promise<void> {\n    console.log('Package Management: Starting item selling');\n\n    const items = await this.getPackageItems();\n    const itemsToSell = this.filterItemsForSelling(items, sellSettings);\n\n    for (const item of itemsToSell) {\n      try {\n        await this.sellItem(item);\n        await this.delay(1000); // Delay between sales\n      } catch (error) {\n        console.error(`Package Management: Error selling item ${item.name}:`, error);\n      }\n    }\n\n    console.log(`Package Management: Sold ${itemsToSell.length} items`);\n  }\n\n  private async getPackageItems(): Promise<PackageItem[]> {\n    const items: PackageItem[] = [];\n    \n    // Parse items from packages page\n    const itemElements = document.querySelectorAll('.package_item, .inventory-item');\n    \n    for (let i = 0; i < itemElements.length; i++) {\n      const element = itemElements[i] as HTMLElement;\n      \n      try {\n        const item = await this.parsePackageItem(element, i);\n        if (item) {\n          items.push(item);\n        }\n      } catch (error) {\n        console.error('Package Management: Error parsing item:', error);\n      }\n    }\n\n    return items;\n  }\n\n  private async parsePackageItem(element: HTMLElement, index: number): Promise<PackageItem | null> {\n    const nameElement = element.querySelector('.item_name, .name');\n    const qualityElement = element.querySelector('.item_quality, .quality');\n    const levelElement = element.querySelector('.item_level, .level');\n    const priceElement = element.querySelector('.item_price, .price');\n    \n    if (!nameElement) return null;\n\n    const name = nameElement.textContent?.trim() || '';\n    const qualityClass = qualityElement?.className || element.className;\n    const level = parseInt(levelElement?.textContent?.trim() || '1');\n    const price = parseInt(priceElement?.textContent?.replace(/\\D/g, '') || '0');\n\n    // Determine quality from CSS classes\n    let quality: ItemQuality = ItemQuality.WHITE;\n    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;\n    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;\n    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;\n    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;\n    else if (qualityClass.includes('red')) quality = ItemQuality.RED;\n\n    // Check for underworld items\n    const isUnderworld = element.classList.contains('underworld') || \n                        name.toLowerCase().includes('underworld') ||\n                        element.querySelector('.underworld-icon') !== null;\n\n    // Check for soulbound items\n    const isSoulbound = element.classList.contains('soulbound') ||\n                       element.querySelector('.soulbound-icon') !== null;\n\n    // Get conditioning\n    const conditioningElement = element.querySelector('.conditioning, .condition');\n    const conditioning = parseInt(conditioningElement?.textContent?.trim() || '100');\n\n    // Check if item can be sold/rotated\n    const sellButton = element.querySelector('.sell_button, [data-action=\"sell\"]');\n    const rotateButton = element.querySelector('.rotate_button, [data-action=\"rotate\"]');\n\n    return {\n      id: index.toString(),\n      name,\n      quality,\n      level,\n      price,\n      isUnderworld,\n      isSoulbound,\n      conditioning,\n      canSell: !!(sellButton && !sellButton.hasAttribute('disabled')),\n      canRotate: !!(rotateButton && !rotateButton.hasAttribute('disabled'))\n    };\n  }\n\n  private filterItemsForRotation(items: PackageItem[], settings: any): PackageItem[] {\n    return items.filter(item => {\n      // Must be able to rotate\n      if (!item.canRotate) return false;\n\n      // Check if soulbound items should be excluded\n      if (item.isSoulbound && !settings.includeSoulbound) return false;\n\n      // Check underworld items setting\n      if (item.isUnderworld && !settings.underworldItems) return false;\n\n      // Check selected item types\n      if (settings.selectedItems.length > 0) {\n        const itemTypeMatch = settings.selectedItems.some((selectedType: string) => \n          item.name.toLowerCase().includes(selectedType.toLowerCase())\n        );\n        if (!itemTypeMatch) return false;\n      }\n\n      // Check quality colors\n      if (settings.colors.length > 0) {\n        const qualityMatch = settings.colors.includes(item.quality);\n        if (!qualityMatch) return false;\n      }\n\n      return true;\n    });\n  }\n\n  private filterItemsForSelling(items: PackageItem[], settings: any): PackageItem[] {\n    return items.filter(item => {\n      // Must be able to sell\n      if (!item.canSell) return false;\n\n      // Don't sell soulbound items\n      if (item.isSoulbound) return false;\n\n      // Apply selling rules\n      for (const rule of settings.rules) {\n        if (this.itemMatchesRule(item, rule)) {\n          return true;\n        }\n      }\n\n      return false;\n    });\n  }\n\n  private itemMatchesRule(item: PackageItem, rule: any): boolean {\n    // Check item type\n    if (rule.itemType && !item.name.toLowerCase().includes(rule.itemType.toLowerCase())) {\n      return false;\n    }\n\n    // Check quality\n    if (rule.quality && item.quality !== rule.quality) {\n      return false;\n    }\n\n    // Check level range\n    if (rule.minLevel && item.level < rule.minLevel) {\n      return false;\n    }\n    if (rule.maxLevel && item.level > rule.maxLevel) {\n      return false;\n    }\n\n    // Check price range\n    if (rule.minPrice && item.price < rule.minPrice) {\n      return false;\n    }\n    if (rule.maxPrice && item.price > rule.maxPrice) {\n      return false;\n    }\n\n    // Check conditioning\n    if (rule.minConditioning && item.conditioning < rule.minConditioning) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private async rotateItem(item: PackageItem): Promise<void> {\n    console.log(`Package Management: Rotating ${item.name}`);\n\n    const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n\n    const rotateButton = itemElement.querySelector('.rotate_button, [data-action=\"rotate\"]');\n    if (!rotateButton || rotateButton.hasAttribute('disabled')) return;\n\n    (rotateButton as HTMLElement).click();\n    await this.delay(1500);\n  }\n\n  private async sellItem(item: PackageItem): Promise<void> {\n    console.log(`Package Management: Selling ${item.name} for ${item.price} gold`);\n\n    const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);\n    if (!itemElement) return;\n\n    const sellButton = itemElement.querySelector('.sell_button, [data-action=\"sell\"]');\n    if (!sellButton || sellButton.hasAttribute('disabled')) return;\n\n    (sellButton as HTMLElement).click();\n    \n    // Handle confirmation dialog if present\n    await this.delay(500);\n    const confirmButton = document.querySelector('.confirm_sell, [data-action=\"confirm\"]');\n    if (confirmButton) {\n      (confirmButton as HTMLElement).click();\n    }\n    \n    await this.delay(1500);\n  }\n\n  async shouldProcessPackages(settings: PackageSettings): Promise<boolean> {\n    // Check if any package management is enabled\n    return settings.pickGold.enabled || \n           settings.rotateItems.enabled || \n           settings.sellItems.enabled;\n  }\n\n  async getPackageStatus(): Promise<any> {\n    if (!window.location.href.includes('mod=packages')) {\n      return null;\n    }\n\n    const items = await this.getPackageItems();\n    const totalItems = items.length;\n    const sellableItems = items.filter(item => item.canSell).length;\n    const rotatableItems = items.filter(item => item.canRotate).length;\n\n    return {\n      totalItems,\n      sellableItems,\n      rotatableItems,\n      lastProcessed: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Quest Management Module - Handles automatic quest selection and completion\n\nimport { QuestManagementSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface Quest {\n  id: string;\n  name: string;\n  description: string;\n  reward: string;\n  difficulty: 'easy' | 'medium' | 'hard';\n  isCompleted: boolean;\n  isAvailable: boolean;\n  requirements: string[];\n}\n\nexport interface QuestManagementRule {\n  priority: number;\n  questType: string;\n  minReward: number;\n  maxDifficulty: 'easy' | 'medium' | 'hard';\n  autoComplete: boolean;\n}\n\nexport class QuestManagementModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private questsCompleted: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: QuestManagementSettings): Promise<void> {\n    if (this.isRunning || !settings.enabled) return;\n    \n    this.isRunning = true;\n    this.questsCompleted = 0;\n    \n    console.log('Quest Management: Starting quest automation');\n    \n    try {\n      await this.performQuestLoop(settings);\n    } catch (error) {\n      console.error('Quest Management: Error in quest automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Quest Management: Stopped quest automation');\n  }\n\n  private async performQuestLoop(settings: QuestManagementSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueQuests(settings)) {\n      try {\n        await this.navigateToQuests();\n        \n        // Get available quests\n        const availableQuests = await this.getAvailableQuests();\n        \n        if (availableQuests.length === 0) {\n          console.log('Quest Management: No available quests found');\n          await this.delay(30000);\n          continue;\n        }\n\n        // Select best quest based on rules\n        const selectedQuest = this.selectBestQuest(availableQuests, settings.rules);\n        \n        if (!selectedQuest) {\n          console.log('Quest Management: No suitable quest found');\n          await this.delay(30000);\n          continue;\n        }\n\n        // Accept and complete quest\n        await this.acceptQuest(selectedQuest);\n        \n        if (settings.autoComplete) {\n          await this.completeQuest(selectedQuest, settings);\n        }\n\n        this.questsCompleted++;\n        await this.delay(10000);\n        \n      } catch (error) {\n        console.error('Quest Management: Error in quest loop:', error);\n        await this.delay(15000);\n      }\n    }\n  }\n\n  private async navigateToQuests(): Promise<void> {\n    if (!window.location.href.includes('mod=quest')) {\n      console.log('Quest Management: Navigating to quests');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=quest`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async getAvailableQuests(): Promise<Quest[]> {\n    const quests: Quest[] = [];\n    const questElements = document.querySelectorAll('.quest_item, .available-quest');\n    \n    for (let i = 0; i < questElements.length; i++) {\n      const element = questElements[i] as HTMLElement;\n      \n      try {\n        const quest = await this.parseQuestElement(element, i);\n        if (quest && quest.isAvailable && !quest.isCompleted) {\n          quests.push(quest);\n        }\n      } catch (error) {\n        console.error('Quest Management: Error parsing quest:', error);\n      }\n    }\n    \n    return quests;\n  }\n\n  private async parseQuestElement(element: HTMLElement, index: number): Promise<Quest | null> {\n    const nameElement = element.querySelector('.quest_name, .name');\n    const descriptionElement = element.querySelector('.quest_description, .description');\n    const rewardElement = element.querySelector('.quest_reward, .reward');\n    const acceptButton = element.querySelector('.accept_quest, [data-action=\"accept\"]');\n    const completedIndicator = element.querySelector('.quest_completed, .completed');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const description = descriptionElement?.textContent?.trim() || '';\n    const reward = rewardElement?.textContent?.trim() || '';\n    \n    // Determine difficulty based on quest name or description\n    let difficulty: 'easy' | 'medium' | 'hard' = 'medium';\n    const text = (name + ' ' + description).toLowerCase();\n    if (text.includes('easy') || text.includes('simple')) {\n      difficulty = 'easy';\n    } else if (text.includes('hard') || text.includes('difficult') || text.includes('challenging')) {\n      difficulty = 'hard';\n    }\n    \n    // Parse requirements\n    const requirements: string[] = [];\n    const reqElements = element.querySelectorAll('.quest_requirement, .requirement');\n    reqElements.forEach(req => {\n      const reqText = req.textContent?.trim();\n      if (reqText) requirements.push(reqText);\n    });\n    \n    return {\n      id: index.toString(),\n      name,\n      description,\n      reward,\n      difficulty,\n      isCompleted: !!completedIndicator,\n      isAvailable: !!(acceptButton && !acceptButton.hasAttribute('disabled')),\n      requirements\n    };\n  }\n\n  private selectBestQuest(quests: Quest[], rules: QuestManagementRule[]): Quest | null {\n    if (quests.length === 0) return null;\n    \n    // Sort rules by priority (highest first)\n    const sortedRules = [...rules].sort((a, b) => b.priority - a.priority);\n    \n    for (const rule of sortedRules) {\n      const matchingQuests = quests.filter(quest => this.questMatchesRule(quest, rule));\n      \n      if (matchingQuests.length > 0) {\n        // Return the first matching quest for the highest priority rule\n        return matchingQuests[0];\n      }\n    }\n    \n    // If no rules match, return the first available quest\n    return quests[0];\n  }\n\n  private questMatchesRule(quest: Quest, rule: QuestManagementRule): boolean {\n    // Check quest type\n    if (rule.questType && !quest.name.toLowerCase().includes(rule.questType.toLowerCase())) {\n      return false;\n    }\n    \n    // Check difficulty\n    const difficultyOrder = { easy: 1, medium: 2, hard: 3 };\n    if (difficultyOrder[quest.difficulty] > difficultyOrder[rule.maxDifficulty]) {\n      return false;\n    }\n    \n    // Check minimum reward (simplified - would need better reward parsing)\n    const rewardValue = this.parseRewardValue(quest.reward);\n    if (rewardValue < rule.minReward) {\n      return false;\n    }\n    \n    return true;\n  }\n\n  private parseRewardValue(reward: string): number {\n    // Simple reward parsing - extract numbers from reward string\n    const goldMatch = reward.match(/(\\d+)\\s*gold/i);\n    const xpMatch = reward.match(/(\\d+)\\s*xp/i);\n    \n    let value = 0;\n    if (goldMatch) value += parseInt(goldMatch[1]);\n    if (xpMatch) value += parseInt(xpMatch[1]) * 0.1; // XP worth less than gold\n    \n    return value;\n  }\n\n  private async acceptQuest(quest: Quest): Promise<void> {\n    console.log(`Quest Management: Accepting quest \"${quest.name}\"`);\n    \n    const questElement = document.querySelector(`.quest_item:nth-child(${parseInt(quest.id) + 1})`);\n    if (!questElement) return;\n    \n    const acceptButton = questElement.querySelector('.accept_quest, [data-action=\"accept\"]');\n    if (acceptButton && !acceptButton.hasAttribute('disabled')) {\n      (acceptButton as HTMLElement).click();\n      await this.delay(2000);\n    }\n  }\n\n  private async completeQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {\n    console.log(`Quest Management: Attempting to complete quest \"${quest.name}\"`);\n    \n    // This would contain logic to actually complete the quest\n    // For example, if it's a combat quest, go fight monsters\n    // If it's a collection quest, collect items, etc.\n    \n    if (quest.name.toLowerCase().includes('combat') || quest.name.toLowerCase().includes('fight')) {\n      await this.completeCombatQuest(quest, settings);\n    } else if (quest.name.toLowerCase().includes('collect') || quest.name.toLowerCase().includes('gather')) {\n      await this.completeCollectionQuest(quest, settings);\n    } else if (quest.name.toLowerCase().includes('travel') || quest.name.toLowerCase().includes('visit')) {\n      await this.completeTravelQuest(quest, settings);\n    }\n    \n    // Check if quest is completed and claim reward\n    await this.claimQuestReward(quest);\n  }\n\n  private async completeCombatQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {\n    // Navigate to appropriate combat area and fight required enemies\n    console.log(`Quest Management: Completing combat quest \"${quest.name}\"`);\n    \n    // This would integrate with arena/turma combat modules\n    // For now, just simulate completion\n    await this.delay(5000);\n  }\n\n  private async completeCollectionQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {\n    // Navigate to appropriate location and collect required items\n    console.log(`Quest Management: Completing collection quest \"${quest.name}\"`);\n    \n    // This would integrate with expedition/dungeon modules\n    await this.delay(5000);\n  }\n\n  private async completeTravelQuest(quest: Quest, settings: QuestManagementSettings): Promise<void> {\n    // Navigate to required locations\n    console.log(`Quest Management: Completing travel quest \"${quest.name}\"`);\n    \n    // Parse quest requirements to find locations to visit\n    for (const requirement of quest.requirements) {\n      if (requirement.toLowerCase().includes('visit') || requirement.toLowerCase().includes('go to')) {\n        // Extract location and navigate there\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async claimQuestReward(quest: Quest): Promise<void> {\n    // Look for quest completion and claim reward\n    const claimButton = document.querySelector('.claim_reward, [data-action=\"claim\"]');\n    if (claimButton && !claimButton.hasAttribute('disabled')) {\n      console.log(`Quest Management: Claiming reward for quest \"${quest.name}\"`);\n      (claimButton as HTMLElement).click();\n      await this.delay(2000);\n    }\n  }\n\n  private shouldContinueQuests(settings: QuestManagementSettings): boolean {\n    // Check daily quest limit\n    if (settings.dailyLimit > 0 && this.questsCompleted >= settings.dailyLimit) {\n      console.log('Quest Management: Daily quest limit reached');\n      return false;\n    }\n    \n    return true;\n  }\n\n  async getQuestStatus(): Promise<any> {\n    return {\n      isRunning: this.isRunning,\n      questsCompleted: this.questsCompleted,\n      lastUpdate: new Date()\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Turma Combat Module - Handles turma (guild war) combat functionality\n\nimport { TurmaSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface TurmaTarget {\n  id: string;\n  name: string;\n  level: number;\n  guild: string;\n  gold: number;\n  isOnline: boolean;\n  canAttack: boolean;\n  lastAttacked?: Date;\n}\n\nexport class TurmaCombatModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private attackCount: number = 0;\n  private goldRaided: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: TurmaSettings): Promise<void> {\n    if (this.isRunning || !settings.enabled) return;\n    \n    this.isRunning = true;\n    this.attackCount = 0;\n    this.goldRaided = 0;\n    \n    console.log('Turma Combat: Starting turma automation');\n    \n    try {\n      await this.navigateToTurma();\n      await this.performTurmaLoop(settings);\n    } catch (error) {\n      console.error('Turma Combat: Error in turma automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Turma Combat: Stopped turma automation');\n  }\n\n  private async navigateToTurma(): Promise<void> {\n    const currentUrl = window.location.href;\n    \n    if (!currentUrl.includes('mod=turma')) {\n      console.log('Turma Combat: Navigating to turma');\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=turma`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async performTurmaLoop(settings: TurmaSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueAttacking(settings)) {\n      try {\n        const targets = await this.getAvailableTargets(settings);\n        \n        if (targets.length === 0) {\n          console.log('Turma Combat: No suitable targets found');\n          await this.delay(10000);\n          continue;\n        }\n\n        const target = this.selectBestTarget(targets, settings);\n        if (!target) {\n          console.log('Turma Combat: No target selected');\n          await this.delay(10000);\n          continue;\n        }\n\n        const result = await this.attackTarget(target, settings);\n        await this.processAttackResult(result, target);\n        \n        // Random delay between attacks (longer than arena)\n        const delay = Math.random() * 20000 + 10000; // 10-30 seconds\n        await this.delay(delay);\n        \n      } catch (error) {\n        console.error('Turma Combat: Error in attack loop:', error);\n        await this.delay(15000);\n      }\n    }\n  }\n\n  private async getAvailableTargets(settings: TurmaSettings): Promise<TurmaTarget[]> {\n    const targets: TurmaTarget[] = [];\n    \n    // Parse turma page for targets\n    const targetElements = document.querySelectorAll('.turma_opponent, .enemy_player');\n    \n    for (const element of targetElements) {\n      try {\n        const target = await this.parseTurmaTarget(element as HTMLElement, settings);\n        if (target && this.isValidTarget(target, settings)) {\n          targets.push(target);\n        }\n      } catch (error) {\n        console.error('Turma Combat: Error parsing target:', error);\n      }\n    }\n    \n    return targets;\n  }\n\n  private async parseTurmaTarget(element: HTMLElement, settings: TurmaSettings): Promise<TurmaTarget | null> {\n    const nameElement = element.querySelector('.player_name, .name');\n    const levelElement = element.querySelector('.player_level, .level');\n    const guildElement = element.querySelector('.guild_name, .guild');\n    const goldElement = element.querySelector('.player_gold, .gold');\n    const attackButton = element.querySelector('.attack_button, [data-action=\"attack\"]');\n    const onlineIndicator = element.querySelector('.online_status, .status');\n    \n    if (!nameElement || !attackButton) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const level = parseInt(levelElement?.textContent?.trim() || '0');\n    const guild = guildElement?.textContent?.trim() || '';\n    const gold = parseInt(goldElement?.textContent?.replace(/\\D/g, '') || '0');\n    const id = attackButton.getAttribute('data-target-id') || \n               attackButton.getAttribute('href')?.match(/target=(\\d+)/)?.[1] || '';\n    \n    const isOnline = onlineIndicator?.classList.contains('online') || \n                    onlineIndicator?.textContent?.includes('online') || false;\n    \n    return {\n      id,\n      name,\n      level,\n      guild,\n      gold,\n      isOnline,\n      canAttack: !attackButton.hasAttribute('disabled')\n    };\n  }\n\n  private isValidTarget(target: TurmaTarget, settings: TurmaSettings): boolean {\n    // Skip if can't attack\n    if (!target.canAttack) return false;\n    \n    // Skip if in ignore list\n    if (settings.ignorePlayers.includes(target.name)) return false;\n    \n    // Skip if auto-ignore and recently attacked\n    if (settings.autoIgnorePlayers.includes(target.name)) return false;\n    \n    // Check level restriction\n    if (settings.levelRestriction > 0 && target.level > settings.levelRestriction) return false;\n    \n    // Check if targeting specific players\n    if (settings.attackPlayers.length > 0) {\n      return settings.attackPlayers.includes(target.name);\n    }\n    \n    // Check same server restriction\n    if (!settings.attackSameServer) {\n      // This would need server detection logic\n      return true; // Simplified for now\n    }\n    \n    return true;\n  }\n\n  private selectBestTarget(targets: TurmaTarget[], settings: TurmaSettings): TurmaTarget | null {\n    if (targets.length === 0) return null;\n    \n    // Sort by gold amount (highest first) and then by level (lowest first)\n    targets.sort((a, b) => {\n      if (Math.abs(a.gold - b.gold) < 1000) {\n        return a.level - b.level; // Lower level first if gold is similar\n      }\n      return b.gold - a.gold; // Higher gold first\n    });\n    \n    return targets[0];\n  }\n\n  private async attackTarget(target: TurmaTarget, settings: TurmaSettings): Promise<any> {\n    console.log(`Turma Combat: Attacking ${target.name} (Level ${target.level}, Gold: ${target.gold})`);\n    \n    // Find and click attack button\n    const attackButton = document.querySelector(`[data-target-id=\"${target.id}\"], [href*=\"target=${target.id}\"]`);\n    if (!attackButton) {\n      throw new Error('Attack button not found');\n    }\n    \n    (attackButton as HTMLElement).click();\n    \n    // Wait for attack result\n    await this.waitForAttackResult();\n    \n    return this.parseAttackResult();\n  }\n\n  private async processAttackResult(result: any, target: TurmaTarget): Promise<void> {\n    this.attackCount++;\n    \n    if (result.won) {\n      console.log(`Turma Combat: Victory! Raided ${result.gold} gold from ${target.name}`);\n      this.goldRaided += result.gold;\n      \n      // Add to auto-ignore list if configured\n      if (result.gold < 100) { // Low gold raids\n        this.addToAutoIgnore(target.name);\n      }\n    } else {\n      console.log(`Turma Combat: Defeat against ${target.name}`);\n    }\n    \n    // Update statistics\n    await this.updateStatistics(result);\n  }\n\n  private addToAutoIgnore(playerName: string): void {\n    // This would add player to temporary ignore list\n    console.log(`Turma Combat: Adding ${playerName} to auto-ignore list`);\n  }\n\n  private shouldContinueAttacking(settings: TurmaSettings): boolean {\n    // Check auto stop limit\n    if (settings.autoStop > 0 && this.attackCount >= settings.autoStop) {\n      console.log('Turma Combat: Auto stop limit reached');\n      return false;\n    }\n    \n    // Check gold raided limit\n    if (settings.goldRaided > 0 && this.goldRaided >= settings.goldRaided) {\n      console.log('Turma Combat: Gold raided limit reached');\n      return false;\n    }\n    \n    // Check if quest-based attacking\n    if (settings.attackIfQuest && !this.hasActiveTurmaQuest()) {\n      console.log('Turma Combat: No active turma quest');\n      return false;\n    }\n    \n    return true;\n  }\n\n  private hasActiveTurmaQuest(): boolean {\n    // Check if there's an active turma-related quest\n    const questElements = document.querySelectorAll('.quest_item, .active-quest');\n    \n    for (const quest of questElements) {\n      const questText = quest.textContent?.toLowerCase() || '';\n      if (questText.includes('turma') || questText.includes('guild') || questText.includes('war')) {\n        return true;\n      }\n    }\n    \n    return false;\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async waitForAttackResult(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkResult = () => {\n        const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');\n        if (resultElement) {\n          resolve();\n        } else {\n          setTimeout(checkResult, 500);\n        }\n      };\n      setTimeout(checkResult, 3000); // Wait at least 3 seconds for turma\n    });\n  }\n\n  private parseAttackResult(): any {\n    const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');\n    if (!resultElement) return { won: false, gold: 0, xp: 0 };\n    \n    const won = resultElement.textContent?.includes('Victory') || \n                resultElement.textContent?.includes('Won') ||\n                resultElement.classList.contains('victory') || false;\n    \n    const goldMatch = resultElement.textContent?.match(/(\\d+)\\s*gold/i);\n    const xpMatch = resultElement.textContent?.match(/(\\d+)\\s*xp/i);\n    \n    return {\n      won,\n      gold: goldMatch ? parseInt(goldMatch[1]) : 0,\n      xp: xpMatch ? parseInt(xpMatch[1]) : 0\n    };\n  }\n\n  private async updateStatistics(result: any): Promise<void> {\n    const stats = await this.storageManager.getBotStatus();\n    if (stats?.statistics) {\n      stats.statistics.actionsPerformed++;\n      if (result.won) {\n        stats.statistics.combatsWon++;\n        stats.statistics.goldEarned += result.gold;\n        stats.statistics.experienceGained += result.xp;\n      } else {\n        stats.statistics.combatsLost++;\n      }\n      \n      await this.storageManager.saveBotStatus(stats);\n    }\n  }\n\n  async getTurmaStatus(): Promise<any> {\n    return {\n      isRunning: this.isRunning,\n      attackCount: this.attackCount,\n      goldRaided: this.goldRaided,\n      lastUpdate: new Date()\n    };\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// Underworld System Module - Handles underworld combat and management\n\nimport { UnderworldSettings } from '../types';\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\nimport { StorageManager } from '../utils/storage-manager';\n\nexport interface UnderworldStatus {\n  isActive: boolean;\n  timeRemaining: number;\n  currentWave: number;\n  totalWaves: number;\n  guildHealth: number;\n  maxGuildHealth: number;\n  playerHealth: number;\n  maxPlayerHealth: number;\n}\n\nexport interface UnderworldBoss {\n  name: string;\n  health: number;\n  maxHealth: number;\n  isDisPater: boolean;\n  canAttack: boolean;\n}\n\nexport class UnderworldSystemModule {\n  private urlHelper: GladiatorUrlHelper;\n  private storageManager: StorageManager;\n  private isRunning: boolean = false;\n  private medicsUsed: number = 0;\n  private rubyUsed: number = 0;\n\n  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {\n    this.urlHelper = urlHelper;\n    this.storageManager = storageManager;\n  }\n\n  async start(settings: UnderworldSettings): Promise<void> {\n    if (this.isRunning || !settings.enabled) return;\n    \n    this.isRunning = true;\n    this.medicsUsed = 0;\n    this.rubyUsed = 0;\n    \n    console.log('Underworld System: Starting underworld automation');\n    \n    try {\n      await this.navigateToUnderworld();\n      await this.performUnderworldLoop(settings);\n    } catch (error) {\n      console.error('Underworld System: Error in underworld automation:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isRunning = false;\n    console.log('Underworld System: Stopped underworld automation');\n  }\n\n  private async navigateToUnderworld(): Promise<void> {\n    if (!window.location.href.includes('mod=underworld')) {\n      console.log('Underworld System: Navigating to underworld');\n      const currentUrl = window.location.href;\n      const baseUrl = currentUrl.split('?')[0];\n      window.location.href = `${baseUrl}?mod=underworld`;\n      \n      await this.waitForPageLoad();\n    }\n  }\n\n  private async performUnderworldLoop(settings: UnderworldSettings): Promise<void> {\n    while (this.isRunning && this.shouldContinueUnderworld(settings)) {\n      try {\n        const status = await this.getUnderworldStatus();\n        \n        if (!status.isActive) {\n          console.log('Underworld System: Underworld not active');\n          break;\n        }\n\n        // Handle healing\n        await this.handleUnderworldHealing(settings, status);\n        \n        // Handle buffs\n        if (settings.buffs) {\n          await this.applyBuffs(settings);\n        }\n        \n        // Handle costumes\n        if (settings.costumes) {\n          await this.manageCostumes(settings);\n        }\n        \n        // Handle combat\n        await this.handleUnderworldCombat(settings, status);\n        \n        // Handle reinforcements\n        if (settings.allowReinforcements) {\n          await this.manageReinforcements(settings);\n        }\n        \n        await this.delay(10000); // 10 second loop\n        \n      } catch (error) {\n        console.error('Underworld System: Error in underworld loop:', error);\n        await this.delay(15000);\n      }\n    }\n  }\n\n  private async getUnderworldStatus(): Promise<UnderworldStatus> {\n    const timeElement = document.querySelector('.underworld_timer, .timer');\n    const waveElement = document.querySelector('.current_wave, .wave');\n    const guildHealthElement = document.querySelector('.guild_health, .guild-hp');\n    const playerHealthElement = document.querySelector('.player_health, .player-hp');\n    \n    const timeText = timeElement?.textContent?.trim() || '0:00:00';\n    const timeMatch = timeText.match(/(\\d+):(\\d+):(\\d+)/);\n    const timeRemaining = timeMatch ? \n      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;\n    \n    const waveText = waveElement?.textContent?.trim() || '1/10';\n    const waveMatch = waveText.match(/(\\d+)\\/(\\d+)/);\n    const currentWave = waveMatch ? parseInt(waveMatch[1]) : 1;\n    const totalWaves = waveMatch ? parseInt(waveMatch[2]) : 10;\n    \n    const guildHealthText = guildHealthElement?.textContent?.trim() || '100/100';\n    const guildHealthMatch = guildHealthText.match(/(\\d+)\\/(\\d+)/);\n    const guildHealth = guildHealthMatch ? parseInt(guildHealthMatch[1]) : 100;\n    const maxGuildHealth = guildHealthMatch ? parseInt(guildHealthMatch[2]) : 100;\n    \n    const playerHealthText = playerHealthElement?.textContent?.trim() || '100/100';\n    const playerHealthMatch = playerHealthText.match(/(\\d+)\\/(\\d+)/);\n    const playerHealth = playerHealthMatch ? parseInt(playerHealthMatch[1]) : 100;\n    const maxPlayerHealth = playerHealthMatch ? parseInt(playerHealthMatch[2]) : 100;\n    \n    return {\n      isActive: timeRemaining > 0,\n      timeRemaining,\n      currentWave,\n      totalWaves,\n      guildHealth,\n      maxGuildHealth,\n      playerHealth,\n      maxPlayerHealth\n    };\n  }\n\n  private async handleUnderworldHealing(settings: UnderworldSettings, status: UnderworldStatus): Promise<void> {\n    // Heal guild if enabled and needed\n    if (settings.healGuild && status.guildHealth < status.maxGuildHealth * 0.8) {\n      await this.healGuild(settings);\n    }\n    \n    // Heal player if needed\n    const playerHealthPercent = (status.playerHealth / status.maxPlayerHealth) * 100;\n    if (playerHealthPercent < 80) {\n      if (settings.healPotions) {\n        await this.useHealingPotions();\n      }\n      \n      if (settings.healSacrifice && playerHealthPercent < 50) {\n        await this.useSacrificeHealing();\n      }\n    }\n  }\n\n  private async healGuild(settings: UnderworldSettings): Promise<void> {\n    // Use medics if available and under limit\n    if (this.medicsUsed < settings.maxMedics) {\n      const medicButton = document.querySelector('.use_medic, [data-action=\"use-medic\"]');\n      if (medicButton && !medicButton.hasAttribute('disabled')) {\n        console.log('Underworld System: Using medic to heal guild');\n        (medicButton as HTMLElement).click();\n        this.medicsUsed++;\n        await this.delay(2000);\n        return;\n      }\n    }\n    \n    // Use ruby if allowed and under limit\n    if (settings.allowRuby && this.rubyUsed < settings.maxRuby) {\n      const rubyButton = document.querySelector('.use_ruby, [data-action=\"use-ruby\"]');\n      if (rubyButton && !rubyButton.hasAttribute('disabled')) {\n        console.log('Underworld System: Using ruby to heal guild');\n        (rubyButton as HTMLElement).click();\n        this.rubyUsed++;\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async useHealingPotions(): Promise<void> {\n    const potionButton = document.querySelector('.use_potion, [data-action=\"use-potion\"]');\n    if (potionButton && !potionButton.hasAttribute('disabled')) {\n      console.log('Underworld System: Using healing potion');\n      (potionButton as HTMLElement).click();\n      await this.delay(1500);\n    }\n  }\n\n  private async useSacrificeHealing(): Promise<void> {\n    const sacrificeButton = document.querySelector('.sacrifice_heal, [data-action=\"sacrifice\"]');\n    if (sacrificeButton && !sacrificeButton.hasAttribute('disabled')) {\n      console.log('Underworld System: Using sacrifice healing');\n      (sacrificeButton as HTMLElement).click();\n      await this.delay(2000);\n    }\n  }\n\n  private async applyBuffs(settings: UnderworldSettings): Promise<void> {\n    // Auto buff gods if enabled\n    if (settings.autoBuffGods) {\n      const godBuffButtons = document.querySelectorAll('.god_buff, [data-action=\"buff-god\"]');\n      for (const button of godBuffButtons) {\n        if (!button.hasAttribute('disabled')) {\n          (button as HTMLElement).click();\n          await this.delay(1000);\n        }\n      }\n    }\n    \n    // Auto buff with oils if enabled\n    if (settings.autoBuffOils) {\n      const oilBuffButtons = document.querySelectorAll('.oil_buff, [data-action=\"buff-oil\"]');\n      for (const button of oilBuffButtons) {\n        if (!button.hasAttribute('disabled')) {\n          (button as HTMLElement).click();\n          await this.delay(1000);\n        }\n      }\n    }\n  }\n\n  private async manageCostumes(settings: UnderworldSettings): Promise<void> {\n    // Heal costumes if enabled\n    if (settings.healCostumes) {\n      const healCostumeButton = document.querySelector('.heal_costume, [data-action=\"heal-costume\"]');\n      if (healCostumeButton && !healCostumeButton.hasAttribute('disabled')) {\n        console.log('Underworld System: Healing costumes');\n        (healCostumeButton as HTMLElement).click();\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private async handleUnderworldCombat(settings: UnderworldSettings, status: UnderworldStatus): Promise<void> {\n    // Check for Dis Pater and attack ASAP if enabled\n    if (settings.attackDisPaterAsap) {\n      const disPater = await this.findDisPater();\n      if (disPater && disPater.canAttack) {\n        console.log('Underworld System: Attacking Dis Pater ASAP');\n        await this.attackBoss(disPater);\n        return;\n      }\n    }\n    \n    // Regular combat logic\n    const availableBosses = await this.getAvailableBosses();\n    for (const boss of availableBosses) {\n      if (boss.canAttack) {\n        await this.attackBoss(boss);\n        break;\n      }\n    }\n  }\n\n  private async findDisPater(): Promise<UnderworldBoss | null> {\n    const bossElements = document.querySelectorAll('.underworld_boss, .boss');\n    \n    for (const element of bossElements) {\n      const nameElement = element.querySelector('.boss_name, .name');\n      const name = nameElement?.textContent?.trim().toLowerCase() || '';\n      \n      if (name.includes('dis pater') || name.includes('dispater')) {\n        return await this.parseBossElement(element as HTMLElement);\n      }\n    }\n    \n    return null;\n  }\n\n  private async getAvailableBosses(): Promise<UnderworldBoss[]> {\n    const bosses: UnderworldBoss[] = [];\n    const bossElements = document.querySelectorAll('.underworld_boss, .boss');\n    \n    for (const element of bossElements) {\n      const boss = await this.parseBossElement(element as HTMLElement);\n      if (boss) {\n        bosses.push(boss);\n      }\n    }\n    \n    return bosses;\n  }\n\n  private async parseBossElement(element: HTMLElement): Promise<UnderworldBoss | null> {\n    const nameElement = element.querySelector('.boss_name, .name');\n    const healthElement = element.querySelector('.boss_health, .health');\n    const attackButton = element.querySelector('.attack_boss, [data-action=\"attack\"]');\n    \n    if (!nameElement) return null;\n    \n    const name = nameElement.textContent?.trim() || '';\n    const healthText = healthElement?.textContent?.trim() || '100/100';\n    const healthMatch = healthText.match(/(\\d+)\\/(\\d+)/);\n    const health = healthMatch ? parseInt(healthMatch[1]) : 100;\n    const maxHealth = healthMatch ? parseInt(healthMatch[2]) : 100;\n    \n    return {\n      name,\n      health,\n      maxHealth,\n      isDisPater: name.toLowerCase().includes('dis pater'),\n      canAttack: !!(attackButton && !attackButton.hasAttribute('disabled'))\n    };\n  }\n\n  private async attackBoss(boss: UnderworldBoss): Promise<void> {\n    console.log(`Underworld System: Attacking ${boss.name}`);\n    \n    const bossElements = document.querySelectorAll('.underworld_boss, .boss');\n    for (const element of bossElements) {\n      const nameElement = element.querySelector('.boss_name, .name');\n      if (nameElement?.textContent?.trim() === boss.name) {\n        const attackButton = element.querySelector('.attack_boss, [data-action=\"attack\"]');\n        if (attackButton && !attackButton.hasAttribute('disabled')) {\n          (attackButton as HTMLElement).click();\n          await this.delay(3000);\n          break;\n        }\n      }\n    }\n  }\n\n  private async manageReinforcements(settings: UnderworldSettings): Promise<void> {\n    if (settings.reinforcements.length === 0) return;\n    \n    for (const reinforcement of settings.reinforcements) {\n      const reinforcementButton = document.querySelector(`[data-reinforcement=\"${reinforcement}\"]`);\n      if (reinforcementButton && !reinforcementButton.hasAttribute('disabled')) {\n        console.log(`Underworld System: Calling reinforcement: ${reinforcement}`);\n        (reinforcementButton as HTMLElement).click();\n        await this.delay(2000);\n      }\n    }\n  }\n\n  private shouldContinueUnderworld(settings: UnderworldSettings): boolean {\n    // Check if should exit underworld\n    if (settings.exitUnder) {\n      const currentTime = new Date().getHours();\n      if (currentTime >= settings.stayHours) {\n        console.log('Underworld System: Time limit reached, exiting underworld');\n        return false;\n      }\n    }\n    \n    // Check resource limits\n    if (this.medicsUsed >= settings.maxMedics && this.rubyUsed >= settings.maxRuby) {\n      console.log('Underworld System: Resource limits reached');\n      return false;\n    }\n    \n    return true;\n  }\n\n  async getUnderworldInfo(): Promise<any> {\n    const status = await this.getUnderworldStatus();\n    \n    return {\n      ...status,\n      medicsUsed: this.medicsUsed,\n      rubyUsed: this.rubyUsed,\n      isRunning: this.isRunning\n    };\n  }\n\n  private async waitForPageLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkLoad = () => {\n        if (document.readyState === 'complete') {\n          setTimeout(resolve, 1000);\n        } else {\n          setTimeout(checkLoad, 100);\n        }\n      };\n      checkLoad();\n    });\n  }\n\n  private async delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n", "// API and network request types\r\n\r\nexport interface ApiResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport interface LoginApiResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface LicenseInfo {\r\n  isActive: boolean;\r\n  expiresAt?: Date;\r\n  daysRemaining?: number;\r\n  type: LicenseType;\r\n  key?: string;\r\n}\r\n\r\nexport enum LicenseType {\r\n  FREE_TRIAL = 'free_trial',\r\n  ESSENTIAL = 'essential',\r\n  PREMIUM = 'premium',\r\n  INACTIVE = 'inactive'\r\n}\r\n\r\nexport interface BankData {\r\n  bankId?: string;\r\n  isBank: boolean;\r\n  clients: BankClient[];\r\n  requests: BankRequest[];\r\n  selectedBackpack: number;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankClient {\r\n  id: string;\r\n  name: string;\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankRequest {\r\n  clientId: string;\r\n  goldAmount: number;\r\n  createdAt: Date;\r\n  state: BankRequestState;\r\n}\r\n\r\nexport enum BankRequestState {\r\n  PENDING = 'pending',\r\n  PROGRESS = 'progress',\r\n  READY = 'ready',\r\n  COMPLETE = 'complete'\r\n}\r\n\r\nexport interface QuestRule {\r\n  id: string;\r\n  position: number;\r\n  type: string;\r\n  conditions: QuestCondition[];\r\n  pickIfInactive: boolean;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface QuestCondition {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport interface MarketRule {\r\n  id: string;\r\n  name: string;\r\n  itemTypes: string[];\r\n  colors: string[];\r\n  maxPrice: number;\r\n  autoBuy: boolean;\r\n  allowBounded: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface PauseSchedule {\r\n  id: string;\r\n  startTime: string;\r\n  endTime: string;\r\n  workOnPause: boolean;\r\n  randomStartMinutes: number;\r\n  randomEndMinutes: number;\r\n  omitFromUW: boolean;\r\n  forcePause: boolean;\r\n  donateGold: boolean;\r\n  enabled: boolean;\r\n}", "// Chrome extension specific type definitions\r\n\r\nimport {\r\n  CostumeSettings,\r\n  TurmaSettings,\r\n  ArenaSettings,\r\n  ExpeditionSettings,\r\n  DungeonSettings,\r\n  EventSettings,\r\n  UnderworldSettings,\r\n  PackageSettings,\r\n  ForgeAutomationSettings,\r\n  RepairAutomationSettings,\r\n  QuestManagementSettings,\r\n  MarketOperationsSettings\r\n} from './settings';\r\nimport {\r\n  ShopSettings,\r\n  BankSettings,\r\n  GuildSettings\r\n} from './game';\r\nimport { QuestRule, MarketRule, PauseSchedule, BankData, LicenseInfo } from './api';\r\n\r\nexport interface ExtensionMessage {\r\n  type: MessageType;\r\n  data?: any;\r\n  isLobby?: boolean;\r\n  shouldLogin?: boolean;\r\n  loginDelay?: number;\r\n  loginFailed?: boolean;\r\n  server?: string;\r\n  playerId?: string;\r\n  language?: string;\r\n}\r\n\r\nexport enum MessageType {\r\n  LOGIN_REQUEST = 'LOGIN_REQUEST',\r\n  LOGIN_RESPONSE = 'LOGIN_RESPONSE',\r\n  LOGIN_FAILED = 'LOGIN_FAILED',\r\n  SCRIPT_STATUS = 'SCRIPT_STATUS',\r\n  SETTINGS_UPDATE = 'SETTINGS_UPDATE',\r\n  NOTIFICATION = 'NOTIFICATION',\r\n  ERROR = 'ERROR'\r\n}\r\n\r\nexport interface ExtensionStorage {\r\n  settings: BotSettings;\r\n  statistics: BotStatistics;\r\n  licenseInfo: LicenseInfo;\r\n  pauseSchedule: PauseSchedule[];\r\n  questRules: QuestRule[];\r\n  marketRules: MarketRule[];\r\n  bankData: BankData;\r\n}\r\n\r\nexport interface BotSettings {\r\n  general: GeneralSettings;\r\n  heal: HealSettings;\r\n  hideGold: HideGoldSettings;\r\n  packages: PackageSettings;\r\n  costumes: CostumeSettings;\r\n  turma: TurmaSettings;\r\n  arena: ArenaSettings;\r\n  expeditions: ExpeditionSettings;\r\n  dungeon: DungeonSettings;\r\n  event: EventSettings;\r\n  underworld: UnderworldSettings;\r\n  forge: ForgeAutomationSettings;\r\n  repair: RepairAutomationSettings;\r\n  quest: QuestManagementSettings;\r\n  market: MarketOperationsSettings;\r\n  shop: ShopSettings;\r\n  bank: BankSettings;\r\n  guild: GuildSettings;\r\n}\r\n\r\nexport interface GeneralSettings {\r\n  selectedBackpack: number;\r\n  maxRandomDelay: number;\r\n  loginDelay: number;\r\n  highlightUnderworldItems: boolean;\r\n  showShopBox: boolean;\r\n  shopMinQuality: number;\r\n  showBiddenBox: boolean;\r\n  autoCollectGodOils: boolean;\r\n  autoCollectDaily: boolean;\r\n  autoLogin?: boolean;\r\n  autoStart?: boolean;\r\n}\r\n\r\nexport interface HealSettings {\r\n  enabled: boolean;\r\n  minHealth: number;\r\n  healCervisia: boolean;\r\n  healEggs: boolean;\r\n  buyFoodNeeded: boolean;\r\n  selectedEggs: number[];\r\n  renewShop: RenewShopOption;\r\n  autoStopAttacks: boolean;\r\n  buyFood: boolean;\r\n}\r\n\r\nexport enum RenewShopOption {\r\n  NOT_ALLOW = 'not_allow',\r\n  CLOTHE_ONLY = 'clothe_only',\r\n  RUBY_OR_CLOTHE = 'ruby_or_clothe'\r\n}\r\n\r\nexport interface HideGoldSettings {\r\n  primary: HideGoldFlow;\r\n  backup: HideGoldFlow;\r\n  minGoldBackup: number;\r\n  soulBoundTo: string;\r\n  filterBy: PackFilterType;\r\n  allowMultiple: boolean;\r\n  recheckDuration: number;\r\n  pocketMoney: number;\r\n  hideIn: HideGoldLocation;\r\n  duration: PackDuration;\r\n  skillsToTrain: string[];\r\n  minItemPrice: number;\r\n  minPack: number;\r\n  maxPack: number;\r\n  buyFromPlayers: string[];\r\n}\r\n\r\nexport enum HideGoldLocation {\r\n  GUILD = 'guild',\r\n  SHOP = 'shop',\r\n  AUCTION = 'auction',\r\n  TRAINING = 'training',\r\n  MARKET = 'market',\r\n  DONATE = 'donate'\r\n}\r\n\r\nexport enum PackDuration {\r\n  TWO_HOURS = '2h',\r\n  EIGHT_HOURS = '8h',\r\n  TWENTY_FOUR_HOURS = '24h',\r\n  FORTY_EIGHT_HOURS = '48h'\r\n}\r\n\r\nexport enum PackFilterType {\r\n  LOWEST_DURATION = 'd',\r\n  HIGHEST_DURATION = 'dd',\r\n  LOWEST_PRICE = 'p',\r\n  HIGHEST_PRICE = 'pd'\r\n}\r\n\r\nexport interface HideGoldFlow {\r\n  enabled: boolean;\r\n  location: HideGoldLocation;\r\n  settings: any;\r\n}\r\n\r\nexport interface BotStatistics {\r\n  arena: ArenaStats;\r\n  quest: QuestStats;\r\n  expedition: ExpeditionStats;\r\n}\r\n\r\nexport interface ArenaStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  lastHourStats: HourlyStats;\r\n  weekStats: WeeklyStats;\r\n  startedFrom: Date;\r\n}\r\n\r\nexport interface HourlyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n}\r\n\r\nexport interface WeeklyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  totalGold: number;\r\n}\r\n\r\nexport interface QuestStats {\r\n  lastHourTotal: QuestHourlyStats;\r\n  weekStats: QuestWeeklyStats;\r\n}\r\n\r\nexport interface QuestHourlyStats {\r\n  quests: number;\r\n  experience: number;\r\n  honor: number;\r\n  food: number;\r\n  items: number;\r\n  goldEarned: number;\r\n  godRewards: number;\r\n}\r\n\r\nexport interface QuestWeeklyStats extends QuestHourlyStats {\r\n  byQuestType: Record<string, QuestHourlyStats>;\r\n}\r\n\r\nexport interface ExpeditionStats {\r\n  wins: number;\r\n  losses: number;\r\n  items: number;\r\n  fame: number;\r\n}", "// Game mechanics and bot logic types\r\n\r\nimport { ItemQuality } from './gladiatus';\r\nimport { QuestRule, MarketRule } from './api';\r\nimport { FilterCondition } from './settings';\r\n\r\nexport interface BotStatus {\r\n  isActive: boolean;\r\n  isPaused: boolean;\r\n  wasActive?: boolean; // Tracks if bot was active before page reload\r\n  currentAction: BotAction;\r\n  nextActionTime?: Date;\r\n  error?: string;\r\n  statistics: GameBotStatistics;\r\n}\r\n\r\nexport enum BotAction {\r\n  IDLE = 'idle',\r\n  HEALING = 'healing',\r\n  ATTACKING = 'attacking',\r\n  TRAVELING = 'traveling',\r\n  BUYING_PACK = 'buying_pack',\r\n  PICKING_ITEMS = 'picking_items',\r\n  UNDERWORLD = 'underworld',\r\n  EVENT = 'event',\r\n  SELLING_PACK = 'selling_pack',\r\n  QUEST_MANAGEMENT = 'quest_management',\r\n  MARKET_OPERATIONS = 'market_operations',\r\n  FORGE_REPAIR = 'forge_repair',\r\n  ROTATING_ITEMS = 'rotating_items',\r\n  CHECKING_QUESTS = 'checking_quests',\r\n  FORGING = 'forging',\r\n  SMELTING = 'smelting',\r\n  REPAIRING = 'repairing',\r\n  CHECKING_MARKET = 'checking_market',\r\n  CHECKING_AUCTION = 'checking_auction',\r\n  ACTIVATING_PACTS = 'activating_pacts',\r\n  PROCESSING_PACK = 'processing_pack',\r\n  REQUESTING_PACK = 'requesting_pack'\r\n}\r\n\r\nexport interface GameBotStatistics {\r\n  startTime: Date;\r\n  totalRunTime: number;\r\n  actionsPerformed: number;\r\n  errorsEncountered: number;\r\n  goldEarned: number;\r\n  experienceGained: number;\r\n  itemsFound: number;\r\n  combatsWon: number;\r\n  combatsLost: number;\r\n}\r\n\r\nexport interface WorkSettings {\r\n  enabled: boolean;\r\n  workIfNoPoints: boolean;\r\n  selectedWork: WorkType;\r\n  duration: number;\r\n}\r\n\r\nexport enum WorkType {\r\n  SENATOR = 'senator',\r\n  JEWELLER = 'jeweller',\r\n  STABLE_BOY = 'stable_boy',\r\n  FARMER = 'farmer',\r\n  BUTCHER = 'butcher',\r\n  FISHERMAN = 'fisherman',\r\n  BAKER = 'baker',\r\n  BLACKSMITH = 'blacksmith',\r\n  MASTER_BLACKSMITH = 'master_blacksmith'\r\n}\r\n\r\nexport interface ForgeSettings {\r\n  enabled: boolean;\r\n  acceptedColor: ItemQuality[];\r\n  prefix: string;\r\n  item: string;\r\n  suffix: string;\r\n  resourceQuality: ItemQuality;\r\n  stopSuccess: number;\r\n  toolsQuality: ToolQuality;\r\n  stopResources: boolean;\r\n  stopTools: boolean;\r\n  slots: number;\r\n  sendTo: ForgeDestination;\r\n  useHammer: boolean;\r\n}\r\n\r\nexport enum ToolQuality {\r\n  BRONZE = 'bronze',\r\n  SILVER = 'silver',\r\n  GOLD = 'gold'\r\n}\r\n\r\nexport enum ForgeDestination {\r\n  NONE = 'none',\r\n  INVENTORY = 'inventory',\r\n  PACKAGES = 'packages'\r\n}\r\n\r\nexport interface RepairSettings {\r\n  standardProfile: RepairProfile;\r\n  turmaProfile: RepairProfile;\r\n}\r\n\r\nexport interface RepairProfile {\r\n  enabled: boolean;\r\n  minConditioning: number;\r\n  maxQuality: ItemQuality;\r\n  ignoreMaterials: boolean;\r\n}\r\n\r\nexport interface QuestSettings {\r\n  enabled: boolean;\r\n  rules: QuestRule[];\r\n  underworldRules: QuestRule[];\r\n}\r\n\r\nexport interface MarketSettings {\r\n  enabled: boolean;\r\n  sellConfig: MarketSellConfig;\r\n  buyConfig: MarketBuyConfig;\r\n}\r\n\r\nexport interface MarketSellConfig {\r\n  enabled: boolean;\r\n  stackSize: number;\r\n  pricePerUnit: number;\r\n  maxListedItems: number;\r\n  goldRatio: number;\r\n  pickVAT: boolean;\r\n  forgingGoods: string[];\r\n}\r\n\r\nexport interface MarketBuyConfig {\r\n  enabled: boolean;\r\n  rules: MarketRule[];\r\n  autoBuy: boolean;\r\n}\r\n\r\nexport interface ShopSettings {\r\n  enabled: boolean;\r\n  searchRules: ShopRule[];\r\n  maxSearches: number;\r\n  usedSearches: number;\r\n  allowRuby: boolean;\r\n  autoStart: boolean;\r\n}\r\n\r\nexport interface ShopRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  autoBuy: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface BankSettings {\r\n  enabled: boolean;\r\n  isBank: boolean;\r\n  bankId?: string;\r\n  selectedBackpack: number;\r\n  clientSettings: BankClientSettings;\r\n}\r\n\r\nexport interface BankClientSettings {\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n}\r\n\r\nexport interface GuildSettings {\r\n  enabled: boolean;\r\n  attackList: string[];\r\n  ignoreList: string[];\r\n}", "// Gladiatus game-specific type definitions\r\n\r\nexport interface GladiatorServer {\r\n  number: string;\r\n  language: string;\r\n}\r\n\r\nexport interface GladiatorAccount {\r\n  id: string;\r\n  server: GladiatorServer;\r\n  playerId: string;\r\n  lastLogin: string;\r\n}\r\n\r\nexport interface GladiatorQueries {\r\n  sh?: string;\r\n  [key: string]: string | undefined;\r\n}\r\n\r\nexport interface GladiatorUrlInfo {\r\n  server?: string;\r\n  country?: string;\r\n  domain?: string;\r\n  queries: GladiatorQueries;\r\n  resolved: boolean;\r\n}\r\n\r\nexport interface LoginData {\r\n  server: GladiatorServer;\r\n  id: string;\r\n  clickedButton: string;\r\n  blackbox: string;\r\n}\r\n\r\nexport interface LoginResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n// Game item types\r\nexport interface GameItem {\r\n  id: string;\r\n  name: string;\r\n  quality: ItemQuality;\r\n  level: number;\r\n  type: ItemType;\r\n  price?: number;\r\n  isUnderworld?: boolean;\r\n  isSoulbound?: boolean;\r\n  conditioning?: number;\r\n}\r\n\r\nexport enum ItemQuality {\r\n  WHITE = 0,\r\n  GREEN = 1,\r\n  BLUE = 2,\r\n  PURPLE = 3,\r\n  ORANGE = 4,\r\n  RED = 5\r\n}\r\n\r\nexport enum ItemType {\r\n  WEAPON = 'weapon',\r\n  SHIELD = 'shield',\r\n  HELMET = 'helmet',\r\n  CHEST = 'chest',\r\n  GLOVES = 'gloves',\r\n  SHOES = 'shoes',\r\n  RING = 'ring',\r\n  AMULET = 'amulet',\r\n  USABLE = 'usable',\r\n  REINFORCEMENT = 'reinforcement',\r\n  UPGRADE = 'upgrade',\r\n  RECIPE = 'recipe',\r\n  MERCENARY = 'mercenary',\r\n  FORGING_GOODS = 'forging_goods',\r\n  TOOLS = 'tools',\r\n  SCROLL = 'scroll',\r\n  EVENT_ITEMS = 'event_items'\r\n}\r\n\r\n// Combat and character types\r\nexport interface Character {\r\n  id: string;\r\n  name: string;\r\n  level: number;\r\n  health: number;\r\n  maxHealth: number;\r\n  gold: number;\r\n  experience: number;\r\n  honor: number;\r\n}\r\n\r\nexport interface CombatResult {\r\n  won: boolean;\r\n  experience: number;\r\n  gold: number;\r\n  items: GameItem[];\r\n  honor?: number;\r\n}\r\n\r\n// Quest types\r\nexport interface Quest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  type: QuestType;\r\n  reward: QuestReward;\r\n  requirements: QuestRequirement[];\r\n  hasTimer: boolean;\r\n  attacksLeft?: number;\r\n}\r\n\r\nexport enum QuestType {\r\n  ARENA = 'arena',\r\n  GROUP_ARENA = 'group_arena',\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  WORK = 'work',\r\n  FIND_ITEM = 'find_item',\r\n  COMBAT = 'combat',\r\n  ANY_QUEST = 'any_quest'\r\n}\r\n\r\nexport interface QuestReward {\r\n  experience?: number;\r\n  gold?: number;\r\n  honor?: number;\r\n  items?: GameItem[];\r\n  isFood?: boolean;\r\n  gods?: GodType[];\r\n}\r\n\r\nexport interface QuestRequirement {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport enum GodType {\r\n  APOLLO = 'apollo',\r\n  VULCAN = 'vulcan',\r\n  MARS = 'mars',\r\n  MERCURY = 'mercury',\r\n  DIANA = 'diana',\r\n  MINERVA = 'minerva'\r\n}\r\n\r\n// Location and travel types\r\nexport interface Location {\r\n  id: string;\r\n  name: string;\r\n  type: LocationType;\r\n}\r\n\r\nexport enum LocationType {\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  ARENA = 'arena',\r\n  TURMA = 'turma',\r\n  UNDERWORLD = 'underworld',\r\n  MARKET = 'market',\r\n  GUILD = 'guild'\r\n}", "// Main type definitions for Gladiatus Helper Bot\r\n\r\nexport * from './gladiatus';\r\nexport * from './extension';\r\nexport * from './localization';\r\nexport * from './settings';\r\nexport * from './api';\r\nexport * from './ui';\r\nexport * from './game';\r\n\r\n// Re-export commonly used types\r\nexport type { BotStatus } from './game';", "// Localization and internationalization types\r\n\r\nexport interface LocalizationData {\r\n  [key: string]: string | LocalizationData;\r\n}\r\n\r\nexport interface TranslationKeys {\r\n  EXP_STATS: {\r\n    WIN: string;\r\n    LOSE: string;\r\n    ITEMS: string;\r\n    FAME: string;\r\n  };\r\n  QUEST_STATS: {\r\n    LAST_HOUR_TOTAL: string;\r\n    WEEK_STATS: string;\r\n    QUESTS: string;\r\n    AVG: string;\r\n    EXP: string;\r\n    HONOR: string;\r\n    FOOD: string;\r\n    ITEMS: string;\r\n    WITH_TIMER: string;\r\n    GOLD_EARNED: string;\r\n    GOD_REWARDS: string;\r\n    BY_QUEST_TYPE: string;\r\n  };\r\n  TITLE: {\r\n    GENERAL: string;\r\n    HEAL: string;\r\n    HIDE_GOLD: string;\r\n    PACKAGES: string;\r\n    COSTUMES: string;\r\n    TURMA: string;\r\n    ARENA: string;\r\n    EXPEDITIONS: string;\r\n    DUNGEON: string;\r\n    EVENT: string;\r\n    UNDERWORLD: string;\r\n    FORGE: string;\r\n    REPAIR: string;\r\n    QUEST: string;\r\n    MARKET: string;\r\n    SHOP: string;\r\n    BANK: string;\r\n    GUILD: string;\r\n  };\r\n  STATUS: {\r\n    FORGING: string;\r\n    WAIT: string;\r\n    INACTIVE: string;\r\n    PAUSED: string;\r\n    PICK_ITEMS: string;\r\n    SELL_ITEMS: string;\r\n    ATTACKING: string;\r\n    BUY_PACK: string;\r\n    CHECK_QUESTS: string;\r\n    SELL_PACK: string;\r\n    BUY_FOOD: string;\r\n    TRAVELING: string;\r\n    ROTATE_ITEMS: string;\r\n    HEALING: string;\r\n    BUFFING: string;\r\n    REPAIRING: string;\r\n    SMELTING: string;\r\n    EQUIP_COSTUMES: string;\r\n    CHECK_AUCTION: string;\r\n    CHECK_SMELTING: string;\r\n    CHECK_MARKET: string;\r\n    SELL_MARKET: string;\r\n    ACTIVATE_PACTS: string;\r\n    CHECKING_SHOP: string;\r\n    DICE: string;\r\n    PROCESSING_PACK: string;\r\n    REQUEST_PACK: string;\r\n  };\r\n  BUTTONS: {\r\n    CANCEL: string;\r\n    SAVE: string;\r\n    ADD_NEW_RULE: string;\r\n    DELETE: string;\r\n    EDIT: string;\r\n    ACTIVE: string;\r\n    ACTIVATE: string;\r\n    BUY_LICENCE: string;\r\n    INACTIVE: string;\r\n    CLOSE: string;\r\n    CLEAR_ALL: string;\r\n    CLEAR: string;\r\n    CONFIRM: string;\r\n  };\r\n}\r\n\r\nexport type SupportedLanguage = 'en' | 'es' | 'de' | 'br' | 'pl' | 'ro' | 'fr' | 'pt' | 'tr' | 'cz' | 'pe' | 'ru' | 'hu' | 'ar' | 'bg' | 'nl';\r\n\r\nexport interface LanguageInfo {\r\n  code: SupportedLanguage;\r\n  name: string;\r\n  nativeName: string;\r\n}", "// Settings and configuration type definitions\r\n\r\nimport { ItemType, ItemQuality } from './gladiatus';\r\n\r\nexport interface PackageSettings {\r\n  rotateItems: RotateItemsSettings;\r\n  sellItems: SellItemsSettings;\r\n  pickGold: PickGoldSettings;\r\n  operationSpeed: OperationSpeed;\r\n}\r\n\r\nexport interface RotateItemsSettings {\r\n  enabled: boolean;\r\n  selectedItems: ItemType[];\r\n  colors: ItemQuality[];\r\n  underworldItems: boolean;\r\n  itemsCooldownDays: number;\r\n  collectResourceHourly: boolean;\r\n}\r\n\r\nexport interface SellItemsSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n  autoSellOnRenew: boolean;\r\n}\r\n\r\nexport interface SellRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  itemTypes: ItemType[];\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface FilterCondition {\r\n  type: FilterType;\r\n  value: string | number;\r\n}\r\n\r\nexport enum FilterType {\r\n  CONTAINS = 'contains',\r\n  NOT_CONTAINS = 'not_contains',\r\n  CONTAINS_ANY = 'contains_any',\r\n  NOT_CONTAINS_ANY = 'not_contains_any',\r\n  CONTAINS_WORD = 'contains_word',\r\n  IS_UNDERWORLD = 'is_underworld',\r\n  IS_NOT_UNDERWORLD = 'is_not_underworld',\r\n  GREATER_THAN = 'greater_than',\r\n  LESS_THAN = 'less_than',\r\n  STARTS_WITH = 'starts_with',\r\n  ENDS_WITH = 'ends_with',\r\n  CONTAINS_RESOURCE = 'contains_resource'\r\n}\r\n\r\nexport interface PickGoldSettings {\r\n  enabled: boolean;\r\n  goldToPick: number;\r\n}\r\n\r\nexport enum OperationSpeed {\r\n  VERY_FAST = 'very_fast',\r\n  FAST = 'fast',\r\n  NORMAL = 'normal',\r\n  SLOW = 'slow'\r\n}\r\n\r\nexport interface CostumeSettings {\r\n  wearUnderworld: boolean;\r\n  preventWearUnderworldOnPause: boolean;\r\n  dontPauseIfUnderworldActive: boolean;\r\n  underworldCostume: UnderworldCostume;\r\n  standardProfile: CostumeProfile;\r\n  turmaProfile: CostumeProfile;\r\n  eventProfile: CostumeProfile;\r\n}\r\n\r\nexport enum UnderworldCostume {\r\n  DIS_PATER_NORMAL = 'dis_pater_normal',\r\n  DIS_PATER_MEDIUM = 'dis_pater_medium',\r\n  DIS_PATER_HARD = 'dis_pater_hard',\r\n  VULCANUS_FORGE = 'vulcanus_forge',\r\n  FERONIAS_EARTHEN_SHIELD = 'feronias_earthen_shield',\r\n  NEPTUNES_FLUID_MIGHT = 'neptunes_fluid_might',\r\n  AELOUS_AERIAL_FREEDOM = 'aelous_aerial_freedom',\r\n  PLUTOS_DEADLY_MIST = 'plutos_deadly_mist',\r\n  JUNOS_BREATH_OF_LIFE = 'junos_breath_of_life',\r\n  WRATH_MOUNTAIN_SCALE_ARMOUR = 'wrath_mountain_scale_armour',\r\n  EAGLE_EYES = 'eagle_eyes',\r\n  SATURNS_WINTER_GARMENT = 'saturns_winter_garment',\r\n  BUBONA_BULL_ARMOUR = 'bubona_bull_armour',\r\n  MERCERIUS_ROBBERS_GARMENTS = 'mercerius_robbers_garments',\r\n  RA_LIGHT_ROBE = 'ra_light_robe'\r\n}\r\n\r\nexport interface CostumeProfile {\r\n  enabled: boolean;\r\n  costume?: UnderworldCostume;\r\n}\r\n\r\nexport interface TurmaSettings {\r\n  enabled: boolean;\r\n  levelRestriction: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  runUntilGetChest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n}\r\n\r\nexport interface ArenaSettings {\r\n  enabled: boolean;\r\n  allowSim: boolean;\r\n  prioritizeChance: number;\r\n  skipLow: boolean;\r\n  loseLimit: number;\r\n  attackTargetPlayers: boolean;\r\n  allowMaxAttacks: boolean;\r\n  autoIgnoreGuildPlayers: boolean;\r\n  attackScore: boolean;\r\n  selectScorePage: number;\r\n  progressLeague: boolean;\r\n  stopAfterLimit: boolean;\r\n  attackGoldWhale: boolean;\r\n  doRevenge: boolean;\r\n  totalWin: boolean;\r\n  limitAttacks: number;\r\n  dailyAttacks: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  attackIfCombatQuest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n  attackScoreSingle: boolean;\r\n}\r\n\r\nexport interface ExpeditionSettings {\r\n  enabled: boolean;\r\n  minDungeonPoints: number;\r\n  location: string;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  travelForDungeon: boolean;\r\n  removeCooldown: CooldownRemovalOption;\r\n  cooldownLimit: number;\r\n  cooldownUsed: number;\r\n}\r\n\r\nexport enum CooldownRemovalOption {\r\n  HOURGLASS = 'hourglass',\r\n  HOURGLASS_OR_RUBY = 'hourglass_or_ruby'\r\n}\r\n\r\nexport interface DungeonSettings {\r\n  enabled: boolean;\r\n  location: string;\r\n  useGateKey: boolean;\r\n  isAdvanced: boolean;\r\n  skipBoss: boolean;\r\n  bossName: string;\r\n  restartAfterFail: number;\r\n}\r\n\r\nexport interface EventSettings {\r\n  enabled: boolean;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  autoRenewEvent: boolean;\r\n  followLeader: string;\r\n  autoStop: boolean;\r\n}\r\n\r\nexport interface UnderworldSettings {\r\n  enabled: boolean;\r\n  noWeapon: boolean;\r\n  sendMail: boolean;\r\n  mailContent: string;\r\n  maxMedics: number;\r\n  usedMedics: number;\r\n  maxRuby: number;\r\n  usedRuby: number;\r\n  buffs: boolean;\r\n  costumes: boolean;\r\n  reinforcements: string[];\r\n  mode: UnderworldMode;\r\n  difficulty: UnderworldDifficulty;\r\n  healGuild: boolean;\r\n  healPotions: boolean;\r\n  healSacrifice: boolean;\r\n  allowMobilisation: boolean;\r\n  allowRuby: boolean;\r\n  attackDisPaterAsap: boolean;\r\n  stopArenaInUnder: boolean;\r\n  healCostumes: boolean;\r\n  allowReinforcements: boolean;\r\n  allowUpgrades: boolean;\r\n  autoBuffGods: boolean;\r\n  autoBuffOils: boolean;\r\n  exitUnder: boolean;\r\n  stayHours: number;\r\n}\r\n\r\nexport enum UnderworldMode {\r\n  FARM_QUEST = 'farm_quest',\r\n  FARM_MOB = 'farm_mob',\r\n  NORMAL = 'normal'\r\n}\r\n\r\nexport enum UnderworldDifficulty {\r\n  NORMAL = 'normal',\r\n  MEDIUM = 'medium',\r\n  HARD = 'hard'\r\n}\r\n\r\nexport interface QuestManagementSettings {\r\n  enabled: boolean;\r\n  autoComplete: boolean;\r\n  dailyLimit: number;\r\n  rules: QuestManagementRule[];\r\n}\r\n\r\nexport interface QuestManagementRule {\r\n  priority: number;\r\n  questType: string;\r\n  minReward: number;\r\n  maxDifficulty: 'easy' | 'medium' | 'hard';\r\n  autoComplete: boolean;\r\n}\r\n\r\nexport interface MarketOperationsSettings {\r\n  enabled: boolean;\r\n  checkInterval: number;\r\n  dailyBuyLimit: number;\r\n  autoBuy: AutoBuySettings;\r\n  autoSell: AutoSellSettings;\r\n  auction: AuctionSettings;\r\n}\r\n\r\nexport interface AutoBuySettings {\r\n  enabled: boolean;\r\n  maxPrice: number;\r\n  maxGoldSpend: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemNames: string[];\r\n  blacklistedSellers: string[];\r\n}\r\n\r\nexport interface AutoSellSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n}\r\n\r\nexport interface AuctionSettings {\r\n  enabled: boolean;\r\n  autoBid: boolean;\r\n  autoBuyout: boolean;\r\n  maxBid: number;\r\n  maxBuyout: number;\r\n  bidIncrement: number;\r\n  minTimeRemaining: number;\r\n  qualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface ForgeAutomationSettings {\r\n  enabled: boolean;\r\n  autoForge: boolean;\r\n  autoSmelt: boolean;\r\n  maxGoldSpend: number;\r\n  maxCostPerItem: number;\r\n  dailyForgeLimit: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemTypes: string[];\r\n  minSmeltValue: number;\r\n  smeltQualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface RepairAutomationSettings {\r\n  enabled: boolean;\r\n  repairThreshold: number;\r\n  maxCostPerItem: number;\r\n  maxGoldSpend: number;\r\n  itemFilter: string[];\r\n}", "// UI and interface types\r\n\r\nexport interface UINotification {\r\n  id: string;\r\n  type: NotificationType;\r\n  title: string;\r\n  message: string;\r\n  timestamp: Date;\r\n  read: boolean;\r\n  actions?: NotificationAction[];\r\n}\r\n\r\nexport enum NotificationType {\r\n  INFO = 'info',\r\n  SUCCESS = 'success',\r\n  WARNING = 'warning',\r\n  ERROR = 'error',\r\n  GOLD_EXPIRY = 'gold_expiry',\r\n  ITEM_FOUND = 'item_found',\r\n  ITEM_BOUGHT = 'item_bought'\r\n}\r\n\r\nexport interface NotificationAction {\r\n  label: string;\r\n  action: string;\r\n  data?: any;\r\n}\r\n\r\nexport interface TabConfig {\r\n  id: string;\r\n  name: string;\r\n  icon?: string;\r\n  component: string;\r\n  enabled: boolean;\r\n  badge?: string | number;\r\n}\r\n\r\nexport interface ModalConfig {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n  actions: ModalAction[];\r\n  size?: ModalSize;\r\n  closable?: boolean;\r\n}\r\n\r\nexport interface ModalAction {\r\n  label: string;\r\n  action: string;\r\n  style?: ButtonStyle;\r\n  data?: any;\r\n}\r\n\r\nexport enum ModalSize {\r\n  SMALL = 'small',\r\n  MEDIUM = 'medium',\r\n  LARGE = 'large',\r\n  EXTRA_LARGE = 'extra_large'\r\n}\r\n\r\nexport enum ButtonStyle {\r\n  PRIMARY = 'primary',\r\n  SECONDARY = 'secondary',\r\n  SUCCESS = 'success',\r\n  DANGER = 'danger',\r\n  WARNING = 'warning',\r\n  INFO = 'info'\r\n}\r\n\r\nexport interface FormField {\r\n  id: string;\r\n  type: FieldType;\r\n  label: string;\r\n  value: any;\r\n  options?: SelectOption[];\r\n  validation?: ValidationRule[];\r\n  disabled?: boolean;\r\n  placeholder?: string;\r\n  help?: string;\r\n}\r\n\r\nexport enum FieldType {\r\n  TEXT = 'text',\r\n  NUMBER = 'number',\r\n  SELECT = 'select',\r\n  CHECKBOX = 'checkbox',\r\n  RADIO = 'radio',\r\n  TEXTAREA = 'textarea',\r\n  RANGE = 'range',\r\n  COLOR = 'color',\r\n  DATE = 'date',\r\n  TIME = 'time'\r\n}\r\n\r\nexport interface SelectOption {\r\n  value: any;\r\n  label: string;\r\n  disabled?: boolean;\r\n  icon?: string;\r\n}\r\n\r\nexport interface ValidationRule {\r\n  type: ValidationType;\r\n  value?: any;\r\n  message: string;\r\n}\r\n\r\nexport enum ValidationType {\r\n  REQUIRED = 'required',\r\n  MIN = 'min',\r\n  MAX = 'max',\r\n  MIN_LENGTH = 'min_length',\r\n  MAX_LENGTH = 'max_length',\r\n  PATTERN = 'pattern',\r\n  EMAIL = 'email',\r\n  URL = 'url'\r\n}\r\n\r\nexport interface UIState {\r\n  activeTab: string;\r\n  sidebarCollapsed: boolean;\r\n  notifications: UINotification[];\r\n  modals: ModalConfig[];\r\n  loading: boolean;\r\n  error?: string;\r\n}", "// Default Settings Factory - Provides default values for all bot settings\n\nimport {\n  BotSettings,\n  ItemQuality,\n  UnderworldMode,\n  UnderworldDifficulty,\n  CooldownRemovalOption,\n  OperationSpeed,\n  RenewShopOption,\n  HideGoldLocation,\n  PackFilterType,\n  PackDuration\n} from '../types';\n\nexport class DefaultSettingsFactory {\n  static createDefaultSettings(): BotSettings {\n    return {\n      general: {\n        selectedBackpack: 1,\n        maxRandomDelay: 10,\n        loginDelay: 5,\n        autoCollectDaily: true,\n        autoCollectGodOils: true,\n        showBiddenBox: true,\n        showShopBox: true,\n        highlightUnderworldItems: true,\n        shopMinQuality: 1\n      },\n      heal: {\n        enabled: false,\n        minHealth: 80,\n        healCervisia: true,\n        healEggs: true,\n        selectedEggs: [1, 2, 3],\n        buyFood: false,\n        buyFoodNeeded: false,\n        renewShop: RenewShopOption.NOT_ALLOW,\n        autoStopAttacks: true\n      },\n      hideGold: {\n        primary: {\n          enabled: false,\n          location: HideGoldLocation.GUILD,\n          settings: {}\n        },\n        backup: {\n          enabled: false,\n          location: HideGoldLocation.SHOP,\n          settings: {}\n        },\n        minGoldBackup: 500,\n        soulBoundTo: '',\n        filterBy: PackFilterType.LOWEST_PRICE,\n        allowMultiple: false,\n        recheckDuration: 3600,\n        pocketMoney: 1000,\n        hideIn: HideGoldLocation.GUILD,\n        duration: PackDuration.TWO_HOURS,\n        skillsToTrain: [],\n        minItemPrice: 100,\n        minPack: 1,\n        maxPack: 10,\n        buyFromPlayers: []\n      },\n      packages: {\n        rotateItems: {\n          enabled: false,\n          selectedItems: [],\n          colors: [],\n          underworldItems: false,\n          itemsCooldownDays: 7,\n          collectResourceHourly: false\n        },\n        sellItems: {\n          enabled: false,\n          rules: [],\n          autoSellOnRenew: false\n        },\n        pickGold: {\n          enabled: false,\n          goldToPick: 0\n        },\n        operationSpeed: OperationSpeed.NORMAL\n      },\n      costumes: {\n        wearUnderworld: false,\n        preventWearUnderworldOnPause: false,\n        dontPauseIfUnderworldActive: false,\n        underworldCostume: 'dis_pater_normal' as any,\n        standardProfile: {\n          enabled: false\n        },\n        turmaProfile: {\n          enabled: false\n        },\n        eventProfile: {\n          enabled: false\n        }\n      },\n      turma: {\n        enabled: false,\n        levelRestriction: 0,\n        autoStop: 0,\n        attackIfQuest: false,\n        runUntilGetChest: false,\n        goldRaided: 0,\n        attackPlayers: [],\n        autoIgnorePlayers: [],\n        ignorePlayers: [],\n        attackSameServer: true\n      },\n      arena: {\n        enabled: false,\n        allowSim: true,\n        prioritizeChance: 80,\n        skipLow: false,\n        loseLimit: 3,\n        attackTargetPlayers: false,\n        allowMaxAttacks: false,\n        autoIgnoreGuildPlayers: true,\n        attackScore: false,\n        selectScorePage: 1,\n        progressLeague: false,\n        stopAfterLimit: false,\n        attackGoldWhale: false,\n        doRevenge: false,\n        totalWin: false,\n        limitAttacks: 0,\n        dailyAttacks: 0,\n        autoStop: 0,\n        attackIfQuest: false,\n        attackIfCombatQuest: false,\n        goldRaided: 0,\n        attackPlayers: [],\n        autoIgnorePlayers: [],\n        ignorePlayers: [],\n        attackSameServer: true,\n        attackScoreSingle: false\n      },\n      expeditions: {\n        enabled: false,\n        minDungeonPoints: 0,\n        location: '',\n        mob: '',\n        autoCollectBonuses: true,\n        travelForDungeon: false,\n        removeCooldown: CooldownRemovalOption.HOURGLASS,\n        cooldownLimit: 10,\n        cooldownUsed: 0\n      },\n      dungeon: {\n        enabled: false,\n        location: '',\n        useGateKey: false,\n        isAdvanced: false,\n        skipBoss: false,\n        bossName: '',\n        restartAfterFail: 0\n      },\n      event: {\n        enabled: false,\n        mob: '',\n        autoCollectBonuses: true,\n        autoRenewEvent: false,\n        followLeader: '',\n        autoStop: false\n      },\n      underworld: {\n        enabled: false,\n        noWeapon: false,\n        sendMail: false,\n        mailContent: '',\n        maxMedics: 5,\n        usedMedics: 0,\n        maxRuby: 2,\n        usedRuby: 0,\n        buffs: true,\n        costumes: true,\n        reinforcements: [],\n        mode: UnderworldMode.NORMAL,\n        difficulty: UnderworldDifficulty.NORMAL,\n        healGuild: true,\n        healPotions: true,\n        healSacrifice: false,\n        allowMobilisation: false,\n        allowRuby: false,\n        attackDisPaterAsap: true,\n        stopArenaInUnder: true,\n        healCostumes: true,\n        allowReinforcements: false,\n        allowUpgrades: false,\n        autoBuffGods: true,\n        autoBuffOils: true,\n        exitUnder: false,\n        stayHours: 24\n      },\n      forge: {\n        enabled: false,\n        autoForge: false,\n        autoSmelt: false,\n        maxGoldSpend: 10000,\n        maxCostPerItem: 1000,\n        dailyForgeLimit: 10,\n        qualityFilter: [ItemQuality.WHITE, ItemQuality.GREEN],\n        minLevel: 1,\n        maxLevel: 50,\n        itemTypes: [],\n        minSmeltValue: 10,\n        smeltQualityFilter: [ItemQuality.WHITE]\n      },\n      repair: {\n        enabled: false,\n        repairThreshold: 50,\n        maxCostPerItem: 500,\n        maxGoldSpend: 5000,\n        itemFilter: []\n      },\n      quest: {\n        enabled: false,\n        autoComplete: false,\n        dailyLimit: 10,\n        rules: []\n      },\n      market: {\n        enabled: false,\n        checkInterval: 300,\n        dailyBuyLimit: 20,\n        autoBuy: {\n          enabled: false,\n          maxPrice: 10000,\n          maxGoldSpend: 50000,\n          qualityFilter: [ItemQuality.BLUE, ItemQuality.PURPLE],\n          minLevel: 1,\n          maxLevel: 100,\n          itemNames: [],\n          blacklistedSellers: []\n        },\n        autoSell: {\n          enabled: false,\n          rules: []\n        },\n        auction: {\n          enabled: false,\n          autoBid: false,\n          autoBuyout: false,\n          maxBid: 10000,\n          maxBuyout: 20000,\n          bidIncrement: 100,\n          minTimeRemaining: 300,\n          qualityFilter: [ItemQuality.PURPLE, ItemQuality.ORANGE]\n        }\n      },\n      shop: {\n        enabled: false,\n        searchRules: [],\n        maxSearches: 10,\n        usedSearches: 0,\n        allowRuby: false,\n        autoStart: false\n      },\n      bank: {\n        enabled: false,\n        isBank: false,\n        selectedBackpack: 1,\n        clientSettings: {\n          requestThreshold: 1000,\n          packGoldAmount: 10000,\n          duration: '2h'\n        }\n      },\n      guild: {\n        enabled: false,\n        attackList: [],\n        ignoreList: []\n      }\n    };\n  }\n\n  static mergeWithDefaults(userSettings: Partial<BotSettings>): BotSettings {\n    const defaultSettings = this.createDefaultSettings();\n    return this.deepMerge(defaultSettings, userSettings);\n  }\n\n  private static deepMerge(target: any, source: any): any {\n    const result = { ...target };\n    \n    for (const key in source) {\n      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {\n        result[key] = this.deepMerge(target[key] || {}, source[key]);\n      } else {\n        result[key] = source[key];\n      }\n    }\n    \n    return result;\n  }\n}\n", "// Gladiatus URL parsing and validation utilities\r\n\r\nimport { GladiatorUrlInfo, GladiatorQueries } from '../types';\r\n\r\nexport class GladiatorUrlHelper {\r\n  private resolved: boolean = false;\r\n  private urlInfo: GladiatorUrlInfo = {\r\n    queries: {},\r\n    resolved: false\r\n  };\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus domain\r\n   */\r\n  isGladiatus(url: string): boolean {\r\n    return /gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus game page (not lobby)\r\n   */\r\n  isPlaying(url: string): boolean {\r\n    return /https:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is the Gladiatus lobby\r\n   */\r\n  isLobby(url: string): boolean {\r\n    return /lobby\\.gladiatus\\.gameforge\\.com/.test(url) && url.indexOf('loading') < 0;\r\n  }\r\n\r\n  /**\r\n   * Parse query string into object\r\n   */\r\n  resolveQueries(queryString: string): GladiatorQueries {\r\n    const queries: GladiatorQueries = {};\r\n    const params = queryString.split('&');\r\n\r\n    for (let i = params.length - 1; i >= 0; i--) {\r\n      const [key, value] = params[i].split('=');\r\n      if (key && value) {\r\n        queries[key] = decodeURIComponent(value);\r\n      }\r\n    }\r\n\r\n    return queries;\r\n  }\r\n\r\n  /**\r\n   * Resolve URL information from Gladiatus game URL\r\n   */\r\n  resolve(url: string, force: boolean = false): GladiatorUrlInfo | null {\r\n    if (this.resolved && !force) {\r\n      return this.urlInfo;\r\n    }\r\n\r\n    const match = url.match(\r\n      /https?:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com\\/game\\/(?:index|main)\\.php(?:\\?(.*))?/i\r\n    );\r\n\r\n    if (!match) {\r\n      return null;\r\n    }\r\n\r\n    this.urlInfo = {\r\n      server: match[1],\r\n      country: match[2],\r\n      domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,\r\n      queries: this.resolveQueries(match[3] || ''),\r\n      resolved: true\r\n    };\r\n\r\n    this.resolved = true;\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Build Gladiatus URL with parameters\r\n   */\r\n  buildUrl(params: Record<string, string | number>, page: string = 'index.php'): string {\r\n    const queryParts: string[] = [];\r\n\r\n    for (const key in params) {\r\n      queryParts.push(`${key}=${encodeURIComponent(params[key])}`);\r\n    }\r\n\r\n    // Add security hash if available\r\n    if (this.urlInfo.queries.sh) {\r\n      queryParts.push(`sh=${this.urlInfo.queries.sh}`);\r\n    }\r\n\r\n    return `${page}?${queryParts.join('&')}`;\r\n  }\r\n\r\n  /**\r\n   * Build AJAX URL with parameters\r\n   */\r\n  buildAjaxUrl(params: Record<string, string | number>): string {\r\n    return this.buildUrl(params, 'ajax.php');\r\n  }\r\n\r\n  /**\r\n   * Get current URL info\r\n   */\r\n  getUrlInfo(): GladiatorUrlInfo {\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Reset resolved state\r\n   */\r\n  reset(): void {\r\n    this.resolved = false;\r\n    this.urlInfo = {\r\n      queries: {},\r\n      resolved: false\r\n    };\r\n  }\r\n}", "// Chrome extension storage management utilities\r\n\r\nimport { BotSettings, BotStatus, ExtensionStorage } from '../types';\r\nimport { DefaultSettingsFactory } from './default-settings';\r\n\r\nexport class StorageManager {\r\n  private readonly STORAGE_KEYS = {\r\n    SETTINGS: 'gh_settings',\r\n    STATISTICS: 'gh_statistics',\r\n    LICENSE: 'gh_license',\r\n    PAUSE_SCHEDULE: 'gh_pause_schedule',\r\n    QUEST_RULES: 'gh_quest_rules',\r\n    MARKET_RULES: 'gh_market_rules',\r\n    BANK_DATA: 'gh_bank_data',\r\n    BOT_STATUS: 'gh_bot_status'\r\n  } as const;\r\n\r\n  /**\r\n   * Check if Chrome storage API is available\r\n   */\r\n  private isChromeStorageAvailable(): boolean {\r\n    return typeof chrome !== 'undefined' &&\r\n           chrome.storage &&\r\n           chrome.storage.local &&\r\n           typeof chrome.storage.local.get === 'function';\r\n  }\r\n\r\n  /**\r\n   * Fallback method to get settings from localStorage\r\n   */\r\n  private getSettingsFromLocalStorage(): Partial<BotSettings> | null {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);\r\n      return stored ? JSON.parse(stored) : null;\r\n    } catch (error) {\r\n      console.error('Failed to get settings from localStorage:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback method to save settings to localStorage\r\n   */\r\n  private saveSettingsToLocalStorage(settings: Partial<BotSettings>): void {\r\n    try {\r\n      const currentSettings = this.getSettingsFromLocalStorage() || {};\r\n      const updatedSettings = this.deepMerge(currentSettings, settings);\r\n      localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));\r\n    } catch (error) {\r\n      console.error('Failed to save settings to localStorage:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get bot settings from storage\r\n   */\r\n  async getSettings(): Promise<BotSettings | null> {\r\n    try {\r\n      // Check if Chrome storage API is available\r\n      if (!this.isChromeStorageAvailable()) {\r\n        console.warn('Chrome storage API not available, using localStorage fallback');\r\n        const storedSettings = this.getSettingsFromLocalStorage();\r\n        return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();\r\n      }\r\n\r\n      const result = await chrome.storage.local.get(this.STORAGE_KEYS.SETTINGS);\r\n      const storedSettings = result[this.STORAGE_KEYS.SETTINGS];\r\n\r\n      if (!storedSettings) {\r\n        // Return default settings if none are stored\r\n        return DefaultSettingsFactory.createDefaultSettings();\r\n      }\r\n\r\n      // Merge stored settings with defaults to ensure all properties exist\r\n      return DefaultSettingsFactory.mergeWithDefaults(storedSettings);\r\n    } catch (error) {\r\n      console.error('Failed to get settings:', error);\r\n      // Fallback to localStorage\r\n      const storedSettings = this.getSettingsFromLocalStorage();\r\n      return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save bot settings to storage\r\n   */\r\n  async saveSettings(settings: Partial<BotSettings>): Promise<void> {\r\n    try {\r\n      // Check if Chrome storage API is available\r\n      if (!this.isChromeStorageAvailable()) {\r\n        console.warn('Chrome storage API not available, using localStorage fallback');\r\n        this.saveSettingsToLocalStorage(settings);\r\n        return;\r\n      }\r\n\r\n      const currentSettings = await this.getSettings() || {};\r\n      const updatedSettings = this.deepMerge(currentSettings, settings);\r\n\r\n      await chrome.storage.local.set({\r\n        [this.STORAGE_KEYS.SETTINGS]: updatedSettings\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to save settings:', error);\r\n      // Fallback to localStorage\r\n      this.saveSettingsToLocalStorage(settings);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get bot status from storage\r\n   */\r\n  async getBotStatus(): Promise<BotStatus | null> {\r\n    try {\r\n      if (!this.isChromeStorageAvailable()) {\r\n        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);\r\n        return stored ? JSON.parse(stored) : null;\r\n      }\r\n\r\n      const result = await chrome.storage.local.get(this.STORAGE_KEYS.BOT_STATUS);\r\n      return result[this.STORAGE_KEYS.BOT_STATUS] || null;\r\n    } catch (error) {\r\n      console.error('Failed to get bot status:', error);\r\n      try {\r\n        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);\r\n        return stored ? JSON.parse(stored) : null;\r\n      } catch {\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save bot status to storage\r\n   */\r\n  async saveBotStatus(status: BotStatus): Promise<void> {\r\n    try {\r\n      if (!this.isChromeStorageAvailable()) {\r\n        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));\r\n        return;\r\n      }\r\n\r\n      await chrome.storage.local.set({\r\n        [this.STORAGE_KEYS.BOT_STATUS]: status\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to save bot status:', error);\r\n      // Fallback to localStorage\r\n      try {\r\n        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));\r\n      } catch (fallbackError) {\r\n        console.error('Failed to save bot status to localStorage:', fallbackError);\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all extension data from storage\r\n   */\r\n  async getAllData(): Promise<Partial<ExtensionStorage>> {\r\n    try {\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      const result = await chrome.storage.local.get(keys);\r\n\r\n      return {\r\n        settings: result[this.STORAGE_KEYS.SETTINGS],\r\n        statistics: result[this.STORAGE_KEYS.STATISTICS],\r\n        licenseInfo: result[this.STORAGE_KEYS.LICENSE],\r\n        pauseSchedule: result[this.STORAGE_KEYS.PAUSE_SCHEDULE],\r\n        questRules: result[this.STORAGE_KEYS.QUEST_RULES],\r\n        marketRules: result[this.STORAGE_KEYS.MARKET_RULES],\r\n        bankData: result[this.STORAGE_KEYS.BANK_DATA]\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get all data:', error);\r\n      return {};\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all extension data from storage\r\n   */\r\n  async clearAllData(): Promise<void> {\r\n    try {\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      await chrome.storage.local.remove(keys);\r\n    } catch (error) {\r\n      console.error('Failed to clear all data:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get storage usage information\r\n   */\r\n  async getStorageUsage(): Promise<{ bytesInUse: number; quota: number }> {\r\n    try {\r\n      const bytesInUse = await chrome.storage.local.getBytesInUse();\r\n      const quota = chrome.storage.local.QUOTA_BYTES;\r\n\r\n      return { bytesInUse, quota };\r\n    } catch (error) {\r\n      console.error('Failed to get storage usage:', error);\r\n      return { bytesInUse: 0, quota: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deep merge two objects\r\n   */\r\n  private deepMerge(target: any, source: any): any {\r\n    const result = { ...target };\r\n\r\n    for (const key in source) {\r\n      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {\r\n        result[key] = this.deepMerge(result[key] || {}, source[key]);\r\n      } else {\r\n        result[key] = source[key];\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Export settings as JSON string\r\n   */\r\n  async exportSettings(): Promise<string> {\r\n    const data = await this.getAllData();\r\n    return JSON.stringify(data, null, 2);\r\n  }\r\n\r\n  /**\r\n   * Import settings from JSON string\r\n   */\r\n  async importSettings(jsonData: string): Promise<void> {\r\n    try {\r\n      const data = JSON.parse(jsonData);\r\n\r\n      if (data.settings) {\r\n        await this.saveSettings(data.settings);\r\n      }\r\n\r\n      // Import other data types as needed\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      const importData: Record<string, any> = {};\r\n\r\n      for (const key of keys) {\r\n        const dataKey = key.replace('gh_', '');\r\n        if (data[dataKey]) {\r\n          importData[key] = data[dataKey];\r\n        }\r\n      }\r\n\r\n      if (Object.keys(importData).length > 0) {\r\n        await chrome.storage.local.set(importData);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to import settings:', error);\r\n      throw new Error('Invalid settings data');\r\n    }\r\n  }\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Main application logic for Gladiatus Helper Bot\r\n\r\nimport { BotStatus, BotAction, BotSettings } from '../types';\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\nimport { StorageManager } from '../utils/storage-manager';\r\nimport { BotEngine } from './bot-engine';\r\nimport { LoginManager } from './login-manager';\r\nimport { UIManager } from './ui-manager';\r\n\r\nclass GladiatusHelperBot {\r\n  private urlHelper: GladiatorUrlHelper;\r\n  private storageManager: StorageManager;\r\n  private botEngine: BotEngine;\r\n  private loginManager: LoginManager;\r\n  private uiManager: UIManager;\r\n\r\n  private isInitialized: boolean = false;\r\n  private botStatus: BotStatus;\r\n  private settings: BotSettings | null = null;\r\n\r\n  constructor() {\r\n    this.urlHelper = new GladiatorUrlHelper();\r\n    this.storageManager = new StorageManager();\r\n    this.botEngine = new BotEngine(this.storageManager, this.urlHelper);\r\n    this.loginManager = new LoginManager(this.urlHelper, this.storageManager);\r\n    this.uiManager = new UIManager();\r\n\r\n    this.botStatus = {\r\n      isActive: false,\r\n      isPaused: false,\r\n      currentAction: BotAction.IDLE,\r\n      statistics: {\r\n        startTime: new Date(),\r\n        totalRunTime: 0,\r\n        actionsPerformed: 0,\r\n        errorsEncountered: 0,\r\n        goldEarned: 0,\r\n        experienceGained: 0,\r\n        itemsFound: 0,\r\n        combatsWon: 0,\r\n        combatsLost: 0\r\n      }\r\n    };\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  private async initialize(): Promise<void> {\r\n    try {\r\n      console.log('Gladiatus Helper Bot: Initializing main application');\r\n\r\n      // Load settings\r\n      await this.loadSettings();\r\n\r\n      // Initialize URL info\r\n      const currentUrl = window.location.href;\r\n      this.urlHelper.resolve(currentUrl);\r\n\r\n      // Set up event listeners\r\n      this.setupEventListeners();\r\n\r\n      // Check if we're on lobby page\r\n      if (this.urlHelper.isLobby(currentUrl)) {\r\n        await this.handleLobbyPage();\r\n      } else if (this.urlHelper.isPlaying(currentUrl)) {\r\n        await this.handleGamePage();\r\n      }\r\n\r\n      this.isInitialized = true;\r\n      console.log('Gladiatus Helper Bot: Initialization complete');\r\n\r\n    } catch (error) {\r\n      console.error('Gladiatus Helper Bot: Initialization failed:', error);\r\n      this.botStatus.error = (error as Error).message;\r\n    }\r\n  }\r\n\r\n  private async loadSettings(): Promise<void> {\r\n    this.settings = await this.storageManager.getSettings();\r\n    if (!this.settings) {\r\n      console.log('No settings found, using defaults');\r\n      // Initialize with default settings if none exist\r\n      this.settings = this.getDefaultSettings();\r\n      await this.storageManager.saveSettings(this.settings);\r\n    }\r\n  }\r\n\r\n  private getDefaultSettings(): BotSettings {\r\n    return {\r\n      general: {\r\n        selectedBackpack: 1,\r\n        maxRandomDelay: 10,\r\n        loginDelay: 5,\r\n        highlightUnderworldItems: true,\r\n        showShopBox: true,\r\n        shopMinQuality: 1,\r\n        showBiddenBox: true,\r\n        autoCollectGodOils: true,\r\n        autoCollectDaily: true\r\n      },\r\n      heal: {\r\n        enabled: false,\r\n        minHealth: 50,\r\n        healCervisia: true,\r\n        healEggs: true,\r\n        buyFoodNeeded: true,\r\n        selectedEggs: [1, 2, 3],\r\n        renewShop: 'not_allow' as any,\r\n        autoStopAttacks: true,\r\n        buyFood: true\r\n      },\r\n      hideGold: {\r\n        primary: {\r\n          enabled: true,\r\n          location: 'guild' as any,\r\n          settings: {}\r\n        },\r\n        backup: {\r\n          enabled: false,\r\n          location: 'shop' as any,\r\n          settings: {}\r\n        },\r\n        minGoldBackup: 1000,\r\n        soulBoundTo: '',\r\n        filterBy: 'd' as any,\r\n        allowMultiple: false,\r\n        recheckDuration: 30,\r\n        pocketMoney: 500,\r\n        hideIn: 'guild' as any,\r\n        duration: '24h' as any,\r\n        skillsToTrain: [],\r\n        minItemPrice: 100,\r\n        minPack: 1,\r\n        maxPack: 10,\r\n        buyFromPlayers: []\r\n      },\r\n      packages: {\r\n        rotateItems: {\r\n          enabled: true,\r\n          selectedItems: [],\r\n          colors: [],\r\n          underworldItems: false,\r\n          itemsCooldownDays: 7,\r\n          collectResourceHourly: true\r\n        },\r\n        sellItems: {\r\n          enabled: true,\r\n          rules: [],\r\n          autoSellOnRenew: false\r\n        },\r\n        pickGold: {\r\n          enabled: true,\r\n          goldToPick: 1000\r\n        },\r\n        operationSpeed: 'normal' as any\r\n      },\r\n      costumes: {\r\n        wearUnderworld: true,\r\n        preventWearUnderworldOnPause: false,\r\n        dontPauseIfUnderworldActive: false,\r\n        underworldCostume: 'dis_pater_normal' as any,\r\n        standardProfile: {\r\n          enabled: true\r\n        },\r\n        turmaProfile: {\r\n          enabled: false\r\n        },\r\n        eventProfile: {\r\n          enabled: false\r\n        }\r\n      }\r\n    } as any;\r\n  }\r\n\r\n  private setupEventListeners(): void {\r\n    // Listen for custom events from content script\r\n    window.addEventListener('ghToggleBot', () => {\r\n      this.toggleBot();\r\n    });\r\n\r\n    window.addEventListener('ghOpenStatistics', () => {\r\n      this.uiManager.showStatistics(this.botStatus.statistics);\r\n    });\r\n\r\n    window.addEventListener('ghSettingsUpdated', (event: any) => {\r\n      this.updateSettings(event.detail);\r\n    });\r\n\r\n    // Listen for page navigation\r\n    window.addEventListener('beforeunload', () => {\r\n      this.cleanup();\r\n    });\r\n\r\n    // Listen for storage changes to sync status across tabs\r\n    window.addEventListener('storage', (event) => {\r\n      if (event.key === 'gh_bot_status') {\r\n        this.syncBotStatus();\r\n      }\r\n    });\r\n  }\r\n\r\n  private async handleLobbyPage(): Promise<void> {\r\n    console.log('Gladiatus Helper Bot: Handling lobby page');\r\n    await this.loginManager.handleLobby();\r\n  }\r\n\r\n  private async handleGamePage(): Promise<void> {\r\n    console.log('Gladiatus Helper Bot: Handling game page');\r\n\r\n    // Load and restore bot status from storage\r\n    await this.loadBotStatus();\r\n\r\n    // Update UI with current status (this will show the persisted state)\r\n    this.uiManager.updateStatus(this.botStatus);\r\n\r\n    // Auto-start only if explicitly enabled in settings, not based on previous state\r\n    if (this.settings?.general?.autoStart) {\r\n      console.log('Auto-start enabled, starting bot...');\r\n      await this.startBot();\r\n    } else {\r\n      console.log('Bot status loaded, current state:', this.botStatus.isActive ? 'Active' : 'Inactive');\r\n    }\r\n  }\r\n\r\n  private async loadBotStatus(): Promise<void> {\r\n    try {\r\n      const storedStatus = await this.storageManager.getBotStatus();\r\n      if (storedStatus) {\r\n        console.log('Loaded bot status from storage:', storedStatus);\r\n        // Preserve the stored status for UI display but reset active state for safety\r\n        this.botStatus = {\r\n          ...this.botStatus,\r\n          ...storedStatus,\r\n          // Keep stored state for UI display but don't auto-resume\r\n          wasActive: storedStatus.isActive, // Remember if it was active\r\n          isActive: false, // Always start inactive for safety\r\n          isPaused: false, // Reset pause state\r\n          currentAction: BotAction.IDLE,\r\n          // Preserve statistics from stored status\r\n          statistics: {\r\n            ...this.botStatus.statistics,\r\n            ...storedStatus.statistics\r\n          }\r\n        };\r\n        console.log('Bot status after merge:', this.botStatus);\r\n      } else {\r\n        console.log('No stored bot status found, using defaults');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load bot status:', error);\r\n    }\r\n  }\r\n\r\n  private async syncBotStatus(): Promise<void> {\r\n    try {\r\n      const storedStatus = await this.storageManager.getBotStatus();\r\n      if (storedStatus) {\r\n        this.botStatus = { ...this.botStatus, ...storedStatus };\r\n        this.uiManager.updateStatus(this.botStatus);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to sync bot status:', error);\r\n    }\r\n  }\r\n\r\n  public async toggleBot(): Promise<void> {\r\n    if (this.botStatus.isActive) {\r\n      await this.stopBot();\r\n    } else {\r\n      await this.startBot();\r\n    }\r\n  }\r\n\r\n  public async startBot(): Promise<void> {\r\n    if (!this.isInitialized || !this.settings) {\r\n      console.error('Bot not initialized or settings not loaded');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('Gladiatus Helper Bot: Starting bot');\r\n\r\n      this.botStatus.isActive = true;\r\n      this.botStatus.isPaused = false;\r\n      this.botStatus.currentAction = BotAction.IDLE;\r\n      this.botStatus.statistics.startTime = new Date();\r\n\r\n      // Save status\r\n      await this.storageManager.saveBotStatus(this.botStatus);\r\n      console.log('Bot status saved to storage:', this.botStatus);\r\n\r\n      // Update UI\r\n      this.uiManager.updateStatus(this.botStatus);\r\n\r\n      // Start bot engine\r\n      await this.botEngine.start(this.settings);\r\n\r\n      console.log('Gladiatus Helper Bot: Bot started successfully');\r\n\r\n    } catch (error) {\r\n      console.error('Gladiatus Helper Bot: Failed to start bot:', error);\r\n      this.botStatus.error = (error as Error).message;\r\n      this.botStatus.isActive = false;\r\n      this.uiManager.updateStatus(this.botStatus);\r\n    }\r\n  }\r\n\r\n  public async stopBot(): Promise<void> {\r\n    try {\r\n      console.log('Gladiatus Helper Bot: Stopping bot');\r\n\r\n      this.botStatus.isActive = false;\r\n      this.botStatus.currentAction = BotAction.IDLE;\r\n\r\n      // Stop bot engine\r\n      await this.botEngine.stop();\r\n\r\n      // Save status\r\n      await this.storageManager.saveBotStatus(this.botStatus);\r\n      console.log('Bot status saved to storage:', this.botStatus);\r\n\r\n      // Update UI\r\n      this.uiManager.updateStatus(this.botStatus);\r\n\r\n      console.log('Gladiatus Helper Bot: Bot stopped successfully');\r\n\r\n    } catch (error) {\r\n      console.error('Gladiatus Helper Bot: Failed to stop bot:', error);\r\n      this.botStatus.error = (error as Error).message;\r\n      this.uiManager.updateStatus(this.botStatus);\r\n    }\r\n  }\r\n\r\n  public async pauseBot(): Promise<void> {\r\n    if (!this.botStatus.isActive) return;\r\n\r\n    this.botStatus.isPaused = true;\r\n    this.botStatus.currentAction = BotAction.IDLE;\r\n\r\n    await this.botEngine.pause();\r\n    await this.storageManager.saveBotStatus(this.botStatus);\r\n    this.uiManager.updateStatus(this.botStatus);\r\n\r\n    console.log('Gladiatus Helper Bot: Bot paused');\r\n  }\r\n\r\n  public async resumeBot(): Promise<void> {\r\n    if (!this.botStatus.isActive || !this.botStatus.isPaused) return;\r\n\r\n    this.botStatus.isPaused = false;\r\n\r\n    await this.botEngine.resume();\r\n    await this.storageManager.saveBotStatus(this.botStatus);\r\n    this.uiManager.updateStatus(this.botStatus);\r\n\r\n    console.log('Gladiatus Helper Bot: Bot resumed');\r\n  }\r\n\r\n  public getBotStatus(): BotStatus {\r\n    return { ...this.botStatus };\r\n  }\r\n\r\n  public async updateSettings(newSettings: Partial<BotSettings>): Promise<void> {\r\n    if (!this.settings) return;\r\n\r\n    // Merge with existing settings\r\n    this.settings = { ...this.settings, ...newSettings };\r\n\r\n    // Save to storage\r\n    await this.storageManager.saveSettings(this.settings);\r\n\r\n    // Update bot engine if running\r\n    if (this.botStatus.isActive) {\r\n      await this.botEngine.updateSettings(this.settings);\r\n    }\r\n\r\n    console.log('Gladiatus Helper Bot: Settings updated');\r\n  }\r\n\r\n  private cleanup(): void {\r\n    if (this.botStatus.isActive) {\r\n      this.botEngine.stop();\r\n    }\r\n  }\r\n}\r\n\r\n// Initialize the bot when script loads\r\nconst gladiatusBot = new GladiatusHelperBot();\r\n\r\n// Make bot available globally for debugging\r\n(window as any).gladiatusBot = gladiatusBot;"], "names": [], "sourceRoot": ""}