// Chrome extension storage management utilities

import { BotSettings, BotStatus, ExtensionStorage } from '../types';
import { DefaultSettingsFactory } from './default-settings';

export class StorageManager {
  private readonly STORAGE_KEYS = {
    SETTINGS: 'gh_settings',
    STATISTICS: 'gh_statistics',
    LICENSE: 'gh_license',
    PAUSE_SCHEDULE: 'gh_pause_schedule',
    QUEST_RULES: 'gh_quest_rules',
    MARKET_RULES: 'gh_market_rules',
    BANK_DATA: 'gh_bank_data',
    BOT_STATUS: 'gh_bot_status'
  } as const;

  /**
   * Check if Chrome storage API is available
   */
  private isChromeStorageAvailable(): boolean {
    return typeof chrome !== 'undefined' &&
           chrome.storage &&
           chrome.storage.local &&
           typeof chrome.storage.local.get === 'function';
  }

  /**
   * Fallback method to get settings from localStorage
   */
  private getSettingsFromLocalStorage(): Partial<BotSettings> | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get settings from localStorage:', error);
      return null;
    }
  }

  /**
   * Fallback method to save settings to localStorage
   */
  private saveSettingsToLocalStorage(settings: Partial<BotSettings>): void {
    try {
      const currentSettings = this.getSettingsFromLocalStorage() || {};
      const updatedSettings = this.deepMerge(currentSettings, settings);
      localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));
    } catch (error) {
      console.error('Failed to save settings to localStorage:', error);
    }
  }

  /**
   * Get bot settings from storage
   */
  async getSettings(): Promise<BotSettings | null> {
    try {
      // Check if Chrome storage API is available
      if (!this.isChromeStorageAvailable()) {
        console.warn('Chrome storage API not available, using localStorage fallback');
        const storedSettings = this.getSettingsFromLocalStorage();
        return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();
      }

      const result = await chrome.storage.local.get(this.STORAGE_KEYS.SETTINGS);
      const storedSettings = result[this.STORAGE_KEYS.SETTINGS];

      if (!storedSettings) {
        // Return default settings if none are stored
        return DefaultSettingsFactory.createDefaultSettings();
      }

      // Merge stored settings with defaults to ensure all properties exist
      return DefaultSettingsFactory.mergeWithDefaults(storedSettings);
    } catch (error) {
      console.error('Failed to get settings:', error);
      // Fallback to localStorage
      const storedSettings = this.getSettingsFromLocalStorage();
      return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();
    }
  }

  /**
   * Save bot settings to storage
   */
  async saveSettings(settings: Partial<BotSettings>): Promise<void> {
    try {
      // Check if Chrome storage API is available
      if (!this.isChromeStorageAvailable()) {
        console.warn('Chrome storage API not available, using localStorage fallback');
        this.saveSettingsToLocalStorage(settings);
        return;
      }

      const currentSettings = await this.getSettings() || {};
      const updatedSettings = this.deepMerge(currentSettings, settings);

      await chrome.storage.local.set({
        [this.STORAGE_KEYS.SETTINGS]: updatedSettings
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
      // Fallback to localStorage
      this.saveSettingsToLocalStorage(settings);
    }
  }

  /**
   * Get bot status from storage
   */
  async getBotStatus(): Promise<BotStatus | null> {
    try {
      if (!this.isChromeStorageAvailable()) {
        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
        return stored ? JSON.parse(stored) : null;
      }

      const result = await chrome.storage.local.get(this.STORAGE_KEYS.BOT_STATUS);
      return result[this.STORAGE_KEYS.BOT_STATUS] || null;
    } catch (error) {
      console.error('Failed to get bot status:', error);
      try {
        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);
        return stored ? JSON.parse(stored) : null;
      } catch {
        return null;
      }
    }
  }

  /**
   * Save bot status to storage
   */
  async saveBotStatus(status: BotStatus): Promise<void> {
    try {
      if (!this.isChromeStorageAvailable()) {
        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
        return;
      }

      await chrome.storage.local.set({
        [this.STORAGE_KEYS.BOT_STATUS]: status
      });
    } catch (error) {
      console.error('Failed to save bot status:', error);
      // Fallback to localStorage
      try {
        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));
      } catch (fallbackError) {
        console.error('Failed to save bot status to localStorage:', fallbackError);
        throw error;
      }
    }
  }

  /**
   * Get all extension data from storage
   */
  async getAllData(): Promise<Partial<ExtensionStorage>> {
    try {
      const keys = Object.values(this.STORAGE_KEYS);
      const result = await chrome.storage.local.get(keys);

      return {
        settings: result[this.STORAGE_KEYS.SETTINGS],
        statistics: result[this.STORAGE_KEYS.STATISTICS],
        licenseInfo: result[this.STORAGE_KEYS.LICENSE],
        pauseSchedule: result[this.STORAGE_KEYS.PAUSE_SCHEDULE],
        questRules: result[this.STORAGE_KEYS.QUEST_RULES],
        marketRules: result[this.STORAGE_KEYS.MARKET_RULES],
        bankData: result[this.STORAGE_KEYS.BANK_DATA]
      };
    } catch (error) {
      console.error('Failed to get all data:', error);
      return {};
    }
  }

  /**
   * Clear all extension data from storage
   */
  async clearAllData(): Promise<void> {
    try {
      const keys = Object.values(this.STORAGE_KEYS);
      await chrome.storage.local.remove(keys);
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw error;
    }
  }

  /**
   * Get storage usage information
   */
  async getStorageUsage(): Promise<{ bytesInUse: number; quota: number }> {
    try {
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;

      return { bytesInUse, quota };
    } catch (error) {
      console.error('Failed to get storage usage:', error);
      return { bytesInUse: 0, quota: 0 };
    }
  }

  /**
   * Deep merge two objects
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  /**
   * Export settings as JSON string
   */
  async exportSettings(): Promise<string> {
    const data = await this.getAllData();
    return JSON.stringify(data, null, 2);
  }

  /**
   * Import settings from JSON string
   */
  async importSettings(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);

      if (data.settings) {
        await this.saveSettings(data.settings);
      }

      // Import other data types as needed
      const keys = Object.values(this.STORAGE_KEYS);
      const importData: Record<string, any> = {};

      for (const key of keys) {
        const dataKey = key.replace('gh_', '');
        if (data[dataKey]) {
          importData[key] = data[dataKey];
        }
      }

      if (Object.keys(importData).length > 0) {
        await chrome.storage.local.set(importData);
      }
    } catch (error) {
      console.error('Failed to import settings:', error);
      throw new Error('Invalid settings data');
    }
  }
}