{"version": 3, "file": "background/index.iife.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;ACVA,gCAAgC;AAsBhC,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,wCAAyB;IACzB,sCAAuB;IACvB,kCAAmB;IACnB,oCAAqB;AACvB,CAAC,EALW,WAAW,KAAX,WAAW,QAKtB;AA2BD,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,uCAAmB;IACnB,yCAAqB;IACrB,mCAAe;IACf,yCAAqB;AACvB,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,QAK3B;;;;;;;;;;;;;;;;;;;AC3DD,6CAA6C;AAmC7C,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,8CAA+B;IAC/B,gDAAiC;IACjC,4CAA6B;IAC7B,8CAA+B;IAC/B,kDAAmC;IACnC,4CAA6B;IAC7B,8BAAe;AACjB,CAAC,EARW,WAAW,KAAX,WAAW,QAQtB;AA2DD,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,0CAAuB;IACvB,8CAA2B;IAC3B,oDAAiC;AACnC,CAAC,EAJW,eAAe,KAAf,eAAe,QAI1B;AAoBD,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,mCAAe;IACf,iCAAa;IACb,uCAAmB;IACnB,yCAAqB;IACrB,qCAAiB;IACjB,qCAAiB;AACnB,CAAC,EAPW,gBAAgB,KAAhB,gBAAgB,QAO3B;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,gCAAgB;IAChB,kCAAkB;IAClB,yCAAyB;IACzB,yCAAyB;AAC3B,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AAED,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,uCAAqB;IACrB,yCAAuB;IACvB,oCAAkB;IAClB,sCAAoB;AACtB,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;;;;;;;;;;;;;;;;;;ACnJD,qCAAqC;AAgBrC,IAAY,SAuBX;AAvBD,WAAY,SAAS;IACnB,0BAAa;IACb,gCAAmB;IACnB,oCAAuB;IACvB,oCAAuB;IACvB,wCAA2B;IAC3B,4CAA+B;IAC/B,sCAAyB;IACzB,4BAAe;IACf,0CAA6B;IAC7B,kDAAqC;IACrC,oDAAuC;IACvC,0CAA6B;IAC7B,8CAAiC;IACjC,gDAAmC;IACnC,gCAAmB;IACnB,kCAAqB;IACrB,oCAAuB;IACvB,gDAAmC;IACnC,kDAAqC;IACrC,kDAAqC;IACrC,gDAAmC;IACnC,gDAAmC;AACrC,CAAC,EAvBW,SAAS,KAAT,SAAS,QAuBpB;AAqBD,IAAY,QAUX;AAVD,WAAY,QAAQ;IAClB,+BAAmB;IACnB,iCAAqB;IACrB,qCAAyB;IACzB,6BAAiB;IACjB,+BAAmB;IACnB,mCAAuB;IACvB,2BAAe;IACf,qCAAyB;IACzB,mDAAuC;AACzC,CAAC,EAVW,QAAQ,KAAR,QAAQ,QAUnB;AAkBD,IAAY,WAIX;AAJD,WAAY,WAAW;IACrB,gCAAiB;IACjB,gCAAiB;IACjB,4BAAa;AACf,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,2CAAuB;IACvB,yCAAqB;AACvB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;;;;;;;;;;;;;;;;;;;AClGD,2CAA2C;AAoD3C,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,+CAAS;IACT,+CAAS;IACT,6CAAQ;IACR,iDAAU;IACV,iDAAU;IACV,2CAAO;AACT,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAED,IAAY,QAkBX;AAlBD,WAAY,QAAQ;IAClB,6BAAiB;IACjB,6BAAiB;IACjB,6BAAiB;IACjB,2BAAe;IACf,6BAAiB;IACjB,2BAAe;IACf,yBAAa;IACb,6BAAiB;IACjB,6BAAiB;IACjB,2CAA+B;IAC/B,+BAAmB;IACnB,6BAAiB;IACjB,mCAAuB;IACvB,2CAA+B;IAC/B,2BAAe;IACf,6BAAiB;IACjB,uCAA2B;AAC7B,CAAC,EAlBW,QAAQ,KAAR,QAAQ,QAkBnB;AAkCD,IAAY,SASX;AATD,WAAY,SAAS;IACnB,4BAAe;IACf,wCAA2B;IAC3B,sCAAyB;IACzB,gCAAmB;IACnB,0BAAa;IACb,oCAAuB;IACvB,8BAAiB;IACjB,oCAAuB;AACzB,CAAC,EATW,SAAS,KAAT,SAAS,QASpB;AAgBD,IAAY,OAOX;AAPD,WAAY,OAAO;IACjB,4BAAiB;IACjB,4BAAiB;IACjB,wBAAa;IACb,8BAAmB;IACnB,0BAAe;IACf,8BAAmB;AACrB,CAAC,EAPW,OAAO,KAAP,OAAO,QAOlB;AASD,IAAY,YAQX;AARD,WAAY,YAAY;IACtB,yCAAyB;IACzB,mCAAmB;IACnB,+BAAe;IACf,+BAAe;IACf,yCAAyB;IACzB,iCAAiB;IACjB,+BAAe;AACjB,CAAC,EARW,YAAY,KAAZ,YAAY,QAQvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClKD,iDAAiD;AAErB;AACA;AACG;AACJ;AACL;AACD;AACE;;;;;;;;;;;;ACRvB,8CAA8C;;;;;;;;;;;;;;;;;;;;;ACA9C,8CAA8C;AAsC9C,IAAY,UAaX;AAbD,WAAY,UAAU;IACpB,mCAAqB;IACrB,2CAA6B;IAC7B,2CAA6B;IAC7B,mDAAqC;IACrC,6CAA+B;IAC/B,6CAA+B;IAC/B,qDAAuC;IACvC,2CAA6B;IAC7B,qCAAuB;IACvB,yCAA2B;IAC3B,qCAAuB;IACvB,qDAAuC;AACzC,CAAC,EAbW,UAAU,KAAV,UAAU,QAarB;AAOD,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,yCAAuB;IACvB,+BAAa;IACb,mCAAiB;IACjB,+BAAa;AACf,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;AAYD,IAAY,iBAgBX;AAhBD,WAAY,iBAAiB;IAC3B,0DAAqC;IACrC,0DAAqC;IACrC,sDAAiC;IACjC,sDAAiC;IACjC,wEAAmD;IACnD,kEAA6C;IAC7C,oEAA+C;IAC/C,8DAAyC;IACzC,kEAA6C;IAC7C,gFAA2D;IAC3D,8CAAyB;IACzB,sEAAiD;IACjD,8DAAyC;IACzC,8EAAyD;IACzD,oDAA+B;AACjC,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,QAgB5B;AA6DD,IAAY,qBAGX;AAHD,WAAY,qBAAqB;IAC/B,gDAAuB;IACvB,gEAAuC;AACzC,CAAC,EAHW,qBAAqB,KAArB,qBAAqB,QAGhC;AAmDD,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,2CAAyB;IACzB,uCAAqB;IACrB,mCAAiB;AACnB,CAAC,EAJW,cAAc,KAAd,cAAc,QAIzB;AAED,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,yCAAiB;IACjB,yCAAiB;IACjB,qCAAa;AACf,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,QAI/B;;;;;;;;;;;;;;;;;;;ACxND,yBAAyB;AAYzB,IAAY,gBAQX;AARD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,uCAAmB;IACnB,uCAAmB;IACnB,mCAAe;IACf,+CAA2B;IAC3B,6CAAyB;IACzB,+CAA2B;AAC7B,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,QAQ3B;AAiCD,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,4BAAe;IACf,8BAAiB;IACjB,4BAAe;IACf,wCAA2B;AAC7B,CAAC,EALW,SAAS,KAAT,SAAS,QAKpB;AAED,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,kCAAmB;IACnB,sCAAuB;IACvB,kCAAmB;IACnB,gCAAiB;IACjB,kCAAmB;IACnB,4BAAa;AACf,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAcD,IAAY,SAWX;AAXD,WAAY,SAAS;IACnB,0BAAa;IACb,8BAAiB;IACjB,8BAAiB;IACjB,kCAAqB;IACrB,4BAAe;IACf,kCAAqB;IACrB,4BAAe;IACf,4BAAe;IACf,0BAAa;IACb,0BAAa;AACf,CAAC,EAXW,SAAS,KAAT,SAAS,QAWpB;AAeD,IAAY,cASX;AATD,WAAY,cAAc;IACxB,uCAAqB;IACrB,6BAAW;IACX,6BAAW;IACX,2CAAyB;IACzB,2CAAyB;IACzB,qCAAmB;IACnB,iCAAe;IACf,6BAAW;AACb,CAAC,EATW,cAAc,KAAd,cAAc,QASzB;;;;;;;;;;;;;;;;ACpHD,0EAA0E;AAaxD;AAEX,MAAM,sBAAsB;IACjC,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,CAAC;gBACb,gBAAgB,EAAE,IAAI;gBACtB,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;gBACjB,wBAAwB,EAAE,IAAI;gBAC9B,cAAc,EAAE,CAAC;aAClB;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACvB,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,mDAAe,CAAC,SAAS;gBACpC,eAAe,EAAE,IAAI;aACtB;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,oDAAgB,CAAC,KAAK;oBAChC,QAAQ,EAAE,EAAE;iBACb;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,oDAAgB,CAAC,IAAI;oBAC/B,QAAQ,EAAE,EAAE;iBACb;gBACD,aAAa,EAAE,GAAG;gBAClB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,kDAAc,CAAC,YAAY;gBACrC,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,IAAI;gBACrB,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,oDAAgB,CAAC,KAAK;gBAC9B,QAAQ,EAAE,gDAAY,CAAC,SAAS;gBAChC,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,GAAG;gBACjB,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,EAAE;gBACX,cAAc,EAAE,EAAE;aACnB;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE;oBACX,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,EAAE;oBACjB,MAAM,EAAE,EAAE;oBACV,eAAe,EAAE,KAAK;oBACtB,iBAAiB,EAAE,CAAC;oBACpB,qBAAqB,EAAE,KAAK;iBAC7B;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,EAAE;oBACT,eAAe,EAAE,KAAK;iBACvB;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,CAAC;iBACd;gBACD,cAAc,EAAE,kDAAc,CAAC,MAAM;aACtC;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,KAAK;gBACrB,4BAA4B,EAAE,KAAK;gBACnC,2BAA2B,EAAE,KAAK;gBAClC,iBAAiB,EAAE,kBAAyB;gBAC5C,eAAe,EAAE;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;gBACD,YAAY,EAAE;oBACZ,OAAO,EAAE,KAAK;iBACf;aACF;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,KAAK;gBACpB,gBAAgB,EAAE,KAAK;gBACvB,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,EAAE;gBACrB,aAAa,EAAE,EAAE;gBACjB,gBAAgB,EAAE,IAAI;aACvB;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,IAAI;gBACd,gBAAgB,EAAE,EAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,CAAC;gBACZ,mBAAmB,EAAE,KAAK;gBAC1B,eAAe,EAAE,KAAK;gBACtB,sBAAsB,EAAE,IAAI;gBAC5B,WAAW,EAAE,KAAK;gBAClB,eAAe,EAAE,CAAC;gBAClB,cAAc,EAAE,KAAK;gBACrB,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,KAAK;gBACtB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,KAAK;gBACpB,mBAAmB,EAAE,KAAK;gBAC1B,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,EAAE;gBACjB,iBAAiB,EAAE,EAAE;gBACrB,aAAa,EAAE,EAAE;gBACjB,gBAAgB,EAAE,IAAI;gBACtB,iBAAiB,EAAE,KAAK;aACzB;YACD,WAAW,EAAE;gBACX,OAAO,EAAE,KAAK;gBACd,gBAAgB,EAAE,CAAC;gBACnB,QAAQ,EAAE,EAAE;gBACZ,GAAG,EAAE,EAAE;gBACP,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,yDAAqB,CAAC,SAAS;gBAC/C,aAAa,EAAE,EAAE;gBACjB,YAAY,EAAE,CAAC;aAChB;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,CAAC;aACpB;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,EAAE;gBACP,kBAAkB,EAAE,IAAI;gBACxB,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,KAAK;aAChB;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;gBACV,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE,EAAE;gBAClB,IAAI,EAAE,kDAAc,CAAC,MAAM;gBAC3B,UAAU,EAAE,wDAAoB,CAAC,MAAM;gBACvC,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,KAAK;gBACpB,iBAAiB,EAAE,KAAK;gBACxB,SAAS,EAAE,KAAK;gBAChB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;gBACtB,YAAY,EAAE,IAAI;gBAClB,mBAAmB,EAAE,KAAK;gBAC1B,aAAa,EAAE,KAAK;gBACpB,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,EAAE;aACd;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,KAAK;gBACnB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,CAAC,+CAAW,CAAC,KAAK,EAAE,+CAAW,CAAC,KAAK,CAAC;gBACrD,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,EAAE;gBACb,aAAa,EAAE,EAAE;gBACjB,kBAAkB,EAAE,CAAC,+CAAW,CAAC,KAAK,CAAC;aACxC;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,GAAG;gBACnB,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,EAAE;aACf;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,EAAE;aACV;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,GAAG;gBAClB,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,KAAK;oBACnB,aAAa,EAAE,CAAC,+CAAW,CAAC,IAAI,EAAE,+CAAW,CAAC,MAAM,CAAC;oBACrD,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,GAAG;oBACb,SAAS,EAAE,EAAE;oBACb,kBAAkB,EAAE,EAAE;iBACvB;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,EAAE;iBACV;gBACD,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,KAAK;oBAChB,YAAY,EAAE,GAAG;oBACjB,gBAAgB,EAAE,GAAG;oBACrB,aAAa,EAAE,CAAC,+CAAW,CAAC,MAAM,EAAE,+CAAW,CAAC,MAAM,CAAC;iBACxD;aACF;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;aACjB;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE;oBACd,gBAAgB,EAAE,IAAI;oBACtB,cAAc,EAAE,KAAK;oBACrB,QAAQ,EAAE,IAAI;iBACf;aACF;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACf;SACF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,YAAkC;QACzD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,MAAW,EAAE,MAAW;QAC/C,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3F,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;;;;;;;;;;;;;;;ACzSD,iDAAiD;AAI1C,MAAM,kBAAkB;IAA/B;QACU,aAAQ,GAAY,KAAK,CAAC;QAC1B,YAAO,GAAqB;YAClC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IA8GJ,CAAC;IA5GC;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,mDAAmD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW;QACjB,OAAO,kCAAkC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW,EAAE,QAAiB,KAAK;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QAEF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,0BAA0B;YAC1D,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5C,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAuC,EAAE,OAAe,WAAW;QAC1E,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAuC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;CACF;;;;;;;;;;;;;;;;ACvHD,gDAAgD;AAGY;AAErD,MAAM,cAAc;IAA3B;QACmB,iBAAY,GAAG;YAC9B,QAAQ,EAAE,aAAa;YACvB,UAAU,EAAE,eAAe;YAC3B,OAAO,EAAE,YAAY;YACrB,cAAc,EAAE,mBAAmB;YACnC,WAAW,EAAE,gBAAgB;YAC7B,YAAY,EAAE,iBAAiB;YAC/B,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE,eAAe;SACnB,CAAC;IAuPb,CAAC;IArPC;;OAEG;IACK,wBAAwB;QAC9B,OAAO,OAAO,MAAM,KAAK,WAAW;YAC7B,MAAM,CAAC,OAAO;YACd,MAAM,CAAC,OAAO,CAAC,KAAK;YACpB,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,UAAU,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAChE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,QAA8B;QAC/D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,IAAI,EAAE,CAAC;YACjE,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAClE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC9E,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC1D,OAAO,cAAc,CAAC,CAAC,CAAC,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;YACpI,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC1E,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE1D,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,6CAA6C;gBAC7C,OAAO,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;YACxD,CAAC;YAED,qEAAqE;YACrE,OAAO,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,2BAA2B;YAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC1D,OAAO,cAAc,CAAC,CAAC,CAAC,qEAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,qEAAsB,CAAC,qBAAqB,EAAE,CAAC;QACpI,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAA8B;QAC/C,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC9E,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAElE,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBAC7B,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,eAAe;aAC9C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,2BAA2B;YAC3B,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC5E,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5C,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAiB;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACrC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBAC7B,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,MAAM;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,2BAA2B;YAC3B,IAAI,CAAC;gBACH,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7E,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,aAAa,CAAC,CAAC;gBAC3E,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEpD,OAAO;gBACL,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC5C,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAChD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBAC9C,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;gBACvD,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;gBACjD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;gBACnD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;YAE/C,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,MAAW,EAAE,MAAW;QACxC,MAAM,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAClF,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;YAED,oCAAoC;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAwB,EAAE,CAAC;YAE3C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACvC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClB,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvC,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;CACF;;;;;;;UCtQD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;ACNA,qDAAqD;AAEI;AACU;AACT;AAE1D,MAAM,iBAAiB;IAIrB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,2EAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,kEAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,gCAAgC;QAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAClC,CAAC,OAAyB,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;YAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC,CAAC,+CAA+C;QAC9D,CAAC,CACF,CAAC;QAEF,mDAAmD;QACnD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE;YAC3D,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;gBAChD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,EAAE;YACjD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAyB,EACzB,OAAqC,EACrC,YAAsC;QAEtC,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,+CAAW,CAAC,aAAa;oBAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBACrD,MAAM;gBAER,KAAK,+CAAW,CAAC,eAAe;oBAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBACvD,MAAM;gBAER,KAAK,+CAAW,CAAC,aAAa;oBAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBACrD,MAAM;gBAER;oBACE,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACpD,YAAY,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,YAAY,CAAC,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,OAAyB,EACzB,YAAsC;QAEtC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE9C,YAAY,CAAC;gBACX,WAAW;gBACX,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,OAAyB,EACzB,YAAsC;QAEtC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrD,YAAY,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,QAA0B,EAC1B,YAAsC;QAEtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACxD,YAAY,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,GAAW;QACtD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;YACpC,iDAAiD;YACjD,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC;oBACnC,MAAM,EAAE,EAAE,KAAK,EAAE;oBACjB,KAAK,EAAE,CAAC,0BAA0B,CAAC;iBACpC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;IAEO,eAAe,CAAC,OAAwC;QAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QACzD,OAAO,QAAQ,EAAE,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QACzD,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,0BAA0B;IAChF,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE;gBACP,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,CAAC;gBACb,wBAAwB,EAAE,IAAI;gBAC9B,WAAW,EAAE,IAAI;gBACjB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,IAAI;gBACnB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;aACvB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,eAAwB;QACjD,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,wBAAwB,eAAe,OAAO,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;IACpG,CAAC;CACF;AAED,oCAAoC;AACpC,IAAI,iBAAiB,EAAE,CAAC", "sources": ["webpack://gladiatus-helper-bot-ts/webpack/universalModuleDefinition", "webpack://gladiatus-helper-bot-ts/./src/types/api.ts", "webpack://gladiatus-helper-bot-ts/./src/types/extension.ts", "webpack://gladiatus-helper-bot-ts/./src/types/game.ts", "webpack://gladiatus-helper-bot-ts/./src/types/gladiatus.ts", "webpack://gladiatus-helper-bot-ts/./src/types/index.ts", "webpack://gladiatus-helper-bot-ts/./src/types/localization.ts", "webpack://gladiatus-helper-bot-ts/./src/types/settings.ts", "webpack://gladiatus-helper-bot-ts/./src/types/ui.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/default-settings.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/gladiator-url-helper.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/storage-manager.ts", "webpack://gladiatus-helper-bot-ts/webpack/bootstrap", "webpack://gladiatus-helper-bot-ts/webpack/runtime/define property getters", "webpack://gladiatus-helper-bot-ts/webpack/runtime/hasOwnProperty shorthand", "webpack://gladiatus-helper-bot-ts/webpack/runtime/make namespace object", "webpack://gladiatus-helper-bot-ts/./src/background/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "// API and network request types\r\n\r\nexport interface ApiResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport interface LoginApiResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface LicenseInfo {\r\n  isActive: boolean;\r\n  expiresAt?: Date;\r\n  daysRemaining?: number;\r\n  type: LicenseType;\r\n  key?: string;\r\n}\r\n\r\nexport enum LicenseType {\r\n  FREE_TRIAL = 'free_trial',\r\n  ESSENTIAL = 'essential',\r\n  PREMIUM = 'premium',\r\n  INACTIVE = 'inactive'\r\n}\r\n\r\nexport interface BankData {\r\n  bankId?: string;\r\n  isBank: boolean;\r\n  clients: BankClient[];\r\n  requests: BankRequest[];\r\n  selectedBackpack: number;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankClient {\r\n  id: string;\r\n  name: string;\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankRequest {\r\n  clientId: string;\r\n  goldAmount: number;\r\n  createdAt: Date;\r\n  state: BankRequestState;\r\n}\r\n\r\nexport enum BankRequestState {\r\n  PENDING = 'pending',\r\n  PROGRESS = 'progress',\r\n  READY = 'ready',\r\n  COMPLETE = 'complete'\r\n}\r\n\r\nexport interface QuestRule {\r\n  id: string;\r\n  position: number;\r\n  type: string;\r\n  conditions: QuestCondition[];\r\n  pickIfInactive: boolean;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface QuestCondition {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport interface MarketRule {\r\n  id: string;\r\n  name: string;\r\n  itemTypes: string[];\r\n  colors: string[];\r\n  maxPrice: number;\r\n  autoBuy: boolean;\r\n  allowBounded: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface PauseSchedule {\r\n  id: string;\r\n  startTime: string;\r\n  endTime: string;\r\n  workOnPause: boolean;\r\n  randomStartMinutes: number;\r\n  randomEndMinutes: number;\r\n  omitFromUW: boolean;\r\n  forcePause: boolean;\r\n  donateGold: boolean;\r\n  enabled: boolean;\r\n}", "// Chrome extension specific type definitions\r\n\r\nimport {\r\n  CostumeSettings,\r\n  TurmaSettings,\r\n  ArenaSettings,\r\n  ExpeditionSettings,\r\n  DungeonSettings,\r\n  EventSettings,\r\n  UnderworldSettings,\r\n  PackageSettings,\r\n  ForgeAutomationSettings,\r\n  RepairAutomationSettings,\r\n  QuestManagementSettings,\r\n  MarketOperationsSettings\r\n} from './settings';\r\nimport {\r\n  ShopSettings,\r\n  BankSettings,\r\n  GuildSettings\r\n} from './game';\r\nimport { QuestRule, MarketRule, PauseSchedule, BankData, LicenseInfo } from './api';\r\n\r\nexport interface ExtensionMessage {\r\n  type: MessageType;\r\n  data?: any;\r\n  isLobby?: boolean;\r\n  shouldLogin?: boolean;\r\n  loginDelay?: number;\r\n  loginFailed?: boolean;\r\n  server?: string;\r\n  playerId?: string;\r\n  language?: string;\r\n}\r\n\r\nexport enum MessageType {\r\n  LOGIN_REQUEST = 'LOGIN_REQUEST',\r\n  LOGIN_RESPONSE = 'LOGIN_RESPONSE',\r\n  LOGIN_FAILED = 'LOGIN_FAILED',\r\n  SCRIPT_STATUS = 'SCRIPT_STATUS',\r\n  SETTINGS_UPDATE = 'SETTINGS_UPDATE',\r\n  NOTIFICATION = 'NOTIFICATION',\r\n  ERROR = 'ERROR'\r\n}\r\n\r\nexport interface ExtensionStorage {\r\n  settings: BotSettings;\r\n  statistics: BotStatistics;\r\n  licenseInfo: LicenseInfo;\r\n  pauseSchedule: PauseSchedule[];\r\n  questRules: QuestRule[];\r\n  marketRules: MarketRule[];\r\n  bankData: BankData;\r\n}\r\n\r\nexport interface BotSettings {\r\n  general: GeneralSettings;\r\n  heal: HealSettings;\r\n  hideGold: HideGoldSettings;\r\n  packages: PackageSettings;\r\n  costumes: CostumeSettings;\r\n  turma: TurmaSettings;\r\n  arena: ArenaSettings;\r\n  expeditions: ExpeditionSettings;\r\n  dungeon: DungeonSettings;\r\n  event: EventSettings;\r\n  underworld: UnderworldSettings;\r\n  forge: ForgeAutomationSettings;\r\n  repair: RepairAutomationSettings;\r\n  quest: QuestManagementSettings;\r\n  market: MarketOperationsSettings;\r\n  shop: ShopSettings;\r\n  bank: BankSettings;\r\n  guild: GuildSettings;\r\n}\r\n\r\nexport interface GeneralSettings {\r\n  selectedBackpack: number;\r\n  maxRandomDelay: number;\r\n  loginDelay: number;\r\n  highlightUnderworldItems: boolean;\r\n  showShopBox: boolean;\r\n  shopMinQuality: number;\r\n  showBiddenBox: boolean;\r\n  autoCollectGodOils: boolean;\r\n  autoCollectDaily: boolean;\r\n  autoLogin?: boolean;\r\n  autoStart?: boolean;\r\n}\r\n\r\nexport interface HealSettings {\r\n  enabled: boolean;\r\n  minHealth: number;\r\n  healCervisia: boolean;\r\n  healEggs: boolean;\r\n  buyFoodNeeded: boolean;\r\n  selectedEggs: number[];\r\n  renewShop: RenewShopOption;\r\n  autoStopAttacks: boolean;\r\n  buyFood: boolean;\r\n}\r\n\r\nexport enum RenewShopOption {\r\n  NOT_ALLOW = 'not_allow',\r\n  CLOTHE_ONLY = 'clothe_only',\r\n  RUBY_OR_CLOTHE = 'ruby_or_clothe'\r\n}\r\n\r\nexport interface HideGoldSettings {\r\n  primary: HideGoldFlow;\r\n  backup: HideGoldFlow;\r\n  minGoldBackup: number;\r\n  soulBoundTo: string;\r\n  filterBy: PackFilterType;\r\n  allowMultiple: boolean;\r\n  recheckDuration: number;\r\n  pocketMoney: number;\r\n  hideIn: HideGoldLocation;\r\n  duration: PackDuration;\r\n  skillsToTrain: string[];\r\n  minItemPrice: number;\r\n  minPack: number;\r\n  maxPack: number;\r\n  buyFromPlayers: string[];\r\n}\r\n\r\nexport enum HideGoldLocation {\r\n  GUILD = 'guild',\r\n  SHOP = 'shop',\r\n  AUCTION = 'auction',\r\n  TRAINING = 'training',\r\n  MARKET = 'market',\r\n  DONATE = 'donate'\r\n}\r\n\r\nexport enum PackDuration {\r\n  TWO_HOURS = '2h',\r\n  EIGHT_HOURS = '8h',\r\n  TWENTY_FOUR_HOURS = '24h',\r\n  FORTY_EIGHT_HOURS = '48h'\r\n}\r\n\r\nexport enum PackFilterType {\r\n  LOWEST_DURATION = 'd',\r\n  HIGHEST_DURATION = 'dd',\r\n  LOWEST_PRICE = 'p',\r\n  HIGHEST_PRICE = 'pd'\r\n}\r\n\r\nexport interface HideGoldFlow {\r\n  enabled: boolean;\r\n  location: HideGoldLocation;\r\n  settings: any;\r\n}\r\n\r\nexport interface BotStatistics {\r\n  arena: ArenaStats;\r\n  quest: QuestStats;\r\n  expedition: ExpeditionStats;\r\n}\r\n\r\nexport interface ArenaStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  lastHourStats: HourlyStats;\r\n  weekStats: WeeklyStats;\r\n  startedFrom: Date;\r\n}\r\n\r\nexport interface HourlyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n}\r\n\r\nexport interface WeeklyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  totalGold: number;\r\n}\r\n\r\nexport interface QuestStats {\r\n  lastHourTotal: QuestHourlyStats;\r\n  weekStats: QuestWeeklyStats;\r\n}\r\n\r\nexport interface QuestHourlyStats {\r\n  quests: number;\r\n  experience: number;\r\n  honor: number;\r\n  food: number;\r\n  items: number;\r\n  goldEarned: number;\r\n  godRewards: number;\r\n}\r\n\r\nexport interface QuestWeeklyStats extends QuestHourlyStats {\r\n  byQuestType: Record<string, QuestHourlyStats>;\r\n}\r\n\r\nexport interface ExpeditionStats {\r\n  wins: number;\r\n  losses: number;\r\n  items: number;\r\n  fame: number;\r\n}", "// Game mechanics and bot logic types\r\n\r\nimport { ItemQuality } from './gladiatus';\r\nimport { QuestRule, MarketRule } from './api';\r\nimport { FilterCondition } from './settings';\r\n\r\nexport interface BotStatus {\r\n  isActive: boolean;\r\n  isPaused: boolean;\r\n  wasActive?: boolean; // Tracks if bot was active before page reload\r\n  currentAction: BotAction;\r\n  nextActionTime?: Date;\r\n  error?: string;\r\n  statistics: GameBotStatistics;\r\n}\r\n\r\nexport enum BotAction {\r\n  IDLE = 'idle',\r\n  HEALING = 'healing',\r\n  ATTACKING = 'attacking',\r\n  TRAVELING = 'traveling',\r\n  BUYING_PACK = 'buying_pack',\r\n  PICKING_ITEMS = 'picking_items',\r\n  UNDERWORLD = 'underworld',\r\n  EVENT = 'event',\r\n  SELLING_PACK = 'selling_pack',\r\n  QUEST_MANAGEMENT = 'quest_management',\r\n  MARKET_OPERATIONS = 'market_operations',\r\n  FORGE_REPAIR = 'forge_repair',\r\n  ROTATING_ITEMS = 'rotating_items',\r\n  CHECKING_QUESTS = 'checking_quests',\r\n  FORGING = 'forging',\r\n  SMELTING = 'smelting',\r\n  REPAIRING = 'repairing',\r\n  CHECKING_MARKET = 'checking_market',\r\n  CHECKING_AUCTION = 'checking_auction',\r\n  ACTIVATING_PACTS = 'activating_pacts',\r\n  PROCESSING_PACK = 'processing_pack',\r\n  REQUESTING_PACK = 'requesting_pack'\r\n}\r\n\r\nexport interface GameBotStatistics {\r\n  startTime: Date;\r\n  totalRunTime: number;\r\n  actionsPerformed: number;\r\n  errorsEncountered: number;\r\n  goldEarned: number;\r\n  experienceGained: number;\r\n  itemsFound: number;\r\n  combatsWon: number;\r\n  combatsLost: number;\r\n}\r\n\r\nexport interface WorkSettings {\r\n  enabled: boolean;\r\n  workIfNoPoints: boolean;\r\n  selectedWork: WorkType;\r\n  duration: number;\r\n}\r\n\r\nexport enum WorkType {\r\n  SENATOR = 'senator',\r\n  JEWELLER = 'jeweller',\r\n  STABLE_BOY = 'stable_boy',\r\n  FARMER = 'farmer',\r\n  BUTCHER = 'butcher',\r\n  FISHERMAN = 'fisherman',\r\n  BAKER = 'baker',\r\n  BLACKSMITH = 'blacksmith',\r\n  MASTER_BLACKSMITH = 'master_blacksmith'\r\n}\r\n\r\nexport interface ForgeSettings {\r\n  enabled: boolean;\r\n  acceptedColor: ItemQuality[];\r\n  prefix: string;\r\n  item: string;\r\n  suffix: string;\r\n  resourceQuality: ItemQuality;\r\n  stopSuccess: number;\r\n  toolsQuality: ToolQuality;\r\n  stopResources: boolean;\r\n  stopTools: boolean;\r\n  slots: number;\r\n  sendTo: ForgeDestination;\r\n  useHammer: boolean;\r\n}\r\n\r\nexport enum ToolQuality {\r\n  BRONZE = 'bronze',\r\n  SILVER = 'silver',\r\n  GOLD = 'gold'\r\n}\r\n\r\nexport enum ForgeDestination {\r\n  NONE = 'none',\r\n  INVENTORY = 'inventory',\r\n  PACKAGES = 'packages'\r\n}\r\n\r\nexport interface RepairSettings {\r\n  standardProfile: RepairProfile;\r\n  turmaProfile: RepairProfile;\r\n}\r\n\r\nexport interface RepairProfile {\r\n  enabled: boolean;\r\n  minConditioning: number;\r\n  maxQuality: ItemQuality;\r\n  ignoreMaterials: boolean;\r\n}\r\n\r\nexport interface QuestSettings {\r\n  enabled: boolean;\r\n  rules: QuestRule[];\r\n  underworldRules: QuestRule[];\r\n}\r\n\r\nexport interface MarketSettings {\r\n  enabled: boolean;\r\n  sellConfig: MarketSellConfig;\r\n  buyConfig: MarketBuyConfig;\r\n}\r\n\r\nexport interface MarketSellConfig {\r\n  enabled: boolean;\r\n  stackSize: number;\r\n  pricePerUnit: number;\r\n  maxListedItems: number;\r\n  goldRatio: number;\r\n  pickVAT: boolean;\r\n  forgingGoods: string[];\r\n}\r\n\r\nexport interface MarketBuyConfig {\r\n  enabled: boolean;\r\n  rules: MarketRule[];\r\n  autoBuy: boolean;\r\n}\r\n\r\nexport interface ShopSettings {\r\n  enabled: boolean;\r\n  searchRules: ShopRule[];\r\n  maxSearches: number;\r\n  usedSearches: number;\r\n  allowRuby: boolean;\r\n  autoStart: boolean;\r\n}\r\n\r\nexport interface ShopRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  autoBuy: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface BankSettings {\r\n  enabled: boolean;\r\n  isBank: boolean;\r\n  bankId?: string;\r\n  selectedBackpack: number;\r\n  clientSettings: BankClientSettings;\r\n}\r\n\r\nexport interface BankClientSettings {\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n}\r\n\r\nexport interface GuildSettings {\r\n  enabled: boolean;\r\n  attackList: string[];\r\n  ignoreList: string[];\r\n}", "// Gladiatus game-specific type definitions\r\n\r\nexport interface GladiatorServer {\r\n  number: string;\r\n  language: string;\r\n}\r\n\r\nexport interface GladiatorAccount {\r\n  id: string;\r\n  server: GladiatorServer;\r\n  playerId: string;\r\n  lastLogin: string;\r\n}\r\n\r\nexport interface GladiatorQueries {\r\n  sh?: string;\r\n  [key: string]: string | undefined;\r\n}\r\n\r\nexport interface GladiatorUrlInfo {\r\n  server?: string;\r\n  country?: string;\r\n  domain?: string;\r\n  queries: GladiatorQueries;\r\n  resolved: boolean;\r\n}\r\n\r\nexport interface LoginData {\r\n  server: GladiatorServer;\r\n  id: string;\r\n  clickedButton: string;\r\n  blackbox: string;\r\n}\r\n\r\nexport interface LoginResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n// Game item types\r\nexport interface GameItem {\r\n  id: string;\r\n  name: string;\r\n  quality: ItemQuality;\r\n  level: number;\r\n  type: ItemType;\r\n  price?: number;\r\n  isUnderworld?: boolean;\r\n  isSoulbound?: boolean;\r\n  conditioning?: number;\r\n}\r\n\r\nexport enum ItemQuality {\r\n  WHITE = 0,\r\n  GREEN = 1,\r\n  BLUE = 2,\r\n  PURPLE = 3,\r\n  ORANGE = 4,\r\n  RED = 5\r\n}\r\n\r\nexport enum ItemType {\r\n  WEAPON = 'weapon',\r\n  SHIELD = 'shield',\r\n  HELMET = 'helmet',\r\n  CHEST = 'chest',\r\n  GLOVES = 'gloves',\r\n  SHOES = 'shoes',\r\n  RING = 'ring',\r\n  AMULET = 'amulet',\r\n  USABLE = 'usable',\r\n  REINFORCEMENT = 'reinforcement',\r\n  UPGRADE = 'upgrade',\r\n  RECIPE = 'recipe',\r\n  MERCENARY = 'mercenary',\r\n  FORGING_GOODS = 'forging_goods',\r\n  TOOLS = 'tools',\r\n  SCROLL = 'scroll',\r\n  EVENT_ITEMS = 'event_items'\r\n}\r\n\r\n// Combat and character types\r\nexport interface Character {\r\n  id: string;\r\n  name: string;\r\n  level: number;\r\n  health: number;\r\n  maxHealth: number;\r\n  gold: number;\r\n  experience: number;\r\n  honor: number;\r\n}\r\n\r\nexport interface CombatResult {\r\n  won: boolean;\r\n  experience: number;\r\n  gold: number;\r\n  items: GameItem[];\r\n  honor?: number;\r\n}\r\n\r\n// Quest types\r\nexport interface Quest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  type: QuestType;\r\n  reward: QuestReward;\r\n  requirements: QuestRequirement[];\r\n  hasTimer: boolean;\r\n  attacksLeft?: number;\r\n}\r\n\r\nexport enum QuestType {\r\n  ARENA = 'arena',\r\n  GROUP_ARENA = 'group_arena',\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  WORK = 'work',\r\n  FIND_ITEM = 'find_item',\r\n  COMBAT = 'combat',\r\n  ANY_QUEST = 'any_quest'\r\n}\r\n\r\nexport interface QuestReward {\r\n  experience?: number;\r\n  gold?: number;\r\n  honor?: number;\r\n  items?: GameItem[];\r\n  isFood?: boolean;\r\n  gods?: GodType[];\r\n}\r\n\r\nexport interface QuestRequirement {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport enum GodType {\r\n  APOLLO = 'apollo',\r\n  VULCAN = 'vulcan',\r\n  MARS = 'mars',\r\n  MERCURY = 'mercury',\r\n  DIANA = 'diana',\r\n  MINERVA = 'minerva'\r\n}\r\n\r\n// Location and travel types\r\nexport interface Location {\r\n  id: string;\r\n  name: string;\r\n  type: LocationType;\r\n}\r\n\r\nexport enum LocationType {\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  ARENA = 'arena',\r\n  TURMA = 'turma',\r\n  UNDERWORLD = 'underworld',\r\n  MARKET = 'market',\r\n  GUILD = 'guild'\r\n}", "// Main type definitions for Gladiatus Helper Bot\r\n\r\nexport * from './gladiatus';\r\nexport * from './extension';\r\nexport * from './localization';\r\nexport * from './settings';\r\nexport * from './api';\r\nexport * from './ui';\r\nexport * from './game';\r\n\r\n// Re-export commonly used types\r\nexport type { BotStatus } from './game';", "// Localization and internationalization types\r\n\r\nexport interface LocalizationData {\r\n  [key: string]: string | LocalizationData;\r\n}\r\n\r\nexport interface TranslationKeys {\r\n  EXP_STATS: {\r\n    WIN: string;\r\n    LOSE: string;\r\n    ITEMS: string;\r\n    FAME: string;\r\n  };\r\n  QUEST_STATS: {\r\n    LAST_HOUR_TOTAL: string;\r\n    WEEK_STATS: string;\r\n    QUESTS: string;\r\n    AVG: string;\r\n    EXP: string;\r\n    HONOR: string;\r\n    FOOD: string;\r\n    ITEMS: string;\r\n    WITH_TIMER: string;\r\n    GOLD_EARNED: string;\r\n    GOD_REWARDS: string;\r\n    BY_QUEST_TYPE: string;\r\n  };\r\n  TITLE: {\r\n    GENERAL: string;\r\n    HEAL: string;\r\n    HIDE_GOLD: string;\r\n    PACKAGES: string;\r\n    COSTUMES: string;\r\n    TURMA: string;\r\n    ARENA: string;\r\n    EXPEDITIONS: string;\r\n    DUNGEON: string;\r\n    EVENT: string;\r\n    UNDERWORLD: string;\r\n    FORGE: string;\r\n    REPAIR: string;\r\n    QUEST: string;\r\n    MARKET: string;\r\n    SHOP: string;\r\n    BANK: string;\r\n    GUILD: string;\r\n  };\r\n  STATUS: {\r\n    FORGING: string;\r\n    WAIT: string;\r\n    INACTIVE: string;\r\n    PAUSED: string;\r\n    PICK_ITEMS: string;\r\n    SELL_ITEMS: string;\r\n    ATTACKING: string;\r\n    BUY_PACK: string;\r\n    CHECK_QUESTS: string;\r\n    SELL_PACK: string;\r\n    BUY_FOOD: string;\r\n    TRAVELING: string;\r\n    ROTATE_ITEMS: string;\r\n    HEALING: string;\r\n    BUFFING: string;\r\n    REPAIRING: string;\r\n    SMELTING: string;\r\n    EQUIP_COSTUMES: string;\r\n    CHECK_AUCTION: string;\r\n    CHECK_SMELTING: string;\r\n    CHECK_MARKET: string;\r\n    SELL_MARKET: string;\r\n    ACTIVATE_PACTS: string;\r\n    CHECKING_SHOP: string;\r\n    DICE: string;\r\n    PROCESSING_PACK: string;\r\n    REQUEST_PACK: string;\r\n  };\r\n  BUTTONS: {\r\n    CANCEL: string;\r\n    SAVE: string;\r\n    ADD_NEW_RULE: string;\r\n    DELETE: string;\r\n    EDIT: string;\r\n    ACTIVE: string;\r\n    ACTIVATE: string;\r\n    BUY_LICENCE: string;\r\n    INACTIVE: string;\r\n    CLOSE: string;\r\n    CLEAR_ALL: string;\r\n    CLEAR: string;\r\n    CONFIRM: string;\r\n  };\r\n}\r\n\r\nexport type SupportedLanguage = 'en' | 'es' | 'de' | 'br' | 'pl' | 'ro' | 'fr' | 'pt' | 'tr' | 'cz' | 'pe' | 'ru' | 'hu' | 'ar' | 'bg' | 'nl';\r\n\r\nexport interface LanguageInfo {\r\n  code: SupportedLanguage;\r\n  name: string;\r\n  nativeName: string;\r\n}", "// Settings and configuration type definitions\r\n\r\nimport { ItemType, ItemQuality } from './gladiatus';\r\n\r\nexport interface PackageSettings {\r\n  rotateItems: RotateItemsSettings;\r\n  sellItems: SellItemsSettings;\r\n  pickGold: PickGoldSettings;\r\n  operationSpeed: OperationSpeed;\r\n}\r\n\r\nexport interface RotateItemsSettings {\r\n  enabled: boolean;\r\n  selectedItems: ItemType[];\r\n  colors: ItemQuality[];\r\n  underworldItems: boolean;\r\n  itemsCooldownDays: number;\r\n  collectResourceHourly: boolean;\r\n}\r\n\r\nexport interface SellItemsSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n  autoSellOnRenew: boolean;\r\n}\r\n\r\nexport interface SellRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  itemTypes: ItemType[];\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface FilterCondition {\r\n  type: FilterType;\r\n  value: string | number;\r\n}\r\n\r\nexport enum FilterType {\r\n  CONTAINS = 'contains',\r\n  NOT_CONTAINS = 'not_contains',\r\n  CONTAINS_ANY = 'contains_any',\r\n  NOT_CONTAINS_ANY = 'not_contains_any',\r\n  CONTAINS_WORD = 'contains_word',\r\n  IS_UNDERWORLD = 'is_underworld',\r\n  IS_NOT_UNDERWORLD = 'is_not_underworld',\r\n  GREATER_THAN = 'greater_than',\r\n  LESS_THAN = 'less_than',\r\n  STARTS_WITH = 'starts_with',\r\n  ENDS_WITH = 'ends_with',\r\n  CONTAINS_RESOURCE = 'contains_resource'\r\n}\r\n\r\nexport interface PickGoldSettings {\r\n  enabled: boolean;\r\n  goldToPick: number;\r\n}\r\n\r\nexport enum OperationSpeed {\r\n  VERY_FAST = 'very_fast',\r\n  FAST = 'fast',\r\n  NORMAL = 'normal',\r\n  SLOW = 'slow'\r\n}\r\n\r\nexport interface CostumeSettings {\r\n  wearUnderworld: boolean;\r\n  preventWearUnderworldOnPause: boolean;\r\n  dontPauseIfUnderworldActive: boolean;\r\n  underworldCostume: UnderworldCostume;\r\n  standardProfile: CostumeProfile;\r\n  turmaProfile: CostumeProfile;\r\n  eventProfile: CostumeProfile;\r\n}\r\n\r\nexport enum UnderworldCostume {\r\n  DIS_PATER_NORMAL = 'dis_pater_normal',\r\n  DIS_PATER_MEDIUM = 'dis_pater_medium',\r\n  DIS_PATER_HARD = 'dis_pater_hard',\r\n  VULCANUS_FORGE = 'vulcanus_forge',\r\n  FERONIAS_EARTHEN_SHIELD = 'feronias_earthen_shield',\r\n  NEPTUNES_FLUID_MIGHT = 'neptunes_fluid_might',\r\n  AELOUS_AERIAL_FREEDOM = 'aelous_aerial_freedom',\r\n  PLUTOS_DEADLY_MIST = 'plutos_deadly_mist',\r\n  JUNOS_BREATH_OF_LIFE = 'junos_breath_of_life',\r\n  WRATH_MOUNTAIN_SCALE_ARMOUR = 'wrath_mountain_scale_armour',\r\n  EAGLE_EYES = 'eagle_eyes',\r\n  SATURNS_WINTER_GARMENT = 'saturns_winter_garment',\r\n  BUBONA_BULL_ARMOUR = 'bubona_bull_armour',\r\n  MERCERIUS_ROBBERS_GARMENTS = 'mercerius_robbers_garments',\r\n  RA_LIGHT_ROBE = 'ra_light_robe'\r\n}\r\n\r\nexport interface CostumeProfile {\r\n  enabled: boolean;\r\n  costume?: UnderworldCostume;\r\n}\r\n\r\nexport interface TurmaSettings {\r\n  enabled: boolean;\r\n  levelRestriction: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  runUntilGetChest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n}\r\n\r\nexport interface ArenaSettings {\r\n  enabled: boolean;\r\n  allowSim: boolean;\r\n  prioritizeChance: number;\r\n  skipLow: boolean;\r\n  loseLimit: number;\r\n  attackTargetPlayers: boolean;\r\n  allowMaxAttacks: boolean;\r\n  autoIgnoreGuildPlayers: boolean;\r\n  attackScore: boolean;\r\n  selectScorePage: number;\r\n  progressLeague: boolean;\r\n  stopAfterLimit: boolean;\r\n  attackGoldWhale: boolean;\r\n  doRevenge: boolean;\r\n  totalWin: boolean;\r\n  limitAttacks: number;\r\n  dailyAttacks: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  attackIfCombatQuest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n  attackScoreSingle: boolean;\r\n}\r\n\r\nexport interface ExpeditionSettings {\r\n  enabled: boolean;\r\n  minDungeonPoints: number;\r\n  location: string;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  travelForDungeon: boolean;\r\n  removeCooldown: CooldownRemovalOption;\r\n  cooldownLimit: number;\r\n  cooldownUsed: number;\r\n}\r\n\r\nexport enum CooldownRemovalOption {\r\n  HOURGLASS = 'hourglass',\r\n  HOURGLASS_OR_RUBY = 'hourglass_or_ruby'\r\n}\r\n\r\nexport interface DungeonSettings {\r\n  enabled: boolean;\r\n  location: string;\r\n  useGateKey: boolean;\r\n  isAdvanced: boolean;\r\n  skipBoss: boolean;\r\n  bossName: string;\r\n  restartAfterFail: number;\r\n}\r\n\r\nexport interface EventSettings {\r\n  enabled: boolean;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  autoRenewEvent: boolean;\r\n  followLeader: string;\r\n  autoStop: boolean;\r\n}\r\n\r\nexport interface UnderworldSettings {\r\n  enabled: boolean;\r\n  noWeapon: boolean;\r\n  sendMail: boolean;\r\n  mailContent: string;\r\n  maxMedics: number;\r\n  usedMedics: number;\r\n  maxRuby: number;\r\n  usedRuby: number;\r\n  buffs: boolean;\r\n  costumes: boolean;\r\n  reinforcements: string[];\r\n  mode: UnderworldMode;\r\n  difficulty: UnderworldDifficulty;\r\n  healGuild: boolean;\r\n  healPotions: boolean;\r\n  healSacrifice: boolean;\r\n  allowMobilisation: boolean;\r\n  allowRuby: boolean;\r\n  attackDisPaterAsap: boolean;\r\n  stopArenaInUnder: boolean;\r\n  healCostumes: boolean;\r\n  allowReinforcements: boolean;\r\n  allowUpgrades: boolean;\r\n  autoBuffGods: boolean;\r\n  autoBuffOils: boolean;\r\n  exitUnder: boolean;\r\n  stayHours: number;\r\n}\r\n\r\nexport enum UnderworldMode {\r\n  FARM_QUEST = 'farm_quest',\r\n  FARM_MOB = 'farm_mob',\r\n  NORMAL = 'normal'\r\n}\r\n\r\nexport enum UnderworldDifficulty {\r\n  NORMAL = 'normal',\r\n  MEDIUM = 'medium',\r\n  HARD = 'hard'\r\n}\r\n\r\nexport interface QuestManagementSettings {\r\n  enabled: boolean;\r\n  autoComplete: boolean;\r\n  dailyLimit: number;\r\n  rules: QuestManagementRule[];\r\n}\r\n\r\nexport interface QuestManagementRule {\r\n  priority: number;\r\n  questType: string;\r\n  minReward: number;\r\n  maxDifficulty: 'easy' | 'medium' | 'hard';\r\n  autoComplete: boolean;\r\n}\r\n\r\nexport interface MarketOperationsSettings {\r\n  enabled: boolean;\r\n  checkInterval: number;\r\n  dailyBuyLimit: number;\r\n  autoBuy: AutoBuySettings;\r\n  autoSell: AutoSellSettings;\r\n  auction: AuctionSettings;\r\n}\r\n\r\nexport interface AutoBuySettings {\r\n  enabled: boolean;\r\n  maxPrice: number;\r\n  maxGoldSpend: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemNames: string[];\r\n  blacklistedSellers: string[];\r\n}\r\n\r\nexport interface AutoSellSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n}\r\n\r\nexport interface AuctionSettings {\r\n  enabled: boolean;\r\n  autoBid: boolean;\r\n  autoBuyout: boolean;\r\n  maxBid: number;\r\n  maxBuyout: number;\r\n  bidIncrement: number;\r\n  minTimeRemaining: number;\r\n  qualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface ForgeAutomationSettings {\r\n  enabled: boolean;\r\n  autoForge: boolean;\r\n  autoSmelt: boolean;\r\n  maxGoldSpend: number;\r\n  maxCostPerItem: number;\r\n  dailyForgeLimit: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemTypes: string[];\r\n  minSmeltValue: number;\r\n  smeltQualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface RepairAutomationSettings {\r\n  enabled: boolean;\r\n  repairThreshold: number;\r\n  maxCostPerItem: number;\r\n  maxGoldSpend: number;\r\n  itemFilter: string[];\r\n}", "// UI and interface types\r\n\r\nexport interface UINotification {\r\n  id: string;\r\n  type: NotificationType;\r\n  title: string;\r\n  message: string;\r\n  timestamp: Date;\r\n  read: boolean;\r\n  actions?: NotificationAction[];\r\n}\r\n\r\nexport enum NotificationType {\r\n  INFO = 'info',\r\n  SUCCESS = 'success',\r\n  WARNING = 'warning',\r\n  ERROR = 'error',\r\n  GOLD_EXPIRY = 'gold_expiry',\r\n  ITEM_FOUND = 'item_found',\r\n  ITEM_BOUGHT = 'item_bought'\r\n}\r\n\r\nexport interface NotificationAction {\r\n  label: string;\r\n  action: string;\r\n  data?: any;\r\n}\r\n\r\nexport interface TabConfig {\r\n  id: string;\r\n  name: string;\r\n  icon?: string;\r\n  component: string;\r\n  enabled: boolean;\r\n  badge?: string | number;\r\n}\r\n\r\nexport interface ModalConfig {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n  actions: ModalAction[];\r\n  size?: ModalSize;\r\n  closable?: boolean;\r\n}\r\n\r\nexport interface ModalAction {\r\n  label: string;\r\n  action: string;\r\n  style?: ButtonStyle;\r\n  data?: any;\r\n}\r\n\r\nexport enum ModalSize {\r\n  SMALL = 'small',\r\n  MEDIUM = 'medium',\r\n  LARGE = 'large',\r\n  EXTRA_LARGE = 'extra_large'\r\n}\r\n\r\nexport enum ButtonStyle {\r\n  PRIMARY = 'primary',\r\n  SECONDARY = 'secondary',\r\n  SUCCESS = 'success',\r\n  DANGER = 'danger',\r\n  WARNING = 'warning',\r\n  INFO = 'info'\r\n}\r\n\r\nexport interface FormField {\r\n  id: string;\r\n  type: FieldType;\r\n  label: string;\r\n  value: any;\r\n  options?: SelectOption[];\r\n  validation?: ValidationRule[];\r\n  disabled?: boolean;\r\n  placeholder?: string;\r\n  help?: string;\r\n}\r\n\r\nexport enum FieldType {\r\n  TEXT = 'text',\r\n  NUMBER = 'number',\r\n  SELECT = 'select',\r\n  CHECKBOX = 'checkbox',\r\n  RADIO = 'radio',\r\n  TEXTAREA = 'textarea',\r\n  RANGE = 'range',\r\n  COLOR = 'color',\r\n  DATE = 'date',\r\n  TIME = 'time'\r\n}\r\n\r\nexport interface SelectOption {\r\n  value: any;\r\n  label: string;\r\n  disabled?: boolean;\r\n  icon?: string;\r\n}\r\n\r\nexport interface ValidationRule {\r\n  type: ValidationType;\r\n  value?: any;\r\n  message: string;\r\n}\r\n\r\nexport enum ValidationType {\r\n  REQUIRED = 'required',\r\n  MIN = 'min',\r\n  MAX = 'max',\r\n  MIN_LENGTH = 'min_length',\r\n  MAX_LENGTH = 'max_length',\r\n  PATTERN = 'pattern',\r\n  EMAIL = 'email',\r\n  URL = 'url'\r\n}\r\n\r\nexport interface UIState {\r\n  activeTab: string;\r\n  sidebarCollapsed: boolean;\r\n  notifications: UINotification[];\r\n  modals: ModalConfig[];\r\n  loading: boolean;\r\n  error?: string;\r\n}", "// Default Settings Factory - Provides default values for all bot settings\n\nimport {\n  BotSettings,\n  ItemQuality,\n  UnderworldMode,\n  UnderworldDifficulty,\n  CooldownRemovalOption,\n  OperationSpeed,\n  RenewShopOption,\n  HideGoldLocation,\n  PackFilterType,\n  PackDuration\n} from '../types';\n\nexport class DefaultSettingsFactory {\n  static createDefaultSettings(): BotSettings {\n    return {\n      general: {\n        selectedBackpack: 1,\n        maxRandomDelay: 10,\n        loginDelay: 5,\n        autoCollectDaily: true,\n        autoCollectGodOils: true,\n        showBiddenBox: true,\n        showShopBox: true,\n        highlightUnderworldItems: true,\n        shopMinQuality: 1\n      },\n      heal: {\n        enabled: false,\n        minHealth: 80,\n        healCervisia: true,\n        healEggs: true,\n        selectedEggs: [1, 2, 3],\n        buyFood: false,\n        buyFoodNeeded: false,\n        renewShop: RenewShopOption.NOT_ALLOW,\n        autoStopAttacks: true\n      },\n      hideGold: {\n        primary: {\n          enabled: false,\n          location: HideGoldLocation.GUILD,\n          settings: {}\n        },\n        backup: {\n          enabled: false,\n          location: HideGoldLocation.SHOP,\n          settings: {}\n        },\n        minGoldBackup: 500,\n        soulBoundTo: '',\n        filterBy: PackFilterType.LOWEST_PRICE,\n        allowMultiple: false,\n        recheckDuration: 3600,\n        pocketMoney: 1000,\n        hideIn: HideGoldLocation.GUILD,\n        duration: PackDuration.TWO_HOURS,\n        skillsToTrain: [],\n        minItemPrice: 100,\n        minPack: 1,\n        maxPack: 10,\n        buyFromPlayers: []\n      },\n      packages: {\n        rotateItems: {\n          enabled: false,\n          selectedItems: [],\n          colors: [],\n          underworldItems: false,\n          itemsCooldownDays: 7,\n          collectResourceHourly: false\n        },\n        sellItems: {\n          enabled: false,\n          rules: [],\n          autoSellOnRenew: false\n        },\n        pickGold: {\n          enabled: false,\n          goldToPick: 0\n        },\n        operationSpeed: OperationSpeed.NORMAL\n      },\n      costumes: {\n        wearUnderworld: false,\n        preventWearUnderworldOnPause: false,\n        dontPauseIfUnderworldActive: false,\n        underworldCostume: 'dis_pater_normal' as any,\n        standardProfile: {\n          enabled: false\n        },\n        turmaProfile: {\n          enabled: false\n        },\n        eventProfile: {\n          enabled: false\n        }\n      },\n      turma: {\n        enabled: false,\n        levelRestriction: 0,\n        autoStop: 0,\n        attackIfQuest: false,\n        runUntilGetChest: false,\n        goldRaided: 0,\n        attackPlayers: [],\n        autoIgnorePlayers: [],\n        ignorePlayers: [],\n        attackSameServer: true\n      },\n      arena: {\n        enabled: false,\n        allowSim: true,\n        prioritizeChance: 80,\n        skipLow: false,\n        loseLimit: 3,\n        attackTargetPlayers: false,\n        allowMaxAttacks: false,\n        autoIgnoreGuildPlayers: true,\n        attackScore: false,\n        selectScorePage: 1,\n        progressLeague: false,\n        stopAfterLimit: false,\n        attackGoldWhale: false,\n        doRevenge: false,\n        totalWin: false,\n        limitAttacks: 0,\n        dailyAttacks: 0,\n        autoStop: 0,\n        attackIfQuest: false,\n        attackIfCombatQuest: false,\n        goldRaided: 0,\n        attackPlayers: [],\n        autoIgnorePlayers: [],\n        ignorePlayers: [],\n        attackSameServer: true,\n        attackScoreSingle: false\n      },\n      expeditions: {\n        enabled: false,\n        minDungeonPoints: 0,\n        location: '',\n        mob: '',\n        autoCollectBonuses: true,\n        travelForDungeon: false,\n        removeCooldown: CooldownRemovalOption.HOURGLASS,\n        cooldownLimit: 10,\n        cooldownUsed: 0\n      },\n      dungeon: {\n        enabled: false,\n        location: '',\n        useGateKey: false,\n        isAdvanced: false,\n        skipBoss: false,\n        bossName: '',\n        restartAfterFail: 0\n      },\n      event: {\n        enabled: false,\n        mob: '',\n        autoCollectBonuses: true,\n        autoRenewEvent: false,\n        followLeader: '',\n        autoStop: false\n      },\n      underworld: {\n        enabled: false,\n        noWeapon: false,\n        sendMail: false,\n        mailContent: '',\n        maxMedics: 5,\n        usedMedics: 0,\n        maxRuby: 2,\n        usedRuby: 0,\n        buffs: true,\n        costumes: true,\n        reinforcements: [],\n        mode: UnderworldMode.NORMAL,\n        difficulty: UnderworldDifficulty.NORMAL,\n        healGuild: true,\n        healPotions: true,\n        healSacrifice: false,\n        allowMobilisation: false,\n        allowRuby: false,\n        attackDisPaterAsap: true,\n        stopArenaInUnder: true,\n        healCostumes: true,\n        allowReinforcements: false,\n        allowUpgrades: false,\n        autoBuffGods: true,\n        autoBuffOils: true,\n        exitUnder: false,\n        stayHours: 24\n      },\n      forge: {\n        enabled: false,\n        autoForge: false,\n        autoSmelt: false,\n        maxGoldSpend: 10000,\n        maxCostPerItem: 1000,\n        dailyForgeLimit: 10,\n        qualityFilter: [ItemQuality.WHITE, ItemQuality.GREEN],\n        minLevel: 1,\n        maxLevel: 50,\n        itemTypes: [],\n        minSmeltValue: 10,\n        smeltQualityFilter: [ItemQuality.WHITE]\n      },\n      repair: {\n        enabled: false,\n        repairThreshold: 50,\n        maxCostPerItem: 500,\n        maxGoldSpend: 5000,\n        itemFilter: []\n      },\n      quest: {\n        enabled: false,\n        autoComplete: false,\n        dailyLimit: 10,\n        rules: []\n      },\n      market: {\n        enabled: false,\n        checkInterval: 300,\n        dailyBuyLimit: 20,\n        autoBuy: {\n          enabled: false,\n          maxPrice: 10000,\n          maxGoldSpend: 50000,\n          qualityFilter: [ItemQuality.BLUE, ItemQuality.PURPLE],\n          minLevel: 1,\n          maxLevel: 100,\n          itemNames: [],\n          blacklistedSellers: []\n        },\n        autoSell: {\n          enabled: false,\n          rules: []\n        },\n        auction: {\n          enabled: false,\n          autoBid: false,\n          autoBuyout: false,\n          maxBid: 10000,\n          maxBuyout: 20000,\n          bidIncrement: 100,\n          minTimeRemaining: 300,\n          qualityFilter: [ItemQuality.PURPLE, ItemQuality.ORANGE]\n        }\n      },\n      shop: {\n        enabled: false,\n        searchRules: [],\n        maxSearches: 10,\n        usedSearches: 0,\n        allowRuby: false,\n        autoStart: false\n      },\n      bank: {\n        enabled: false,\n        isBank: false,\n        selectedBackpack: 1,\n        clientSettings: {\n          requestThreshold: 1000,\n          packGoldAmount: 10000,\n          duration: '2h'\n        }\n      },\n      guild: {\n        enabled: false,\n        attackList: [],\n        ignoreList: []\n      }\n    };\n  }\n\n  static mergeWithDefaults(userSettings: Partial<BotSettings>): BotSettings {\n    const defaultSettings = this.createDefaultSettings();\n    return this.deepMerge(defaultSettings, userSettings);\n  }\n\n  private static deepMerge(target: any, source: any): any {\n    const result = { ...target };\n    \n    for (const key in source) {\n      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {\n        result[key] = this.deepMerge(target[key] || {}, source[key]);\n      } else {\n        result[key] = source[key];\n      }\n    }\n    \n    return result;\n  }\n}\n", "// Gladiatus URL parsing and validation utilities\r\n\r\nimport { GladiatorUrlInfo, GladiatorQueries } from '../types';\r\n\r\nexport class GladiatorUrlHelper {\r\n  private resolved: boolean = false;\r\n  private urlInfo: GladiatorUrlInfo = {\r\n    queries: {},\r\n    resolved: false\r\n  };\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus domain\r\n   */\r\n  isGladiatus(url: string): boolean {\r\n    return /gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus game page (not lobby)\r\n   */\r\n  isPlaying(url: string): boolean {\r\n    return /https:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is the Gladiatus lobby\r\n   */\r\n  isLobby(url: string): boolean {\r\n    return /lobby\\.gladiatus\\.gameforge\\.com/.test(url) && url.indexOf('loading') < 0;\r\n  }\r\n\r\n  /**\r\n   * Parse query string into object\r\n   */\r\n  resolveQueries(queryString: string): GladiatorQueries {\r\n    const queries: GladiatorQueries = {};\r\n    const params = queryString.split('&');\r\n\r\n    for (let i = params.length - 1; i >= 0; i--) {\r\n      const [key, value] = params[i].split('=');\r\n      if (key && value) {\r\n        queries[key] = decodeURIComponent(value);\r\n      }\r\n    }\r\n\r\n    return queries;\r\n  }\r\n\r\n  /**\r\n   * Resolve URL information from Gladiatus game URL\r\n   */\r\n  resolve(url: string, force: boolean = false): GladiatorUrlInfo | null {\r\n    if (this.resolved && !force) {\r\n      return this.urlInfo;\r\n    }\r\n\r\n    const match = url.match(\r\n      /https?:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com\\/game\\/(?:index|main)\\.php(?:\\?(.*))?/i\r\n    );\r\n\r\n    if (!match) {\r\n      return null;\r\n    }\r\n\r\n    this.urlInfo = {\r\n      server: match[1],\r\n      country: match[2],\r\n      domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,\r\n      queries: this.resolveQueries(match[3] || ''),\r\n      resolved: true\r\n    };\r\n\r\n    this.resolved = true;\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Build Gladiatus URL with parameters\r\n   */\r\n  buildUrl(params: Record<string, string | number>, page: string = 'index.php'): string {\r\n    const queryParts: string[] = [];\r\n\r\n    for (const key in params) {\r\n      queryParts.push(`${key}=${encodeURIComponent(params[key])}`);\r\n    }\r\n\r\n    // Add security hash if available\r\n    if (this.urlInfo.queries.sh) {\r\n      queryParts.push(`sh=${this.urlInfo.queries.sh}`);\r\n    }\r\n\r\n    return `${page}?${queryParts.join('&')}`;\r\n  }\r\n\r\n  /**\r\n   * Build AJAX URL with parameters\r\n   */\r\n  buildAjaxUrl(params: Record<string, string | number>): string {\r\n    return this.buildUrl(params, 'ajax.php');\r\n  }\r\n\r\n  /**\r\n   * Get current URL info\r\n   */\r\n  getUrlInfo(): GladiatorUrlInfo {\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Reset resolved state\r\n   */\r\n  reset(): void {\r\n    this.resolved = false;\r\n    this.urlInfo = {\r\n      queries: {},\r\n      resolved: false\r\n    };\r\n  }\r\n}", "// Chrome extension storage management utilities\r\n\r\nimport { BotSettings, BotStatus, ExtensionStorage } from '../types';\r\nimport { DefaultSettingsFactory } from './default-settings';\r\n\r\nexport class StorageManager {\r\n  private readonly STORAGE_KEYS = {\r\n    SETTINGS: 'gh_settings',\r\n    STATISTICS: 'gh_statistics',\r\n    LICENSE: 'gh_license',\r\n    PAUSE_SCHEDULE: 'gh_pause_schedule',\r\n    QUEST_RULES: 'gh_quest_rules',\r\n    MARKET_RULES: 'gh_market_rules',\r\n    BANK_DATA: 'gh_bank_data',\r\n    BOT_STATUS: 'gh_bot_status'\r\n  } as const;\r\n\r\n  /**\r\n   * Check if Chrome storage API is available\r\n   */\r\n  private isChromeStorageAvailable(): boolean {\r\n    return typeof chrome !== 'undefined' &&\r\n           chrome.storage &&\r\n           chrome.storage.local &&\r\n           typeof chrome.storage.local.get === 'function';\r\n  }\r\n\r\n  /**\r\n   * Fallback method to get settings from localStorage\r\n   */\r\n  private getSettingsFromLocalStorage(): Partial<BotSettings> | null {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);\r\n      return stored ? JSON.parse(stored) : null;\r\n    } catch (error) {\r\n      console.error('Failed to get settings from localStorage:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback method to save settings to localStorage\r\n   */\r\n  private saveSettingsToLocalStorage(settings: Partial<BotSettings>): void {\r\n    try {\r\n      const currentSettings = this.getSettingsFromLocalStorage() || {};\r\n      const updatedSettings = this.deepMerge(currentSettings, settings);\r\n      localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));\r\n    } catch (error) {\r\n      console.error('Failed to save settings to localStorage:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get bot settings from storage\r\n   */\r\n  async getSettings(): Promise<BotSettings | null> {\r\n    try {\r\n      // Check if Chrome storage API is available\r\n      if (!this.isChromeStorageAvailable()) {\r\n        console.warn('Chrome storage API not available, using localStorage fallback');\r\n        const storedSettings = this.getSettingsFromLocalStorage();\r\n        return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();\r\n      }\r\n\r\n      const result = await chrome.storage.local.get(this.STORAGE_KEYS.SETTINGS);\r\n      const storedSettings = result[this.STORAGE_KEYS.SETTINGS];\r\n\r\n      if (!storedSettings) {\r\n        // Return default settings if none are stored\r\n        return DefaultSettingsFactory.createDefaultSettings();\r\n      }\r\n\r\n      // Merge stored settings with defaults to ensure all properties exist\r\n      return DefaultSettingsFactory.mergeWithDefaults(storedSettings);\r\n    } catch (error) {\r\n      console.error('Failed to get settings:', error);\r\n      // Fallback to localStorage\r\n      const storedSettings = this.getSettingsFromLocalStorage();\r\n      return storedSettings ? DefaultSettingsFactory.mergeWithDefaults(storedSettings) : DefaultSettingsFactory.createDefaultSettings();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save bot settings to storage\r\n   */\r\n  async saveSettings(settings: Partial<BotSettings>): Promise<void> {\r\n    try {\r\n      // Check if Chrome storage API is available\r\n      if (!this.isChromeStorageAvailable()) {\r\n        console.warn('Chrome storage API not available, using localStorage fallback');\r\n        this.saveSettingsToLocalStorage(settings);\r\n        return;\r\n      }\r\n\r\n      const currentSettings = await this.getSettings() || {};\r\n      const updatedSettings = this.deepMerge(currentSettings, settings);\r\n\r\n      await chrome.storage.local.set({\r\n        [this.STORAGE_KEYS.SETTINGS]: updatedSettings\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to save settings:', error);\r\n      // Fallback to localStorage\r\n      this.saveSettingsToLocalStorage(settings);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get bot status from storage\r\n   */\r\n  async getBotStatus(): Promise<BotStatus | null> {\r\n    try {\r\n      if (!this.isChromeStorageAvailable()) {\r\n        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);\r\n        return stored ? JSON.parse(stored) : null;\r\n      }\r\n\r\n      const result = await chrome.storage.local.get(this.STORAGE_KEYS.BOT_STATUS);\r\n      return result[this.STORAGE_KEYS.BOT_STATUS] || null;\r\n    } catch (error) {\r\n      console.error('Failed to get bot status:', error);\r\n      try {\r\n        const stored = localStorage.getItem(this.STORAGE_KEYS.BOT_STATUS);\r\n        return stored ? JSON.parse(stored) : null;\r\n      } catch {\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save bot status to storage\r\n   */\r\n  async saveBotStatus(status: BotStatus): Promise<void> {\r\n    try {\r\n      if (!this.isChromeStorageAvailable()) {\r\n        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));\r\n        return;\r\n      }\r\n\r\n      await chrome.storage.local.set({\r\n        [this.STORAGE_KEYS.BOT_STATUS]: status\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to save bot status:', error);\r\n      // Fallback to localStorage\r\n      try {\r\n        localStorage.setItem(this.STORAGE_KEYS.BOT_STATUS, JSON.stringify(status));\r\n      } catch (fallbackError) {\r\n        console.error('Failed to save bot status to localStorage:', fallbackError);\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all extension data from storage\r\n   */\r\n  async getAllData(): Promise<Partial<ExtensionStorage>> {\r\n    try {\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      const result = await chrome.storage.local.get(keys);\r\n\r\n      return {\r\n        settings: result[this.STORAGE_KEYS.SETTINGS],\r\n        statistics: result[this.STORAGE_KEYS.STATISTICS],\r\n        licenseInfo: result[this.STORAGE_KEYS.LICENSE],\r\n        pauseSchedule: result[this.STORAGE_KEYS.PAUSE_SCHEDULE],\r\n        questRules: result[this.STORAGE_KEYS.QUEST_RULES],\r\n        marketRules: result[this.STORAGE_KEYS.MARKET_RULES],\r\n        bankData: result[this.STORAGE_KEYS.BANK_DATA]\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get all data:', error);\r\n      return {};\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all extension data from storage\r\n   */\r\n  async clearAllData(): Promise<void> {\r\n    try {\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      await chrome.storage.local.remove(keys);\r\n    } catch (error) {\r\n      console.error('Failed to clear all data:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get storage usage information\r\n   */\r\n  async getStorageUsage(): Promise<{ bytesInUse: number; quota: number }> {\r\n    try {\r\n      const bytesInUse = await chrome.storage.local.getBytesInUse();\r\n      const quota = chrome.storage.local.QUOTA_BYTES;\r\n\r\n      return { bytesInUse, quota };\r\n    } catch (error) {\r\n      console.error('Failed to get storage usage:', error);\r\n      return { bytesInUse: 0, quota: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deep merge two objects\r\n   */\r\n  private deepMerge(target: any, source: any): any {\r\n    const result = { ...target };\r\n\r\n    for (const key in source) {\r\n      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {\r\n        result[key] = this.deepMerge(result[key] || {}, source[key]);\r\n      } else {\r\n        result[key] = source[key];\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Export settings as JSON string\r\n   */\r\n  async exportSettings(): Promise<string> {\r\n    const data = await this.getAllData();\r\n    return JSON.stringify(data, null, 2);\r\n  }\r\n\r\n  /**\r\n   * Import settings from JSON string\r\n   */\r\n  async importSettings(jsonData: string): Promise<void> {\r\n    try {\r\n      const data = JSON.parse(jsonData);\r\n\r\n      if (data.settings) {\r\n        await this.saveSettings(data.settings);\r\n      }\r\n\r\n      // Import other data types as needed\r\n      const keys = Object.values(this.STORAGE_KEYS);\r\n      const importData: Record<string, any> = {};\r\n\r\n      for (const key of keys) {\r\n        const dataKey = key.replace('gh_', '');\r\n        if (data[dataKey]) {\r\n          importData[key] = data[dataKey];\r\n        }\r\n      }\r\n\r\n      if (Object.keys(importData).length > 0) {\r\n        await chrome.storage.local.set(importData);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to import settings:', error);\r\n      throw new Error('Invalid settings data');\r\n    }\r\n  }\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Background service worker for Gladiatus Helper Bot\r\n\r\nimport { ExtensionMessage, MessageType } from '../types';\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\nimport { StorageManager } from '../utils/storage-manager';\r\n\r\nclass BackgroundService {\r\n  private urlHelper: GladiatorUrlHelper;\r\n  private storageManager: StorageManager;\r\n\r\n  constructor() {\r\n    this.urlHelper = new GladiatorUrlHelper();\r\n    this.storageManager = new StorageManager();\r\n    this.initialize();\r\n  }\r\n\r\n  private initialize(): void {\r\n    // Listen for extension messages\r\n    chrome.runtime.onMessage.addListener(\r\n      (message: ExtensionMessage, sender, sendResponse) => {\r\n        this.handleMessage(message, sender, sendResponse);\r\n        return true; // Keep message channel open for async response\r\n      }\r\n    );\r\n\r\n    // Listen for tab updates to detect Gladiatus pages\r\n    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {\r\n      if (changeInfo.status === 'complete' && tab.url) {\r\n        this.handleTabUpdate(tabId, tab.url);\r\n      }\r\n    });\r\n\r\n    // Listen for extension installation/startup\r\n    chrome.runtime.onStartup.addListener(() => {\r\n      this.handleStartup();\r\n    });\r\n\r\n    chrome.runtime.onInstalled.addListener((details) => {\r\n      this.handleInstalled(details);\r\n    });\r\n  }\r\n\r\n  private async handleMessage(\r\n    message: ExtensionMessage,\r\n    _sender: chrome.runtime.MessageSender,\r\n    sendResponse: (response?: any) => void\r\n  ): Promise<void> {\r\n    try {\r\n      switch (message.type) {\r\n        case MessageType.LOGIN_REQUEST:\r\n          await this.handleLoginRequest(message, sendResponse);\r\n          break;\r\n\r\n        case MessageType.SETTINGS_UPDATE:\r\n          await this.handleSettingsUpdate(message, sendResponse);\r\n          break;\r\n\r\n        case MessageType.SCRIPT_STATUS:\r\n          await this.handleScriptStatus(message, sendResponse);\r\n          break;\r\n\r\n        default:\r\n          console.warn('Unknown message type:', message.type);\r\n          sendResponse({ error: 'Unknown message type' });\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling message:', error);\r\n      sendResponse({ error: (error as Error).message });\r\n    }\r\n  }\r\n\r\n  private async handleLoginRequest(\r\n    message: ExtensionMessage,\r\n    sendResponse: (response?: any) => void\r\n  ): Promise<void> {\r\n    if (message.isLobby) {\r\n      const shouldLogin = await this.shouldAutoLogin();\r\n      const loginDelay = await this.getLoginDelay();\r\n\r\n      sendResponse({\r\n        shouldLogin,\r\n        loginDelay: shouldLogin ? Date.now() + loginDelay : 0,\r\n        server: message.server,\r\n        playerId: message.playerId,\r\n        language: message.language\r\n      });\r\n    }\r\n  }\r\n\r\n  private async handleSettingsUpdate(\r\n    message: ExtensionMessage,\r\n    sendResponse: (response?: any) => void\r\n  ): Promise<void> {\r\n    try {\r\n      await this.storageManager.saveSettings(message.data);\r\n      sendResponse({ success: true });\r\n    } catch (error) {\r\n      sendResponse({ error: (error as Error).message });\r\n    }\r\n  }\r\n\r\n  private async handleScriptStatus(\r\n    _message: ExtensionMessage,\r\n    sendResponse: (response?: any) => void\r\n  ): Promise<void> {\r\n    const status = await this.storageManager.getBotStatus();\r\n    sendResponse(status);\r\n  }\r\n\r\n  private async handleTabUpdate(tabId: number, url: string): Promise<void> {\r\n    if (this.urlHelper.isGladiatus(url)) {\r\n      // Inject content script if it's a Gladiatus page\r\n      try {\r\n        await chrome.scripting.executeScript({\r\n          target: { tabId },\r\n          files: ['content-ui/index.iife.js']\r\n        });\r\n      } catch (error) {\r\n        console.error('Failed to inject content script:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  private handleStartup(): void {\r\n    console.log('Gladiatus Helper Bot started');\r\n  }\r\n\r\n  private handleInstalled(details: chrome.runtime.InstalledDetails): void {\r\n    if (details.reason === 'install') {\r\n      console.log('Gladiatus Helper Bot installed');\r\n      this.initializeDefaultSettings();\r\n    } else if (details.reason === 'update') {\r\n      console.log('Gladiatus Helper Bot updated');\r\n      this.handleUpdate(details.previousVersion);\r\n    }\r\n  }\r\n\r\n  private async shouldAutoLogin(): Promise<boolean> {\r\n    const settings = await this.storageManager.getSettings();\r\n    return settings?.general?.autoLogin ?? false;\r\n  }\r\n\r\n  private async getLoginDelay(): Promise<number> {\r\n    const settings = await this.storageManager.getSettings();\r\n    return (settings?.general?.loginDelay ?? 5) * 1000; // Convert to milliseconds\r\n  }\r\n\r\n  private async initializeDefaultSettings(): Promise<void> {\r\n    const defaultSettings = {\r\n      general: {\r\n        selectedBackpack: 1,\r\n        maxRandomDelay: 10,\r\n        loginDelay: 5,\r\n        highlightUnderworldItems: true,\r\n        showShopBox: true,\r\n        shopMinQuality: 1,\r\n        showBiddenBox: true,\r\n        autoCollectGodOils: true,\r\n        autoCollectDaily: true\r\n      }\r\n    };\r\n\r\n    await this.storageManager.saveSettings(defaultSettings);\r\n  }\r\n\r\n  private async handleUpdate(previousVersion?: string): Promise<void> {\r\n    // Handle version-specific updates\r\n    console.log(`Updated from version ${previousVersion} to ${chrome.runtime.getManifest().version}`);\r\n  }\r\n}\r\n\r\n// Initialize the background service\r\nnew BackgroundService();"], "names": [], "sourceRoot": ""}