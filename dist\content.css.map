{"version": 3, "file": "content.css", "mappings": ";;;AAAA,2CAA2C;;AAE3C;EACE,sBAAsB;EACtB,qBAAqB;EACrB,uBAAuB;EACvB,qBAAqB;EACrB,qBAAqB;EACrB,oBAAoB;EACpB,kBAAkB;;EAElB,sBAAsB;EACtB,wBAAwB;EACxB,0BAA0B;EAC1B,sBAAsB;EACtB,qBAAqB;;EAErB,gBAAgB;EAChB,0BAA0B;EAC1B,4BAA4B;EAC5B,wBAAwB;EACxB,uBAAuB;;EAEvB,kBAAkB;EAClB,4BAA4B;EAC5B,8BAA8B;EAC9B,0BAA0B;;EAE1B,wBAAwB;EACxB,yCAAyC;EACzC,qCAAqC;EACrC,sCAAsC;EACtC,wCAAwC;EACxC,wCAAwC;EACxC,mCAAmC;;EAEnC,YAAY;EACZ,oBAAoB;EACpB,oBAAoB;EACpB,qBAAqB;EACrB,qBAAqB;EACrB,qBAAqB;;EAErB,kBAAkB;EAClB,mBAAmB;EACnB,mBAAmB;EACnB,oBAAoB;;EAEpB,YAAY;EACZ,4CAA4C;EAC5C,8CAA8C;EAC9C,6CAA6C;;EAE7C,mBAAmB;EACnB,qBAAqB;EACrB,mBAAmB;EACnB,kBAAkB;EAClB,2BAA2B;EAC3B,kBAAkB;EAClB,oBAAoB;EACpB,oBAAoB;;EAEpB,gBAAgB;EAChB,uCAAuC;EACvC,sCAAsC;EACtC,sCAAsC;;EAEtC,eAAe;EACf,qBAAqB;EACrB,sBAAsB;EACtB,oBAAoB;EACpB,sBAAsB;EACtB,qBAAqB;EACrB,qBAAqB;;EAErB,iBAAiB;EACjB,qBAAqB;EACrB,qBAAqB;EACrB,uBAAuB;EACvB,mBAAmB;AACrB,C;;;;AChFA,6BAA6B;;AAE7B;EACE,eAAe;EACf,yBAAyB;EACzB,2BAA2B;EAC3B,0BAA0B;EAC1B,YAAY;EACZ,gCAAgC;EAChC,0CAA0C;EAC1C,kCAAkC;EAClC,6BAA6B;EAC7B,8EAA8E;EAC9E,4BAA4B;EAC5B,+BAA+B;EAC/B,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,6BAA6B;EAC7B,iDAAiD;EACjD,kCAAkC;EAClC,0DAA0D;AAC5D;;AAEA;EACE,SAAS;EACT,8BAA8B;EAC9B,oCAAoC;EACpC,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,yBAAyB;EACzB,mCAAmC;AACrC;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,kCAAkC;EAClC,sDAAsD;AACxD;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,mCAAmC;EACnC,6BAA6B;EAC7B,kCAAkC;EAClC,kCAAkC;AACpC;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,mCAAmC;AACrC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,+BAA+B;EAC/B,4BAA4B;AAC9B;;AAEA;EACE,6BAA6B;EAC7B,kCAAkC;EAClC,4BAA4B;AAC9B;;AAEA;EACE,aAAa;EACb,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,kDAAkD;EAClD,YAAY;EACZ,kCAAkC;EAClC,4BAA4B;EAC5B,kCAAkC;EAClC,qBAAqB;EACrB,eAAe;EACf,yCAAyC;EACzC,iBAAiB;AACnB;;AAEA;EACE,2BAA2B;EAC3B,+BAA+B;AACjC;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,6BAA6B;EAC7B,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,kCAAkC;EAClC,6BAA6B;EAC7B,0CAA0C;AAC5C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,kDAAkD;EAClD,4BAA4B;EAC5B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,4BAA4B;EAC5B,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,6BAA6B;EAC7B,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;AACrB;;AAEA,2BAA2B;AAC3B;EACE;IACE,YAAY;IACZ,yBAAyB;IACzB,2BAA2B;EAC7B;;EAEA;IACE,sBAAsB;EACxB;;EAEA;IACE,WAAW;EACb;AACF,C;;;;ACxLA,iBAAiB;;AAEjB;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,8BAA8B;EAC9B,0BAA0B;EAC1B,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,UAAU;EACV,wCAAwC;AAC1C;;AAEA;EACE,gCAAgC;EAChC,6BAA6B;EAC7B,kCAAkC;EAClC,+BAA+B;EAC/B,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,qBAAqB;EACrB,yCAAyC;AAC3C;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,6BAA6B;EAC7B,iDAAiD;EACjD,kCAAkC;AACpC;;AAEA;EACE,SAAS;EACT,4BAA4B;EAC5B,oCAAoC;EACpC,6BAA6B;AAC/B;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,+BAA+B;EAC/B,6BAA6B;EAC7B,eAAe;EACf,UAAU;EACV,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,kCAAkC;EAClC,yCAAyC;AAC3C;;AAEA;EACE,gCAAgC;EAChC,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;EAC7B,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,yBAAyB;EACzB,yBAAyB;EACzB,6BAA6B;EAC7B,8CAA8C;EAC9C,kCAAkC;AACpC;;AAEA,qCAAqC;AACrC;EACE,mCAAmC;AACrC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,4BAA4B;EAC5B,oCAAoC;EACpC,6BAA6B;EAC7B,iDAAiD;EACjD,oCAAoC;AACtC;;AAEA;EACE,kCAAkC;EAClC,+BAA+B;EAC/B,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;AAClB;;AAEA,0BAA0B;AAC1B;EACE,mCAAmC;AACrC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,4BAA4B;EAC5B,oCAAoC;EACpC,6BAA6B;AAC/B;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,mCAAmC;EACnC,4BAA4B;EAC5B,kCAAkC;EAClC,6BAA6B;AAC/B;;AAEA;;;EAGE,WAAW;EACX,6BAA6B;EAC7B,0CAA0C;EAC1C,kCAAkC;EAClC,kCAAkC;EAClC,6BAA6B;EAC7B,4BAA4B;EAC5B,kDAAkD;AACpD;;AAEA;;;EAGE,aAAa;EACb,4BAA4B;AAC9B;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,yBAAyB;AAC3B;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,+BAA+B;AACjC;;AAEA,eAAe;AACf;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;EACvB;EACA;IACE,mBAAmB;EACrB;AACF;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,mBAAmB;EACrB;EACA;IACE,qBAAqB;EACvB;AACF;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,2BAA2B;AAC3B;EACE;IACE,WAAW;IACX,gBAAgB;EAClB;;EAEA;;;IAGE,6BAA6B;EAC/B;;EAEA;IACE,sBAAsB;EACxB;;EAEA;IACE,WAAW;EACb;AACF,C;;;;AChPA,+BAA+B;;AAE/B;EACE,eAAe;EACf,yBAAyB;EACzB,0BAA0B;EAC1B,0BAA0B;EAC1B,gBAAgB;EAChB,oBAAoB;AACtB;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,yBAAyB;EACzB,6BAA6B;EAC7B,mCAAmC;EACnC,gCAAgC;EAChC,0CAA0C;EAC1C,kCAAkC;EAClC,+BAA+B;EAC/B,8EAA8E;EAC9E,4BAA4B;EAC5B,6BAA6B;EAC7B,oBAAoB;EACpB,4BAA4B;EAC5B,yCAAyC;AAC3C;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,OAAO;AACT;;AAEA;EACE,cAAc;EACd,mCAAmC;EACnC,oCAAoC;EACpC,6BAA6B;AAC/B;;AAEA;EACE,SAAS;EACT,+BAA+B;EAC/B,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,+BAA+B;EAC/B,4BAA4B;EAC5B,eAAe;EACf,UAAU;EACV,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,kCAAkC;EAClC,yCAAyC;AAC3C;;AAEA;EACE,kCAAkC;EAClC,6BAA6B;AAC/B;;AAEA,uBAAuB;AACvB;EACE,qCAAqC;AACvC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,uCAAuC;AACzC;;AAEA,eAAe;AACf;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,wBAAwB;IACxB,UAAU;EACZ;EACA;IACE,4BAA4B;IAC5B,UAAU;EACZ;AACF;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,2BAA2B;AAC3B;EACE;IACE,0BAA0B;IAC1B,2BAA2B;IAC3B,eAAe;EACjB;;EAEA;IACE,6BAA6B;EAC/B;AACF,C;;;;AC9HA,uCAAuC;;AAEvC,+BAA+B;;AAM/B,uCAAuC;AACvC;IACI,0BAA0B;IAC1B,sBAAsB;IACtB,sCAAsC;IACtC,2EAA2E;IAC3E,mBAAmB;AACvB;AACA;IACI,6CAA6C;IAC7C,oBAAoB;IACpB,mBAAmB;;IAEnB,qCAAqC;IACrC,6CAA6C;;IAE7C,WAAW;IACX,gBAAgB;IAChB,aAAa;IACb,sBAAsB;IACtB,8BAA8B;AAClC;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;AACtB;AACA;IACI,eAAe;IACf,cAAc;AAClB;;AAEA;IACI,aAAa;AACjB;;AAEA,0CAA0C;AAC1C;IACI,qBAAqB;IACrB,aAAa;IACb,cAAc;IACd,YAAY;IACZ,yBAAyB,EAAE,iBAAiB;IAC5C,uBAAuB;IACvB,8BAA8B;IAC9B,eAAe;IACf,mBAAmB;IACnB,eAAe;IACf,sBAAsB;IACtB,iCAAiC;AACrC;AACA;IACI,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,wCAAwC;IACxC,8BAA8B;IAC9B,eAAe;IACf,kBAAkB;IAClB,sBAAsB;IACtB,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,wBAAwB;IACxB,mBAAmB;IACnB,0BAA0B;IAC1B,2BAA2B;IAC3B,2CAA2C;IAC3C,kBAAkB;IAClB,+CAA+C;IAC/C,eAAe;AACnB;;AAEA;IACI,wBAAwB;IACxB,mBAAmB;IACnB,0BAA0B;IAC1B,2BAA2B;IAC3B,2CAA2C;IAC3C,kBAAkB;IAClB,+CAA+C;IAC/C,eAAe;AACnB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,oFAAoF;IACpF,4FAA4F;AAChG;;AAEA;IACI,wBAAwB;IACxB,gBAAgB;IAChB,2BAA2B;IAC3B,6BAA6B;IAC7B,kBAAkB;IAClB,2CAA2C;IAC3C,oBAAoB;AACxB;;AAEA;IACI,SAAS;IACT,UAAU;IACV,aAAa;AACjB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,gBAAgB;IAChB,kBAAkB;IAClB,uCAAuC;IACvC,mBAAmB;IACnB,iBAAiB;AACrB;;AAEA;IACI,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,qBAAqB;IACrB,UAAU;IACV,YAAY;IACZ,qBAAqB;AACzB;AACA;IACI,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,iBAAiB;IACjB,iCAAiC;AACrC;AACA;IACI,0BAA0B;IAC1B,qCAAqC;AACzC;;AAEA;IACI,0CAA0C;AAC9C;;AAEA;IACI,WAAW;IACX,YAAY;AAChB;AACA;;IAEI,UAAU;IACV,UAAU;IACV,WAAW;IACX,oBAAoB;AACxB;;AAEA;;IAEI,qBAAqB;AACzB;;AAEA;;IAEI,yDAAyD;AAC7D;;AAEA;;IAEI,8BAA8B;IAC9B,6BAA6B;AACjC;;AAEA;;IAEI,8BAA8B;AAClC;;AAEA,iBAAiB;AACjB;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,2BAA2B;EAC3B,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,+BAA+B;AACjC;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,aAAa;EACb,kBAAkB;EAClB,gBAAgB;EAChB,UAAU;EACV,eAAe;EACf,gBAAgB;EAChB,gCAAgC;AAClC;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,mBAAmB;EACnB,oBAAoB;EACpB,gCAAgC;AAClC;;AAEA;EACE,SAAS;EACT,cAAc;AAChB;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,eAAe;EACf,YAAY;EACZ,YAAY;EACZ,wBAAwB;AAC1B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,eAAe;EACf,eAAe;AACjB;;AAEA;EACE,iBAAiB;EACjB,qBAAqB;AACvB;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ,yBAAyB;EACzB,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,WAAW;AACb;;AAEA;EACE,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;EACjB,6BAA6B;AAC/B;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA,kBAAkB;AAClB;EACE,eAAe;EACf,SAAS;EACT,UAAU;EACV,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,sCAAsC;EACtC,aAAa;EACb,8BAA8B;EAC9B,uBAAuB;EACvB,gCAAgC;AAClC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,OAAO;AACT;;AAEA;EACE,cAAc;EACd,kBAAkB;EAClB,eAAe;AACjB;;AAEA;EACE,SAAS;EACT,eAAe;EACf,YAAY;AACd;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,eAAe;EACf,UAAU;EACV,iBAAiB;EACjB,YAAY;EACZ,wBAAwB;AAC1B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;IACI,wBAAwB;AAC5B;;AAEA;IACI,UAAU;IACV,eAAe;IACf,cAAc;IACd,WAAW;IACX,YAAY;AAChB;;AAEA;IACI,kBAAkB;IAClB,YAAY;IACZ,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,YAAY;AAChB;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,WAAW;IACX,YAAY;AAChB;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,SAAS;AACb;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,SAAS;AACb;;AAEA;IACI,iBAAiB;IACjB,2BAA2B;IAC3B,sBAAsB;IACtB,6BAA6B;IAC7B,+BAA+B;IAC/B,kBAAkB;IAClB,kBAAkB;IAClB,YAAY;IACZ,UAAU;IACV,eAAe;AACnB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,UAAU;AACd;;AAEA;IACI;QACI,sBAAsB;IAC1B;IACA;QACI,sBAAsB;IAC1B;AACJ;;AAEA;;IAEI,wCAAwC;AAC5C;;AAEA;;IAEI,gBAAgB;IAChB,UAAU;IACV,SAAS;AACb;;AAEA;;IAEI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA;;IAEI,kBAAkB;IAClB,SAAS;AACb;;AAEA;;IAEI,qBAAqB;AACzB;;AAEA;;IAEI,kBAAkB;IAClB,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,UAAU;AACd;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,UAAU;AACd;;AAEA;IACI,WAAW;AACf;;AAEA;IACI,WAAW;AACf;AACA;IACI,aAAa;IACb,eAAe;AACnB;AACA;IACI,oCAAoC;IACpC,kBAAkB;IAClB,cAAc;IACd,WAAW;IACX,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;AACtB;AACA;IACI,UAAU;IACV,uBAAuB;AAC3B;AACA;IACI,cAAc;AAClB;AACA;IACI,4CAA4C;AAChD;AACA;IACI,2CAA2C;AAC/C;AACA;IACI,2CAA2C;AAC/C;AACA;IACI,2CAA2C;AAC/C;AACA;IACI,aAAa;IACb,mBAAmB;IACnB,uBAAuB;AAC3B;AACA;IACI,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,cAAc;AAClB;AACA;IACI,UAAU;IACV,mBAAmB;IACnB,uBAAuB;AAC3B;AACA;IACI,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,WAAW;IACX,cAAc;AAClB;AACA;;IAEI,kBAAkB;IAClB,mBAAmB;IACnB,iBAAiB;AACrB;;AAEA;;IAEI,qBAAqB;IACrB,YAAY;AAChB;;AAEA;;IAEI,uBAAuB;AAC3B;;AAEA;;;;IAII,kBAAkB;AACtB;;AAEA;IACI,wFAAwF;AAC5F;;AAEA;IACI,qFAAqF;AACzF;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,6FAA6F;IAC7F,4BAA4B;IAC5B,WAAW;IACX,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,SAAS;IACT,sBAAsB;IACtB,UAAU;IACV,eAAe;AACnB;;AAEA;IACI,yGAAyG;AAC7G;;AAEA;IACI,qFAAqF;AACzF;;AAEA;;;IAGI,mBAAmB;AACvB;;AAEA;;;IAGI,4BAA4B;IAC5B,WAAW;IACX,YAAY;IACZ,cAAc;IACd,WAAW;IACX,2BAA2B;IAC3B,gCAAgC;AACpC;;AAEA;;IAEI,YAAY;AAChB;;AAEA;;IAEI,4FAA4F;IAC5F,oBAAoB;AACxB;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,8FAA8F;IAC9F,qBAAqB;AACzB;;AAEA;IACI,WAAW;IACX,YAAY;IACZ,uBAAuB;IACvB,eAAe;IACf,MAAM;IACN,YAAY;IACZ,YAAY;IACZ,aAAa;AACjB;;AAEA;IACI,UAAU;IACV,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,2BAA2B;IAC3B,kBAAkB;AACtB;;AAEA;IACI,gCAAgC;AACpC;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,eAAe;IACf,aAAa;IACb,8FAA8F;IAC9F,eAAe;IACf,2BAA2B;IAC3B,4BAA4B;IAC5B,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,UAAU;IACV,SAAS;IACT,2BAA2B;IAC3B,iBAAiB;IACjB,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,SAAS;IACT,SAAS;IACT,2BAA2B;IAC3B,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,mCAAmC;IACnC,kBAAkB;AACtB;;AAEA;IACI,mCAAmC;AACvC;;AAEA;IACI,sBAAsB;IACtB,YAAY;IACZ,uDAAuD;IACvD,0BAA0B;IAC1B,yBAAyB;IACzB,eAAe;IACf,mBAAmB;IACnB,4BAA4B;IAC5B,cAAc;IACd,WAAW;IACX,kBAAkB;IAClB,SAAS;IACT,QAAQ;AACZ;;AAEA;IACI,oBAAoB;IACpB,4BAA4B;IAC5B,sBAAsB;IACtB,SAAS;AACb;;AAEA;IACI,8BAA8B;AAClC;;AAEA;IACI,YAAY;IACZ,8BAA8B;IAC9B,oBAAoB;AACxB;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,eAAe;IACf,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,8FAA8F;IAC9F,yBAAyB;IACzB,cAAc;IACd,aAAa;IACb,2BAA2B;IAC3B,YAAY;IACZ,2BAA2B;IAC3B,4BAA4B;IAC5B,YAAY;IACZ,iBAAiB;AACrB;;AAEA;IACI,YAAY;AAChB;AACA;IACI,WAAW;AACf;AACA;IACI,iBAAiB;IACjB,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,wCAAwC;IACxC,gBAAgB;IAChB,mBAAmB;AACvB;;AAEA;IACI,qBAAqB;IACrB,+BAA+B;IAC/B,eAAe;AACnB;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,SAAS;AACb;;AAEA;IACI,YAAY;IACZ,WAAW;IACX,qBAAqB;AACzB;;AAEA;IACI,mNAAmN;AACvN;;AAEA;IACI,mMAAmM;AACvM;;AAEA;IACI,+LAA+L;AACnM;;AAEA;IACI,6DAA6D;AACjE;;AAEA;IACI,6DAA6D;AACjE;;AAEA;IACI,0DAA0D;AAC9D;;AAEA;IACI,gBAAgB;IAChB,eAAe;AACnB;AACA;IACI,aAAa;IACb,mBAAmB;IACnB,QAAQ;IACR,YAAY;AAChB;AACA;IACI,gBAAgB;AACpB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,kBAAkB;IAClB,gBAAgB;IAChB,SAAS;IACT,kBAAkB;IAClB,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,sBAAsB;IACtB,cAAc;IACd,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;AACnB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,sBAAsB;AAC1B;;AAEA;IACI,kBAAkB;IAClB,UAAU;AACd;;AAEA;;IAEI,2BAA2B;AAC/B;;AAEA;IACI,6DAA6D;AACjE;;AAEA;IACI,6DAA6D;AACjE;;AAEA;IACI,0DAA0D;AAC9D;;AAEA;IACI;QACI,2LAA2L;IAC/L;IACA;QACI,2LAA2L;IAC/L;IACA;QACI,2LAA2L;IAC/L;AACJ;;AAEA;IACI;QACI,uMAAuM;IAC3M;IACA;QACI,uMAAuM;IAC3M;IACA;QACI,uMAAuM;IAC3M;AACJ;;AAEA;IACI;QACI,uMAAuM;IAC3M;IACA;QACI,uMAAuM;IAC3M;IACA;QACI,uMAAuM;IAC3M;AACJ;;AAEA;IACI,4BAA4B;IAC5B,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,QAAQ;IACR,aAAa;IACb,qBAAqB;IACrB,kGAAkG;AACtG;;AAEA;IACI,4BAA4B;IAC5B,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,QAAQ;IACR,aAAa;IACb,qBAAqB;IACrB,yFAAyF;AAC7F;AACA;IACI,qFAAqF;AACzF", "sources": ["webpack://gladiatus-helper-bot-ts/./src/styles/variables.css", "webpack://gladiatus-helper-bot-ts/./src/styles/panel.css", "webpack://gladiatus-helper-bot-ts/./src/styles/modal.css", "webpack://gladiatus-helper-bot-ts/./src/styles/notifications.css", "webpack://gladiatus-helper-bot-ts/./src/styles/content.css"], "sourcesContent": ["/* CSS Variables for Gladiatus Helper Bot */\r\n\r\n:root {\r\n  /* Main theme colors */\r\n  --gh-primary: #2c3e50;\r\n  --gh-secondary: #34495e;\r\n  --gh-success: #27ae60;\r\n  --gh-warning: #f39c12;\r\n  --gh-danger: #e74c3c;\r\n  --gh-info: #3498db;\r\n\r\n  /* Background colors */\r\n  --gh-bg-primary: #2c3e50;\r\n  --gh-bg-secondary: #34495e;\r\n  --gh-bg-light: #ecf0f1;\r\n  --gh-bg-dark: #1a252f;\r\n\r\n  /* Text colors */\r\n  --gh-text-primary: #ffffff;\r\n  --gh-text-secondary: #bdc3c7;\r\n  --gh-text-muted: #95a5a6;\r\n  --gh-text-dark: #2c3e50;\r\n\r\n  /* Border colors */\r\n  --gh-border-primary: #34495e;\r\n  --gh-border-secondary: #7f8c8d;\r\n  --gh-border-light: #bdc3c7;\r\n\r\n  /* Item quality colors */\r\n  --gh-item-white: rgba(255, 255, 255, 0.4);\r\n  --gh-item-green: rgba(0, 255, 0, 0.3);\r\n  --gh-item-blue: rgba(81, 89, 247, 0.4);\r\n  --gh-item-purple: rgba(227, 3, 224, 0.4);\r\n  --gh-item-orange: rgba(255, 106, 0, 0.4);\r\n  --gh-item-red: rgba(255, 0, 0, 0.4);\r\n\r\n  /* Spacing */\r\n  --gh-spacing-xs: 4px;\r\n  --gh-spacing-sm: 8px;\r\n  --gh-spacing-md: 16px;\r\n  --gh-spacing-lg: 24px;\r\n  --gh-spacing-xl: 32px;\r\n\r\n  /* Border radius */\r\n  --gh-radius-sm: 4px;\r\n  --gh-radius-md: 8px;\r\n  --gh-radius-lg: 12px;\r\n\r\n  /* Shadows */\r\n  --gh-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  --gh-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  --gh-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);\r\n\r\n  /* Z-index layers */\r\n  --gh-z-dropdown: 1000;\r\n  --gh-z-sticky: 1020;\r\n  --gh-z-fixed: 1030;\r\n  --gh-z-modal-backdrop: 1040;\r\n  --gh-z-modal: 1050;\r\n  --gh-z-popover: 1060;\r\n  --gh-z-tooltip: 1070;\r\n\r\n  /* Transitions */\r\n  --gh-transition-fast: 0.15s ease-in-out;\r\n  --gh-transition-base: 0.3s ease-in-out;\r\n  --gh-transition-slow: 0.5s ease-in-out;\r\n\r\n  /* Font sizes */\r\n  --gh-font-xs: 0.75rem;\r\n  --gh-font-sm: 0.875rem;\r\n  --gh-font-base: 1rem;\r\n  --gh-font-lg: 1.125rem;\r\n  --gh-font-xl: 1.25rem;\r\n  --gh-font-2xl: 1.5rem;\r\n\r\n  /* Font weights */\r\n  --gh-font-normal: 400;\r\n  --gh-font-medium: 500;\r\n  --gh-font-semibold: 600;\r\n  --gh-font-bold: 700;\r\n}", "/* Bot Control Panel Styles */\r\n\r\n.gh-panel {\r\n  position: fixed;\r\n  top: var(--gh-spacing-md);\r\n  right: var(--gh-spacing-md);\r\n  z-index: var(--gh-z-fixed);\r\n  width: 280px;\r\n  background: var(--gh-bg-primary);\r\n  border: 1px solid var(--gh-border-primary);\r\n  border-radius: var(--gh-radius-md);\r\n  color: var(--gh-text-primary);\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  font-size: var(--gh-font-sm);\r\n  box-shadow: var(--gh-shadow-lg);\r\n  user-select: none;\r\n}\r\n\r\n.gh-panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: var(--gh-spacing-md);\r\n  border-bottom: 1px solid var(--gh-border-primary);\r\n  background: var(--gh-bg-secondary);\r\n  border-radius: var(--gh-radius-md) var(--gh-radius-md) 0 0;\r\n}\r\n\r\n.gh-panel-header h3 {\r\n  margin: 0;\r\n  font-size: var(--gh-font-base);\r\n  font-weight: var(--gh-font-semibold);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-panel-content {\r\n  padding: var(--gh-spacing-md);\r\n}\r\n\r\n.gh-status-section {\r\n  margin-bottom: var(--gh-spacing-md);\r\n}\r\n\r\n.gh-status-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--gh-spacing-sm);\r\n  margin-bottom: var(--gh-spacing-sm);\r\n}\r\n\r\n.gh-status-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  background-color: var(--gh-danger);\r\n  transition: background-color var(--gh-transition-base);\r\n}\r\n\r\n.gh-status-dot.active {\r\n  background-color: var(--gh-success);\r\n}\r\n\r\n.gh-status-dot.paused {\r\n  background-color: var(--gh-warning);\r\n}\r\n\r\n.gh-stats-section {\r\n  margin-bottom: var(--gh-spacing-md);\r\n  padding: var(--gh-spacing-sm);\r\n  background: var(--gh-bg-secondary);\r\n  border-radius: var(--gh-radius-sm);\r\n}\r\n\r\n.gh-stat-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: var(--gh-spacing-xs);\r\n}\r\n\r\n.gh-stat-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.gh-stat-label {\r\n  color: var(--gh-text-secondary);\r\n  font-size: var(--gh-font-xs);\r\n}\r\n\r\n.gh-stat-value {\r\n  color: var(--gh-text-primary);\r\n  font-weight: var(--gh-font-medium);\r\n  font-size: var(--gh-font-sm);\r\n}\r\n\r\n.gh-actions-section {\r\n  display: flex;\r\n  gap: var(--gh-spacing-sm);\r\n}\r\n\r\n/* Button Styles */\r\n.gh-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--gh-spacing-sm) var(--gh-spacing-md);\r\n  border: none;\r\n  border-radius: var(--gh-radius-sm);\r\n  font-size: var(--gh-font-sm);\r\n  font-weight: var(--gh-font-medium);\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  transition: all var(--gh-transition-base);\r\n  user-select: none;\r\n}\r\n\r\n.gh-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--gh-shadow-md);\r\n}\r\n\r\n.gh-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.gh-btn-primary {\r\n  background: var(--gh-success);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-btn-primary:hover {\r\n  background: #229954;\r\n}\r\n\r\n.gh-btn-secondary {\r\n  background: var(--gh-bg-secondary);\r\n  color: var(--gh-text-primary);\r\n  border: 1px solid var(--gh-border-primary);\r\n}\r\n\r\n.gh-btn-secondary:hover {\r\n  background: var(--gh-border-primary);\r\n}\r\n\r\n.gh-btn-sm {\r\n  padding: var(--gh-spacing-xs) var(--gh-spacing-sm);\r\n  font-size: var(--gh-font-xs);\r\n  min-width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.gh-btn-danger {\r\n  background: var(--gh-danger);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-btn-danger:hover {\r\n  background: #c0392b;\r\n}\r\n\r\n.gh-btn-warning {\r\n  background: var(--gh-warning);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-btn-warning:hover {\r\n  background: #d68910;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .gh-panel {\r\n    width: 260px;\r\n    top: var(--gh-spacing-sm);\r\n    right: var(--gh-spacing-sm);\r\n  }\r\n\r\n  .gh-actions-section {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .gh-btn {\r\n    width: 100%;\r\n  }\r\n}", "/* Modal Styles */\r\n\r\n.gh-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: var(--gh-z-modal);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  animation: fadeIn 0.3s ease-out forwards;\r\n}\r\n\r\n.gh-modal-content {\r\n  background: var(--gh-bg-primary);\r\n  color: var(--gh-text-primary);\r\n  border-radius: var(--gh-radius-md);\r\n  box-shadow: var(--gh-shadow-lg);\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  transform: scale(0.9);\r\n  animation: scaleIn 0.3s ease-out forwards;\r\n}\r\n\r\n.gh-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: var(--gh-spacing-lg);\r\n  border-bottom: 1px solid var(--gh-border-primary);\r\n  background: var(--gh-bg-secondary);\r\n}\r\n\r\n.gh-modal-header h2 {\r\n  margin: 0;\r\n  font-size: var(--gh-font-xl);\r\n  font-weight: var(--gh-font-semibold);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-modal-close {\r\n  background: none;\r\n  border: none;\r\n  color: var(--gh-text-secondary);\r\n  font-size: var(--gh-font-2xl);\r\n  cursor: pointer;\r\n  padding: 0;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: var(--gh-radius-sm);\r\n  transition: all var(--gh-transition-base);\r\n}\r\n\r\n.gh-modal-close:hover {\r\n  background: var(--gh-bg-primary);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-modal-body {\r\n  padding: var(--gh-spacing-lg);\r\n  overflow-y: auto;\r\n  max-height: 70vh;\r\n}\r\n\r\n.gh-modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: var(--gh-spacing-sm);\r\n  padding: var(--gh-spacing-lg);\r\n  border-top: 1px solid var(--gh-border-primary);\r\n  background: var(--gh-bg-secondary);\r\n}\r\n\r\n/* Statistics Modal Specific Styles */\r\n.gh-stat-group {\r\n  margin-bottom: var(--gh-spacing-lg);\r\n}\r\n\r\n.gh-stat-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.gh-stat-group h3 {\r\n  margin: 0 0 var(--gh-spacing-md) 0;\r\n  font-size: var(--gh-font-lg);\r\n  font-weight: var(--gh-font-semibold);\r\n  color: var(--gh-text-primary);\r\n  border-bottom: 1px solid var(--gh-border-primary);\r\n  padding-bottom: var(--gh-spacing-sm);\r\n}\r\n\r\n.gh-stat-group p {\r\n  margin: 0 0 var(--gh-spacing-sm) 0;\r\n  color: var(--gh-text-secondary);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.gh-stat-group p:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Settings Modal Styles */\r\n.gh-settings-section {\r\n  margin-bottom: var(--gh-spacing-xl);\r\n}\r\n\r\n.gh-settings-section:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.gh-settings-section h3 {\r\n  margin: 0 0 var(--gh-spacing-md) 0;\r\n  font-size: var(--gh-font-lg);\r\n  font-weight: var(--gh-font-semibold);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-form-group {\r\n  margin-bottom: var(--gh-spacing-md);\r\n}\r\n\r\n.gh-form-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.gh-form-label {\r\n  display: block;\r\n  margin-bottom: var(--gh-spacing-xs);\r\n  font-size: var(--gh-font-sm);\r\n  font-weight: var(--gh-font-medium);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-form-input,\r\n.gh-form-select,\r\n.gh-form-textarea {\r\n  width: 100%;\r\n  padding: var(--gh-spacing-sm);\r\n  border: 1px solid var(--gh-border-primary);\r\n  border-radius: var(--gh-radius-sm);\r\n  background: var(--gh-bg-secondary);\r\n  color: var(--gh-text-primary);\r\n  font-size: var(--gh-font-sm);\r\n  transition: border-color var(--gh-transition-base);\r\n}\r\n\r\n.gh-form-input:focus,\r\n.gh-form-select:focus,\r\n.gh-form-textarea:focus {\r\n  outline: none;\r\n  border-color: var(--gh-info);\r\n}\r\n\r\n.gh-form-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--gh-spacing-sm);\r\n}\r\n\r\n.gh-form-checkbox input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: var(--gh-success);\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes scaleIn {\r\n  from {\r\n    transform: scale(0.9);\r\n  }\r\n  to {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes fadeOut {\r\n  from {\r\n    opacity: 1;\r\n  }\r\n  to {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes scaleOut {\r\n  from {\r\n    transform: scale(1);\r\n  }\r\n  to {\r\n    transform: scale(0.9);\r\n  }\r\n}\r\n\r\n.gh-modal.closing {\r\n  animation: fadeOut 0.3s ease-in forwards;\r\n}\r\n\r\n.gh-modal.closing .gh-modal-content {\r\n  animation: scaleOut 0.3s ease-in forwards;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .gh-modal-content {\r\n    width: 95vw;\r\n    max-height: 95vh;\r\n  }\r\n\r\n  .gh-modal-header,\r\n  .gh-modal-body,\r\n  .gh-modal-footer {\r\n    padding: var(--gh-spacing-md);\r\n  }\r\n\r\n  .gh-modal-footer {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .gh-modal-footer .gh-btn {\r\n    width: 100%;\r\n  }\r\n}", "/* Notification System Styles */\r\n\r\n.gh-notifications {\r\n  position: fixed;\r\n  top: var(--gh-spacing-md);\r\n  left: var(--gh-spacing-md);\r\n  z-index: var(--gh-z-modal);\r\n  max-width: 400px;\r\n  pointer-events: none;\r\n}\r\n\r\n.gh-notification {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: var(--gh-spacing-sm);\r\n  padding: var(--gh-spacing-md);\r\n  margin-bottom: var(--gh-spacing-sm);\r\n  background: var(--gh-bg-primary);\r\n  border: 1px solid var(--gh-border-primary);\r\n  border-radius: var(--gh-radius-md);\r\n  box-shadow: var(--gh-shadow-lg);\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  font-size: var(--gh-font-sm);\r\n  color: var(--gh-text-primary);\r\n  pointer-events: auto;\r\n  transform: translateX(-100%);\r\n  animation: slideIn 0.3s ease-out forwards;\r\n}\r\n\r\n.gh-notification:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.gh-notification-content {\r\n  flex: 1;\r\n}\r\n\r\n.gh-notification-content strong {\r\n  display: block;\r\n  margin-bottom: var(--gh-spacing-xs);\r\n  font-weight: var(--gh-font-semibold);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n.gh-notification-content p {\r\n  margin: 0;\r\n  color: var(--gh-text-secondary);\r\n  line-height: 1.4;\r\n}\r\n\r\n.gh-notification-close {\r\n  background: none;\r\n  border: none;\r\n  color: var(--gh-text-secondary);\r\n  font-size: var(--gh-font-lg);\r\n  cursor: pointer;\r\n  padding: 0;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: var(--gh-radius-sm);\r\n  transition: all var(--gh-transition-base);\r\n}\r\n\r\n.gh-notification-close:hover {\r\n  background: var(--gh-bg-secondary);\r\n  color: var(--gh-text-primary);\r\n}\r\n\r\n/* Notification Types */\r\n.gh-notification-info {\r\n  border-left: 4px solid var(--gh-info);\r\n}\r\n\r\n.gh-notification-success {\r\n  border-left: 4px solid var(--gh-success);\r\n}\r\n\r\n.gh-notification-warning {\r\n  border-left: 4px solid var(--gh-warning);\r\n}\r\n\r\n.gh-notification-error {\r\n  border-left: 4px solid var(--gh-danger);\r\n}\r\n\r\n/* Animations */\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideOut {\r\n  from {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n  to {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.gh-notification.removing {\r\n  animation: slideOut 0.3s ease-in forwards;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .gh-notifications {\r\n    left: var(--gh-spacing-sm);\r\n    right: var(--gh-spacing-sm);\r\n    max-width: none;\r\n  }\r\n\r\n  .gh-notification {\r\n    padding: var(--gh-spacing-sm);\r\n  }\r\n}", "/* Gladiatus Helper Bot - Main Styles */\n\n/* Import modular stylesheets */\n@import './variables.css';\n@import './panel.css';\n@import './modal.css';\n@import './notifications.css';\n\n/* Legacy variables for compatibility */\n:root {\n    --main-background: #E7DDBA;\n    --main-border: #c4ac70;\n    --scroll-bar-track-background: #d3c195;\n    --images-folder: 'chrome-extension://__MSG_@@extension_id__/assets/images/';\n    --menu-width: 120px;\n}\n.range_container {\n    --_marker-border-clr: rgba(175, 139, 42, 0.6);\n    --_marker-size: 15px;\n    --_track-heigt: 3px;\n\n    --_tooltip-bg-clr: rgba(0, 0, 0, 0.6);\n    --_tooltip-txt-clr: var(--_marker-border-clr);\n\n    width: 100%;\n    max-width: 600px;\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 18px !important;\n}\n\n.sliders_control {\n    margin-top:  20px;\n    position: relative;\n}\n#pauseList {\n    margin-top: 8px;\n    display: block;\n}\n\n#fileInput {\n    display: none;\n}\n\n/* Style the label to look like a button */\n.custom-file-input {\n    display: inline-block;\n    margin: 0 3px;\n    padding: 0 6px;\n    border: none;\n    background-color: #755229; /* Darker brown */\n    color: white !important;\n    font-weight: normal !important;\n    cursor: pointer;\n    border-radius: 10px;\n    font-size: 12px;\n    box-sizing: border-box;\n    transition: background-color 0.3s;\n}\n.slider-tooltip {\n    width: 26px;\n    position: absolute;\n    top: -2.5rem;\n    left: 0;\n    background-color: var(--_tooltip-bg-clr);\n    color: var(--_tooltip-txt-clr);\n    font-size: 1rem;\n    border-radius: 4px;\n    padding: 0.5rem 0.5rem;\n    text-align: center;\n    translate: -50% 0;\n}\n\ninput[type=range]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    pointer-events: all;\n    width: var(--_marker-size);\n    height: var(--_marker-size);\n    background-color: var(--_marker-border-clr);\n    border-radius: 50%;\n    box-shadow: 0 0 0 1px var(--_marker-border-clr);\n    cursor: pointer;\n}\n\ninput[type=range]::-moz-range-thumb {\n    -webkit-appearance: none;\n    pointer-events: all;\n    width: var(--_marker-size);\n    height: var(--_marker-size);\n    background-color: var(--_marker-border-clr);\n    border-radius: 50%;\n    box-shadow: 0 0 0 1px var(--_marker-border-clr);\n    cursor: pointer;\n}\n\ninput[type=range]::-webkit-slider-thumb:hover {\n    background: #f7f7f7;\n}\n\ninput[type=range]::-webkit-slider-thumb:active {\n    box-shadow: inset 0 0 3px rgba(195, 171, 111, 0.6), 0 0 9px rgba(195, 171, 111, 0.6);\n    -webkit-box-shadow: inset 0 0 3px rgba(195, 171, 111, 0.6), 0 0 9px rgba(195, 171, 111, 0.6);\n}\n\ninput[type=\"range\"] {\n    -webkit-appearance: none;\n    appearance: none;\n    height: var(--_track-heigt);\n    width: -webkit-fill-available;\n    position: absolute;\n    background-color: var(--_marker-border-clr);\n    pointer-events: none;\n}\n\n#fromSlider {\n    height: 0;\n    z-index: 1;\n    border: unset;\n}\n\n.scale {\n    display: flex;\n    justify-content: space-between;\n    margin-top: 2rem;\n    position: relative;\n    width: calc(100% - var(--_marker-size));\n    margin-inline: auto;\n    font-size: 0.8rem;\n}\n\n.scale div {\n    position: absolute;\n    translate: -50% 0;\n}\n\n.scale div::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 50%;\n    translate: -50% -125%;\n    width: 1px;\n    height: 10px;\n    background-color:#666;\n}\n.discord-button {\n    position: absolute;\n    top: 4px;\n    left: 6px;\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    color: white;\n    border-radius: 5px;\n    font-size: 16px;\n    font-weight: bold;\n    transition: background-color 0.3s;\n}\n.discord-button {\n    transform: translate(0, 0);\n    transition: transform .2s ease-in-out;\n}\n\n.discord-button:hover {\n    transform: translate(-4px, 4px) scale(1.3);\n}\n\n.discord-logo {\n    width: 24px;\n    height: 24px;\n}\n.gh-content-scroll,\n.dropdown-list {\n    /* width */\n    /* Track */\n    /* Handle */\n    /* Handle on hover */\n}\n\n.gh-content-scroll::-webkit-scrollbar,\n.dropdown-list::-webkit-scrollbar {\n    width: 7px !important;\n}\n\n.gh-content-scroll::-webkit-scrollbar-track,\n.dropdown-list::-webkit-scrollbar-track {\n    background: var(--scroll-bar-track-background) !important;\n}\n\n.gh-content-scroll::-webkit-scrollbar-thumb,\n.dropdown-list::-webkit-scrollbar-thumb {\n    background: #612d04 !important;\n    border-radius: 5px !important;\n}\n\n.gh-content-scroll::-webkit-scrollbar-thumb:hover,\n.dropdown-list::-webkit-scrollbar-thumb:hover {\n    background: #4F1609 !important;\n}\n\n/* Modal Styles */\n.gh-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0,0,0,0.5);\n  z-index: 10001;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.gh-modal-content {\n  background: #2c3e50;\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  max-width: 600px;\n  width: 90%;\n  max-height: 80%;\n  overflow-y: auto;\n  animation: slideUp 0.3s ease-out;\n}\n\n.gh-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #34495e;\n}\n\n.gh-modal-header h2 {\n  margin: 0;\n  color: #ecf0f1;\n}\n\n.gh-modal-close {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 20px;\n  cursor: pointer;\n  padding: 5px;\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.gh-modal-close:hover {\n  opacity: 1;\n}\n\n.gh-settings-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #34495e;\n  border-radius: 5px;\n}\n\n.gh-settings-section h3 {\n  margin: 0 0 15px 0;\n  color: #3498db;\n  font-size: 16px;\n}\n\n.gh-settings-section label {\n  display: block;\n  margin-bottom: 10px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.gh-settings-section input[type=\"checkbox\"] {\n  margin-right: 8px;\n  transform: scale(1.2);\n}\n\n.gh-settings-section input[type=\"number\"] {\n  margin-left: 10px;\n  padding: 5px;\n  border: 1px solid #7f8c8d;\n  border-radius: 3px;\n  background: #2c3e50;\n  color: white;\n  width: 60px;\n}\n\n.gh-modal-footer {\n  margin-top: 20px;\n  text-align: right;\n  padding-top: 15px;\n  border-top: 1px solid #34495e;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(50px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n/* Notifications */\n.gh-notifications {\n  position: fixed;\n  top: 10px;\n  left: 10px;\n  z-index: 9999;\n  max-width: 400px;\n}\n\n.gh-notification {\n  background: #2c3e50;\n  color: white;\n  padding: 15px;\n  margin-bottom: 10px;\n  border-radius: 5px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.3);\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  animation: slideIn 0.3s ease-out;\n}\n\n.gh-notification-info {\n  border-left: 4px solid #3498db;\n}\n\n.gh-notification-success {\n  border-left: 4px solid #27ae60;\n}\n\n.gh-notification-warning {\n  border-left: 4px solid #f39c12;\n}\n\n.gh-notification-error {\n  border-left: 4px solid #e74c3c;\n}\n\n.gh-notification-content {\n  flex: 1;\n}\n\n.gh-notification-content strong {\n  display: block;\n  margin-bottom: 5px;\n  font-size: 14px;\n}\n\n.gh-notification-content p {\n  margin: 0;\n  font-size: 12px;\n  opacity: 0.9;\n}\n\n.gh-notification-close {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 18px;\n  cursor: pointer;\n  padding: 0;\n  margin-left: 10px;\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.gh-notification-close:hover {\n  opacity: 1;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n.gh-hide {\n    display: none !important;\n}\n\n#char .awesome-button {\n    padding: 0;\n    font-size: 16px;\n    line-height: 1;\n    width: 20px;\n    height: 19px;\n}\n\n#char .put-down {\n    position: absolute;\n    bottom: 23px;\n    right: 44px;\n}\n\n#char .put-up {\n    position: absolute;\n    right: 68px;\n    bottom: 23px;\n}\n\n#char .repair {\n    position: absolute;\n    right: 14px;\n    bottom: 23px;\n    width: 20px;\n    height: 19px;\n}\n\n#char .btn-add-arena-attack-list {\n    position: absolute;\n    right: 55px;\n    top: 21px;\n}\n\n#char .btn-add-circus-attack-list {\n    position: absolute;\n    right: 25px;\n    top: 21px;\n}\n\n#char .add-remove-player {\n    font-weight: bold;\n    animation-name: bubble-text;\n    animation-duration: 5s;\n    animation-fill-mode: forwards;\n    animation-timing-function: ease;\n    position: absolute;\n    text-align: center;\n    width: 230px;\n    left: 50px;\n    font-size: 16px;\n}\n\n#char .add-remove-player.add {\n    color: lawngreen;\n}\n\n#char .add-remove-player.remove {\n    color: red;\n}\n\n@keyframes bubble-text {\n    0% {\n        transform: scale(1, 1);\n    }\n    100% {\n        transform: scale(2, 2);\n    }\n}\n\n.merchant-settings,\n.package-settings {\n    background-color: var(--main-background);\n}\n\n.merchant-settings ul,\n.package-settings ul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n}\n\n.merchant-settings ul li,\n.package-settings ul li {\n    display: inline-block;\n    white-space: nowrap;\n}\n\n.merchant-settings ul li label,\n.package-settings ul li label {\n    position: relative;\n    top: -2px;\n}\n\n.merchant-settings ul li,\n.package-settings ul li {\n    display: inline-block;\n}\n\n.merchant-settings .actions,\n.package-settings .actions {\n    text-align: center;\n    margin-left: -10px;\n    margin-right: -10px;\n}\n\n.merchant-settings {\n    margin: 0 21px 10px;\n}\n\n.merchant-settings li {\n    width: 50%;\n}\n\n.package-settings {\n    margin: 0 0 10px;\n}\n\n.package-settings li {\n    width: 25%;\n}\n\n.package-settings li .pick-level {\n    width: 40px;\n}\n\n.package-settings li .pick-gold {\n    width: 80px;\n}\n.package-settings .item-type-list {\n    display: flex;\n    flex-wrap: wrap;\n}\n.package-settings .item-type-list .item-type {\n    border: 2px solid var(--main-border);\n    border-radius: 3px;\n    display: block;\n    width: 28px;\n    cursor: pointer;\n    opacity: 0.6;\n    margin-right: 2px;\n    position: relative;\n    height: 28px;\n    overflow: hidden;\n    margin-bottom: 2px;\n}\n.package-settings .item-type-list .item-type.active {\n    opacity: 1;\n    border: 2px solid green;\n}\n.package-settings .item-type-list .item-type .item-type-icon {\n    display: block;\n}\n.package-settings .item-type-list .item-type .item-type-icon.icon-big-size {\n    transform: scale(.5) translate(-36px, -36px);\n}\n.package-settings .item-type-list .item-type .item-type-icon.item-i-1-3 {\n    transform: scale(0.5) translate(-4px,-36px);\n}\n.package-settings .item-type-list .item-type .item-type-icon.icon-small-size {\n    transform: scale(0.9) translate(-2px, -3px);\n}\n.package-settings .item-type-list .item-type .item-type-icon.icon-medium-size {\n    transform: scale(0.6) translate(-6px, -6px);\n}\n.package-settings .item-type-list .item-type.with-quality {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n.package-settings .item-type-list .item-type .quality {\n    width: 22px;\n    height: 22px;\n    border-radius: 5px;\n    display: block;\n}\n.package-settings .item-type-list .item-type.separator {\n    width: 1px;\n    border-color: black;\n    background-color: black;\n}\n.package-settings .item-type-list .item-type.with-label {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: auto;\n    padding: 0 5px;\n}\n.workbench-actions,\n.smelter-actions {\n    text-align: center;\n    margin: 0 21px 10px;\n    padding-bottom: 0;\n}\n\n.workbench-actions .workbench_quality,\n.smelter-actions .workbench_quality {\n    display: inline-block;\n    width: 155px;\n}\n\n.package-settings,\n.merchant-settings {\n    padding: 7px !important;\n}\n\n.package-settings .awesome-button,\n.merchant-settings .awesome-button,\n.workbench-actions .awesome-button,\n.smelter-actions .awesome-button {\n    margin-bottom: 5px;\n}\n\n.hammer {\n    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/hammer1.png'), auto;\n}\n\n.burn {\n    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn.png'), auto;\n}\n\n.packageItem {\n    position: relative;\n}\n\n.packageItem .burnable {\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn-off.png');\n    background-repeat: no-repeat;\n    width: 20px;\n    height: 20px;\n    display: block;\n    position: absolute;\n    top: -4px;\n    background-size: cover;\n    left: -1px;\n    cursor: pointer;\n}\n\n.packageItem .burnable.burn-selected {\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/burn-base.png') !important;\n}\n\n.cart {\n    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/cart.png'), auto;\n}\n\n.fixing-inprogress,\n.repair-inprogess,\n.loading {\n    cursor: not-allowed;\n}\n\n.fixing-inprogress::before,\n.repair-inprogess::before,\n.loading::before {\n    background-repeat: no-repeat;\n    width: 100%;\n    height: 100%;\n    display: block;\n    content: '';\n    background-position: center;\n    transform: translate(-2px, -2px);\n}\n\n.fixing-inprogress,\n.loading {\n    opacity: 0.3;\n}\n\n.fixing-inprogress::before,\n.loading::before {\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/spinner.gif');\n    background-size: 50%;\n}\n\n.repair-inprogess {\n    z-index: 999;\n}\n\n.repair-inprogess::before {\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/hammer_ok.gif');\n    background-size: 30px;\n}\n\n.gh-overlay {\n    width: 100%;\n    height: 100%;\n    background-color: black;\n    position: fixed;\n    top: 0;\n    z-index: 998;\n    opacity: 0.5;\n    display: none;\n}\n\n.gh-error-message {\n    color: red;\n    display: block;\n    text-align: center;\n    font-weight: bold;\n    margin-top: 10px;\n}\n\n.inventoryBox .section-header {\n    margin: 15px 21px 0;\n}\n\n.quick-offer {\n    right: -50%;\n    position: relative;\n    transform: translateX(-50%);\n    margin-bottom: 5px;\n}\n\n.underworld-item {\n    background-color: red !important;\n}\n\n.gh-timer {\n    display: inline-block;\n    white-space: nowrap;\n    font-size: 13px;\n    color: yellow;\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/header_bg.png');\n    z-index: 999999;\n    border-top-left-radius: 5px;\n    border-top-right-radius: 5px;\n    min-width: 100px;\n}\n\n.gh-timer.bottom {\n    position: absolute;\n    top: -21px;\n    left: 50%;\n    transform: translateX(-50%);\n    padding: 0px 20px;\n    text-align: center;\n}\n\n.gh-timer.menu-right {\n    position: fixed;\n    bottom: 0;\n    left: 50%;\n    transform: translateX(-50%);\n    padding: 0 10px;\n    text-align: center;\n}\n\n.gh-pause {\n    position: relative;\n    width: 26px;\n    height: 25px;\n    background-color: hsl(32, 50%, 70%);\n    border-radius: 4px;\n}\n\n.gh-pause:hover {\n    background-color: hsl(32, 50%, 80%);\n}\n\n.gh-pause .button {\n    box-sizing: border-box;\n    height: 16px;\n    border-color: transparent transparent transparent green;\n    transition: 100ms all ease;\n    will-change: border-width;\n    cursor: pointer;\n    border-style: solid;\n    border-width: 8px 0 8px 14px;\n    display: block;\n    width: 16px;\n    position: absolute;\n    left: 7px;\n    top: 5px;\n}\n\n.gh-pause.pause .button {\n    border-style: double;\n    border-width: 0px 0 0px 14px;\n    border-left-color: red;\n    left: 6px;\n}\n\n.gh-pause.disabled {\n    cursor: not-allowed !important;\n}\n\n.gh-pause.disabled .button {\n    opacity: 0.3;\n    cursor: not-allowed !important;\n    pointer-events: none;\n}\n\n#selectedSmeltSummary {\n    font-size: 12px;\n    font-weight: normal;\n}\n\n.single_char_item_bg.repair_slot_bg {\n    display: none;\n}\n\n.gh-items-section {\n    position: fixed;\n    left: 0;\n    top: 50%;\n    width: 141px;\n    z-index: 999;\n}\n\n.gh-items-section .gh-items-section-header {\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/header_bg.png');\n    color: #dcbb96 !important;\n    padding: 0 2px;\n    display: flex;\n    justify-content: flex-start;\n    height: 18px;\n    border-top-left-radius: 3px;\n    border-top-right-radius: 4px;\n    cursor: move;\n    user-select: none;\n}\n\n.gh-items-section .gh-items-section-header label, .popup-header {\n    cursor: move;\n}\n.popup-header {\n    width: 100%;\n}\n.gh-items-section .gh-items-section-content {\n    max-height: 100px;\n    min-height: 52px;\n    left: 0;\n    top: 50%;\n    background-color: var(--main-background);\n    overflow-y: auto;\n    padding-bottom: 3px;\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div {\n    transform: scale(0.5);\n    margin: -22px -17px -25px -14px;\n    cursor: pointer;\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .outbid {\n    position: absolute;\n    width: 30px;\n    height: 30px;\n    border: 0;\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .outbid.quest_slot_button_finish {\n    bottom: -2px;\n    right: -2px;\n    transform: scale(0.8);\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-white {\n    filter: drop-shadow(4px 4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(4px -4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(-4px -4px 4px rgba(255, 255, 255, 0.4)) drop-shadow(-4px 4px 4px rgba(255, 255, 255, 0.4));\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-green {\n    filter: drop-shadow(5px 5px 5px var(--gh-item-green)) drop-shadow(5px -5px 5px var(--gh-item-green)) drop-shadow(-5px -5px 5px var(--gh-item-green)) drop-shadow(-5px 5px 5px var(--gh-item-green));\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-blue {\n    filter: drop-shadow(5px 5px 5px var(--gh-item-blue)) drop-shadow(5px -5px 5px var(--gh-item-blue)) drop-shadow(-5px -5px 5px var(--gh-item-blue)) drop-shadow(-5px 5px 5px var(--gh-item-blue));\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-purple {\n    animation: purpleShadowAnimation 2s linear infinite alternate;\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-orange {\n    animation: orangeShadowAnimation 2s linear infinite alternate;\n}\n\n.gh-items-section .gh-items-section-content .auction_item_div .item-quality-red {\n    animation: redShadowAnimation 2s linear infinite alternate;\n}\n\n.gh-info {\n    margin-left: 3px;\n    cursor: pointer;\n}\n.row {\n    display: flex;\n    flex-direction: row;\n    gap: 4px;\n    padding: 6px;\n}\n.row-wrapper {\n    overflow: hidden;\n}\n\n.ml-5 {\n    margin-left: 5px;\n}\n\n.mr-5 {\n    margin-right: 5px;\n}\n\nul.dropdown-list {\n    visibility: hidden;\n    list-style: none;\n    margin: 0;\n    position: absolute;\n    top: 100%;\n    left: -2px;\n    background: white;\n    padding: 0;\n    border: 1px solid #ccc;\n    z-index: 99999;\n    max-height: 200px;\n    overflow-y: auto;\n}\n\nul.dropdown-list.open {\n    visibility: visible;\n}\n\nul.dropdown-list .dropdown-item {\n    display: block;\n    padding: 2px 5px;\n    white-space: nowrap;\n    cursor: initial;\n}\n\nul.dropdown-list .dropdown-item.has-icon {\n    max-height: 31px;\n}\n\nul.dropdown-list .dropdown-item.has-icon > div {\n    display: inline-block;\n    transform: scale(0.75);\n}\n\nul.dropdown-list .dropdown-item.has-icon .dropdown-item-text {\n    position: relative;\n    top: -10px;\n}\n\nul.dropdown-list .dropdown-item:hover,\nul.dropdown-list .dropdown-item.selected {\n    background-color: lightblue;\n}\n\n.gh-animate-item-color [data-quality=\"2\"] {\n    animation: purpleShadowAnimation 2s linear infinite alternate;\n}\n\n.gh-animate-item-color [data-quality=\"3\"] {\n    animation: orangeShadowAnimation 2s linear infinite alternate;\n}\n\n.gh-animate-item-color [data-quality=\"4\"] {\n    animation: redShadowAnimation 2s linear infinite alternate;\n}\n\n@keyframes redShadowAnimation {\n    0% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-red)) drop-shadow(2px -2px 2px var(--gh-item-red)) drop-shadow(-2px -2px 2px var(--gh-item-red)) drop-shadow(-2px 2px 2px var(--gh-item-red));\n    }\n    50% {\n        filter: drop-shadow(4px 4px 4px var(--gh-item-red)) drop-shadow(4px -4px 4px var(--gh-item-red)) drop-shadow(-4px -4px 4px var(--gh-item-red)) drop-shadow(-4px 4px 4px var(--gh-item-red));\n    }\n    100% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-red)) drop-shadow(2px -2px 2px var(--gh-item-red)) drop-shadow(-2px -2px 2px var(--gh-item-red)) drop-shadow(-2px 2px 2px var(--gh-item-red));\n    }\n}\n\n@keyframes orangeShadowAnimation {\n    0% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-orange)) drop-shadow(2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px 2px 2px var(--gh-item-orange));\n    }\n    50% {\n        filter: drop-shadow(4px 4px 4px var(--gh-item-orange)) drop-shadow(4px -4px 4px var(--gh-item-orange)) drop-shadow(-4px -4px 4px var(--gh-item-orange)) drop-shadow(-4px 4px 4px var(--gh-item-orange));\n    }\n    100% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-orange)) drop-shadow(2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px -2px 2px var(--gh-item-orange)) drop-shadow(-2px 2px 2px var(--gh-item-orange));\n    }\n}\n\n@keyframes purpleShadowAnimation {\n    0% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-purple)) drop-shadow(2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px 2px 2px var(--gh-item-purple));\n    }\n    50% {\n        filter: drop-shadow(4px 4px 4px var(--gh-item-purple)) drop-shadow(4px -4px 4px var(--gh-item-purple)) drop-shadow(-4px -4px 4px var(--gh-item-purple)) drop-shadow(-4px 4px 4px var(--gh-item-purple));\n    }\n    100% {\n        filter: drop-shadow(2px 2px 2px var(--gh-item-purple)) drop-shadow(2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px -2px 2px var(--gh-item-purple)) drop-shadow(-2px 2px 2px var(--gh-item-purple));\n    }\n}\n\n.gh-icon-auction-house {\n    background-repeat: no-repeat;\n    background-size: cover;\n    width: 13px;\n    height: 13px;\n    position: relative;\n    top: 2px;\n    margin: 0 2px;\n    display: inline-block;\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/auction-house.png');\n}\n\n.gh-icon-shop {\n    background-repeat: no-repeat;\n    background-size: cover;\n    width: 13px;\n    height: 13px;\n    position: relative;\n    top: 2px;\n    margin: 0 2px;\n    display: inline-block;\n    background-image: url('chrome-extension://__MSG_@@extension_id__/assets/images/shop.png');\n}\n.shop-cart {\n    cursor: url('chrome-extension://__MSG_@@extension_id__/assets/images/cart.png'), auto;\n}\n"], "names": [], "sourceRoot": ""}