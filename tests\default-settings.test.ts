// Tests for DefaultSettingsFactory

import { DefaultSettingsFactory } from '../src/utils/default-settings';
import { BotSettings } from '../src/types';

describe('DefaultSettingsFactory', () => {
  describe('createDefaultSettings', () => {
    it('should create complete default settings', () => {
      const defaultSettings = DefaultSettingsFactory.createDefaultSettings();
      
      // Check that all main sections exist
      expect(defaultSettings.general).toBeDefined();
      expect(defaultSettings.heal).toBeDefined();
      expect(defaultSettings.hideGold).toBeDefined();
      expect(defaultSettings.packages).toBeDefined();
      expect(defaultSettings.costumes).toBeDefined();
      expect(defaultSettings.turma).toBeDefined();
      expect(defaultSettings.arena).toBeDefined();
      expect(defaultSettings.expeditions).toBeDefined();
      expect(defaultSettings.dungeon).toBeDefined();
      expect(defaultSettings.event).toBeDefined();
      expect(defaultSettings.underworld).toBeDefined();
      expect(defaultSettings.forge).toBeDefined();
      expect(defaultSettings.repair).toBeDefined();
      expect(defaultSettings.quest).toBeDefined();
      expect(defaultSettings.market).toBeDefined();
      expect(defaultSettings.shop).toBeDefined();
      expect(defaultSettings.bank).toBeDefined();
      expect(defaultSettings.guild).toBeDefined();
    });

    it('should have sensible default values', () => {
      const defaultSettings = DefaultSettingsFactory.createDefaultSettings();
      
      // Check some key default values
      expect(defaultSettings.general.selectedBackpack).toBe(1);
      expect(defaultSettings.heal.enabled).toBe(false);
      expect(defaultSettings.heal.minHealth).toBe(80);
      expect(defaultSettings.arena.enabled).toBe(false);
      expect(defaultSettings.arena.prioritizeChance).toBe(80);
      expect(defaultSettings.quest.enabled).toBe(false);
      expect(defaultSettings.market.enabled).toBe(false);
      expect(defaultSettings.forge.enabled).toBe(false);
      expect(defaultSettings.repair.enabled).toBe(false);
    });

    it('should have all required properties for new modules', () => {
      const defaultSettings = DefaultSettingsFactory.createDefaultSettings();
      
      // Quest management
      expect(defaultSettings.quest.enabled).toBeDefined();
      expect(defaultSettings.quest.autoComplete).toBeDefined();
      expect(defaultSettings.quest.dailyLimit).toBeDefined();
      expect(defaultSettings.quest.rules).toBeDefined();
      
      // Market operations
      expect(defaultSettings.market.enabled).toBeDefined();
      expect(defaultSettings.market.checkInterval).toBeDefined();
      expect(defaultSettings.market.autoBuy).toBeDefined();
      expect(defaultSettings.market.autoSell).toBeDefined();
      expect(defaultSettings.market.auction).toBeDefined();
      
      // Forge automation
      expect(defaultSettings.forge.enabled).toBeDefined();
      expect(defaultSettings.forge.autoForge).toBeDefined();
      expect(defaultSettings.forge.autoSmelt).toBeDefined();
      expect(defaultSettings.forge.maxGoldSpend).toBeDefined();
      
      // Repair automation
      expect(defaultSettings.repair.enabled).toBeDefined();
      expect(defaultSettings.repair.repairThreshold).toBeDefined();
      expect(defaultSettings.repair.maxCostPerItem).toBeDefined();
    });
  });

  describe('mergeWithDefaults', () => {
    it('should merge user settings with defaults', () => {
      const userSettings = {
        general: {
          selectedBackpack: 2,
          loginDelay: 10
        },
        arena: {
          enabled: true,
          prioritizeChance: 90
        }
      };

      const mergedSettings = DefaultSettingsFactory.mergeWithDefaults(userSettings);
      
      // Should contain user settings
      expect(mergedSettings.general.selectedBackpack).toBe(2);
      expect(mergedSettings.general.loginDelay).toBe(10);
      expect(mergedSettings.arena.enabled).toBe(true);
      expect(mergedSettings.arena.prioritizeChance).toBe(90);
      
      // Should contain defaults for missing properties
      expect(mergedSettings.general.maxRandomDelay).toBe(10); // default value
      expect(mergedSettings.arena.loseLimit).toBe(3); // default value
      expect(mergedSettings.heal).toBeDefined(); // entire missing section
      expect(mergedSettings.quest).toBeDefined(); // new module settings
      expect(mergedSettings.market).toBeDefined(); // new module settings
    });

    it('should handle nested object merging', () => {
      const userSettings = {
        market: {
          enabled: true,
          autoBuy: {
            enabled: true,
            maxPrice: 5000
          }
        }
      };

      const mergedSettings = DefaultSettingsFactory.mergeWithDefaults(userSettings);
      
      // Should merge nested objects
      expect(mergedSettings.market.enabled).toBe(true);
      expect(mergedSettings.market.autoBuy.enabled).toBe(true);
      expect(mergedSettings.market.autoBuy.maxPrice).toBe(5000);
      
      // Should keep defaults for missing nested properties
      expect(mergedSettings.market.autoBuy.maxGoldSpend).toBe(50000); // default
      expect(mergedSettings.market.autoSell).toBeDefined(); // missing nested object
    });

    it('should handle array properties correctly', () => {
      const userSettings = {
        arena: {
          attackPlayers: ['Player1', 'Player2']
        },
        quest: {
          rules: [
            {
              priority: 1,
              questType: 'combat',
              minReward: 1000,
              maxDifficulty: 'medium' as const,
              autoComplete: true
            }
          ]
        }
      };

      const mergedSettings = DefaultSettingsFactory.mergeWithDefaults(userSettings);
      
      // Should preserve user arrays
      expect(mergedSettings.arena.attackPlayers).toEqual(['Player1', 'Player2']);
      expect(mergedSettings.quest.rules).toHaveLength(1);
      expect(mergedSettings.quest.rules[0].priority).toBe(1);
      
      // Should have default arrays for missing properties
      expect(mergedSettings.arena.ignorePlayers).toEqual([]); // default empty array
    });

    it('should handle empty user settings', () => {
      const mergedSettings = DefaultSettingsFactory.mergeWithDefaults({});
      
      // Should be identical to default settings
      const defaultSettings = DefaultSettingsFactory.createDefaultSettings();
      expect(mergedSettings).toEqual(defaultSettings);
    });

    it('should not modify the original objects', () => {
      const userSettings = {
        arena: {
          enabled: true
        }
      };
      
      const originalUserSettings = JSON.parse(JSON.stringify(userSettings));
      const mergedSettings = DefaultSettingsFactory.mergeWithDefaults(userSettings);
      
      // Original user settings should be unchanged
      expect(userSettings).toEqual(originalUserSettings);
      
      // Merged settings should be different
      expect(mergedSettings).not.toEqual(userSettings);
      expect(mergedSettings.arena.enabled).toBe(true);
      expect(mergedSettings.heal).toBeDefined(); // Added by merge
    });
  });
});
