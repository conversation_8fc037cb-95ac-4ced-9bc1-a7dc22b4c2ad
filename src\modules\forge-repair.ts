// Forge & Repair Module - Handles automatic forging, smelting, and item repair

import { ForgeAutomationSettings, RepairAutomationSettings, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface ForgeableItem {
  id: string;
  name: string;
  quality: ItemQuality;
  level: number;
  materials: string[];
  canForge: boolean;
  forgeCost: number;
}

export interface RepairableItem {
  id: string;
  name: string;
  durability: number;
  maxDurability: number;
  repairCost: number;
  canRepair: boolean;
}

export interface SmeltableItem {
  id: string;
  name: string;
  quality: ItemQuality;
  level: number;
  smeltValue: number;
  canSmelt: boolean;
}

export class ForgeRepairModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private itemsForged: number = 0;
  private itemsRepaired: number = 0;
  private itemsSmelted: number = 0;
  private goldSpent: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(forgeSettings: ForgeAutomationSettings, repairSettings: RepairAutomationSettings): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.itemsForged = 0;
    this.itemsRepaired = 0;
    this.itemsSmelted = 0;
    this.goldSpent = 0;
    
    console.log('Forge & Repair: Starting forge and repair automation');
    
    try {
      if (forgeSettings.enabled) {
        await this.performForgeOperations(forgeSettings);
      }
      
      if (repairSettings.enabled) {
        await this.performRepairOperations(repairSettings);
      }
    } catch (error) {
      console.error('Forge & Repair: Error in automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Forge & Repair: Stopped automation');
  }

  private async performForgeOperations(settings: ForgeAutomationSettings): Promise<void> {
    console.log('Forge & Repair: Starting forge operations');
    
    await this.navigateToForge();
    
    while (this.isRunning && this.shouldContinueForging(settings)) {
      try {
        // Handle smelting first if enabled
        if (settings.autoSmelt) {
          await this.performSmelting(settings);
        }
        
        // Handle forging
        if (settings.autoForge) {
          await this.performForging(settings);
        }
        
        await this.delay(10000);
        
      } catch (error) {
        console.error('Forge & Repair: Error in forge operations:', error);
        await this.delay(15000);
      }
    }
  }

  private async performRepairOperations(settings: RepairAutomationSettings): Promise<void> {
    console.log('Forge & Repair: Starting repair operations');
    
    await this.navigateToRepair();
    
    while (this.isRunning && this.shouldContinueRepairing(settings)) {
      try {
        const repairableItems = await this.getRepairableItems();
        const itemsToRepair = this.filterItemsForRepair(repairableItems, settings);
        
        for (const item of itemsToRepair) {
          if (!this.isRunning) break;
          
          if (this.goldSpent + item.repairCost <= settings.maxGoldSpend) {
            await this.repairItem(item);
            this.itemsRepaired++;
            this.goldSpent += item.repairCost;
            
            await this.delay(2000);
          }
        }
        
        await this.delay(30000);
        
      } catch (error) {
        console.error('Forge & Repair: Error in repair operations:', error);
        await this.delay(15000);
      }
    }
  }

  private async performSmelting(settings: ForgeAutomationSettings): Promise<void> {
    console.log('Forge & Repair: Performing smelting operations');
    
    const smeltableItems = await this.getSmeltableItems();
    const itemsToSmelt = this.filterItemsForSmelting(smeltableItems, settings);
    
    for (const item of itemsToSmelt) {
      if (!this.isRunning) break;
      
      await this.smeltItem(item);
      this.itemsSmelted++;
      
      await this.delay(1500);
    }
  }

  private async performForging(settings: ForgeAutomationSettings): Promise<void> {
    console.log('Forge & Repair: Performing forging operations');
    
    const forgeableItems = await this.getForgeableItems();
    const itemsToForge = this.filterItemsForForging(forgeableItems, settings);
    
    for (const item of itemsToForge) {
      if (!this.isRunning) break;
      
      if (this.goldSpent + item.forgeCost <= settings.maxGoldSpend) {
        await this.forgeItem(item);
        this.itemsForged++;
        this.goldSpent += item.forgeCost;
        
        await this.delay(3000);
      }
    }
  }

  private async navigateToForge(): Promise<void> {
    if (!window.location.href.includes('mod=forge')) {
      console.log('Forge & Repair: Navigating to forge');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=forge`;
      
      await this.waitForPageLoad();
    }
  }

  private async navigateToRepair(): Promise<void> {
    if (!window.location.href.includes('mod=repair')) {
      console.log('Forge & Repair: Navigating to repair shop');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=repair`;
      
      await this.waitForPageLoad();
    }
  }

  private async getForgeableItems(): Promise<ForgeableItem[]> {
    const items: ForgeableItem[] = [];
    const itemElements = document.querySelectorAll('.forgeable_item, .forge-recipe');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parseForgeableItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Forge & Repair: Error parsing forgeable item:', error);
      }
    }
    
    return items;
  }

  private async parseForgeableItem(element: HTMLElement, index: number): Promise<ForgeableItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const levelElement = element.querySelector('.item_level, .level');
    const costElement = element.querySelector('.forge_cost, .cost');
    const forgeButton = element.querySelector('.forge_button, [data-action="forge"]');
    const materialsElements = element.querySelectorAll('.material, .required-material');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    const forgeCost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
    
    // Parse materials
    const materials: string[] = [];
    materialsElements.forEach(mat => {
      const matText = mat.textContent?.trim();
      if (matText) materials.push(matText);
    });
    
    // Determine quality from CSS classes
    let quality: ItemQuality = ItemQuality.WHITE;
    const qualityClass = element.className;
    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;
    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;
    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;
    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;
    else if (qualityClass.includes('red')) quality = ItemQuality.RED;
    
    return {
      id: index.toString(),
      name,
      quality,
      level,
      materials,
      canForge: !!(forgeButton && !forgeButton.hasAttribute('disabled')),
      forgeCost
    };
  }

  private async getRepairableItems(): Promise<RepairableItem[]> {
    const items: RepairableItem[] = [];
    const itemElements = document.querySelectorAll('.repairable_item, .damaged-item');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parseRepairableItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Forge & Repair: Error parsing repairable item:', error);
      }
    }
    
    return items;
  }

  private async parseRepairableItem(element: HTMLElement, index: number): Promise<RepairableItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const durabilityElement = element.querySelector('.durability, .condition');
    const costElement = element.querySelector('.repair_cost, .cost');
    const repairButton = element.querySelector('.repair_button, [data-action="repair"]');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const repairCost = parseInt(costElement?.textContent?.replace(/\D/g, '') || '0');
    
    // Parse durability
    const durabilityText = durabilityElement?.textContent?.trim() || '100/100';
    const durabilityMatch = durabilityText.match(/(\d+)\/(\d+)/);
    const durability = durabilityMatch ? parseInt(durabilityMatch[1]) : 100;
    const maxDurability = durabilityMatch ? parseInt(durabilityMatch[2]) : 100;
    
    return {
      id: index.toString(),
      name,
      durability,
      maxDurability,
      repairCost,
      canRepair: !!(repairButton && !repairButton.hasAttribute('disabled'))
    };
  }

  private async getSmeltableItems(): Promise<SmeltableItem[]> {
    const items: SmeltableItem[] = [];
    const itemElements = document.querySelectorAll('.smeltable_item, .inventory-item');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parseSmeltableItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Forge & Repair: Error parsing smeltable item:', error);
      }
    }
    
    return items;
  }

  private async parseSmeltableItem(element: HTMLElement, index: number): Promise<SmeltableItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const levelElement = element.querySelector('.item_level, .level');
    const valueElement = element.querySelector('.smelt_value, .value');
    const smeltButton = element.querySelector('.smelt_button, [data-action="smelt"]');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    const smeltValue = parseInt(valueElement?.textContent?.replace(/\D/g, '') || '0');
    
    // Determine quality from CSS classes
    let quality: ItemQuality = ItemQuality.WHITE;
    const qualityClass = element.className;
    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;
    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;
    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;
    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;
    else if (qualityClass.includes('red')) quality = ItemQuality.RED;
    
    return {
      id: index.toString(),
      name,
      quality,
      level,
      smeltValue,
      canSmelt: !!(smeltButton && !smeltButton.hasAttribute('disabled'))
    };
  }

  private filterItemsForForging(items: ForgeableItem[], settings: ForgeAutomationSettings): ForgeableItem[] {
    return items.filter(item => {
      // Check if can forge
      if (!item.canForge) return false;
      
      // Check cost limit
      if (item.forgeCost > settings.maxCostPerItem) return false;
      
      // Check quality filter
      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
        return false;
      }
      
      // Check level range
      if (settings.minLevel && item.level < settings.minLevel) return false;
      if (settings.maxLevel && item.level > settings.maxLevel) return false;
      
      // Check item type filter
      if (settings.itemTypes.length > 0) {
        const typeMatch = settings.itemTypes.some(type => 
          item.name.toLowerCase().includes(type.toLowerCase())
        );
        if (!typeMatch) return false;
      }
      
      return true;
    });
  }

  private filterItemsForRepair(items: RepairableItem[], settings: RepairAutomationSettings): RepairableItem[] {
    return items.filter(item => {
      // Check if can repair
      if (!item.canRepair) return false;
      
      // Check durability threshold
      const durabilityPercent = (item.durability / item.maxDurability) * 100;
      if (durabilityPercent > settings.repairThreshold) return false;
      
      // Check cost limit
      if (item.repairCost > settings.maxCostPerItem) return false;
      
      // Check item name filter
      if (settings.itemFilter.length > 0) {
        const nameMatch = settings.itemFilter.some(filter => 
          item.name.toLowerCase().includes(filter.toLowerCase())
        );
        if (!nameMatch) return false;
      }
      
      return true;
    });
  }

  private filterItemsForSmelting(items: SmeltableItem[], settings: ForgeAutomationSettings): SmeltableItem[] {
    return items.filter(item => {
      // Check if can smelt
      if (!item.canSmelt) return false;
      
      // Check minimum smelt value
      if (item.smeltValue < settings.minSmeltValue) return false;
      
      // Check quality - usually smelt lower quality items
      if (settings.smeltQualityFilter.length > 0 && !settings.smeltQualityFilter.includes(item.quality)) {
        return false;
      }
      
      return true;
    });
  }

  private async forgeItem(item: ForgeableItem): Promise<void> {
    console.log(`Forge & Repair: Forging ${item.name} for ${item.forgeCost} gold`);
    
    const itemElement = document.querySelector(`.forgeable_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const forgeButton = itemElement.querySelector('.forge_button, [data-action="forge"]');
    if (forgeButton && !forgeButton.hasAttribute('disabled')) {
      (forgeButton as HTMLElement).click();
      
      // Handle confirmation if present
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_forge, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private async repairItem(item: RepairableItem): Promise<void> {
    console.log(`Forge & Repair: Repairing ${item.name} for ${item.repairCost} gold`);
    
    const itemElement = document.querySelector(`.repairable_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const repairButton = itemElement.querySelector('.repair_button, [data-action="repair"]');
    if (repairButton && !repairButton.hasAttribute('disabled')) {
      (repairButton as HTMLElement).click();
      
      // Handle confirmation if present
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_repair, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private async smeltItem(item: SmeltableItem): Promise<void> {
    console.log(`Forge & Repair: Smelting ${item.name} for ${item.smeltValue} materials`);
    
    const itemElement = document.querySelector(`.smeltable_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const smeltButton = itemElement.querySelector('.smelt_button, [data-action="smelt"]');
    if (smeltButton && !smeltButton.hasAttribute('disabled')) {
      (smeltButton as HTMLElement).click();
      
      // Handle confirmation if present
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_smelt, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private shouldContinueForging(settings: ForgeAutomationSettings): boolean {
    // Check gold spending limit
    if (this.goldSpent >= settings.maxGoldSpend) {
      console.log('Forge & Repair: Gold spending limit reached for forging');
      return false;
    }
    
    // Check daily limits
    if (settings.dailyForgeLimit > 0 && this.itemsForged >= settings.dailyForgeLimit) {
      console.log('Forge & Repair: Daily forge limit reached');
      return false;
    }
    
    return true;
  }

  private shouldContinueRepairing(settings: RepairAutomationSettings): boolean {
    // Check gold spending limit
    if (this.goldSpent >= settings.maxGoldSpend) {
      console.log('Forge & Repair: Gold spending limit reached for repairs');
      return false;
    }
    
    return true;
  }

  async getForgeRepairStatus(): Promise<any> {
    return {
      isRunning: this.isRunning,
      itemsForged: this.itemsForged,
      itemsRepaired: this.itemsRepaired,
      itemsSmelted: this.itemsSmelted,
      goldSpent: this.goldSpent,
      lastUpdate: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
