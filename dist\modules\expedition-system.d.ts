import { ExpeditionSettings, DungeonSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface ExpeditionLocation {
    id: string;
    name: string;
    level: number;
    dungeonPoints: number;
    isAvailable: boolean;
    cooldownRemaining: number;
}
export interface DungeonInfo {
    name: string;
    difficulty: string;
    isAdvanced: boolean;
    hasBoss: boolean;
    bossName?: string;
    requiresKey: boolean;
}
export declare class ExpeditionSystemModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private expeditionCount;
    private dungeonCount;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(expeditionSettings: ExpeditionSettings, dungeonSettings: DungeonSettings): Promise<void>;
    stop(): Promise<void>;
    private performExpeditions;
    private performDungeons;
    private navigateToExpedition;
    private navigateToDungeon;
    private getAvailableLocations;
    private parseExpeditionLocation;
    private isValidExpeditionLocation;
    private selectBestLocation;
    private startExpedition;
    private removeCooldownWithHourglass;
    private waitForExpeditionCompletion;
    private collectExpeditionBonuses;
    private getDungeonInfo;
    private enterDungeon;
    private completeDungeon;
    private waitForDungeonCompletion;
    private shouldContinueExpeditions;
    private shouldContinueDungeons;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=expedition-system.d.ts.map