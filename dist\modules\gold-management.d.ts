import { HideGoldSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface GoldHidingLocation {
    type: 'guild' | 'shop' | 'market' | 'training';
    name: string;
    isAvailable: boolean;
    capacity: number;
    currentAmount: number;
}
export interface MarketItem {
    id: string;
    name: string;
    price: number;
    quality: string;
    seller: string;
    canBuy: boolean;
}
export declare class GoldManagementModule {
    private urlHelper;
    private storageManager;
    private isProcessing;
    private currentGold;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    manageGold(settings: HideGoldSettings): Promise<void>;
    private getCurrentGold;
    private shouldHideGold;
    private hideGold;
    private hideGoldInLocation;
    private hideGoldInGuild;
    private hideGoldInShop;
    private hideGoldInMarket;
    private hideGoldInTraining;
    private getShopItems;
    private getMarketItems;
    private parseShopItem;
    private parseMarketItem;
    private isValidPurchase;
    private buyShopItem;
    private buyMarketItem;
    private trainSkill;
    private navigateToGuild;
    private navigateToShop;
    private navigateToMarket;
    private navigateToTraining;
    getGoldStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=gold-management.d.ts.map