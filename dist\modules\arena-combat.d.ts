import { ArenaSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface ArenaTarget {
    id: string;
    name: string;
    level: number;
    points: number;
    winChance: number;
    isGuildMember: boolean;
    canAttack: boolean;
}
export declare class ArenaCombatModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private attackCount;
    private loseCount;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: ArenaSettings): Promise<void>;
    stop(): Promise<void>;
    private navigateToArena;
    private performArenaLoop;
    private getAvailableTargets;
    private parseTargetElement;
    private isValidTarget;
    private selectBestTarget;
    private attackTarget;
    private processAttackResult;
    private shouldContinueAttacking;
    private calculateWinChance;
    private isGuildMember;
    private getPlayerLevel;
    private getPlayerPoints;
    private waitForPageLoad;
    private waitForAttackResult;
    private parseAttackResult;
    private updateStatistics;
    private delay;
}
//# sourceMappingURL=arena-combat.d.ts.map