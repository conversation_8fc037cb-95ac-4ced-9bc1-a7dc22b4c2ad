import { BotSettings, BotStatus, ExtensionStorage } from '../types';
export declare class StorageManager {
    private readonly STORAGE_KEYS;
    /**
     * Check if Chrome storage API is available
     */
    private isChromeStorageAvailable;
    /**
     * Fallback method to get settings from localStorage
     */
    private getSettingsFromLocalStorage;
    /**
     * Fallback method to save settings to localStorage
     */
    private saveSettingsToLocalStorage;
    /**
     * Get bot settings from storage
     */
    getSettings(): Promise<BotSettings | null>;
    /**
     * Save bot settings to storage
     */
    saveSettings(settings: Partial<BotSettings>): Promise<void>;
    /**
     * Get bot status from storage
     */
    getBotStatus(): Promise<BotStatus | null>;
    /**
     * Save bot status to storage
     */
    saveBotStatus(status: BotStatus): Promise<void>;
    /**
     * Get all extension data from storage
     */
    getAllData(): Promise<Partial<ExtensionStorage>>;
    /**
     * Clear all extension data from storage
     */
    clearAllData(): Promise<void>;
    /**
     * Get storage usage information
     */
    getStorageUsage(): Promise<{
        bytesInUse: number;
        quota: number;
    }>;
    /**
     * Deep merge two objects
     */
    private deepMerge;
    /**
     * Export settings as JSON string
     */
    exportSettings(): Promise<string>;
    /**
     * Import settings from JSON string
     */
    importSettings(jsonData: string): Promise<void>;
}
//# sourceMappingURL=storage-manager.d.ts.map