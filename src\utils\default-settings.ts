// Default Settings Factory - Provides default values for all bot settings

import {
  BotSettings,
  ItemQuality,
  UnderworldMode,
  UnderworldDifficulty,
  CooldownRemovalOption,
  OperationSpeed,
  RenewShopOption,
  HideGoldLocation,
  PackFilterType,
  PackDuration
} from '../types';

export class DefaultSettingsFactory {
  static createDefaultSettings(): BotSettings {
    return {
      general: {
        selectedBackpack: 1,
        maxRandomDelay: 10,
        loginDelay: 5,
        autoCollectDaily: true,
        autoCollectGodOils: true,
        showBiddenBox: true,
        showShopBox: true,
        highlightUnderworldItems: true,
        shopMinQuality: 1
      },
      heal: {
        enabled: false,
        minHealth: 80,
        healCervisia: true,
        healEggs: true,
        selectedEggs: [1, 2, 3],
        buyFood: false,
        buyFoodNeeded: false,
        renewShop: RenewShopOption.NOT_ALLOW,
        autoStopAttacks: true
      },
      hideGold: {
        primary: {
          enabled: false,
          location: HideGoldLocation.GUILD,
          settings: {}
        },
        backup: {
          enabled: false,
          location: HideGoldLocation.SHOP,
          settings: {}
        },
        minGoldBackup: 500,
        soulBoundTo: '',
        filterBy: PackFilterType.LOWEST_PRICE,
        allowMultiple: false,
        recheckDuration: 3600,
        pocketMoney: 1000,
        hideIn: HideGoldLocation.GUILD,
        duration: PackDuration.TWO_HOURS,
        skillsToTrain: [],
        minItemPrice: 100,
        minPack: 1,
        maxPack: 10,
        buyFromPlayers: []
      },
      packages: {
        rotateItems: {
          enabled: false,
          selectedItems: [],
          colors: [],
          underworldItems: false,
          itemsCooldownDays: 7,
          collectResourceHourly: false
        },
        sellItems: {
          enabled: false,
          rules: [],
          autoSellOnRenew: false
        },
        pickGold: {
          enabled: false,
          goldToPick: 0
        },
        operationSpeed: OperationSpeed.NORMAL
      },
      costumes: {
        wearUnderworld: false,
        preventWearUnderworldOnPause: false,
        dontPauseIfUnderworldActive: false,
        underworldCostume: 'dis_pater_normal' as any,
        standardProfile: {
          enabled: false
        },
        turmaProfile: {
          enabled: false
        },
        eventProfile: {
          enabled: false
        }
      },
      turma: {
        enabled: false,
        levelRestriction: 0,
        autoStop: 0,
        attackIfQuest: false,
        runUntilGetChest: false,
        goldRaided: 0,
        attackPlayers: [],
        autoIgnorePlayers: [],
        ignorePlayers: [],
        attackSameServer: true
      },
      arena: {
        enabled: false,
        allowSim: true,
        prioritizeChance: 80,
        skipLow: false,
        loseLimit: 3,
        attackTargetPlayers: false,
        allowMaxAttacks: false,
        autoIgnoreGuildPlayers: true,
        attackScore: false,
        selectScorePage: 1,
        progressLeague: false,
        stopAfterLimit: false,
        attackGoldWhale: false,
        doRevenge: false,
        totalWin: false,
        limitAttacks: 0,
        dailyAttacks: 0,
        autoStop: 0,
        attackIfQuest: false,
        attackIfCombatQuest: false,
        goldRaided: 0,
        attackPlayers: [],
        autoIgnorePlayers: [],
        ignorePlayers: [],
        attackSameServer: true,
        attackScoreSingle: false
      },
      expeditions: {
        enabled: false,
        minDungeonPoints: 0,
        location: '',
        mob: '',
        autoCollectBonuses: true,
        travelForDungeon: false,
        removeCooldown: CooldownRemovalOption.HOURGLASS,
        cooldownLimit: 10,
        cooldownUsed: 0
      },
      dungeon: {
        enabled: false,
        location: '',
        useGateKey: false,
        isAdvanced: false,
        skipBoss: false,
        bossName: '',
        restartAfterFail: 0
      },
      event: {
        enabled: false,
        mob: '',
        autoCollectBonuses: true,
        autoRenewEvent: false,
        followLeader: '',
        autoStop: false
      },
      underworld: {
        enabled: false,
        noWeapon: false,
        sendMail: false,
        mailContent: '',
        maxMedics: 5,
        usedMedics: 0,
        maxRuby: 2,
        usedRuby: 0,
        buffs: true,
        costumes: true,
        reinforcements: [],
        mode: UnderworldMode.NORMAL,
        difficulty: UnderworldDifficulty.NORMAL,
        healGuild: true,
        healPotions: true,
        healSacrifice: false,
        allowMobilisation: false,
        allowRuby: false,
        attackDisPaterAsap: true,
        stopArenaInUnder: true,
        healCostumes: true,
        allowReinforcements: false,
        allowUpgrades: false,
        autoBuffGods: true,
        autoBuffOils: true,
        exitUnder: false,
        stayHours: 24
      },
      forge: {
        enabled: false,
        autoForge: false,
        autoSmelt: false,
        maxGoldSpend: 10000,
        maxCostPerItem: 1000,
        dailyForgeLimit: 10,
        qualityFilter: [ItemQuality.WHITE, ItemQuality.GREEN],
        minLevel: 1,
        maxLevel: 50,
        itemTypes: [],
        minSmeltValue: 10,
        smeltQualityFilter: [ItemQuality.WHITE]
      },
      repair: {
        enabled: false,
        repairThreshold: 50,
        maxCostPerItem: 500,
        maxGoldSpend: 5000,
        itemFilter: []
      },
      quest: {
        enabled: false,
        autoComplete: false,
        dailyLimit: 10,
        rules: []
      },
      market: {
        enabled: false,
        checkInterval: 300,
        dailyBuyLimit: 20,
        autoBuy: {
          enabled: false,
          maxPrice: 10000,
          maxGoldSpend: 50000,
          qualityFilter: [ItemQuality.BLUE, ItemQuality.PURPLE],
          minLevel: 1,
          maxLevel: 100,
          itemNames: [],
          blacklistedSellers: []
        },
        autoSell: {
          enabled: false,
          rules: []
        },
        auction: {
          enabled: false,
          autoBid: false,
          autoBuyout: false,
          maxBid: 10000,
          maxBuyout: 20000,
          bidIncrement: 100,
          minTimeRemaining: 300,
          qualityFilter: [ItemQuality.PURPLE, ItemQuality.ORANGE]
        }
      },
      shop: {
        enabled: false,
        searchRules: [],
        maxSearches: 10,
        usedSearches: 0,
        allowRuby: false,
        autoStart: false
      },
      bank: {
        enabled: false,
        isBank: false,
        selectedBackpack: 1,
        clientSettings: {
          requestThreshold: 1000,
          packGoldAmount: 10000,
          duration: '2h'
        }
      },
      guild: {
        enabled: false,
        attackList: [],
        ignoreList: []
      }
    };
  }

  static mergeWithDefaults(userSettings: Partial<BotSettings>): BotSettings {
    const defaultSettings = this.createDefaultSettings();
    return this.deepMerge(defaultSettings, userSettings);
  }

  private static deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}
