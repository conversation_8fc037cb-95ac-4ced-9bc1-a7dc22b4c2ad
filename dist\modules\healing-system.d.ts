import { HealSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface HealthInfo {
    current: number;
    maximum: number;
    percentage: number;
}
export interface FoodItem {
    id: string;
    name: string;
    healAmount: number;
    count: number;
    isAvailable: boolean;
}
export declare class HealingSystemModule {
    private urlHelper;
    private storageManager;
    private isHealing;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    checkAndHeal(settings: HealSettings): Promise<boolean>;
    private getCurrentHealth;
    private performHealing;
    private useCervisia;
    private useEggs;
    private getAvailableFood;
    private consumeFood;
    private buyFood;
    private navigateToLocation;
    private waitForPageLoad;
    shouldStopAttacks(settings: HealSettings): Promise<boolean>;
    private canPerformHealing;
    private delay;
}
//# sourceMappingURL=healing-system.d.ts.map