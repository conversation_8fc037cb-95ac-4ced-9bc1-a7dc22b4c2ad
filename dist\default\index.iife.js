(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/utils/gladiator-url-helper.ts":
/*!*******************************************!*\
  !*** ./src/utils/gladiator-url-helper.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GladiatorUrlHelper: () => (/* binding */ GladiatorUrlHelper)
/* harmony export */ });
// Gladiatus URL parsing and validation utilities
class GladiatorUrlHelper {
    constructor() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
    /**
     * Check if URL is a Gladiatus domain
     */
    isGladiatus(url) {
        return /gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is a Gladiatus game page (not lobby)
     */
    isPlaying(url) {
        return /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(url);
    }
    /**
     * Check if URL is the Gladiatus lobby
     */
    isLobby(url) {
        return /lobby\.gladiatus\.gameforge\.com/.test(url) && url.indexOf('loading') < 0;
    }
    /**
     * Parse query string into object
     */
    resolveQueries(queryString) {
        const queries = {};
        const params = queryString.split('&');
        for (let i = params.length - 1; i >= 0; i--) {
            const [key, value] = params[i].split('=');
            if (key && value) {
                queries[key] = decodeURIComponent(value);
            }
        }
        return queries;
    }
    /**
     * Resolve URL information from Gladiatus game URL
     */
    resolve(url, force = false) {
        if (this.resolved && !force) {
            return this.urlInfo;
        }
        const match = url.match(/https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main)\.php(?:\?(.*))?/i);
        if (!match) {
            return null;
        }
        this.urlInfo = {
            server: match[1],
            country: match[2],
            domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,
            queries: this.resolveQueries(match[3] || ''),
            resolved: true
        };
        this.resolved = true;
        return this.urlInfo;
    }
    /**
     * Build Gladiatus URL with parameters
     */
    buildUrl(params, page = 'index.php') {
        const queryParts = [];
        for (const key in params) {
            queryParts.push(`${key}=${encodeURIComponent(params[key])}`);
        }
        // Add security hash if available
        if (this.urlInfo.queries.sh) {
            queryParts.push(`sh=${this.urlInfo.queries.sh}`);
        }
        return `${page}?${queryParts.join('&')}`;
    }
    /**
     * Build AJAX URL with parameters
     */
    buildAjaxUrl(params) {
        return this.buildUrl(params, 'ajax.php');
    }
    /**
     * Get current URL info
     */
    getUrlInfo() {
        return this.urlInfo;
    }
    /**
     * Reset resolved state
     */
    reset() {
        this.resolved = false;
        this.urlInfo = {
            queries: {},
            resolved: false
        };
    }
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!******************************!*\
  !*** ./src/default/index.ts ***!
  \******************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/gladiator-url-helper */ "./src/utils/gladiator-url-helper.ts");
// Default script for Gladiatus Helper Bot
// This script provides fallback functionality and utilities

class DefaultScript {
    constructor() {
        this.urlHelper = new _utils_gladiator_url_helper__WEBPACK_IMPORTED_MODULE_0__.GladiatorUrlHelper();
        this.initialize();
    }
    initialize() {
        console.log('Gladiatus Helper Bot: Default script loaded');
        // Set up global utilities
        this.setupGlobalUtilities();
        // Handle any default page functionality
        this.handleDefaultPage();
    }
    setupGlobalUtilities() {
        // Make URL helper available globally
        window.ghUrlHelper = this.urlHelper;
        // Add utility functions
        window.ghUtils = {
            isGladiatus: (url) => this.urlHelper.isGladiatus(url || window.location.href),
            isLobby: (url) => this.urlHelper.isLobby(url || window.location.href),
            isPlaying: (url) => this.urlHelper.isPlaying(url || window.location.href),
            getUrlInfo: () => this.urlHelper.getUrlInfo(),
            resolve: (url) => this.urlHelper.resolve(url || window.location.href)
        };
    }
    handleDefaultPage() {
        const currentUrl = window.location.href;
        if (this.urlHelper.isGladiatus(currentUrl)) {
            console.log('Gladiatus Helper Bot: Gladiatus domain detected');
            // Add any default Gladiatus page handling here
            this.addGladiatorStyles();
        }
    }
    addGladiatorStyles() {
        // Add any default styles or modifications
        const style = document.createElement('style');
        style.textContent = `
      /* Default Gladiatus Helper Bot styles */
      .gh-highlight-underworld {
        border: 2px solid #e74c3c !important;
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.5) !important;
      }

      .gh-item-quality-white { background-color: var(--gh-item-white, rgba(255, 255, 255, 0.4)) !important; }
      .gh-item-quality-green { background-color: var(--gh-item-green, rgba(0, 255, 0, 0.3)) !important; }
      .gh-item-quality-blue { background-color: var(--gh-item-blue, rgba(81, 89, 247, 0.4)) !important; }
      .gh-item-quality-purple { background-color: var(--gh-item-purple, rgba(227, 3, 224, 0.4)) !important; }
      .gh-item-quality-orange { background-color: var(--gh-item-orange, rgba(255, 106, 0, 0.4)) !important; }
      .gh-item-quality-red { background-color: var(--gh-item-red, rgba(255, 0, 0, 0.4)) !important; }
    `;
        document.head.appendChild(style);
    }
}
// Initialize default script
new DefaultScript();

})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=index.iife.js.map