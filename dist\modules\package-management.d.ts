import { PackageSettings, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface PackageItem {
    id: string;
    name: string;
    quality: ItemQuality;
    level: number;
    price: number;
    isUnderworld: boolean;
    isSoulbound: boolean;
    conditioning: number;
    canSell: boolean;
    canRotate: boolean;
}
export declare class PackageManagementModule {
    private urlHelper;
    private storageManager;
    private isProcessing;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    processPackages(settings: PackageSettings): Promise<void>;
    private navigateToPackages;
    private pickGold;
    private rotateItems;
    private sellItems;
    private getPackageItems;
    private parsePackageItem;
    private filterItemsForRotation;
    private filterItemsForSelling;
    private itemMatchesRule;
    private rotateItem;
    private sellItem;
    shouldProcessPackages(settings: PackageSettings): Promise<boolean>;
    getPackageStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=package-management.d.ts.map