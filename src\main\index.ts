// Main application logic for Gladiatus Helper Bot

import { BotStatus, BotAction, BotSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
import { BotEngine } from './bot-engine';
import { LoginManager } from './login-manager';
import { UIManager } from './ui-manager';

class GladiatusHelperBot {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private botEngine: BotEngine;
  private loginManager: LoginManager;
  private uiManager: UIManager;

  private isInitialized: boolean = false;
  private botStatus: BotStatus;
  private settings: BotSettings | null = null;

  constructor() {
    this.urlHelper = new GladiatorUrlHelper();
    this.storageManager = new StorageManager();
    this.botEngine = new BotEngine(this.storageManager, this.urlHelper);
    this.loginManager = new LoginManager(this.urlHelper, this.storageManager);
    this.uiManager = new UIManager();

    this.botStatus = {
      isActive: false,
      isPaused: false,
      currentAction: BotAction.IDLE,
      statistics: {
        startTime: new Date(),
        totalRunTime: 0,
        actionsPerformed: 0,
        errorsEncountered: 0,
        goldEarned: 0,
        experienceGained: 0,
        itemsFound: 0,
        combatsWon: 0,
        combatsLost: 0
      }
    };

    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      console.log('Gladiatus Helper Bot: Initializing main application');

      // Load settings
      await this.loadSettings();

      // Initialize URL info
      const currentUrl = window.location.href;
      this.urlHelper.resolve(currentUrl);

      // Set up event listeners
      this.setupEventListeners();

      // Check if we're on lobby page
      if (this.urlHelper.isLobby(currentUrl)) {
        await this.handleLobbyPage();
      } else if (this.urlHelper.isPlaying(currentUrl)) {
        await this.handleGamePage();
      }

      this.isInitialized = true;
      console.log('Gladiatus Helper Bot: Initialization complete');

    } catch (error) {
      console.error('Gladiatus Helper Bot: Initialization failed:', error);
      this.botStatus.error = (error as Error).message;
    }
  }

  private async loadSettings(): Promise<void> {
    this.settings = await this.storageManager.getSettings();
    if (!this.settings) {
      console.log('No settings found, using defaults');
      // Initialize with default settings if none exist
      this.settings = this.getDefaultSettings();
      await this.storageManager.saveSettings(this.settings);
    }
  }

  private getDefaultSettings(): BotSettings {
    return {
      general: {
        selectedBackpack: 1,
        maxRandomDelay: 10,
        loginDelay: 5,
        highlightUnderworldItems: true,
        showShopBox: true,
        shopMinQuality: 1,
        showBiddenBox: true,
        autoCollectGodOils: true,
        autoCollectDaily: true
      },
      heal: {
        enabled: false,
        minHealth: 50,
        healCervisia: true,
        healEggs: true,
        buyFoodNeeded: true,
        selectedEggs: [1, 2, 3],
        renewShop: 'not_allow' as any,
        autoStopAttacks: true,
        buyFood: true
      },
      hideGold: {
        primary: {
          enabled: true,
          location: 'guild' as any,
          settings: {}
        },
        backup: {
          enabled: false,
          location: 'shop' as any,
          settings: {}
        },
        minGoldBackup: 1000,
        soulBoundTo: '',
        filterBy: 'd' as any,
        allowMultiple: false,
        recheckDuration: 30,
        pocketMoney: 500,
        hideIn: 'guild' as any,
        duration: '24h' as any,
        skillsToTrain: [],
        minItemPrice: 100,
        minPack: 1,
        maxPack: 10,
        buyFromPlayers: []
      },
      packages: {
        rotateItems: {
          enabled: true,
          selectedItems: [],
          colors: [],
          underworldItems: false,
          itemsCooldownDays: 7,
          collectResourceHourly: true
        },
        sellItems: {
          enabled: true,
          rules: [],
          autoSellOnRenew: false
        },
        pickGold: {
          enabled: true,
          goldToPick: 1000
        },
        operationSpeed: 'normal' as any
      },
      costumes: {
        wearUnderworld: true,
        preventWearUnderworldOnPause: false,
        dontPauseIfUnderworldActive: false,
        underworldCostume: 'dis_pater_normal' as any,
        standardProfile: {
          enabled: true
        },
        turmaProfile: {
          enabled: false
        },
        eventProfile: {
          enabled: false
        }
      }
    } as any;
  }

  private setupEventListeners(): void {
    // Listen for custom events from content script
    window.addEventListener('ghToggleBot', () => {
      this.toggleBot();
    });

    window.addEventListener('ghOpenStatistics', () => {
      this.uiManager.showStatistics(this.botStatus.statistics);
    });

    window.addEventListener('ghSettingsUpdated', (event: any) => {
      this.updateSettings(event.detail);
    });

    // Listen for page navigation
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Listen for storage changes to sync status across tabs
    window.addEventListener('storage', (event) => {
      if (event.key === 'gh_bot_status') {
        this.syncBotStatus();
      }
    });
  }

  private async handleLobbyPage(): Promise<void> {
    console.log('Gladiatus Helper Bot: Handling lobby page');
    await this.loginManager.handleLobby();
  }

  private async handleGamePage(): Promise<void> {
    console.log('Gladiatus Helper Bot: Handling game page');

    // Load and restore bot status from storage
    await this.loadBotStatus();

    // Update UI with current status (this will show the persisted state)
    this.uiManager.updateStatus(this.botStatus);

    // Auto-start only if explicitly enabled in settings, not based on previous state
    if (this.settings?.general?.autoStart) {
      console.log('Auto-start enabled, starting bot...');
      await this.startBot();
    } else {
      console.log('Bot status loaded, current state:', this.botStatus.isActive ? 'Active' : 'Inactive');
    }
  }

  private async loadBotStatus(): Promise<void> {
    try {
      const storedStatus = await this.storageManager.getBotStatus();
      if (storedStatus) {
        console.log('Loaded bot status from storage:', storedStatus);
        // Preserve the stored status for UI display but reset active state for safety
        this.botStatus = {
          ...this.botStatus,
          ...storedStatus,
          // Keep stored state for UI display but don't auto-resume
          wasActive: storedStatus.isActive, // Remember if it was active
          isActive: false, // Always start inactive for safety
          isPaused: false, // Reset pause state
          currentAction: BotAction.IDLE,
          // Preserve statistics from stored status
          statistics: {
            ...this.botStatus.statistics,
            ...storedStatus.statistics
          }
        };
        console.log('Bot status after merge:', this.botStatus);
      } else {
        console.log('No stored bot status found, using defaults');
      }
    } catch (error) {
      console.error('Failed to load bot status:', error);
    }
  }

  private async syncBotStatus(): Promise<void> {
    try {
      const storedStatus = await this.storageManager.getBotStatus();
      if (storedStatus) {
        this.botStatus = { ...this.botStatus, ...storedStatus };
        this.uiManager.updateStatus(this.botStatus);
      }
    } catch (error) {
      console.error('Failed to sync bot status:', error);
    }
  }

  public async toggleBot(): Promise<void> {
    if (this.botStatus.isActive) {
      await this.stopBot();
    } else {
      await this.startBot();
    }
  }

  public async startBot(): Promise<void> {
    if (!this.isInitialized || !this.settings) {
      console.error('Bot not initialized or settings not loaded');
      return;
    }

    try {
      console.log('Gladiatus Helper Bot: Starting bot');

      this.botStatus.isActive = true;
      this.botStatus.isPaused = false;
      this.botStatus.currentAction = BotAction.IDLE;
      this.botStatus.statistics.startTime = new Date();

      // Save status
      await this.storageManager.saveBotStatus(this.botStatus);
      console.log('Bot status saved to storage:', this.botStatus);

      // Update UI
      this.uiManager.updateStatus(this.botStatus);

      // Start bot engine
      await this.botEngine.start(this.settings);

      console.log('Gladiatus Helper Bot: Bot started successfully');

    } catch (error) {
      console.error('Gladiatus Helper Bot: Failed to start bot:', error);
      this.botStatus.error = (error as Error).message;
      this.botStatus.isActive = false;
      this.uiManager.updateStatus(this.botStatus);
    }
  }

  public async stopBot(): Promise<void> {
    try {
      console.log('Gladiatus Helper Bot: Stopping bot');

      this.botStatus.isActive = false;
      this.botStatus.currentAction = BotAction.IDLE;

      // Stop bot engine
      await this.botEngine.stop();

      // Save status
      await this.storageManager.saveBotStatus(this.botStatus);
      console.log('Bot status saved to storage:', this.botStatus);

      // Update UI
      this.uiManager.updateStatus(this.botStatus);

      console.log('Gladiatus Helper Bot: Bot stopped successfully');

    } catch (error) {
      console.error('Gladiatus Helper Bot: Failed to stop bot:', error);
      this.botStatus.error = (error as Error).message;
      this.uiManager.updateStatus(this.botStatus);
    }
  }

  public async pauseBot(): Promise<void> {
    if (!this.botStatus.isActive) return;

    this.botStatus.isPaused = true;
    this.botStatus.currentAction = BotAction.IDLE;

    await this.botEngine.pause();
    await this.storageManager.saveBotStatus(this.botStatus);
    this.uiManager.updateStatus(this.botStatus);

    console.log('Gladiatus Helper Bot: Bot paused');
  }

  public async resumeBot(): Promise<void> {
    if (!this.botStatus.isActive || !this.botStatus.isPaused) return;

    this.botStatus.isPaused = false;

    await this.botEngine.resume();
    await this.storageManager.saveBotStatus(this.botStatus);
    this.uiManager.updateStatus(this.botStatus);

    console.log('Gladiatus Helper Bot: Bot resumed');
  }

  public getBotStatus(): BotStatus {
    return { ...this.botStatus };
  }

  public async updateSettings(newSettings: Partial<BotSettings>): Promise<void> {
    if (!this.settings) return;

    // Merge with existing settings
    this.settings = { ...this.settings, ...newSettings };

    // Save to storage
    await this.storageManager.saveSettings(this.settings);

    // Update bot engine if running
    if (this.botStatus.isActive) {
      await this.botEngine.updateSettings(this.settings);
    }

    console.log('Gladiatus Helper Bot: Settings updated');
  }

  private cleanup(): void {
    if (this.botStatus.isActive) {
      this.botEngine.stop();
    }
  }
}

// Initialize the bot when script loads
const gladiatusBot = new GladiatusHelperBot();

// Make bot available globally for debugging
(window as any).gladiatusBot = gladiatusBot;