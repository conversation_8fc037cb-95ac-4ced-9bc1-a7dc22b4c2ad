import { BotSettings } from '../types';
import { StorageManager } from '../utils/storage-manager';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
export declare class BotEngine {
    private storageManager;
    private urlHelper;
    private isRunning;
    private isPaused;
    private currentSettings;
    private actionInterval;
    private arenaCombat;
    private turmaCombat;
    private healingSystem;
    private packageManagement;
    private expeditionSystem;
    private underworldSystem;
    private goldManagement;
    private eventSystem;
    private questManagement;
    private marketOperations;
    private forgeRepair;
    constructor(storageManager: StorageManager, urlHelper: GladiatorUrlHelper);
    start(settings: BotSettings): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    updateSettings(settings: BotSettings): Promise<void>;
    private startAutomationLoop;
    private performNextAction;
    private determineNextAction;
    private needsHealing;
    private canAttackArena;
    private canAttackTurma;
    private needsPackageManagement;
    private canDoExpedition;
    private isUnderworldActive;
    private hasActiveEvents;
    private hasAvailableQuests;
    private shouldPerformMarketOperations;
    private shouldPerformForgeRepair;
    private performHealing;
    private performAttacking;
    private performItemPicking;
    private performPackBuying;
    private performTraveling;
    private performUnderworld;
    private performEvents;
    private performQuests;
    private performMarketOperations;
    private performForgeRepair;
}
//# sourceMappingURL=bot-engine.d.ts.map