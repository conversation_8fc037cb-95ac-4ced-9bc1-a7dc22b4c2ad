import { EventSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface GameEvent {
    id: string;
    name: string;
    type: 'combat' | 'collection' | 'special';
    isActive: boolean;
    timeRemaining: number;
    hasRewards: boolean;
    canParticipate: boolean;
}
export interface EventReward {
    type: 'gold' | 'xp' | 'item' | 'points';
    amount: number;
    description: string;
    isCollected: boolean;
}
export declare class EventSystemModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private eventCount;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: EventSettings): Promise<void>;
    stop(): Promise<void>;
    private performEventLoop;
    private getActiveEvents;
    private parseEventElement;
    private processEvent;
    private processCombatEvent;
    private processCollectionEvent;
    private processSpecialEvent;
    private getEventTargets;
    private attackEventTarget;
    private collectEventItems;
    private collectEventBonuses;
    private renewEvents;
    private navigateToEvents;
    private navigateToEventCombat;
    private navigateToEventCollection;
    private shouldContinueEvents;
    getEventStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=event-system.d.ts.map