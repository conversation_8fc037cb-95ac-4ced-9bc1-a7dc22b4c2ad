// Tests for GladiatorUrlHelper

import { GladiatorUrlHelper } from '../src/utils/gladiator-url-helper';

describe('GladiatorUrlHelper', () => {
  let urlHelper: GladiatorUrlHelper;

  beforeEach(() => {
    urlHelper = new GladiatorUrlHelper();
  });

  describe('isGladiatus', () => {
    it('should return true for gladiatus.gameforge.com URLs', () => {
      expect(urlHelper.isGladiatus('https://s1-en.gladiatus.gameforge.com/game/index.php')).toBe(true);
      expect(urlHelper.isGladiatus('https://s2-de.gladiatus.gameforge.com/game/c.php')).toBe(true);
    });

    it('should return false for non-gladiatus URLs', () => {
      expect(urlHelper.isGladiatus('https://google.com')).toBe(false);
      expect(urlHelper.isGladiatus('https://example.com')).toBe(false);
    });
  });

  describe('isLobby', () => {
    it('should return true for lobby URLs', () => {
      expect(urlHelper.isLobby('https://lobby.gladiatus.gameforge.com/en_GB/accounts')).toBe(true);
    });

    it('should return false for game URLs', () => {
      expect(urlHelper.isLobby('https://s1-en.gladiatus.gameforge.com/game/index.php')).toBe(false);
    });
  });

  describe('isPlaying', () => {
    it('should return true for game URLs', () => {
      expect(urlHelper.isPlaying('https://s1-en.gladiatus.gameforge.com/game/index.php')).toBe(true);
      expect(urlHelper.isPlaying('https://s2-de.gladiatus.gameforge.com/game/c.php')).toBe(true);
    });

    it('should return false for lobby URLs', () => {
      expect(urlHelper.isPlaying('https://lobby.gladiatus.gameforge.com/en_GB/accounts')).toBe(false);
    });
  });

  describe('resolve', () => {
    it('should parse game URLs correctly', () => {
      const result = urlHelper.resolve('https://s1-en.gladiatus.gameforge.com/game/index.php?mod=overview');

      expect(result).toBeDefined();
      expect(result?.server).toBe('1');
      expect(result?.country).toBe('en');
      expect(result?.domain).toBe('s1-en.gladiatus.gameforge.com');
      expect(result?.resolved).toBe(true);
    });

    it('should return null for lobby URLs', () => {
      const result = urlHelper.resolve('https://lobby.gladiatus.gameforge.com/en_GB/accounts');

      expect(result).toBeNull();
    });

    it('should return null for invalid URLs', () => {
      const result = urlHelper.resolve('https://google.com');
      expect(result).toBeNull();
    });
  });

  describe('getUrlInfo', () => {
    it('should return current URL info after resolve', () => {
      urlHelper.resolve('https://s1-en.gladiatus.gameforge.com/game/index.php');
      const info = urlHelper.getUrlInfo();

      expect(info).toBeDefined();
      expect(info?.server).toBe('1');
      expect(info?.country).toBe('en');
    });

    it('should return default info if no URL has been resolved', () => {
      const info = urlHelper.getUrlInfo();
      expect(info).toEqual({
        queries: {},
        resolved: false
      });
    });
  });
});
