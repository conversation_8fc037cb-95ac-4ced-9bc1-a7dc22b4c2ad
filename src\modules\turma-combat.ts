// Turma Combat Module - Handles turma (guild war) combat functionality

import { TurmaSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface TurmaTarget {
  id: string;
  name: string;
  level: number;
  guild: string;
  gold: number;
  isOnline: boolean;
  canAttack: boolean;
  lastAttacked?: Date;
}

export class TurmaCombatModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private attackCount: number = 0;
  private goldRaided: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: TurmaSettings): Promise<void> {
    if (this.isRunning || !settings.enabled) return;
    
    this.isRunning = true;
    this.attackCount = 0;
    this.goldRaided = 0;
    
    console.log('Turma Combat: Starting turma automation');
    
    try {
      await this.navigateToTurma();
      await this.performTurmaLoop(settings);
    } catch (error) {
      console.error('Turma Combat: Error in turma automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Turma Combat: Stopped turma automation');
  }

  private async navigateToTurma(): Promise<void> {
    const currentUrl = window.location.href;
    
    if (!currentUrl.includes('mod=turma')) {
      console.log('Turma Combat: Navigating to turma');
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=turma`;
      
      await this.waitForPageLoad();
    }
  }

  private async performTurmaLoop(settings: TurmaSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueAttacking(settings)) {
      try {
        const targets = await this.getAvailableTargets(settings);
        
        if (targets.length === 0) {
          console.log('Turma Combat: No suitable targets found');
          await this.delay(10000);
          continue;
        }

        const target = this.selectBestTarget(targets, settings);
        if (!target) {
          console.log('Turma Combat: No target selected');
          await this.delay(10000);
          continue;
        }

        const result = await this.attackTarget(target, settings);
        await this.processAttackResult(result, target);
        
        // Random delay between attacks (longer than arena)
        const delay = Math.random() * 20000 + 10000; // 10-30 seconds
        await this.delay(delay);
        
      } catch (error) {
        console.error('Turma Combat: Error in attack loop:', error);
        await this.delay(15000);
      }
    }
  }

  private async getAvailableTargets(settings: TurmaSettings): Promise<TurmaTarget[]> {
    const targets: TurmaTarget[] = [];
    
    // Parse turma page for targets
    const targetElements = document.querySelectorAll('.turma_opponent, .enemy_player');
    
    for (const element of targetElements) {
      try {
        const target = await this.parseTurmaTarget(element as HTMLElement, settings);
        if (target && this.isValidTarget(target, settings)) {
          targets.push(target);
        }
      } catch (error) {
        console.error('Turma Combat: Error parsing target:', error);
      }
    }
    
    return targets;
  }

  private async parseTurmaTarget(element: HTMLElement, settings: TurmaSettings): Promise<TurmaTarget | null> {
    const nameElement = element.querySelector('.player_name, .name');
    const levelElement = element.querySelector('.player_level, .level');
    const guildElement = element.querySelector('.guild_name, .guild');
    const goldElement = element.querySelector('.player_gold, .gold');
    const attackButton = element.querySelector('.attack_button, [data-action="attack"]');
    const onlineIndicator = element.querySelector('.online_status, .status');
    
    if (!nameElement || !attackButton) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '0');
    const guild = guildElement?.textContent?.trim() || '';
    const gold = parseInt(goldElement?.textContent?.replace(/\D/g, '') || '0');
    const id = attackButton.getAttribute('data-target-id') || 
               attackButton.getAttribute('href')?.match(/target=(\d+)/)?.[1] || '';
    
    const isOnline = onlineIndicator?.classList.contains('online') || 
                    onlineIndicator?.textContent?.includes('online') || false;
    
    return {
      id,
      name,
      level,
      guild,
      gold,
      isOnline,
      canAttack: !attackButton.hasAttribute('disabled')
    };
  }

  private isValidTarget(target: TurmaTarget, settings: TurmaSettings): boolean {
    // Skip if can't attack
    if (!target.canAttack) return false;
    
    // Skip if in ignore list
    if (settings.ignorePlayers.includes(target.name)) return false;
    
    // Skip if auto-ignore and recently attacked
    if (settings.autoIgnorePlayers.includes(target.name)) return false;
    
    // Check level restriction
    if (settings.levelRestriction > 0 && target.level > settings.levelRestriction) return false;
    
    // Check if targeting specific players
    if (settings.attackPlayers.length > 0) {
      return settings.attackPlayers.includes(target.name);
    }
    
    // Check same server restriction
    if (!settings.attackSameServer) {
      // This would need server detection logic
      return true; // Simplified for now
    }
    
    return true;
  }

  private selectBestTarget(targets: TurmaTarget[], settings: TurmaSettings): TurmaTarget | null {
    if (targets.length === 0) return null;
    
    // Sort by gold amount (highest first) and then by level (lowest first)
    targets.sort((a, b) => {
      if (Math.abs(a.gold - b.gold) < 1000) {
        return a.level - b.level; // Lower level first if gold is similar
      }
      return b.gold - a.gold; // Higher gold first
    });
    
    return targets[0];
  }

  private async attackTarget(target: TurmaTarget, settings: TurmaSettings): Promise<any> {
    console.log(`Turma Combat: Attacking ${target.name} (Level ${target.level}, Gold: ${target.gold})`);
    
    // Find and click attack button
    const attackButton = document.querySelector(`[data-target-id="${target.id}"], [href*="target=${target.id}"]`);
    if (!attackButton) {
      throw new Error('Attack button not found');
    }
    
    (attackButton as HTMLElement).click();
    
    // Wait for attack result
    await this.waitForAttackResult();
    
    return this.parseAttackResult();
  }

  private async processAttackResult(result: any, target: TurmaTarget): Promise<void> {
    this.attackCount++;
    
    if (result.won) {
      console.log(`Turma Combat: Victory! Raided ${result.gold} gold from ${target.name}`);
      this.goldRaided += result.gold;
      
      // Add to auto-ignore list if configured
      if (result.gold < 100) { // Low gold raids
        this.addToAutoIgnore(target.name);
      }
    } else {
      console.log(`Turma Combat: Defeat against ${target.name}`);
    }
    
    // Update statistics
    await this.updateStatistics(result);
  }

  private addToAutoIgnore(playerName: string): void {
    // This would add player to temporary ignore list
    console.log(`Turma Combat: Adding ${playerName} to auto-ignore list`);
  }

  private shouldContinueAttacking(settings: TurmaSettings): boolean {
    // Check auto stop limit
    if (settings.autoStop > 0 && this.attackCount >= settings.autoStop) {
      console.log('Turma Combat: Auto stop limit reached');
      return false;
    }
    
    // Check gold raided limit
    if (settings.goldRaided > 0 && this.goldRaided >= settings.goldRaided) {
      console.log('Turma Combat: Gold raided limit reached');
      return false;
    }
    
    // Check if quest-based attacking
    if (settings.attackIfQuest && !this.hasActiveTurmaQuest()) {
      console.log('Turma Combat: No active turma quest');
      return false;
    }
    
    return true;
  }

  private hasActiveTurmaQuest(): boolean {
    // Check if there's an active turma-related quest
    const questElements = document.querySelectorAll('.quest_item, .active-quest');
    
    for (const quest of questElements) {
      const questText = quest.textContent?.toLowerCase() || '';
      if (questText.includes('turma') || questText.includes('guild') || questText.includes('war')) {
        return true;
      }
    }
    
    return false;
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async waitForAttackResult(): Promise<void> {
    return new Promise((resolve) => {
      const checkResult = () => {
        const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');
        if (resultElement) {
          resolve();
        } else {
          setTimeout(checkResult, 500);
        }
      };
      setTimeout(checkResult, 3000); // Wait at least 3 seconds for turma
    });
  }

  private parseAttackResult(): any {
    const resultElement = document.querySelector('.combat_result, .attack_result, .turma_result');
    if (!resultElement) return { won: false, gold: 0, xp: 0 };
    
    const won = resultElement.textContent?.includes('Victory') || 
                resultElement.textContent?.includes('Won') ||
                resultElement.classList.contains('victory') || false;
    
    const goldMatch = resultElement.textContent?.match(/(\d+)\s*gold/i);
    const xpMatch = resultElement.textContent?.match(/(\d+)\s*xp/i);
    
    return {
      won,
      gold: goldMatch ? parseInt(goldMatch[1]) : 0,
      xp: xpMatch ? parseInt(xpMatch[1]) : 0
    };
  }

  private async updateStatistics(result: any): Promise<void> {
    const stats = await this.storageManager.getBotStatus();
    if (stats?.statistics) {
      stats.statistics.actionsPerformed++;
      if (result.won) {
        stats.statistics.combatsWon++;
        stats.statistics.goldEarned += result.gold;
        stats.statistics.experienceGained += result.xp;
      } else {
        stats.statistics.combatsLost++;
      }
      
      await this.storageManager.saveBotStatus(stats);
    }
  }

  async getTurmaStatus(): Promise<any> {
    return {
      isRunning: this.isRunning,
      attackCount: this.attackCount,
      goldRaided: this.goldRaided,
      lastUpdate: new Date()
    };
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
