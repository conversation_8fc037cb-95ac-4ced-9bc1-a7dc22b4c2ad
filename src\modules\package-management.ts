// Package Management Module - Handles item rotation, selling, and gold picking

import { PackageSettings, GameItem, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface PackageItem {
  id: string;
  name: string;
  quality: ItemQuality;
  level: number;
  price: number;
  isUnderworld: boolean;
  isSoulbound: boolean;
  conditioning: number;
  canSell: boolean;
  canRotate: boolean;
}

export class PackageManagementModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isProcessing: boolean = false;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async processPackages(settings: PackageSettings): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log('Package Management: Starting package processing');

    try {
      // Navigate to packages if not already there
      await this.navigateToPackages();

      // Pick gold if enabled
      if (settings.pickGold.enabled) {
        await this.pickGold(settings.pickGold.goldToPick);
      }

      // Rotate items if enabled
      if (settings.rotateItems.enabled) {
        await this.rotateItems(settings.rotateItems);
      }

      // Sell items if enabled
      if (settings.sellItems.enabled) {
        await this.sellItems(settings.sellItems);
      }

      console.log('Package Management: Package processing completed');

    } catch (error) {
      console.error('Package Management: Error processing packages:', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  private async navigateToPackages(): Promise<void> {
    if (!window.location.href.includes('mod=packages')) {
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      
      console.log('Package Management: Navigating to packages');
      window.location.href = `${baseUrl}?mod=packages`;
      
      await this.waitForPageLoad();
    }
  }

  private async pickGold(goldToPick: number): Promise<void> {
    if (goldToPick <= 0) return;

    console.log(`Package Management: Picking ${goldToPick} gold`);

    // Look for gold input field and pick button
    const goldInput = document.querySelector('#gold_amount, .gold-input') as HTMLInputElement;
    const pickButton = document.querySelector('.pick_gold_button, [data-action="pick-gold"]');

    if (!goldInput || !pickButton) {
      console.log('Package Management: Gold picking interface not found');
      return;
    }

    // Set gold amount
    goldInput.value = goldToPick.toString();
    
    // Trigger input event
    goldInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Click pick button
    (pickButton as HTMLElement).click();
    
    await this.delay(2000);
    console.log('Package Management: Gold picked successfully');
  }

  private async rotateItems(rotateSettings: any): Promise<void> {
    console.log('Package Management: Starting item rotation');

    const items = await this.getPackageItems();
    const itemsToRotate = this.filterItemsForRotation(items, rotateSettings);

    for (const item of itemsToRotate) {
      try {
        await this.rotateItem(item);
        await this.delay(1000); // Delay between rotations
      } catch (error) {
        console.error(`Package Management: Error rotating item ${item.name}:`, error);
      }
    }

    console.log(`Package Management: Rotated ${itemsToRotate.length} items`);
  }

  private async sellItems(sellSettings: any): Promise<void> {
    console.log('Package Management: Starting item selling');

    const items = await this.getPackageItems();
    const itemsToSell = this.filterItemsForSelling(items, sellSettings);

    for (const item of itemsToSell) {
      try {
        await this.sellItem(item);
        await this.delay(1000); // Delay between sales
      } catch (error) {
        console.error(`Package Management: Error selling item ${item.name}:`, error);
      }
    }

    console.log(`Package Management: Sold ${itemsToSell.length} items`);
  }

  private async getPackageItems(): Promise<PackageItem[]> {
    const items: PackageItem[] = [];
    
    // Parse items from packages page
    const itemElements = document.querySelectorAll('.package_item, .inventory-item');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parsePackageItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Package Management: Error parsing item:', error);
      }
    }

    return items;
  }

  private async parsePackageItem(element: HTMLElement, index: number): Promise<PackageItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const qualityElement = element.querySelector('.item_quality, .quality');
    const levelElement = element.querySelector('.item_level, .level');
    const priceElement = element.querySelector('.item_price, .price');
    
    if (!nameElement) return null;

    const name = nameElement.textContent?.trim() || '';
    const qualityClass = qualityElement?.className || element.className;
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    const price = parseInt(priceElement?.textContent?.replace(/\D/g, '') || '0');

    // Determine quality from CSS classes
    let quality: ItemQuality = ItemQuality.WHITE;
    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;
    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;
    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;
    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;
    else if (qualityClass.includes('red')) quality = ItemQuality.RED;

    // Check for underworld items
    const isUnderworld = element.classList.contains('underworld') || 
                        name.toLowerCase().includes('underworld') ||
                        element.querySelector('.underworld-icon') !== null;

    // Check for soulbound items
    const isSoulbound = element.classList.contains('soulbound') ||
                       element.querySelector('.soulbound-icon') !== null;

    // Get conditioning
    const conditioningElement = element.querySelector('.conditioning, .condition');
    const conditioning = parseInt(conditioningElement?.textContent?.trim() || '100');

    // Check if item can be sold/rotated
    const sellButton = element.querySelector('.sell_button, [data-action="sell"]');
    const rotateButton = element.querySelector('.rotate_button, [data-action="rotate"]');

    return {
      id: index.toString(),
      name,
      quality,
      level,
      price,
      isUnderworld,
      isSoulbound,
      conditioning,
      canSell: !!(sellButton && !sellButton.hasAttribute('disabled')),
      canRotate: !!(rotateButton && !rotateButton.hasAttribute('disabled'))
    };
  }

  private filterItemsForRotation(items: PackageItem[], settings: any): PackageItem[] {
    return items.filter(item => {
      // Must be able to rotate
      if (!item.canRotate) return false;

      // Check if soulbound items should be excluded
      if (item.isSoulbound && !settings.includeSoulbound) return false;

      // Check underworld items setting
      if (item.isUnderworld && !settings.underworldItems) return false;

      // Check selected item types
      if (settings.selectedItems.length > 0) {
        const itemTypeMatch = settings.selectedItems.some((selectedType: string) => 
          item.name.toLowerCase().includes(selectedType.toLowerCase())
        );
        if (!itemTypeMatch) return false;
      }

      // Check quality colors
      if (settings.colors.length > 0) {
        const qualityMatch = settings.colors.includes(item.quality);
        if (!qualityMatch) return false;
      }

      return true;
    });
  }

  private filterItemsForSelling(items: PackageItem[], settings: any): PackageItem[] {
    return items.filter(item => {
      // Must be able to sell
      if (!item.canSell) return false;

      // Don't sell soulbound items
      if (item.isSoulbound) return false;

      // Apply selling rules
      for (const rule of settings.rules) {
        if (this.itemMatchesRule(item, rule)) {
          return true;
        }
      }

      return false;
    });
  }

  private itemMatchesRule(item: PackageItem, rule: any): boolean {
    // Check item type
    if (rule.itemType && !item.name.toLowerCase().includes(rule.itemType.toLowerCase())) {
      return false;
    }

    // Check quality
    if (rule.quality && item.quality !== rule.quality) {
      return false;
    }

    // Check level range
    if (rule.minLevel && item.level < rule.minLevel) {
      return false;
    }
    if (rule.maxLevel && item.level > rule.maxLevel) {
      return false;
    }

    // Check price range
    if (rule.minPrice && item.price < rule.minPrice) {
      return false;
    }
    if (rule.maxPrice && item.price > rule.maxPrice) {
      return false;
    }

    // Check conditioning
    if (rule.minConditioning && item.conditioning < rule.minConditioning) {
      return false;
    }

    return true;
  }

  private async rotateItem(item: PackageItem): Promise<void> {
    console.log(`Package Management: Rotating ${item.name}`);

    const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;

    const rotateButton = itemElement.querySelector('.rotate_button, [data-action="rotate"]');
    if (!rotateButton || rotateButton.hasAttribute('disabled')) return;

    (rotateButton as HTMLElement).click();
    await this.delay(1500);
  }

  private async sellItem(item: PackageItem): Promise<void> {
    console.log(`Package Management: Selling ${item.name} for ${item.price} gold`);

    const itemElement = document.querySelector(`.package_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;

    const sellButton = itemElement.querySelector('.sell_button, [data-action="sell"]');
    if (!sellButton || sellButton.hasAttribute('disabled')) return;

    (sellButton as HTMLElement).click();
    
    // Handle confirmation dialog if present
    await this.delay(500);
    const confirmButton = document.querySelector('.confirm_sell, [data-action="confirm"]');
    if (confirmButton) {
      (confirmButton as HTMLElement).click();
    }
    
    await this.delay(1500);
  }

  async shouldProcessPackages(settings: PackageSettings): Promise<boolean> {
    // Check if any package management is enabled
    return settings.pickGold.enabled || 
           settings.rotateItems.enabled || 
           settings.sellItems.enabled;
  }

  async getPackageStatus(): Promise<any> {
    if (!window.location.href.includes('mod=packages')) {
      return null;
    }

    const items = await this.getPackageItems();
    const totalItems = items.length;
    const sellableItems = items.filter(item => item.canSell).length;
    const rotatableItems = items.filter(item => item.canRotate).length;

    return {
      totalItems,
      sellableItems,
      rotatableItems,
      lastProcessed: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
