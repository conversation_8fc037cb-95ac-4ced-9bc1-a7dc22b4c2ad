import { MarketOperationsSettings, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface MarketItem {
    id: string;
    name: string;
    quality: ItemQuality;
    level: number;
    price: number;
    seller: string;
    timeRemaining: number;
    canBuy: boolean;
    canBid: boolean;
}
export interface AuctionItem {
    id: string;
    name: string;
    quality: ItemQuality;
    level: number;
    currentBid: number;
    buyoutPrice: number;
    timeRemaining: number;
    bidder: string;
    canBid: boolean;
    canBuyout: boolean;
}
export declare class MarketOperationsModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private itemsBought;
    private itemsSold;
    private goldSpent;
    private goldEarned;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: MarketOperationsSettings): Promise<void>;
    stop(): Promise<void>;
    private performMarketLoop;
    private performBuyingOperations;
    private performSellingOperations;
    private performAuctionOperations;
    private navigateToMarket;
    private navigateToAuction;
    private getMarketItems;
    private parseMarketItem;
    private getAuctionItems;
    private parseAuctionItem;
    private filterItemsForBuying;
    private filterItemsForSelling;
    private filterItemsForBidding;
    private buyItem;
    private sellItem;
    private bidOnItem;
    private buyoutItem;
    private getInventoryItems;
    private shouldContinueOperations;
    getMarketStatus(): Promise<any>;
    private waitForPageLoad;
    private delay;
}
//# sourceMappingURL=market-operations.d.ts.map