<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test</title>
    <style>
        /* Modal Styles */
        .gh-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.5);
          z-index: 10001;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease-out;
        }

        .gh-modal-content {
          background: #2c3e50;
          color: white;
          padding: 20px;
          border-radius: 8px;
          max-width: 600px;
          width: 90%;
          max-height: 80%;
          overflow-y: auto;
          animation: slideUp 0.3s ease-out;
        }

        .gh-modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 1px solid #34495e;
        }

        .gh-modal-header h2 {
          margin: 0;
          color: #ecf0f1;
        }

        .gh-modal-close {
          background: none;
          border: none;
          color: white;
          font-size: 20px;
          cursor: pointer;
          padding: 5px;
          opacity: 0.7;
          transition: opacity 0.2s;
        }

        .gh-modal-close:hover {
          opacity: 1;
        }

        .gh-settings-section {
          margin-bottom: 20px;
          padding: 15px;
          background: #34495e;
          border-radius: 5px;
        }

        .gh-settings-section h3 {
          margin: 0 0 15px 0;
          color: #3498db;
          font-size: 16px;
        }

        .gh-settings-section label {
          display: block;
          margin-bottom: 10px;
          cursor: pointer;
          font-size: 14px;
        }

        .gh-settings-section input[type="checkbox"] {
          margin-right: 8px;
          transform: scale(1.2);
        }

        .gh-settings-section input[type="number"] {
          margin-left: 10px;
          padding: 5px;
          border: 1px solid #7f8c8d;
          border-radius: 3px;
          background: #2c3e50;
          color: white;
          width: 60px;
        }

        .gh-modal-footer {
          margin-top: 20px;
          text-align: right;
          padding-top: 15px;
          border-top: 1px solid #34495e;
        }

        .gh-btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: background-color 0.2s;
        }

        .gh-btn-primary {
          background: #3498db;
          color: white;
        }

        .gh-btn-secondary {
          background: #7f8c8d;
          color: white;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes slideUp {
          from {
            transform: translateY(50px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }

        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #ecf0f1;
        }

        button {
            padding: 10px 20px;
            margin: 10px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Modal Test Page</h1>
    <button onclick="showModal()">Show Settings Modal</button>
    <button onclick="showStatsModal()">Show Statistics Modal</button>

    <script>
        function showModal() {
            console.log('Creating settings modal...');
            
            // Remove existing modal
            const existingModal = document.getElementById('gh-settings-modal');
            if (existingModal) {
                console.log('Modal already exists, removing...');
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'gh-settings-modal';
            modal.className = 'gh-modal';

            const modalContent = document.createElement('div');
            modalContent.className = 'gh-modal-content';

            modalContent.innerHTML = `
              <div class="gh-modal-header">
                <h2>Bot Settings</h2>
                <button class="gh-modal-close">×</button>
              </div>
              <div class="gh-modal-body">
                <div class="gh-settings-section">
                  <h3>General Settings</h3>
                  <label>
                    <input type="checkbox" id="gh-setting-auto-start"> Auto-start bot on page load
                  </label>
                  <label>
                    <input type="number" id="gh-setting-login-delay" min="1" max="60" value="5"> Login delay (seconds)
                  </label>
                </div>
                <div class="gh-settings-section">
                  <h3>Combat Settings</h3>
                  <label>
                    <input type="checkbox" id="gh-setting-arena-enabled"> Enable Arena Combat
                  </label>
                  <label>
                    <input type="checkbox" id="gh-setting-turma-enabled"> Enable Turma Combat
                  </label>
                </div>
              </div>
              <div class="gh-modal-footer">
                <button id="gh-settings-save" class="gh-btn gh-btn-primary">Save Settings</button>
                <button id="gh-settings-cancel" class="gh-btn gh-btn-secondary">Cancel</button>
              </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            console.log('Modal added to DOM');

            // Add event listeners
            attachEventListeners(modal);
        }

        function attachEventListeners(modal) {
            console.log('Attaching event listeners...');
            
            const closeBtn = modal.querySelector('.gh-modal-close');
            const saveBtn = document.getElementById('gh-settings-save');
            const cancelBtn = document.getElementById('gh-settings-cancel');
            const modalContent = modal.querySelector('.gh-modal-content');

            console.log('Found elements:', { closeBtn, saveBtn, cancelBtn, modalContent });

            closeBtn?.addEventListener('click', (e) => {
                console.log('Close button clicked');
                e.preventDefault();
                e.stopPropagation();
                modal.remove();
            });
            
            cancelBtn?.addEventListener('click', (e) => {
                console.log('Cancel button clicked');
                e.preventDefault();
                e.stopPropagation();
                modal.remove();
            });
            
            saveBtn?.addEventListener('click', (e) => {
                console.log('Save button clicked');
                e.preventDefault();
                e.stopPropagation();
                alert('Settings saved!');
                modal.remove();
            });

            // Prevent modal content clicks from closing modal
            modalContent?.addEventListener('click', (e) => {
                console.log('Modal content clicked');
                e.stopPropagation();
            });

            // Close on background click only
            modal.addEventListener('click', (e) => {
                console.log('Modal background clicked, target:', e.target, 'modal:', modal);
                if (e.target === modal) {
                    console.log('Closing modal via background click');
                    e.preventDefault();
                    e.stopPropagation();
                    modal.remove();
                }
            });

            console.log('Event listeners attached successfully');
        }

        function showStatsModal() {
            alert('Statistics modal would show here');
        }
    </script>
</body>
</html>
