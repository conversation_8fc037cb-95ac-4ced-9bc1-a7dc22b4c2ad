// Market Operations Module - Handles automated buying, selling, and auction management

import { MarketOperationsSettings, ItemQuality } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';

export interface MarketItem {
  id: string;
  name: string;
  quality: ItemQuality;
  level: number;
  price: number;
  seller: string;
  timeRemaining: number;
  canBuy: boolean;
  canBid: boolean;
}

export interface AuctionItem {
  id: string;
  name: string;
  quality: ItemQuality;
  level: number;
  currentBid: number;
  buyoutPrice: number;
  timeRemaining: number;
  bidder: string;
  canBid: boolean;
  canBuyout: boolean;
}

export class MarketOperationsModule {
  private urlHelper: GladiatorUrlHelper;
  private storageManager: StorageManager;
  private isRunning: boolean = false;
  private itemsBought: number = 0;
  private itemsSold: number = 0;
  private goldSpent: number = 0;
  private goldEarned: number = 0;

  constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager) {
    this.urlHelper = urlHelper;
    this.storageManager = storageManager;
  }

  async start(settings: MarketOperationsSettings): Promise<void> {
    if (this.isRunning || !settings.enabled) return;
    
    this.isRunning = true;
    this.itemsBought = 0;
    this.itemsSold = 0;
    this.goldSpent = 0;
    this.goldEarned = 0;
    
    console.log('Market Operations: Starting market automation');
    
    try {
      await this.performMarketLoop(settings);
    } catch (error) {
      console.error('Market Operations: Error in market automation:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Market Operations: Stopped market automation');
  }

  private async performMarketLoop(settings: MarketOperationsSettings): Promise<void> {
    while (this.isRunning && this.shouldContinueOperations(settings)) {
      try {
        // Handle buying operations
        if (settings.autoBuy.enabled) {
          await this.performBuyingOperations(settings.autoBuy);
        }
        
        // Handle selling operations
        if (settings.autoSell.enabled) {
          await this.performSellingOperations(settings.autoSell);
        }
        
        // Handle auction operations
        if (settings.auction.enabled) {
          await this.performAuctionOperations(settings.auction);
        }
        
        await this.delay(settings.checkInterval * 1000);
        
      } catch (error) {
        console.error('Market Operations: Error in market loop:', error);
        await this.delay(30000);
      }
    }
  }

  private async performBuyingOperations(buySettings: any): Promise<void> {
    console.log('Market Operations: Performing buying operations');
    
    await this.navigateToMarket();
    
    const availableItems = await this.getMarketItems();
    const itemsToBuy = this.filterItemsForBuying(availableItems, buySettings);
    
    for (const item of itemsToBuy) {
      if (!this.isRunning) break;
      
      if (this.goldSpent + item.price <= buySettings.maxGoldSpend) {
        await this.buyItem(item);
        this.itemsBought++;
        this.goldSpent += item.price;
        
        await this.delay(2000);
      }
    }
  }

  private async performSellingOperations(sellSettings: any): Promise<void> {
    console.log('Market Operations: Performing selling operations');
    
    await this.navigateToMarket();
    
    // Get items from inventory to sell
    const inventoryItems = await this.getInventoryItems();
    const itemsToSell = this.filterItemsForSelling(inventoryItems, sellSettings);
    
    for (const item of itemsToSell) {
      if (!this.isRunning) break;
      
      await this.sellItem(item, sellSettings);
      this.itemsSold++;
      
      await this.delay(2000);
    }
  }

  private async performAuctionOperations(auctionSettings: any): Promise<void> {
    console.log('Market Operations: Performing auction operations');
    
    await this.navigateToAuction();
    
    const auctionItems = await this.getAuctionItems();
    const itemsToBid = this.filterItemsForBidding(auctionItems, auctionSettings);
    
    for (const item of itemsToBid) {
      if (!this.isRunning) break;
      
      if (auctionSettings.autoBid) {
        await this.bidOnItem(item, auctionSettings);
      }
      
      if (auctionSettings.autoBuyout && item.canBuyout) {
        await this.buyoutItem(item, auctionSettings);
      }
      
      await this.delay(3000);
    }
  }

  private async navigateToMarket(): Promise<void> {
    if (!window.location.href.includes('mod=market')) {
      console.log('Market Operations: Navigating to market');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=market`;
      
      await this.waitForPageLoad();
    }
  }

  private async navigateToAuction(): Promise<void> {
    if (!window.location.href.includes('mod=auction')) {
      console.log('Market Operations: Navigating to auction');
      const currentUrl = window.location.href;
      const baseUrl = currentUrl.split('?')[0];
      window.location.href = `${baseUrl}?mod=auction`;
      
      await this.waitForPageLoad();
    }
  }

  private async getMarketItems(): Promise<MarketItem[]> {
    const items: MarketItem[] = [];
    const itemElements = document.querySelectorAll('.market_item, .market-listing');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parseMarketItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Market Operations: Error parsing market item:', error);
      }
    }
    
    return items;
  }

  private async parseMarketItem(element: HTMLElement, index: number): Promise<MarketItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const priceElement = element.querySelector('.item_price, .price');
    const sellerElement = element.querySelector('.seller_name, .seller');
    const levelElement = element.querySelector('.item_level, .level');
    const buyButton = element.querySelector('.buy_button, [data-action="buy"]');
    const timeElement = element.querySelector('.time_remaining, .timer');
    
    if (!nameElement || !priceElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const price = parseInt(priceElement.textContent?.replace(/\D/g, '') || '0');
    const seller = sellerElement?.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    
    // Determine quality from CSS classes
    let quality: ItemQuality = ItemQuality.WHITE;
    const qualityClass = element.className;
    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;
    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;
    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;
    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;
    else if (qualityClass.includes('red')) quality = ItemQuality.RED;
    
    // Parse time remaining
    const timeText = timeElement?.textContent?.trim() || '0:00:00';
    const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
    const timeRemaining = timeMatch ? 
      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
    
    return {
      id: index.toString(),
      name,
      quality,
      level,
      price,
      seller,
      timeRemaining,
      canBuy: !!(buyButton && !buyButton.hasAttribute('disabled')),
      canBid: false // Market items are direct purchase
    };
  }

  private async getAuctionItems(): Promise<AuctionItem[]> {
    const items: AuctionItem[] = [];
    const itemElements = document.querySelectorAll('.auction_item, .auction-listing');
    
    for (let i = 0; i < itemElements.length; i++) {
      const element = itemElements[i] as HTMLElement;
      
      try {
        const item = await this.parseAuctionItem(element, i);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.error('Market Operations: Error parsing auction item:', error);
      }
    }
    
    return items;
  }

  private async parseAuctionItem(element: HTMLElement, index: number): Promise<AuctionItem | null> {
    const nameElement = element.querySelector('.item_name, .name');
    const currentBidElement = element.querySelector('.current_bid, .bid');
    const buyoutElement = element.querySelector('.buyout_price, .buyout');
    const bidderElement = element.querySelector('.current_bidder, .bidder');
    const levelElement = element.querySelector('.item_level, .level');
    const bidButton = element.querySelector('.bid_button, [data-action="bid"]');
    const buyoutButton = element.querySelector('.buyout_button, [data-action="buyout"]');
    const timeElement = element.querySelector('.time_remaining, .timer');
    
    if (!nameElement) return null;
    
    const name = nameElement.textContent?.trim() || '';
    const currentBid = parseInt(currentBidElement?.textContent?.replace(/\D/g, '') || '0');
    const buyoutPrice = parseInt(buyoutElement?.textContent?.replace(/\D/g, '') || '0');
    const bidder = bidderElement?.textContent?.trim() || '';
    const level = parseInt(levelElement?.textContent?.trim() || '1');
    
    // Determine quality from CSS classes
    let quality: ItemQuality = ItemQuality.WHITE;
    const qualityClass = element.className;
    if (qualityClass.includes('green')) quality = ItemQuality.GREEN;
    else if (qualityClass.includes('blue')) quality = ItemQuality.BLUE;
    else if (qualityClass.includes('purple')) quality = ItemQuality.PURPLE;
    else if (qualityClass.includes('orange')) quality = ItemQuality.ORANGE;
    else if (qualityClass.includes('red')) quality = ItemQuality.RED;
    
    // Parse time remaining
    const timeText = timeElement?.textContent?.trim() || '0:00:00';
    const timeMatch = timeText.match(/(\d+):(\d+):(\d+)/);
    const timeRemaining = timeMatch ? 
      parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]) : 0;
    
    return {
      id: index.toString(),
      name,
      quality,
      level,
      currentBid,
      buyoutPrice,
      timeRemaining,
      bidder,
      canBid: !!(bidButton && !bidButton.hasAttribute('disabled')),
      canBuyout: !!(buyoutButton && !buyoutButton.hasAttribute('disabled'))
    };
  }

  private filterItemsForBuying(items: MarketItem[], settings: any): MarketItem[] {
    return items.filter(item => {
      // Check price limit
      if (item.price > settings.maxPrice) return false;
      
      // Check quality filter
      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
        return false;
      }
      
      // Check level range
      if (settings.minLevel && item.level < settings.minLevel) return false;
      if (settings.maxLevel && item.level > settings.maxLevel) return false;
      
      // Check item name filter
      if (settings.itemNames.length > 0) {
        const nameMatch = settings.itemNames.some((name: string) => 
          item.name.toLowerCase().includes(name.toLowerCase())
        );
        if (!nameMatch) return false;
      }
      
      // Check seller blacklist
      if (settings.blacklistedSellers.includes(item.seller)) return false;
      
      return item.canBuy;
    });
  }

  private filterItemsForSelling(items: any[], settings: any): any[] {
    // This would filter inventory items for selling
    return items.filter(item => {
      // Apply selling rules similar to package management
      return true; // Simplified
    });
  }

  private filterItemsForBidding(items: AuctionItem[], settings: any): AuctionItem[] {
    return items.filter(item => {
      // Check if we're already the highest bidder
      if (item.bidder === 'You') return false;
      
      // Check maximum bid
      if (item.currentBid >= settings.maxBid) return false;
      
      // Check time remaining (don't bid on items ending too soon)
      if (item.timeRemaining < settings.minTimeRemaining) return false;
      
      // Check quality and other filters similar to buying
      if (settings.qualityFilter.length > 0 && !settings.qualityFilter.includes(item.quality)) {
        return false;
      }
      
      return item.canBid;
    });
  }

  private async buyItem(item: MarketItem): Promise<void> {
    console.log(`Market Operations: Buying ${item.name} for ${item.price} gold`);
    
    const itemElement = document.querySelector(`.market_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const buyButton = itemElement.querySelector('.buy_button, [data-action="buy"]');
    if (buyButton && !buyButton.hasAttribute('disabled')) {
      (buyButton as HTMLElement).click();
      
      // Handle confirmation if present
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_buy, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private async sellItem(item: any, settings: any): Promise<void> {
    console.log(`Market Operations: Selling ${item.name}`);
    
    // This would handle putting items up for sale
    // Implementation would depend on the game's selling interface
  }

  private async bidOnItem(item: AuctionItem, settings: any): Promise<void> {
    const bidAmount = Math.min(item.currentBid + settings.bidIncrement, settings.maxBid);
    
    console.log(`Market Operations: Bidding ${bidAmount} gold on ${item.name}`);
    
    const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const bidInput = itemElement.querySelector('.bid_input, input[name="bid"]') as HTMLInputElement;
    const bidButton = itemElement.querySelector('.bid_button, [data-action="bid"]');
    
    if (bidInput && bidButton && !bidButton.hasAttribute('disabled')) {
      bidInput.value = bidAmount.toString();
      bidInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      (bidButton as HTMLElement).click();
    }
  }

  private async buyoutItem(item: AuctionItem, settings: any): Promise<void> {
    if (item.buyoutPrice > settings.maxBuyout) return;
    
    console.log(`Market Operations: Buying out ${item.name} for ${item.buyoutPrice} gold`);
    
    const itemElement = document.querySelector(`.auction_item:nth-child(${parseInt(item.id) + 1})`);
    if (!itemElement) return;
    
    const buyoutButton = itemElement.querySelector('.buyout_button, [data-action="buyout"]');
    if (buyoutButton && !buyoutButton.hasAttribute('disabled')) {
      (buyoutButton as HTMLElement).click();
      
      // Handle confirmation
      await this.delay(500);
      const confirmButton = document.querySelector('.confirm_buyout, [data-action="confirm"]');
      if (confirmButton) {
        (confirmButton as HTMLElement).click();
      }
    }
  }

  private async getInventoryItems(): Promise<any[]> {
    // This would get items from the player's inventory
    // Implementation would depend on how inventory is accessed
    return [];
  }

  private shouldContinueOperations(settings: MarketOperationsSettings): boolean {
    // Check gold spending limit
    if (settings.autoBuy.enabled && this.goldSpent >= settings.autoBuy.maxGoldSpend) {
      console.log('Market Operations: Gold spending limit reached');
      return false;
    }
    
    // Check daily limits
    if (settings.dailyBuyLimit > 0 && this.itemsBought >= settings.dailyBuyLimit) {
      console.log('Market Operations: Daily buy limit reached');
      return false;
    }
    
    return true;
  }

  async getMarketStatus(): Promise<any> {
    return {
      isRunning: this.isRunning,
      itemsBought: this.itemsBought,
      itemsSold: this.itemsSold,
      goldSpent: this.goldSpent,
      goldEarned: this.goldEarned,
      lastUpdate: new Date()
    };
  }

  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 1000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
