import { TurmaSettings } from '../types';
import { GladiatorUrlHelper } from '../utils/gladiator-url-helper';
import { StorageManager } from '../utils/storage-manager';
export interface TurmaTarget {
    id: string;
    name: string;
    level: number;
    guild: string;
    gold: number;
    isOnline: boolean;
    canAttack: boolean;
    lastAttacked?: Date;
}
export declare class TurmaCombatModule {
    private urlHelper;
    private storageManager;
    private isRunning;
    private attackCount;
    private goldRaided;
    constructor(urlHelper: GladiatorUrlHelper, storageManager: StorageManager);
    start(settings: TurmaSettings): Promise<void>;
    stop(): Promise<void>;
    private navigateToTurma;
    private performTurmaLoop;
    private getAvailableTargets;
    private parseTurmaTarget;
    private isValidTarget;
    private selectBestTarget;
    private attackTarget;
    private processAttackResult;
    private addToAutoIgnore;
    private shouldContinueAttacking;
    private hasActiveTurmaQuest;
    private waitForPageLoad;
    private waitForAttackResult;
    private parseAttackResult;
    private updateStatistics;
    getTurmaStatus(): Promise<any>;
    private delay;
}
//# sourceMappingURL=turma-combat.d.ts.map