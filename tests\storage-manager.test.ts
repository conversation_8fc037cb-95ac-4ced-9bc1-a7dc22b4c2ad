// Tests for StorageManager

import { StorageManager } from '../src/utils/storage-manager';
import { BotSettings, BotStatus } from '../src/types';

// Mock chrome.storage API with Promise-based methods
const mockStorage = {
  local: {
    get: jest.fn().mockImplementation((keys) => {
      return Promise.resolve({});
    }),
    set: jest.fn().mockImplementation((data) => {
      return Promise.resolve();
    }),
    remove: jest.fn().mockImplementation((keys) => {
      return Promise.resolve();
    }),
    clear: jest.fn().mockImplementation(() => {
      return Promise.resolve();
    })
  }
};

// @ts-ignore
global.chrome = {
  storage: mockStorage
};

describe('StorageManager', () => {
  let storageManager: StorageManager;

  beforeEach(() => {
    storageManager = new StorageManager();
    jest.clearAllMocks();
  });

  describe('getSettings', () => {
    it('should return settings from storage merged with defaults', async () => {
      const mockSettings: Partial<BotSettings> = {
        general: {
          selectedBackpack: 1,
          maxRandomDelay: 10,
          loginDelay: 5,
          highlightUnderworldItems: true,
          showShopBox: true,
          shopMinQuality: 1,
          showBiddenBox: true,
          autoCollectGodOils: true,
          autoCollectDaily: true
        }
      };

      mockStorage.local.get.mockResolvedValue({ 'gh_settings': mockSettings });

      const result = await storageManager.getSettings();

      // Should contain the stored settings
      expect(result?.general).toEqual(mockSettings.general);

      // Should also contain default settings for other properties
      expect(result?.arena).toBeDefined();
      expect(result?.heal).toBeDefined();
      expect(result?.quest).toBeDefined();
      expect(result?.market).toBeDefined();

      expect(mockStorage.local.get).toHaveBeenCalledWith('gh_settings');
    });

    it('should return default settings if no settings found', async () => {
      mockStorage.local.get.mockResolvedValue({});

      const result = await storageManager.getSettings();

      // Should return default settings, not null
      expect(result).not.toBeNull();
      expect(result?.general).toBeDefined();
      expect(result?.arena).toBeDefined();
      expect(result?.heal).toBeDefined();
    });
  });

  describe('saveSettings', () => {
    it('should save settings to storage', async () => {
      const mockSettings: Partial<BotSettings> = {
        general: {
          selectedBackpack: 1,
          maxRandomDelay: 10,
          loginDelay: 5,
          highlightUnderworldItems: true,
          showShopBox: true,
          shopMinQuality: 1,
          showBiddenBox: true,
          autoCollectGodOils: true,
          autoCollectDaily: true
        }
      };

      // Mock getSettings to return empty object for merging
      mockStorage.local.get.mockResolvedValue({});
      mockStorage.local.set.mockResolvedValue(undefined);

      await storageManager.saveSettings(mockSettings);

      // Should have been called with merged settings (defaults + user settings)
      expect(mockStorage.local.set).toHaveBeenCalledTimes(1);
      const savedSettings = mockStorage.local.set.mock.calls[0][0]['gh_settings'];

      // Should contain the user settings
      expect(savedSettings.general).toEqual(mockSettings.general);

      // Should also contain default settings
      expect(savedSettings.arena).toBeDefined();
      expect(savedSettings.heal).toBeDefined();
    });
  });

  describe('getBotStatus', () => {
    it('should return bot status from storage', async () => {
      const mockStatus: Partial<BotStatus> = {
        isActive: true,
        isPaused: false
      };

      mockStorage.local.get.mockResolvedValue({ 'gh_bot_status': mockStatus });

      const result = await storageManager.getBotStatus();
      expect(result).toEqual(mockStatus);
    });

    it('should return null if no status found', async () => {
      mockStorage.local.get.mockResolvedValue({});

      const result = await storageManager.getBotStatus();
      expect(result).toBeNull();
    });
  });

  describe('saveBotStatus', () => {
    it('should save bot status to storage', async () => {
      const mockStatus: Partial<BotStatus> = {
        isActive: true,
        isPaused: false
      };

      mockStorage.local.set.mockResolvedValue(undefined);

      await storageManager.saveBotStatus(mockStatus);
      expect(mockStorage.local.set).toHaveBeenCalledWith({
        'gh_bot_status': mockStatus
      });
    });
  });
});
