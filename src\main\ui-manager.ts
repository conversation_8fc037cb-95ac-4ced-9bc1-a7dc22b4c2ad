// UI manager - handles bot UI updates and interactions

import { BotStatus, GameBotStatistics } from '../types';

export class UIManager {
  constructor() {
    // UI Manager initialized
  }

  updateStatus(status: BotStatus): void {
    // Update status indicator
    const statusIndicator = document.getElementById('gh-status-indicator');
    const statusText = document.getElementById('gh-status-text');
    const toggleButton = document.getElementById('gh-toggle-bot') as HTMLButtonElement;

    if (statusIndicator && statusText && toggleButton) {
      const statusDot = statusIndicator.querySelector('.gh-status-dot') as HTMLElement;

      if (status.isActive) {
        if (status.isPaused) {
          statusDot.style.backgroundColor = '#f39c12'; // Orange for paused
          statusText.textContent = 'Paused';
          toggleButton.textContent = 'Resume Bot';
        } else {
          statusDot.style.backgroundColor = '#27ae60'; // Green for active
          statusText.textContent = 'Active';
          toggleButton.textContent = 'Stop Bot';
        }
      } else {
        // Show different status based on whether it was previously active
        if (status.wasActive) {
          statusDot.style.backgroundColor = '#f39c12'; // Orange for was active but stopped
          statusText.textContent = 'Ready (was active)';
          toggleButton.textContent = 'Start Bot';
        } else {
          statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive
          statusText.textContent = status.error ? 'Error' : 'Inactive';
          toggleButton.textContent = 'Start Bot';
        }
      }
    }

    // Update runtime
    this.updateRuntime(status.statistics);

    // Update actions count
    this.updateActionsCount(status.statistics.actionsPerformed);
  }

  private updateRuntime(statistics: GameBotStatistics): void {
    const runtimeElement = document.getElementById('gh-runtime');
    if (!runtimeElement) return;

    const now = new Date();
    const startTime = new Date(statistics.startTime);
    const runtime = now.getTime() - startTime.getTime();

    const hours = Math.floor(runtime / (1000 * 60 * 60));
    const minutes = Math.floor((runtime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((runtime % (1000 * 60)) / 1000);

    runtimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  private updateActionsCount(actionsPerformed: number): void {
    const actionsElement = document.getElementById('gh-actions');
    if (actionsElement) {
      actionsElement.textContent = actionsPerformed.toString();
    }
  }

  showStatistics(statistics: GameBotStatistics): void {
    // Remove existing modal if present
    const existingModal = document.getElementById('gh-statistics-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create and show statistics modal
    const modal = this.createStatisticsModal(statistics);
    modal.id = 'gh-statistics-modal';
    document.body.appendChild(modal);

    // Add close functionality with proper event handling
    const closeBtn = modal.querySelector('.gh-modal-close');
    const modalContent = modal.querySelector('.gh-modal-content');

    closeBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      modal.remove();
    });

    // Prevent modal content clicks from closing modal
    modalContent?.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    // Close on background click only - with delay to prevent immediate closure
    setTimeout(() => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          e.preventDefault();
          e.stopPropagation();
          modal.remove();
        }
      });
    }, 100);

    // Close on Escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        modal.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }

  private createStatisticsModal(statistics: GameBotStatistics): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'gh-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'gh-modal-content';
    modalContent.style.cssText = `
      background: #2c3e50;
      color: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 500px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    `;

    modalContent.innerHTML = `
      <div class="gh-modal-header">
        <h2>Bot Statistics</h2>
        <button class="gh-modal-close" style="float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
      </div>
      <div class="gh-modal-body">
        <div class="gh-stat-group">
          <h3>General</h3>
          <p>Start Time: ${new Date(statistics.startTime).toLocaleString()}</p>
          <p>Total Runtime: ${this.formatDuration(statistics.totalRunTime)}</p>
          <p>Actions Performed: ${statistics.actionsPerformed}</p>
          <p>Errors Encountered: ${statistics.errorsEncountered}</p>
        </div>
        <div class="gh-stat-group">
          <h3>Combat</h3>
          <p>Combats Won: ${statistics.combatsWon}</p>
          <p>Combats Lost: ${statistics.combatsLost}</p>
          <p>Win Rate: ${statistics.combatsWon + statistics.combatsLost > 0 ?
            ((statistics.combatsWon / (statistics.combatsWon + statistics.combatsLost)) * 100).toFixed(1) : 0}%</p>
        </div>
        <div class="gh-stat-group">
          <h3>Rewards</h3>
          <p>Gold Earned: ${statistics.goldEarned.toLocaleString()}</p>
          <p>Experience Gained: ${statistics.experienceGained.toLocaleString()}</p>
          <p>Items Found: ${statistics.itemsFound}</p>
        </div>
      </div>
    `;

    modal.appendChild(modalContent);
    return modal;
  }

  private formatDuration(milliseconds: number): string {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);

    return `${hours}h ${minutes}m ${seconds}s`;
  }

  showNotification(title: string, message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    const container = document.getElementById('gh-notifications');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `gh-notification gh-notification-${type}`;
    notification.innerHTML = `
      <div class="gh-notification-content">
        <strong>${title}</strong>
        <p>${message}</p>
      </div>
      <button class="gh-notification-close">×</button>
    `;

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);

    // Add close button functionality
    notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {
      notification.remove();
    });
  }
}