{"version": 3, "file": "content-ui/index.iife.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;ACVA;;;;;;;;;;;;;;;;ACAA,gCAAgC;AAsBhC,IAAY,WAKX;AALD,WAAY,WAAW;IACrB,wCAAyB;IACzB,sCAAuB;IACvB,kCAAmB;IACnB,oCAAqB;AACvB,CAAC,EALW,WAAW,KAAX,WAAW,QAKtB;AA2BD,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,uCAAmB;IACnB,yCAAqB;IACrB,mCAAe;IACf,yCAAqB;AACvB,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,QAK3B;;;;;;;;;;;;;;;;;;;AC3DD,6CAA6C;AAmC7C,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,8CAA+B;IAC/B,gDAAiC;IACjC,4CAA6B;IAC7B,8CAA+B;IAC/B,kDAAmC;IACnC,4CAA6B;IAC7B,8BAAe;AACjB,CAAC,EARW,WAAW,KAAX,WAAW,QAQtB;AA2DD,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,0CAAuB;IACvB,8CAA2B;IAC3B,oDAAiC;AACnC,CAAC,EAJW,eAAe,KAAf,eAAe,QAI1B;AAoBD,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IAC1B,mCAAe;IACf,iCAAa;IACb,uCAAmB;IACnB,yCAAqB;IACrB,qCAAiB;IACjB,qCAAiB;AACnB,CAAC,EAPW,gBAAgB,KAAhB,gBAAgB,QAO3B;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,gCAAgB;IAChB,kCAAkB;IAClB,yCAAyB;IACzB,yCAAyB;AAC3B,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AAED,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,uCAAqB;IACrB,yCAAuB;IACvB,oCAAkB;IAClB,sCAAoB;AACtB,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;;;;;;;;;;;;;;;;;;ACnJD,qCAAqC;AAgBrC,IAAY,SAuBX;AAvBD,WAAY,SAAS;IACnB,0BAAa;IACb,gCAAmB;IACnB,oCAAuB;IACvB,oCAAuB;IACvB,wCAA2B;IAC3B,4CAA+B;IAC/B,sCAAyB;IACzB,4BAAe;IACf,0CAA6B;IAC7B,kDAAqC;IACrC,oDAAuC;IACvC,0CAA6B;IAC7B,8CAAiC;IACjC,gDAAmC;IACnC,gCAAmB;IACnB,kCAAqB;IACrB,oCAAuB;IACvB,gDAAmC;IACnC,kDAAqC;IACrC,kDAAqC;IACrC,gDAAmC;IACnC,gDAAmC;AACrC,CAAC,EAvBW,SAAS,KAAT,SAAS,QAuBpB;AAqBD,IAAY,QAUX;AAVD,WAAY,QAAQ;IAClB,+BAAmB;IACnB,iCAAqB;IACrB,qCAAyB;IACzB,6BAAiB;IACjB,+BAAmB;IACnB,mCAAuB;IACvB,2BAAe;IACf,qCAAyB;IACzB,mDAAuC;AACzC,CAAC,EAVW,QAAQ,KAAR,QAAQ,QAUnB;AAkBD,IAAY,WAIX;AAJD,WAAY,WAAW;IACrB,gCAAiB;IACjB,gCAAiB;IACjB,4BAAa;AACf,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,2CAAuB;IACvB,yCAAqB;AACvB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;;;;;;;;;;;;;;;;;;;AClGD,2CAA2C;AAoD3C,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,+CAAS;IACT,+CAAS;IACT,6CAAQ;IACR,iDAAU;IACV,iDAAU;IACV,2CAAO;AACT,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAED,IAAY,QAkBX;AAlBD,WAAY,QAAQ;IAClB,6BAAiB;IACjB,6BAAiB;IACjB,6BAAiB;IACjB,2BAAe;IACf,6BAAiB;IACjB,2BAAe;IACf,yBAAa;IACb,6BAAiB;IACjB,6BAAiB;IACjB,2CAA+B;IAC/B,+BAAmB;IACnB,6BAAiB;IACjB,mCAAuB;IACvB,2CAA+B;IAC/B,2BAAe;IACf,6BAAiB;IACjB,uCAA2B;AAC7B,CAAC,EAlBW,QAAQ,KAAR,QAAQ,QAkBnB;AAkCD,IAAY,SASX;AATD,WAAY,SAAS;IACnB,4BAAe;IACf,wCAA2B;IAC3B,sCAAyB;IACzB,gCAAmB;IACnB,0BAAa;IACb,oCAAuB;IACvB,8BAAiB;IACjB,oCAAuB;AACzB,CAAC,EATW,SAAS,KAAT,SAAS,QASpB;AAgBD,IAAY,OAOX;AAPD,WAAY,OAAO;IACjB,4BAAiB;IACjB,4BAAiB;IACjB,wBAAa;IACb,8BAAmB;IACnB,0BAAe;IACf,8BAAmB;AACrB,CAAC,EAPW,OAAO,KAAP,OAAO,QAOlB;AASD,IAAY,YAQX;AARD,WAAY,YAAY;IACtB,yCAAyB;IACzB,mCAAmB;IACnB,+BAAe;IACf,+BAAe;IACf,yCAAyB;IACzB,iCAAiB;IACjB,+BAAe;AACjB,CAAC,EARW,YAAY,KAAZ,YAAY,QAQvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClKD,iDAAiD;AAErB;AACA;AACG;AACJ;AACL;AACD;AACE;;;;;;;;;;;;ACRvB,8CAA8C;;;;;;;;;;;;;;;;;;;;;ACA9C,8CAA8C;AAsC9C,IAAY,UAaX;AAbD,WAAY,UAAU;IACpB,mCAAqB;IACrB,2CAA6B;IAC7B,2CAA6B;IAC7B,mDAAqC;IACrC,6CAA+B;IAC/B,6CAA+B;IAC/B,qDAAuC;IACvC,2CAA6B;IAC7B,qCAAuB;IACvB,yCAA2B;IAC3B,qCAAuB;IACvB,qDAAuC;AACzC,CAAC,EAbW,UAAU,KAAV,UAAU,QAarB;AAOD,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,yCAAuB;IACvB,+BAAa;IACb,mCAAiB;IACjB,+BAAa;AACf,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;AAYD,IAAY,iBAgBX;AAhBD,WAAY,iBAAiB;IAC3B,0DAAqC;IACrC,0DAAqC;IACrC,sDAAiC;IACjC,sDAAiC;IACjC,wEAAmD;IACnD,kEAA6C;IAC7C,oEAA+C;IAC/C,8DAAyC;IACzC,kEAA6C;IAC7C,gFAA2D;IAC3D,8CAAyB;IACzB,sEAAiD;IACjD,8DAAyC;IACzC,8EAAyD;IACzD,oDAA+B;AACjC,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,QAgB5B;AA6DD,IAAY,qBAGX;AAHD,WAAY,qBAAqB;IAC/B,gDAAuB;IACvB,gEAAuC;AACzC,CAAC,EAHW,qBAAqB,KAArB,qBAAqB,QAGhC;AAmDD,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,2CAAyB;IACzB,uCAAqB;IACrB,mCAAiB;AACnB,CAAC,EAJW,cAAc,KAAd,cAAc,QAIzB;AAED,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,yCAAiB;IACjB,yCAAiB;IACjB,qCAAa;AACf,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,QAI/B;;;;;;;;;;;;;;;;;;;ACxND,yBAAyB;AAYzB,IAAY,gBAQX;AARD,WAAY,gBAAgB;IAC1B,iCAAa;IACb,uCAAmB;IACnB,uCAAmB;IACnB,mCAAe;IACf,+CAA2B;IAC3B,6CAAyB;IACzB,+CAA2B;AAC7B,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,QAQ3B;AAiCD,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,4BAAe;IACf,8BAAiB;IACjB,4BAAe;IACf,wCAA2B;AAC7B,CAAC,EALW,SAAS,KAAT,SAAS,QAKpB;AAED,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,kCAAmB;IACnB,sCAAuB;IACvB,kCAAmB;IACnB,gCAAiB;IACjB,kCAAmB;IACnB,4BAAa;AACf,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAcD,IAAY,SAWX;AAXD,WAAY,SAAS;IACnB,0BAAa;IACb,8BAAiB;IACjB,8BAAiB;IACjB,kCAAqB;IACrB,4BAAe;IACf,kCAAqB;IACrB,4BAAe;IACf,4BAAe;IACf,0BAAa;IACb,0BAAa;AACf,CAAC,EAXW,SAAS,KAAT,SAAS,QAWpB;AAeD,IAAY,cASX;AATD,WAAY,cAAc;IACxB,uCAAqB;IACrB,6BAAW;IACX,6BAAW;IACX,2CAAyB;IACzB,2CAAyB;IACzB,qCAAmB;IACnB,iCAAe;IACf,6BAAW;AACb,CAAC,EATW,cAAc,KAAd,cAAc,QASzB;;;;;;;;;;;;;;;ACpHD,iDAAiD;AAI1C,MAAM,kBAAkB;IAA/B;QACU,aAAQ,GAAY,KAAK,CAAC;QAC1B,YAAO,GAAqB;YAClC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IA8GJ,CAAC;IA5GC;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,mDAAmD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW;QACjB,OAAO,kCAAkC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW,EAAE,QAAiB,KAAK;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QAEF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,0BAA0B;YAC1D,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5C,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAuC,EAAE,OAAe,WAAW;QAC1E,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAuC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;CACF;;;;;;;UCvHD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;ACNA,uDAAuD;AAEE;AACU;AACpC;AAE/B,MAAM,gBAAgB;IAMpB;QAJQ,eAAU,GAAY,KAAK,CAAC;QAE5B,cAAS,GAAQ,IAAI,CAAC;QAG5B,IAAI,CAAC,SAAS,GAAG,IAAI,2EAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACtC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;QAED,6CAA6C;QAC7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,OAAyB,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;YACvF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU;QAChB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,0CAA0C;QAC1C,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,+CAAW,CAAC,aAAa;YAC/B,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACnC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACvC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,EAAE;SACxC,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YAC/C,IAAI,QAAQ,EAAE,WAAW,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,GAAW;QAChC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;QACT,CAAC;QAED,0CAA0C;QACzC,MAAc,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;QAEjD,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,2CAA2C;QAC3C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,8BAA8B;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAE5B,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACzD,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACnB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC,CAAC;QACF,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACtE,CAAC,CAAC;QAEF,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC;IAEO,QAAQ;QACd,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,6BAA6B;QAC7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,wCAAwC;QACxC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAEO,kBAAkB;QACxB,gCAAgC;QAChC,IAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC;YAAE,OAAO;QAExD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,GAAG,kBAAkB,CAAC;QAC9B,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;QAE7B,KAAK,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BjB,CAAC;QAEF,iBAAiB;QACjB,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;;;;;KAarB,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEjC,sBAAsB;QACtB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,yBAAyB;QAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAC/D,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE/D,WAAW,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC1C,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAAgB,CAAC;YAC3E,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC;YACrD,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACvD,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,SAAS,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACxC,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,WAAW,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAC3C,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACvC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,aAAa,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAC7C,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,wBAAwB;QAC9B,IAAI,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC;YAAE,OAAO;QAExD,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,EAAE,GAAG,kBAAkB,CAAC;QAClC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;QACzC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;KAMzB,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAEO,wBAAwB;QAC9B,6DAA6D;QAC7D,MAAM,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QACzE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAClB,IAAoB,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YACxD,IAAoB,CAAC,KAAK,CAAC,SAAS,GAAG,iCAAiC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,MAAM,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACzC,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC,EAAE,aAAa,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,qCAAqC;QACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,6DAA6D,CAAgB,CAAC;QACzH,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC/D,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC;YAE9C,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEjE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,+BAA+B;gBAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,6BAA6B;QAC7B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;YAC1D,IAAI,SAAS,KAAK,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACnD,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC9E,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;gBAChD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,WAAW,CAAC,KAAK,IAAI,EAAE;YACrB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBAC/D,MAAM,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC;gBACvC,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9E,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;oBAC/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;oBAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,qBAAqB;YACvB,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAEO,cAAc,CAAC,MAAW;QAChC,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAsB,CAAC;QACnF,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAgB,CAAC;QAE1E,IAAI,UAAU,IAAI,YAAY,IAAI,SAAS,EAAE,CAAC;YAC5C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACpB,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,oBAAoB;oBACjE,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAClC,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,mBAAmB;oBAChE,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAClC,YAAY,CAAC,WAAW,GAAG,UAAU,CAAC;gBACxC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,CAAC,mBAAmB;gBAChE,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;gBAC7D,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,SAAS;QACf,4CAA4C;QAC5C,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,aAAa,CAAC,CAAC;QAC7C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEO,YAAY;QAClB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,0CAA0C;QAC1C,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,mBAAmB;QACzB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAE1C,gCAAgC;QAChC,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QACnE,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,aAAa,CAAC,MAAM,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,GAAG,mBAAmB,CAAC;QAC/B,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;QAC7B,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;;;KAWrB,CAAC;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,CAAC,SAAS,GAAG,kBAAkB,CAAC;QAC5C,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;KAS5B,CAAC;QAEF,YAAY,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwDxB,CAAC;QAEF,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEzD,wBAAwB;QACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,sBAAsB;QACtB,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;YAE1C,6CAA6C;YAC7C,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAqB,CAAC;YACvF,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAqB,CAAC;YACzF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAqB,CAAC;YAC7F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAqB,CAAC;YAC7F,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAqB,CAAC;YAC3F,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAqB,CAAC;YACvF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAqB,CAAC;YAC7F,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAqB,CAAC;YAC/F,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAqB,CAAC;YAC7F,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAqB,CAAC;YAE/F,IAAI,SAAS;gBAAE,SAAS,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC;YACxE,IAAI,UAAU;gBAAE,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,UAAU,IAAI,CAAC,CAAC;YACrE,IAAI,YAAY;gBAAE,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC;YAC1E,IAAI,YAAY;gBAAE,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC;YAC1E,IAAI,WAAW;gBAAE,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC;YACvE,IAAI,SAAS;gBAAE,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE,CAAC;YAChE,IAAI,YAAY;gBAAE,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC;YAC1E,IAAI,aAAa;gBAAE,aAAa,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,IAAI,KAAK,CAAC;YAC7E,IAAI,YAAY;gBAAE,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC;YAC1E,IAAI,aAAa;gBAAE,aAAa,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,IAAI,KAAK,CAAC;QAE/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAEO,4BAA4B,CAAC,KAAkB;QACrD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QAChE,MAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC;QAE/E,QAAQ,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACxC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,SAAS,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACzC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACvC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,kDAAkD;QAClD,YAAY,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAC5C,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,CAAC,CAAC,eAAe,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,2EAA2E;QAC3E,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACpC,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5E,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;oBACvB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAClD,CAAC,CAAC,cAAc,EAAE,CAAC;oBACnB,CAAC,CAAC,eAAe,EAAE,CAAC;oBACpB,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,sBAAsB;QACtB,MAAM,YAAY,GAAG,CAAC,CAAgB,EAAE,EAAE;YACxC,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACjD,KAAK,CAAC,MAAM,EAAE,CAAC;gBACf,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC;QACF,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;YAE1C,4BAA4B;YAC5B,MAAM,SAAS,GAAI,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAsB,EAAE,OAAO,CAAC;YAClG,MAAM,UAAU,GAAG,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAsB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACjH,MAAM,YAAY,GAAI,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAsB,EAAE,OAAO,CAAC;YACxG,MAAM,YAAY,GAAI,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAsB,EAAE,OAAO,CAAC;YACxG,MAAM,WAAW,GAAI,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAsB,EAAE,OAAO,CAAC;YACtG,MAAM,SAAS,GAAG,QAAQ,CAAE,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAsB,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;YAChH,MAAM,YAAY,GAAI,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAsB,EAAE,OAAO,CAAC;YACxG,MAAM,aAAa,GAAI,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAsB,EAAE,OAAO,CAAC;YAC1G,MAAM,YAAY,GAAI,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAsB,EAAE,OAAO,CAAC;YACxG,MAAM,aAAa,GAAI,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAsB,EAAE,OAAO,CAAC;YAE1G,yBAAyB;YACzB,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;YAC1C,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;YACvC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YAEzC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;YACtC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;YAEtC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;YACtC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;YAEtC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAEpC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;YACtC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;YAEtC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC;YACxC,QAAQ,CAAC,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC;YAExC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;YACtC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;YAEtC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC;YACxC,QAAQ,CAAC,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC;YAExC,kBAAkB;YAClB,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE5D,wCAAwC;YACxC,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;YACzE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAE5B,IAAI,CAAC,gBAAgB,CAAC;gBACpB,KAAK,EAAE,gBAAgB;gBACvB,OAAO,EAAE,6CAA6C;aACvD,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC;gBACpB,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,4CAA4C;aACtD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,cAAc;QACpB,wBAAwB;QACxB,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAClD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEO,aAAa,CAAC,OAAyB,EAAE,OAAY,EAAE,aAAuC;QACpG,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,+CAAW,CAAC,YAAY;gBAC3B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,+CAAW,CAAC,KAAK;gBACpB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM;YACR;gBACE,MAAM;QACV,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAS;QAChC,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,CAAC,SAAS,GAAG,sCAAsC,CAAC;QAChE,YAAY,CAAC,SAAS,GAAG;;kBAEX,IAAI,CAAC,KAAK,IAAI,cAAc;aACjC,IAAI,CAAC,OAAO;;;KAGpB,CAAC;QAEF,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEpC,8BAA8B;QAC9B,UAAU,CAAC,GAAG,EAAE;YACd,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,iCAAiC;QACjC,YAAY,CAAC,aAAa,CAAC,wBAAwB,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACnF,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,IAAS;QACzB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC;YACpB,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,mBAAmB;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,CAAC;IAEO,sBAAsB;QAC5B,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9D,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACzC,CAAC;IAEO,sBAAsB;QAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACjC,CAAC;CACF;AAED,gCAAgC;AAChC,IAAI,gBAAgB,EAAE,CAAC", "sources": ["webpack://gladiatus-helper-bot-ts/webpack/universalModuleDefinition", "webpack://gladiatus-helper-bot-ts/./src/styles/content.css?850d", "webpack://gladiatus-helper-bot-ts/./src/types/api.ts", "webpack://gladiatus-helper-bot-ts/./src/types/extension.ts", "webpack://gladiatus-helper-bot-ts/./src/types/game.ts", "webpack://gladiatus-helper-bot-ts/./src/types/gladiatus.ts", "webpack://gladiatus-helper-bot-ts/./src/types/index.ts", "webpack://gladiatus-helper-bot-ts/./src/types/localization.ts", "webpack://gladiatus-helper-bot-ts/./src/types/settings.ts", "webpack://gladiatus-helper-bot-ts/./src/types/ui.ts", "webpack://gladiatus-helper-bot-ts/./src/utils/gladiator-url-helper.ts", "webpack://gladiatus-helper-bot-ts/webpack/bootstrap", "webpack://gladiatus-helper-bot-ts/webpack/runtime/define property getters", "webpack://gladiatus-helper-bot-ts/webpack/runtime/hasOwnProperty shorthand", "webpack://gladiatus-helper-bot-ts/webpack/runtime/make namespace object", "webpack://gladiatus-helper-bot-ts/./src/content/content-ui.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "// extracted by mini-css-extract-plugin\nexport {};", "// API and network request types\r\n\r\nexport interface ApiResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport interface LoginApiResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface LicenseInfo {\r\n  isActive: boolean;\r\n  expiresAt?: Date;\r\n  daysRemaining?: number;\r\n  type: LicenseType;\r\n  key?: string;\r\n}\r\n\r\nexport enum LicenseType {\r\n  FREE_TRIAL = 'free_trial',\r\n  ESSENTIAL = 'essential',\r\n  PREMIUM = 'premium',\r\n  INACTIVE = 'inactive'\r\n}\r\n\r\nexport interface BankData {\r\n  bankId?: string;\r\n  isBank: boolean;\r\n  clients: BankClient[];\r\n  requests: BankRequest[];\r\n  selectedBackpack: number;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankClient {\r\n  id: string;\r\n  name: string;\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n  goldDeposited: number;\r\n}\r\n\r\nexport interface BankRequest {\r\n  clientId: string;\r\n  goldAmount: number;\r\n  createdAt: Date;\r\n  state: BankRequestState;\r\n}\r\n\r\nexport enum BankRequestState {\r\n  PENDING = 'pending',\r\n  PROGRESS = 'progress',\r\n  READY = 'ready',\r\n  COMPLETE = 'complete'\r\n}\r\n\r\nexport interface QuestRule {\r\n  id: string;\r\n  position: number;\r\n  type: string;\r\n  conditions: QuestCondition[];\r\n  pickIfInactive: boolean;\r\n  priority: number;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface QuestCondition {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport interface MarketRule {\r\n  id: string;\r\n  name: string;\r\n  itemTypes: string[];\r\n  colors: string[];\r\n  maxPrice: number;\r\n  autoBuy: boolean;\r\n  allowBounded: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface PauseSchedule {\r\n  id: string;\r\n  startTime: string;\r\n  endTime: string;\r\n  workOnPause: boolean;\r\n  randomStartMinutes: number;\r\n  randomEndMinutes: number;\r\n  omitFromUW: boolean;\r\n  forcePause: boolean;\r\n  donateGold: boolean;\r\n  enabled: boolean;\r\n}", "// Chrome extension specific type definitions\r\n\r\nimport {\r\n  CostumeSettings,\r\n  TurmaSettings,\r\n  ArenaSettings,\r\n  ExpeditionSettings,\r\n  DungeonSettings,\r\n  EventSettings,\r\n  UnderworldSettings,\r\n  PackageSettings,\r\n  ForgeAutomationSettings,\r\n  RepairAutomationSettings,\r\n  QuestManagementSettings,\r\n  MarketOperationsSettings\r\n} from './settings';\r\nimport {\r\n  ShopSettings,\r\n  BankSettings,\r\n  GuildSettings\r\n} from './game';\r\nimport { QuestRule, MarketRule, PauseSchedule, BankData, LicenseInfo } from './api';\r\n\r\nexport interface ExtensionMessage {\r\n  type: MessageType;\r\n  data?: any;\r\n  isLobby?: boolean;\r\n  shouldLogin?: boolean;\r\n  loginDelay?: number;\r\n  loginFailed?: boolean;\r\n  server?: string;\r\n  playerId?: string;\r\n  language?: string;\r\n}\r\n\r\nexport enum MessageType {\r\n  LOGIN_REQUEST = 'LOGIN_REQUEST',\r\n  LOGIN_RESPONSE = 'LOGIN_RESPONSE',\r\n  LOGIN_FAILED = 'LOGIN_FAILED',\r\n  SCRIPT_STATUS = 'SCRIPT_STATUS',\r\n  SETTINGS_UPDATE = 'SETTINGS_UPDATE',\r\n  NOTIFICATION = 'NOTIFICATION',\r\n  ERROR = 'ERROR'\r\n}\r\n\r\nexport interface ExtensionStorage {\r\n  settings: BotSettings;\r\n  statistics: BotStatistics;\r\n  licenseInfo: LicenseInfo;\r\n  pauseSchedule: PauseSchedule[];\r\n  questRules: QuestRule[];\r\n  marketRules: MarketRule[];\r\n  bankData: BankData;\r\n}\r\n\r\nexport interface BotSettings {\r\n  general: GeneralSettings;\r\n  heal: HealSettings;\r\n  hideGold: HideGoldSettings;\r\n  packages: PackageSettings;\r\n  costumes: CostumeSettings;\r\n  turma: TurmaSettings;\r\n  arena: ArenaSettings;\r\n  expeditions: ExpeditionSettings;\r\n  dungeon: DungeonSettings;\r\n  event: EventSettings;\r\n  underworld: UnderworldSettings;\r\n  forge: ForgeAutomationSettings;\r\n  repair: RepairAutomationSettings;\r\n  quest: QuestManagementSettings;\r\n  market: MarketOperationsSettings;\r\n  shop: ShopSettings;\r\n  bank: BankSettings;\r\n  guild: GuildSettings;\r\n}\r\n\r\nexport interface GeneralSettings {\r\n  selectedBackpack: number;\r\n  maxRandomDelay: number;\r\n  loginDelay: number;\r\n  highlightUnderworldItems: boolean;\r\n  showShopBox: boolean;\r\n  shopMinQuality: number;\r\n  showBiddenBox: boolean;\r\n  autoCollectGodOils: boolean;\r\n  autoCollectDaily: boolean;\r\n  autoLogin?: boolean;\r\n  autoStart?: boolean;\r\n}\r\n\r\nexport interface HealSettings {\r\n  enabled: boolean;\r\n  minHealth: number;\r\n  healCervisia: boolean;\r\n  healEggs: boolean;\r\n  buyFoodNeeded: boolean;\r\n  selectedEggs: number[];\r\n  renewShop: RenewShopOption;\r\n  autoStopAttacks: boolean;\r\n  buyFood: boolean;\r\n}\r\n\r\nexport enum RenewShopOption {\r\n  NOT_ALLOW = 'not_allow',\r\n  CLOTHE_ONLY = 'clothe_only',\r\n  RUBY_OR_CLOTHE = 'ruby_or_clothe'\r\n}\r\n\r\nexport interface HideGoldSettings {\r\n  primary: HideGoldFlow;\r\n  backup: HideGoldFlow;\r\n  minGoldBackup: number;\r\n  soulBoundTo: string;\r\n  filterBy: PackFilterType;\r\n  allowMultiple: boolean;\r\n  recheckDuration: number;\r\n  pocketMoney: number;\r\n  hideIn: HideGoldLocation;\r\n  duration: PackDuration;\r\n  skillsToTrain: string[];\r\n  minItemPrice: number;\r\n  minPack: number;\r\n  maxPack: number;\r\n  buyFromPlayers: string[];\r\n}\r\n\r\nexport enum HideGoldLocation {\r\n  GUILD = 'guild',\r\n  SHOP = 'shop',\r\n  AUCTION = 'auction',\r\n  TRAINING = 'training',\r\n  MARKET = 'market',\r\n  DONATE = 'donate'\r\n}\r\n\r\nexport enum PackDuration {\r\n  TWO_HOURS = '2h',\r\n  EIGHT_HOURS = '8h',\r\n  TWENTY_FOUR_HOURS = '24h',\r\n  FORTY_EIGHT_HOURS = '48h'\r\n}\r\n\r\nexport enum PackFilterType {\r\n  LOWEST_DURATION = 'd',\r\n  HIGHEST_DURATION = 'dd',\r\n  LOWEST_PRICE = 'p',\r\n  HIGHEST_PRICE = 'pd'\r\n}\r\n\r\nexport interface HideGoldFlow {\r\n  enabled: boolean;\r\n  location: HideGoldLocation;\r\n  settings: any;\r\n}\r\n\r\nexport interface BotStatistics {\r\n  arena: ArenaStats;\r\n  quest: QuestStats;\r\n  expedition: ExpeditionStats;\r\n}\r\n\r\nexport interface ArenaStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  lastHourStats: HourlyStats;\r\n  weekStats: WeeklyStats;\r\n  startedFrom: Date;\r\n}\r\n\r\nexport interface HourlyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n}\r\n\r\nexport interface WeeklyStats {\r\n  wins: number;\r\n  losses: number;\r\n  goldEarned: number;\r\n  experience: number;\r\n  totalGold: number;\r\n}\r\n\r\nexport interface QuestStats {\r\n  lastHourTotal: QuestHourlyStats;\r\n  weekStats: QuestWeeklyStats;\r\n}\r\n\r\nexport interface QuestHourlyStats {\r\n  quests: number;\r\n  experience: number;\r\n  honor: number;\r\n  food: number;\r\n  items: number;\r\n  goldEarned: number;\r\n  godRewards: number;\r\n}\r\n\r\nexport interface QuestWeeklyStats extends QuestHourlyStats {\r\n  byQuestType: Record<string, QuestHourlyStats>;\r\n}\r\n\r\nexport interface ExpeditionStats {\r\n  wins: number;\r\n  losses: number;\r\n  items: number;\r\n  fame: number;\r\n}", "// Game mechanics and bot logic types\r\n\r\nimport { ItemQuality } from './gladiatus';\r\nimport { QuestRule, MarketRule } from './api';\r\nimport { FilterCondition } from './settings';\r\n\r\nexport interface BotStatus {\r\n  isActive: boolean;\r\n  isPaused: boolean;\r\n  wasActive?: boolean; // Tracks if bot was active before page reload\r\n  currentAction: BotAction;\r\n  nextActionTime?: Date;\r\n  error?: string;\r\n  statistics: GameBotStatistics;\r\n}\r\n\r\nexport enum BotAction {\r\n  IDLE = 'idle',\r\n  HEALING = 'healing',\r\n  ATTACKING = 'attacking',\r\n  TRAVELING = 'traveling',\r\n  BUYING_PACK = 'buying_pack',\r\n  PICKING_ITEMS = 'picking_items',\r\n  UNDERWORLD = 'underworld',\r\n  EVENT = 'event',\r\n  SELLING_PACK = 'selling_pack',\r\n  QUEST_MANAGEMENT = 'quest_management',\r\n  MARKET_OPERATIONS = 'market_operations',\r\n  FORGE_REPAIR = 'forge_repair',\r\n  ROTATING_ITEMS = 'rotating_items',\r\n  CHECKING_QUESTS = 'checking_quests',\r\n  FORGING = 'forging',\r\n  SMELTING = 'smelting',\r\n  REPAIRING = 'repairing',\r\n  CHECKING_MARKET = 'checking_market',\r\n  CHECKING_AUCTION = 'checking_auction',\r\n  ACTIVATING_PACTS = 'activating_pacts',\r\n  PROCESSING_PACK = 'processing_pack',\r\n  REQUESTING_PACK = 'requesting_pack'\r\n}\r\n\r\nexport interface GameBotStatistics {\r\n  startTime: Date;\r\n  totalRunTime: number;\r\n  actionsPerformed: number;\r\n  errorsEncountered: number;\r\n  goldEarned: number;\r\n  experienceGained: number;\r\n  itemsFound: number;\r\n  combatsWon: number;\r\n  combatsLost: number;\r\n}\r\n\r\nexport interface WorkSettings {\r\n  enabled: boolean;\r\n  workIfNoPoints: boolean;\r\n  selectedWork: WorkType;\r\n  duration: number;\r\n}\r\n\r\nexport enum WorkType {\r\n  SENATOR = 'senator',\r\n  JEWELLER = 'jeweller',\r\n  STABLE_BOY = 'stable_boy',\r\n  FARMER = 'farmer',\r\n  BUTCHER = 'butcher',\r\n  FISHERMAN = 'fisherman',\r\n  BAKER = 'baker',\r\n  BLACKSMITH = 'blacksmith',\r\n  MASTER_BLACKSMITH = 'master_blacksmith'\r\n}\r\n\r\nexport interface ForgeSettings {\r\n  enabled: boolean;\r\n  acceptedColor: ItemQuality[];\r\n  prefix: string;\r\n  item: string;\r\n  suffix: string;\r\n  resourceQuality: ItemQuality;\r\n  stopSuccess: number;\r\n  toolsQuality: ToolQuality;\r\n  stopResources: boolean;\r\n  stopTools: boolean;\r\n  slots: number;\r\n  sendTo: ForgeDestination;\r\n  useHammer: boolean;\r\n}\r\n\r\nexport enum ToolQuality {\r\n  BRONZE = 'bronze',\r\n  SILVER = 'silver',\r\n  GOLD = 'gold'\r\n}\r\n\r\nexport enum ForgeDestination {\r\n  NONE = 'none',\r\n  INVENTORY = 'inventory',\r\n  PACKAGES = 'packages'\r\n}\r\n\r\nexport interface RepairSettings {\r\n  standardProfile: RepairProfile;\r\n  turmaProfile: RepairProfile;\r\n}\r\n\r\nexport interface RepairProfile {\r\n  enabled: boolean;\r\n  minConditioning: number;\r\n  maxQuality: ItemQuality;\r\n  ignoreMaterials: boolean;\r\n}\r\n\r\nexport interface QuestSettings {\r\n  enabled: boolean;\r\n  rules: QuestRule[];\r\n  underworldRules: QuestRule[];\r\n}\r\n\r\nexport interface MarketSettings {\r\n  enabled: boolean;\r\n  sellConfig: MarketSellConfig;\r\n  buyConfig: MarketBuyConfig;\r\n}\r\n\r\nexport interface MarketSellConfig {\r\n  enabled: boolean;\r\n  stackSize: number;\r\n  pricePerUnit: number;\r\n  maxListedItems: number;\r\n  goldRatio: number;\r\n  pickVAT: boolean;\r\n  forgingGoods: string[];\r\n}\r\n\r\nexport interface MarketBuyConfig {\r\n  enabled: boolean;\r\n  rules: MarketRule[];\r\n  autoBuy: boolean;\r\n}\r\n\r\nexport interface ShopSettings {\r\n  enabled: boolean;\r\n  searchRules: ShopRule[];\r\n  maxSearches: number;\r\n  usedSearches: number;\r\n  allowRuby: boolean;\r\n  autoStart: boolean;\r\n}\r\n\r\nexport interface ShopRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  autoBuy: boolean;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface BankSettings {\r\n  enabled: boolean;\r\n  isBank: boolean;\r\n  bankId?: string;\r\n  selectedBackpack: number;\r\n  clientSettings: BankClientSettings;\r\n}\r\n\r\nexport interface BankClientSettings {\r\n  requestThreshold: number;\r\n  packGoldAmount: number;\r\n  duration: string;\r\n}\r\n\r\nexport interface GuildSettings {\r\n  enabled: boolean;\r\n  attackList: string[];\r\n  ignoreList: string[];\r\n}", "// Gladiatus game-specific type definitions\r\n\r\nexport interface GladiatorServer {\r\n  number: string;\r\n  language: string;\r\n}\r\n\r\nexport interface GladiatorAccount {\r\n  id: string;\r\n  server: GladiatorServer;\r\n  playerId: string;\r\n  lastLogin: string;\r\n}\r\n\r\nexport interface GladiatorQueries {\r\n  sh?: string;\r\n  [key: string]: string | undefined;\r\n}\r\n\r\nexport interface GladiatorUrlInfo {\r\n  server?: string;\r\n  country?: string;\r\n  domain?: string;\r\n  queries: GladiatorQueries;\r\n  resolved: boolean;\r\n}\r\n\r\nexport interface LoginData {\r\n  server: GladiatorServer;\r\n  id: string;\r\n  clickedButton: string;\r\n  blackbox: string;\r\n}\r\n\r\nexport interface LoginResponse {\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n// Game item types\r\nexport interface GameItem {\r\n  id: string;\r\n  name: string;\r\n  quality: ItemQuality;\r\n  level: number;\r\n  type: ItemType;\r\n  price?: number;\r\n  isUnderworld?: boolean;\r\n  isSoulbound?: boolean;\r\n  conditioning?: number;\r\n}\r\n\r\nexport enum ItemQuality {\r\n  WHITE = 0,\r\n  GREEN = 1,\r\n  BLUE = 2,\r\n  PURPLE = 3,\r\n  ORANGE = 4,\r\n  RED = 5\r\n}\r\n\r\nexport enum ItemType {\r\n  WEAPON = 'weapon',\r\n  SHIELD = 'shield',\r\n  HELMET = 'helmet',\r\n  CHEST = 'chest',\r\n  GLOVES = 'gloves',\r\n  SHOES = 'shoes',\r\n  RING = 'ring',\r\n  AMULET = 'amulet',\r\n  USABLE = 'usable',\r\n  REINFORCEMENT = 'reinforcement',\r\n  UPGRADE = 'upgrade',\r\n  RECIPE = 'recipe',\r\n  MERCENARY = 'mercenary',\r\n  FORGING_GOODS = 'forging_goods',\r\n  TOOLS = 'tools',\r\n  SCROLL = 'scroll',\r\n  EVENT_ITEMS = 'event_items'\r\n}\r\n\r\n// Combat and character types\r\nexport interface Character {\r\n  id: string;\r\n  name: string;\r\n  level: number;\r\n  health: number;\r\n  maxHealth: number;\r\n  gold: number;\r\n  experience: number;\r\n  honor: number;\r\n}\r\n\r\nexport interface CombatResult {\r\n  won: boolean;\r\n  experience: number;\r\n  gold: number;\r\n  items: GameItem[];\r\n  honor?: number;\r\n}\r\n\r\n// Quest types\r\nexport interface Quest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  type: QuestType;\r\n  reward: QuestReward;\r\n  requirements: QuestRequirement[];\r\n  hasTimer: boolean;\r\n  attacksLeft?: number;\r\n}\r\n\r\nexport enum QuestType {\r\n  ARENA = 'arena',\r\n  GROUP_ARENA = 'group_arena',\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  WORK = 'work',\r\n  FIND_ITEM = 'find_item',\r\n  COMBAT = 'combat',\r\n  ANY_QUEST = 'any_quest'\r\n}\r\n\r\nexport interface QuestReward {\r\n  experience?: number;\r\n  gold?: number;\r\n  honor?: number;\r\n  items?: GameItem[];\r\n  isFood?: boolean;\r\n  gods?: GodType[];\r\n}\r\n\r\nexport interface QuestRequirement {\r\n  type: string;\r\n  value: string | number;\r\n}\r\n\r\nexport enum GodType {\r\n  APOLLO = 'apollo',\r\n  VULCAN = 'vulcan',\r\n  MARS = 'mars',\r\n  MERCURY = 'mercury',\r\n  DIANA = 'diana',\r\n  MINERVA = 'minerva'\r\n}\r\n\r\n// Location and travel types\r\nexport interface Location {\r\n  id: string;\r\n  name: string;\r\n  type: LocationType;\r\n}\r\n\r\nexport enum LocationType {\r\n  EXPEDITION = 'expedition',\r\n  DUNGEON = 'dungeon',\r\n  ARENA = 'arena',\r\n  TURMA = 'turma',\r\n  UNDERWORLD = 'underworld',\r\n  MARKET = 'market',\r\n  GUILD = 'guild'\r\n}", "// Main type definitions for Gladiatus Helper Bot\r\n\r\nexport * from './gladiatus';\r\nexport * from './extension';\r\nexport * from './localization';\r\nexport * from './settings';\r\nexport * from './api';\r\nexport * from './ui';\r\nexport * from './game';\r\n\r\n// Re-export commonly used types\r\nexport type { BotStatus } from './game';", "// Localization and internationalization types\r\n\r\nexport interface LocalizationData {\r\n  [key: string]: string | LocalizationData;\r\n}\r\n\r\nexport interface TranslationKeys {\r\n  EXP_STATS: {\r\n    WIN: string;\r\n    LOSE: string;\r\n    ITEMS: string;\r\n    FAME: string;\r\n  };\r\n  QUEST_STATS: {\r\n    LAST_HOUR_TOTAL: string;\r\n    WEEK_STATS: string;\r\n    QUESTS: string;\r\n    AVG: string;\r\n    EXP: string;\r\n    HONOR: string;\r\n    FOOD: string;\r\n    ITEMS: string;\r\n    WITH_TIMER: string;\r\n    GOLD_EARNED: string;\r\n    GOD_REWARDS: string;\r\n    BY_QUEST_TYPE: string;\r\n  };\r\n  TITLE: {\r\n    GENERAL: string;\r\n    HEAL: string;\r\n    HIDE_GOLD: string;\r\n    PACKAGES: string;\r\n    COSTUMES: string;\r\n    TURMA: string;\r\n    ARENA: string;\r\n    EXPEDITIONS: string;\r\n    DUNGEON: string;\r\n    EVENT: string;\r\n    UNDERWORLD: string;\r\n    FORGE: string;\r\n    REPAIR: string;\r\n    QUEST: string;\r\n    MARKET: string;\r\n    SHOP: string;\r\n    BANK: string;\r\n    GUILD: string;\r\n  };\r\n  STATUS: {\r\n    FORGING: string;\r\n    WAIT: string;\r\n    INACTIVE: string;\r\n    PAUSED: string;\r\n    PICK_ITEMS: string;\r\n    SELL_ITEMS: string;\r\n    ATTACKING: string;\r\n    BUY_PACK: string;\r\n    CHECK_QUESTS: string;\r\n    SELL_PACK: string;\r\n    BUY_FOOD: string;\r\n    TRAVELING: string;\r\n    ROTATE_ITEMS: string;\r\n    HEALING: string;\r\n    BUFFING: string;\r\n    REPAIRING: string;\r\n    SMELTING: string;\r\n    EQUIP_COSTUMES: string;\r\n    CHECK_AUCTION: string;\r\n    CHECK_SMELTING: string;\r\n    CHECK_MARKET: string;\r\n    SELL_MARKET: string;\r\n    ACTIVATE_PACTS: string;\r\n    CHECKING_SHOP: string;\r\n    DICE: string;\r\n    PROCESSING_PACK: string;\r\n    REQUEST_PACK: string;\r\n  };\r\n  BUTTONS: {\r\n    CANCEL: string;\r\n    SAVE: string;\r\n    ADD_NEW_RULE: string;\r\n    DELETE: string;\r\n    EDIT: string;\r\n    ACTIVE: string;\r\n    ACTIVATE: string;\r\n    BUY_LICENCE: string;\r\n    INACTIVE: string;\r\n    CLOSE: string;\r\n    CLEAR_ALL: string;\r\n    CLEAR: string;\r\n    CONFIRM: string;\r\n  };\r\n}\r\n\r\nexport type SupportedLanguage = 'en' | 'es' | 'de' | 'br' | 'pl' | 'ro' | 'fr' | 'pt' | 'tr' | 'cz' | 'pe' | 'ru' | 'hu' | 'ar' | 'bg' | 'nl';\r\n\r\nexport interface LanguageInfo {\r\n  code: SupportedLanguage;\r\n  name: string;\r\n  nativeName: string;\r\n}", "// Settings and configuration type definitions\r\n\r\nimport { ItemType, ItemQuality } from './gladiatus';\r\n\r\nexport interface PackageSettings {\r\n  rotateItems: RotateItemsSettings;\r\n  sellItems: SellItemsSettings;\r\n  pickGold: PickGoldSettings;\r\n  operationSpeed: OperationSpeed;\r\n}\r\n\r\nexport interface RotateItemsSettings {\r\n  enabled: boolean;\r\n  selectedItems: ItemType[];\r\n  colors: ItemQuality[];\r\n  underworldItems: boolean;\r\n  itemsCooldownDays: number;\r\n  collectResourceHourly: boolean;\r\n}\r\n\r\nexport interface SellItemsSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n  autoSellOnRenew: boolean;\r\n}\r\n\r\nexport interface SellRule {\r\n  id: string;\r\n  conditions: FilterCondition[];\r\n  itemTypes: ItemType[];\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface FilterCondition {\r\n  type: FilterType;\r\n  value: string | number;\r\n}\r\n\r\nexport enum FilterType {\r\n  CONTAINS = 'contains',\r\n  NOT_CONTAINS = 'not_contains',\r\n  CONTAINS_ANY = 'contains_any',\r\n  NOT_CONTAINS_ANY = 'not_contains_any',\r\n  CONTAINS_WORD = 'contains_word',\r\n  IS_UNDERWORLD = 'is_underworld',\r\n  IS_NOT_UNDERWORLD = 'is_not_underworld',\r\n  GREATER_THAN = 'greater_than',\r\n  LESS_THAN = 'less_than',\r\n  STARTS_WITH = 'starts_with',\r\n  ENDS_WITH = 'ends_with',\r\n  CONTAINS_RESOURCE = 'contains_resource'\r\n}\r\n\r\nexport interface PickGoldSettings {\r\n  enabled: boolean;\r\n  goldToPick: number;\r\n}\r\n\r\nexport enum OperationSpeed {\r\n  VERY_FAST = 'very_fast',\r\n  FAST = 'fast',\r\n  NORMAL = 'normal',\r\n  SLOW = 'slow'\r\n}\r\n\r\nexport interface CostumeSettings {\r\n  wearUnderworld: boolean;\r\n  preventWearUnderworldOnPause: boolean;\r\n  dontPauseIfUnderworldActive: boolean;\r\n  underworldCostume: UnderworldCostume;\r\n  standardProfile: CostumeProfile;\r\n  turmaProfile: CostumeProfile;\r\n  eventProfile: CostumeProfile;\r\n}\r\n\r\nexport enum UnderworldCostume {\r\n  DIS_PATER_NORMAL = 'dis_pater_normal',\r\n  DIS_PATER_MEDIUM = 'dis_pater_medium',\r\n  DIS_PATER_HARD = 'dis_pater_hard',\r\n  VULCANUS_FORGE = 'vulcanus_forge',\r\n  FERONIAS_EARTHEN_SHIELD = 'feronias_earthen_shield',\r\n  NEPTUNES_FLUID_MIGHT = 'neptunes_fluid_might',\r\n  AELOUS_AERIAL_FREEDOM = 'aelous_aerial_freedom',\r\n  PLUTOS_DEADLY_MIST = 'plutos_deadly_mist',\r\n  JUNOS_BREATH_OF_LIFE = 'junos_breath_of_life',\r\n  WRATH_MOUNTAIN_SCALE_ARMOUR = 'wrath_mountain_scale_armour',\r\n  EAGLE_EYES = 'eagle_eyes',\r\n  SATURNS_WINTER_GARMENT = 'saturns_winter_garment',\r\n  BUBONA_BULL_ARMOUR = 'bubona_bull_armour',\r\n  MERCERIUS_ROBBERS_GARMENTS = 'mercerius_robbers_garments',\r\n  RA_LIGHT_ROBE = 'ra_light_robe'\r\n}\r\n\r\nexport interface CostumeProfile {\r\n  enabled: boolean;\r\n  costume?: UnderworldCostume;\r\n}\r\n\r\nexport interface TurmaSettings {\r\n  enabled: boolean;\r\n  levelRestriction: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  runUntilGetChest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n}\r\n\r\nexport interface ArenaSettings {\r\n  enabled: boolean;\r\n  allowSim: boolean;\r\n  prioritizeChance: number;\r\n  skipLow: boolean;\r\n  loseLimit: number;\r\n  attackTargetPlayers: boolean;\r\n  allowMaxAttacks: boolean;\r\n  autoIgnoreGuildPlayers: boolean;\r\n  attackScore: boolean;\r\n  selectScorePage: number;\r\n  progressLeague: boolean;\r\n  stopAfterLimit: boolean;\r\n  attackGoldWhale: boolean;\r\n  doRevenge: boolean;\r\n  totalWin: boolean;\r\n  limitAttacks: number;\r\n  dailyAttacks: number;\r\n  autoStop: number;\r\n  attackIfQuest: boolean;\r\n  attackIfCombatQuest: boolean;\r\n  goldRaided: number;\r\n  attackPlayers: string[];\r\n  autoIgnorePlayers: string[];\r\n  ignorePlayers: string[];\r\n  attackSameServer: boolean;\r\n  attackScoreSingle: boolean;\r\n}\r\n\r\nexport interface ExpeditionSettings {\r\n  enabled: boolean;\r\n  minDungeonPoints: number;\r\n  location: string;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  travelForDungeon: boolean;\r\n  removeCooldown: CooldownRemovalOption;\r\n  cooldownLimit: number;\r\n  cooldownUsed: number;\r\n}\r\n\r\nexport enum CooldownRemovalOption {\r\n  HOURGLASS = 'hourglass',\r\n  HOURGLASS_OR_RUBY = 'hourglass_or_ruby'\r\n}\r\n\r\nexport interface DungeonSettings {\r\n  enabled: boolean;\r\n  location: string;\r\n  useGateKey: boolean;\r\n  isAdvanced: boolean;\r\n  skipBoss: boolean;\r\n  bossName: string;\r\n  restartAfterFail: number;\r\n}\r\n\r\nexport interface EventSettings {\r\n  enabled: boolean;\r\n  mob: string;\r\n  autoCollectBonuses: boolean;\r\n  autoRenewEvent: boolean;\r\n  followLeader: string;\r\n  autoStop: boolean;\r\n}\r\n\r\nexport interface UnderworldSettings {\r\n  enabled: boolean;\r\n  noWeapon: boolean;\r\n  sendMail: boolean;\r\n  mailContent: string;\r\n  maxMedics: number;\r\n  usedMedics: number;\r\n  maxRuby: number;\r\n  usedRuby: number;\r\n  buffs: boolean;\r\n  costumes: boolean;\r\n  reinforcements: string[];\r\n  mode: UnderworldMode;\r\n  difficulty: UnderworldDifficulty;\r\n  healGuild: boolean;\r\n  healPotions: boolean;\r\n  healSacrifice: boolean;\r\n  allowMobilisation: boolean;\r\n  allowRuby: boolean;\r\n  attackDisPaterAsap: boolean;\r\n  stopArenaInUnder: boolean;\r\n  healCostumes: boolean;\r\n  allowReinforcements: boolean;\r\n  allowUpgrades: boolean;\r\n  autoBuffGods: boolean;\r\n  autoBuffOils: boolean;\r\n  exitUnder: boolean;\r\n  stayHours: number;\r\n}\r\n\r\nexport enum UnderworldMode {\r\n  FARM_QUEST = 'farm_quest',\r\n  FARM_MOB = 'farm_mob',\r\n  NORMAL = 'normal'\r\n}\r\n\r\nexport enum UnderworldDifficulty {\r\n  NORMAL = 'normal',\r\n  MEDIUM = 'medium',\r\n  HARD = 'hard'\r\n}\r\n\r\nexport interface QuestManagementSettings {\r\n  enabled: boolean;\r\n  autoComplete: boolean;\r\n  dailyLimit: number;\r\n  rules: QuestManagementRule[];\r\n}\r\n\r\nexport interface QuestManagementRule {\r\n  priority: number;\r\n  questType: string;\r\n  minReward: number;\r\n  maxDifficulty: 'easy' | 'medium' | 'hard';\r\n  autoComplete: boolean;\r\n}\r\n\r\nexport interface MarketOperationsSettings {\r\n  enabled: boolean;\r\n  checkInterval: number;\r\n  dailyBuyLimit: number;\r\n  autoBuy: AutoBuySettings;\r\n  autoSell: AutoSellSettings;\r\n  auction: AuctionSettings;\r\n}\r\n\r\nexport interface AutoBuySettings {\r\n  enabled: boolean;\r\n  maxPrice: number;\r\n  maxGoldSpend: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemNames: string[];\r\n  blacklistedSellers: string[];\r\n}\r\n\r\nexport interface AutoSellSettings {\r\n  enabled: boolean;\r\n  rules: SellRule[];\r\n}\r\n\r\nexport interface AuctionSettings {\r\n  enabled: boolean;\r\n  autoBid: boolean;\r\n  autoBuyout: boolean;\r\n  maxBid: number;\r\n  maxBuyout: number;\r\n  bidIncrement: number;\r\n  minTimeRemaining: number;\r\n  qualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface ForgeAutomationSettings {\r\n  enabled: boolean;\r\n  autoForge: boolean;\r\n  autoSmelt: boolean;\r\n  maxGoldSpend: number;\r\n  maxCostPerItem: number;\r\n  dailyForgeLimit: number;\r\n  qualityFilter: ItemQuality[];\r\n  minLevel: number;\r\n  maxLevel: number;\r\n  itemTypes: string[];\r\n  minSmeltValue: number;\r\n  smeltQualityFilter: ItemQuality[];\r\n}\r\n\r\nexport interface RepairAutomationSettings {\r\n  enabled: boolean;\r\n  repairThreshold: number;\r\n  maxCostPerItem: number;\r\n  maxGoldSpend: number;\r\n  itemFilter: string[];\r\n}", "// UI and interface types\r\n\r\nexport interface UINotification {\r\n  id: string;\r\n  type: NotificationType;\r\n  title: string;\r\n  message: string;\r\n  timestamp: Date;\r\n  read: boolean;\r\n  actions?: NotificationAction[];\r\n}\r\n\r\nexport enum NotificationType {\r\n  INFO = 'info',\r\n  SUCCESS = 'success',\r\n  WARNING = 'warning',\r\n  ERROR = 'error',\r\n  GOLD_EXPIRY = 'gold_expiry',\r\n  ITEM_FOUND = 'item_found',\r\n  ITEM_BOUGHT = 'item_bought'\r\n}\r\n\r\nexport interface NotificationAction {\r\n  label: string;\r\n  action: string;\r\n  data?: any;\r\n}\r\n\r\nexport interface TabConfig {\r\n  id: string;\r\n  name: string;\r\n  icon?: string;\r\n  component: string;\r\n  enabled: boolean;\r\n  badge?: string | number;\r\n}\r\n\r\nexport interface ModalConfig {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n  actions: ModalAction[];\r\n  size?: ModalSize;\r\n  closable?: boolean;\r\n}\r\n\r\nexport interface ModalAction {\r\n  label: string;\r\n  action: string;\r\n  style?: ButtonStyle;\r\n  data?: any;\r\n}\r\n\r\nexport enum ModalSize {\r\n  SMALL = 'small',\r\n  MEDIUM = 'medium',\r\n  LARGE = 'large',\r\n  EXTRA_LARGE = 'extra_large'\r\n}\r\n\r\nexport enum ButtonStyle {\r\n  PRIMARY = 'primary',\r\n  SECONDARY = 'secondary',\r\n  SUCCESS = 'success',\r\n  DANGER = 'danger',\r\n  WARNING = 'warning',\r\n  INFO = 'info'\r\n}\r\n\r\nexport interface FormField {\r\n  id: string;\r\n  type: FieldType;\r\n  label: string;\r\n  value: any;\r\n  options?: SelectOption[];\r\n  validation?: ValidationRule[];\r\n  disabled?: boolean;\r\n  placeholder?: string;\r\n  help?: string;\r\n}\r\n\r\nexport enum FieldType {\r\n  TEXT = 'text',\r\n  NUMBER = 'number',\r\n  SELECT = 'select',\r\n  CHECKBOX = 'checkbox',\r\n  RADIO = 'radio',\r\n  TEXTAREA = 'textarea',\r\n  RANGE = 'range',\r\n  COLOR = 'color',\r\n  DATE = 'date',\r\n  TIME = 'time'\r\n}\r\n\r\nexport interface SelectOption {\r\n  value: any;\r\n  label: string;\r\n  disabled?: boolean;\r\n  icon?: string;\r\n}\r\n\r\nexport interface ValidationRule {\r\n  type: ValidationType;\r\n  value?: any;\r\n  message: string;\r\n}\r\n\r\nexport enum ValidationType {\r\n  REQUIRED = 'required',\r\n  MIN = 'min',\r\n  MAX = 'max',\r\n  MIN_LENGTH = 'min_length',\r\n  MAX_LENGTH = 'max_length',\r\n  PATTERN = 'pattern',\r\n  EMAIL = 'email',\r\n  URL = 'url'\r\n}\r\n\r\nexport interface UIState {\r\n  activeTab: string;\r\n  sidebarCollapsed: boolean;\r\n  notifications: UINotification[];\r\n  modals: ModalConfig[];\r\n  loading: boolean;\r\n  error?: string;\r\n}", "// Gladiatus URL parsing and validation utilities\r\n\r\nimport { GladiatorUrlInfo, GladiatorQueries } from '../types';\r\n\r\nexport class GladiatorUrlHelper {\r\n  private resolved: boolean = false;\r\n  private urlInfo: GladiatorUrlInfo = {\r\n    queries: {},\r\n    resolved: false\r\n  };\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus domain\r\n   */\r\n  isGladiatus(url: string): boolean {\r\n    return /gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is a Gladiatus game page (not lobby)\r\n   */\r\n  isPlaying(url: string): boolean {\r\n    return /https:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com/.test(url);\r\n  }\r\n\r\n  /**\r\n   * Check if URL is the Gladiatus lobby\r\n   */\r\n  isLobby(url: string): boolean {\r\n    return /lobby\\.gladiatus\\.gameforge\\.com/.test(url) && url.indexOf('loading') < 0;\r\n  }\r\n\r\n  /**\r\n   * Parse query string into object\r\n   */\r\n  resolveQueries(queryString: string): GladiatorQueries {\r\n    const queries: GladiatorQueries = {};\r\n    const params = queryString.split('&');\r\n\r\n    for (let i = params.length - 1; i >= 0; i--) {\r\n      const [key, value] = params[i].split('=');\r\n      if (key && value) {\r\n        queries[key] = decodeURIComponent(value);\r\n      }\r\n    }\r\n\r\n    return queries;\r\n  }\r\n\r\n  /**\r\n   * Resolve URL information from Gladiatus game URL\r\n   */\r\n  resolve(url: string, force: boolean = false): GladiatorUrlInfo | null {\r\n    if (this.resolved && !force) {\r\n      return this.urlInfo;\r\n    }\r\n\r\n    const match = url.match(\r\n      /https?:\\/\\/s(\\d+)-(\\w+)\\.gladiatus\\.gameforge\\.com\\/game\\/(?:index|main)\\.php(?:\\?(.*))?/i\r\n    );\r\n\r\n    if (!match) {\r\n      return null;\r\n    }\r\n\r\n    this.urlInfo = {\r\n      server: match[1],\r\n      country: match[2],\r\n      domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,\r\n      queries: this.resolveQueries(match[3] || ''),\r\n      resolved: true\r\n    };\r\n\r\n    this.resolved = true;\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Build Gladiatus URL with parameters\r\n   */\r\n  buildUrl(params: Record<string, string | number>, page: string = 'index.php'): string {\r\n    const queryParts: string[] = [];\r\n\r\n    for (const key in params) {\r\n      queryParts.push(`${key}=${encodeURIComponent(params[key])}`);\r\n    }\r\n\r\n    // Add security hash if available\r\n    if (this.urlInfo.queries.sh) {\r\n      queryParts.push(`sh=${this.urlInfo.queries.sh}`);\r\n    }\r\n\r\n    return `${page}?${queryParts.join('&')}`;\r\n  }\r\n\r\n  /**\r\n   * Build AJAX URL with parameters\r\n   */\r\n  buildAjaxUrl(params: Record<string, string | number>): string {\r\n    return this.buildUrl(params, 'ajax.php');\r\n  }\r\n\r\n  /**\r\n   * Get current URL info\r\n   */\r\n  getUrlInfo(): GladiatorUrlInfo {\r\n    return this.urlInfo;\r\n  }\r\n\r\n  /**\r\n   * Reset resolved state\r\n   */\r\n  reset(): void {\r\n    this.resolved = false;\r\n    this.urlInfo = {\r\n      queries: {},\r\n      resolved: false\r\n    };\r\n  }\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Content script for Gladiatus Helper Bot UI injection\r\n\r\nimport { ExtensionMessage, MessageType } from '../types';\r\nimport { GladiatorUrlHelper } from '../utils/gladiator-url-helper';\r\nimport '../styles/content.css';\r\n\r\nclass ContentUIManager {\r\n  private urlHelper: GladiatorUrlHelper;\r\n  private isInjected: boolean = false;\r\n  private extensionId: string;\r\n  private botStatus: any = null;\r\n\r\n  constructor() {\r\n    this.urlHelper = new GladiatorUrlHelper();\r\n    this.extensionId = chrome.runtime.id;\r\n    this.initialize();\r\n  }\r\n\r\n  private initialize(): void {\r\n    // Wait for DOM to be ready\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => this.onDOMReady());\r\n    } else {\r\n      this.onDOMReady();\r\n    }\r\n\r\n    // Listen for messages from background script\r\n    chrome.runtime.onMessage.addListener((message: ExtensionMessage, sender, sendResponse) => {\r\n      this.handleMessage(message, sender, sendResponse);\r\n      return true;\r\n    });\r\n  }\r\n\r\n  private onDOMReady(): void {\r\n    const currentUrl = window.location.href;\r\n\r\n    if (this.urlHelper.isLobby(currentUrl)) {\r\n      this.handleLobbyPage();\r\n    } else if (this.urlHelper.isPlaying(currentUrl)) {\r\n      this.handleGamePage(currentUrl);\r\n    }\r\n  }\r\n\r\n  private handleLobbyPage(): void {\r\n    console.log('Gladiatus Helper Bot: Lobby page detected');\r\n\r\n    // Send login request to background script\r\n    const message: ExtensionMessage = {\r\n      type: MessageType.LOGIN_REQUEST,\r\n      isLobby: true,\r\n      server: this.extractServerFromUrl(),\r\n      playerId: this.extractPlayerIdFromUrl(),\r\n      language: this.extractLanguageFromUrl()\r\n    };\r\n\r\n    chrome.runtime.sendMessage(message, (response) => {\r\n      if (response?.shouldLogin && response.loginDelay) {\r\n        this.scheduleAutoLogin(response.loginDelay);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleGamePage(url: string): void {\r\n    console.log('Gladiatus Helper Bot: Game page detected');\r\n\r\n    const urlInfo = this.urlHelper.resolve(url);\r\n    if (!urlInfo) {\r\n      console.error('Failed to resolve game URL');\r\n      return;\r\n    }\r\n\r\n    // Set global extension ID for main script\r\n    (window as any).ghExtensionId = this.extensionId;\r\n\r\n    // Inject UI elements first\r\n    this.injectUI();\r\n\r\n    // Inject main bot script after UI is ready\r\n    setTimeout(() => {\r\n      this.injectMainScript();\r\n    }, 100);\r\n\r\n    // Load and restore bot status\r\n    this.loadBotStatus();\r\n  }\r\n\r\n  private injectMainScript(): void {\r\n    if (this.isInjected) return;\r\n\r\n    const script = document.createElement('script');\r\n    script.src = chrome.runtime.getURL('main/index.iife.js');\r\n    script.onload = () => {\r\n      console.log('Gladiatus Helper Bot: Main script injected');\r\n      this.isInjected = true;\r\n    };\r\n    script.onerror = () => {\r\n      console.error('Gladiatus Helper Bot: Failed to inject main script');\r\n    };\r\n\r\n    (document.head || document.documentElement).appendChild(script);\r\n  }\r\n\r\n  private injectUI(): void {\r\n    // Create bot control panel\r\n    this.createControlPanel();\r\n\r\n    // Create notification system\r\n    this.createNotificationSystem();\r\n\r\n    // Highlight underworld items if enabled\r\n    this.highlightUnderworldItems();\r\n  }\r\n\r\n  private createControlPanel(): void {\r\n    // Check if panel already exists\r\n    if (document.getElementById('gh-control-panel')) return;\r\n\r\n    const panel = document.createElement('div');\r\n    panel.id = 'gh-control-panel';\r\n    panel.className = 'gh-panel';\r\n\r\n    panel.innerHTML = `\r\n      <div class=\"gh-panel-header\">\r\n        <h3>Gladiatus Helper Bot</h3>\r\n        <button id=\"gh-toggle-panel\" class=\"gh-btn gh-btn-sm\">−</button>\r\n      </div>\r\n      <div class=\"gh-panel-content\">\r\n        <div class=\"gh-status-section\">\r\n          <div class=\"gh-status-indicator\" id=\"gh-status-indicator\">\r\n            <span class=\"gh-status-dot\"></span>\r\n            <span id=\"gh-status-text\">Inactive</span>\r\n          </div>\r\n          <button id=\"gh-toggle-bot\" class=\"gh-btn gh-btn-primary\">Start Bot</button>\r\n        </div>\r\n        <div class=\"gh-stats-section\">\r\n          <div class=\"gh-stat-item\">\r\n            <span class=\"gh-stat-label\">Runtime:</span>\r\n            <span id=\"gh-runtime\" class=\"gh-stat-value\">00:00:00</span>\r\n          </div>\r\n          <div class=\"gh-stat-item\">\r\n            <span class=\"gh-stat-label\">Actions:</span>\r\n            <span id=\"gh-actions\" class=\"gh-stat-value\">0</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"gh-actions-section\">\r\n          <button id=\"gh-settings\" class=\"gh-btn gh-btn-secondary\">Settings</button>\r\n          <button id=\"gh-statistics\" class=\"gh-btn gh-btn-secondary\">Statistics</button>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    // Position panel\r\n    panel.style.cssText = `\r\n      position: fixed;\r\n      top: 10px;\r\n      right: 10px;\r\n      z-index: 10000;\r\n      width: 280px;\r\n      background: #2c3e50;\r\n      border: 1px solid #34495e;\r\n      border-radius: 8px;\r\n      color: white;\r\n      font-family: Arial, sans-serif;\r\n      font-size: 12px;\r\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n    `;\r\n\r\n    document.body.appendChild(panel);\r\n\r\n    // Add event listeners\r\n    this.attachPanelEventListeners();\r\n  }\r\n\r\n  private attachPanelEventListeners(): void {\r\n    const togglePanel = document.getElementById('gh-toggle-panel');\r\n    const toggleBot = document.getElementById('gh-toggle-bot');\r\n    const settingsBtn = document.getElementById('gh-settings');\r\n    const statisticsBtn = document.getElementById('gh-statistics');\r\n\r\n    togglePanel?.addEventListener('click', () => {\r\n      const content = document.querySelector('.gh-panel-content') as HTMLElement;\r\n      const isCollapsed = content.style.display === 'none';\r\n      content.style.display = isCollapsed ? 'block' : 'none';\r\n      togglePanel.textContent = isCollapsed ? '−' : '+';\r\n    });\r\n\r\n    toggleBot?.addEventListener('click', () => {\r\n      this.toggleBot();\r\n    });\r\n\r\n    settingsBtn?.addEventListener('click', (e) => {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      console.log('Settings button clicked');\r\n      this.openSettings();\r\n    });\r\n\r\n    statisticsBtn?.addEventListener('click', (e) => {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      console.log('Statistics button clicked');\r\n      this.openStatistics();\r\n    });\r\n  }\r\n\r\n  private createNotificationSystem(): void {\r\n    if (document.getElementById('gh-notifications')) return;\r\n\r\n    const container = document.createElement('div');\r\n    container.id = 'gh-notifications';\r\n    container.className = 'gh-notifications';\r\n    container.style.cssText = `\r\n      position: fixed;\r\n      top: 10px;\r\n      left: 10px;\r\n      z-index: 9999;\r\n      max-width: 400px;\r\n    `;\r\n\r\n    document.body.appendChild(container);\r\n  }\r\n\r\n  private highlightUnderworldItems(): void {\r\n    // This would be implemented based on game-specific selectors\r\n    const items = document.querySelectorAll('[data-item-type=\"underworld\"]');\r\n    items.forEach(item => {\r\n      (item as HTMLElement).style.border = '2px solid #e74c3c';\r\n      (item as HTMLElement).style.boxShadow = '0 0 10px rgba(231, 76, 60, 0.5)';\r\n    });\r\n  }\r\n\r\n  private scheduleAutoLogin(delay: number): void {\r\n    const remainingTime = delay - Date.now();\r\n    if (remainingTime > 0) {\r\n      setTimeout(() => {\r\n        this.performAutoLogin();\r\n      }, remainingTime);\r\n    }\r\n  }\r\n\r\n  private performAutoLogin(): void {\r\n    // Look for login button and click it\r\n    const loginButton = document.querySelector('input[type=\"submit\"][value*=\"Login\"], button[type=\"submit\"]') as HTMLElement;\r\n    if (loginButton) {\r\n      loginButton.click();\r\n    }\r\n  }\r\n\r\n  private async loadBotStatus(): Promise<void> {\r\n    try {\r\n      // Get bot status from storage\r\n      const result = await chrome.storage.local.get('gh_bot_status');\r\n      this.botStatus = result.gh_bot_status || null;\r\n\r\n      console.log('Content script loaded bot status:', this.botStatus);\r\n\r\n      if (this.botStatus) {\r\n        // Update UI with stored status\r\n        this.updateStatusUI(this.botStatus);\r\n      }\r\n\r\n      // Set up periodic status sync\r\n      this.setupStatusSync();\r\n    } catch (error) {\r\n      console.error('Failed to load bot status:', error);\r\n    }\r\n  }\r\n\r\n  private setupStatusSync(): void {\r\n    // Listen for storage changes\r\n    chrome.storage.onChanged.addListener((changes, namespace) => {\r\n      if (namespace === 'local' && changes.gh_bot_status) {\r\n        console.log('Bot status changed in storage:', changes.gh_bot_status.newValue);\r\n        this.botStatus = changes.gh_bot_status.newValue;\r\n        if (this.botStatus) {\r\n          this.updateStatusUI(this.botStatus);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Periodic sync every 5 seconds\r\n    setInterval(async () => {\r\n      try {\r\n        const result = await chrome.storage.local.get('gh_bot_status');\r\n        const newStatus = result.gh_bot_status;\r\n        if (newStatus && JSON.stringify(newStatus) !== JSON.stringify(this.botStatus)) {\r\n          console.log('Syncing bot status from storage');\r\n          this.botStatus = newStatus;\r\n          this.updateStatusUI(this.botStatus);\r\n        }\r\n      } catch (error) {\r\n        // Ignore sync errors\r\n      }\r\n    }, 5000);\r\n  }\r\n\r\n  private updateStatusUI(status: any): void {\r\n    const statusText = document.getElementById('gh-status-text');\r\n    const toggleButton = document.getElementById('gh-toggle-bot') as HTMLButtonElement;\r\n    const statusDot = document.querySelector('.gh-status-dot') as HTMLElement;\r\n\r\n    if (statusText && toggleButton && statusDot) {\r\n      if (status.isActive) {\r\n        if (status.isPaused) {\r\n          statusDot.style.backgroundColor = '#f39c12'; // Orange for paused\r\n          statusText.textContent = 'Paused';\r\n          toggleButton.textContent = 'Resume Bot';\r\n        } else {\r\n          statusDot.style.backgroundColor = '#27ae60'; // Green for active\r\n          statusText.textContent = 'Active';\r\n          toggleButton.textContent = 'Stop Bot';\r\n        }\r\n      } else {\r\n        statusDot.style.backgroundColor = '#e74c3c'; // Red for inactive\r\n        statusText.textContent = status.error ? 'Error' : 'Inactive';\r\n        toggleButton.textContent = 'Start Bot';\r\n      }\r\n    }\r\n  }\r\n\r\n  private toggleBot(): void {\r\n    // Send message to main script to toggle bot\r\n    const event = new CustomEvent('ghToggleBot');\r\n    window.dispatchEvent(event);\r\n  }\r\n\r\n  private openSettings(): void {\r\n    console.log('Opening settings modal...');\r\n    // Prevent any immediate event propagation\r\n    setTimeout(() => {\r\n      this.createSettingsModal();\r\n    }, 10);\r\n  }\r\n\r\n  private createSettingsModal(): void {\r\n    console.log('Creating settings modal...');\r\n\r\n    // Check if modal already exists\r\n    const existingModal = document.getElementById('gh-settings-modal');\r\n    if (existingModal) {\r\n      console.log('Modal already exists, removing...');\r\n      existingModal.remove();\r\n    }\r\n\r\n    const modal = document.createElement('div');\r\n    modal.id = 'gh-settings-modal';\r\n    modal.className = 'gh-modal';\r\n    modal.style.cssText = `\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: rgba(0,0,0,0.5);\r\n      z-index: 10001;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    `;\r\n\r\n    const modalContent = document.createElement('div');\r\n    modalContent.className = 'gh-modal-content';\r\n    modalContent.style.cssText = `\r\n      background: #2c3e50;\r\n      color: white;\r\n      padding: 20px;\r\n      border-radius: 8px;\r\n      max-width: 600px;\r\n      width: 90%;\r\n      max-height: 80%;\r\n      overflow-y: auto;\r\n    `;\r\n\r\n    modalContent.innerHTML = `\r\n      <div class=\"gh-modal-header\">\r\n        <h2>Bot Settings</h2>\r\n        <button class=\"gh-modal-close\" style=\"float: right; background: none; border: none; color: white; font-size: 20px; cursor: pointer;\">×</button>\r\n      </div>\r\n      <div class=\"gh-modal-body\">\r\n        <div class=\"gh-settings-section\">\r\n          <h3>General Settings</h3>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-auto-start\"> Auto-start bot on page load\r\n          </label>\r\n          <label>\r\n            <input type=\"number\" id=\"gh-setting-login-delay\" min=\"1\" max=\"60\" value=\"5\"> Login delay (seconds)\r\n          </label>\r\n        </div>\r\n        <div class=\"gh-settings-section\">\r\n          <h3>Combat Settings</h3>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-arena-enabled\"> Enable Arena Combat\r\n          </label>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-turma-enabled\"> Enable Turma Combat\r\n          </label>\r\n        </div>\r\n        <div class=\"gh-settings-section\">\r\n          <h3>Healing Settings</h3>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-heal-enabled\"> Enable Auto-Healing\r\n          </label>\r\n          <label>\r\n            <input type=\"number\" id=\"gh-setting-min-health\" min=\"1\" max=\"100\" value=\"50\"> Minimum Health %\r\n          </label>\r\n        </div>\r\n        <div class=\"gh-settings-section\">\r\n          <h3>Quest & Market Settings</h3>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-quest-enabled\"> Enable Quest Management\r\n          </label>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-market-enabled\"> Enable Market Operations\r\n          </label>\r\n        </div>\r\n        <div class=\"gh-settings-section\">\r\n          <h3>Forge & Repair Settings</h3>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-forge-enabled\"> Enable Auto-Forging\r\n          </label>\r\n          <label>\r\n            <input type=\"checkbox\" id=\"gh-setting-repair-enabled\"> Enable Auto-Repair\r\n          </label>\r\n        </div>\r\n      </div>\r\n      <div class=\"gh-modal-footer\" style=\"margin-top: 20px; text-align: right;\">\r\n        <button id=\"gh-settings-save\" class=\"gh-btn gh-btn-primary\">Save Settings</button>\r\n        <button id=\"gh-settings-cancel\" class=\"gh-btn gh-btn-secondary\" style=\"margin-left: 10px;\">Cancel</button>\r\n      </div>\r\n    `;\r\n\r\n    modal.appendChild(modalContent);\r\n    document.body.appendChild(modal);\r\n\r\n    console.log('Modal added to DOM, modal element:', modal);\r\n    console.log('Modal display style:', modal.style.display);\r\n\r\n    // Load current settings\r\n    this.loadSettingsIntoModal();\r\n\r\n    // Add event listeners\r\n    this.attachSettingsEventListeners(modal);\r\n\r\n    console.log('Settings modal fully initialized');\r\n  }\r\n\r\n  private async loadSettingsIntoModal(): Promise<void> {\r\n    try {\r\n      const result = await chrome.storage.local.get('gh_settings');\r\n      const settings = result.gh_settings || {};\r\n\r\n      // Populate form fields with current settings\r\n      const autoStart = document.getElementById('gh-setting-auto-start') as HTMLInputElement;\r\n      const loginDelay = document.getElementById('gh-setting-login-delay') as HTMLInputElement;\r\n      const arenaEnabled = document.getElementById('gh-setting-arena-enabled') as HTMLInputElement;\r\n      const turmaEnabled = document.getElementById('gh-setting-turma-enabled') as HTMLInputElement;\r\n      const healEnabled = document.getElementById('gh-setting-heal-enabled') as HTMLInputElement;\r\n      const minHealth = document.getElementById('gh-setting-min-health') as HTMLInputElement;\r\n      const questEnabled = document.getElementById('gh-setting-quest-enabled') as HTMLInputElement;\r\n      const marketEnabled = document.getElementById('gh-setting-market-enabled') as HTMLInputElement;\r\n      const forgeEnabled = document.getElementById('gh-setting-forge-enabled') as HTMLInputElement;\r\n      const repairEnabled = document.getElementById('gh-setting-repair-enabled') as HTMLInputElement;\r\n\r\n      if (autoStart) autoStart.checked = settings.general?.autoStart || false;\r\n      if (loginDelay) loginDelay.value = settings.general?.loginDelay || 5;\r\n      if (arenaEnabled) arenaEnabled.checked = settings.arena?.enabled || false;\r\n      if (turmaEnabled) turmaEnabled.checked = settings.turma?.enabled || false;\r\n      if (healEnabled) healEnabled.checked = settings.heal?.enabled || false;\r\n      if (minHealth) minHealth.value = settings.heal?.minHealth || 50;\r\n      if (questEnabled) questEnabled.checked = settings.quest?.enabled || false;\r\n      if (marketEnabled) marketEnabled.checked = settings.market?.enabled || false;\r\n      if (forgeEnabled) forgeEnabled.checked = settings.forge?.enabled || false;\r\n      if (repairEnabled) repairEnabled.checked = settings.repair?.enabled || false;\r\n\r\n    } catch (error) {\r\n      console.error('Failed to load settings:', error);\r\n    }\r\n  }\r\n\r\n  private attachSettingsEventListeners(modal: HTMLElement): void {\r\n    console.log('Attaching settings event listeners...');\r\n\r\n    const closeBtn = modal.querySelector('.gh-modal-close');\r\n    const saveBtn = document.getElementById('gh-settings-save');\r\n    const cancelBtn = document.getElementById('gh-settings-cancel');\r\n    const modalContent = modal.querySelector('.gh-modal-content');\r\n\r\n    console.log('Found elements:', { closeBtn, saveBtn, cancelBtn, modalContent });\r\n\r\n    closeBtn?.addEventListener('click', (e) => {\r\n      console.log('Close button clicked');\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      modal.remove();\r\n    });\r\n\r\n    cancelBtn?.addEventListener('click', (e) => {\r\n      console.log('Cancel button clicked');\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      modal.remove();\r\n    });\r\n\r\n    saveBtn?.addEventListener('click', (e) => {\r\n      console.log('Save button clicked');\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      this.saveSettings();\r\n      modal.remove();\r\n    });\r\n\r\n    // Prevent modal content clicks from closing modal\r\n    modalContent?.addEventListener('click', (e) => {\r\n      console.log('Modal content clicked');\r\n      e.stopPropagation();\r\n    });\r\n\r\n    // Close on background click only - with delay to prevent immediate closure\r\n    setTimeout(() => {\r\n      modal.addEventListener('click', (e) => {\r\n        console.log('Modal background clicked, target:', e.target, 'modal:', modal);\r\n        if (e.target === modal) {\r\n          console.log('Closing modal via background click');\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          modal.remove();\r\n        }\r\n      });\r\n    }, 100);\r\n\r\n    // Close on Escape key\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape') {\r\n        console.log('Escape key pressed, closing modal');\r\n        modal.remove();\r\n        document.removeEventListener('keydown', handleEscape);\r\n      }\r\n    };\r\n    document.addEventListener('keydown', handleEscape);\r\n\r\n    console.log('Event listeners attached successfully');\r\n  }\r\n\r\n  private async saveSettings(): Promise<void> {\r\n    try {\r\n      // Get current settings\r\n      const result = await chrome.storage.local.get('gh_settings');\r\n      const settings = result.gh_settings || {};\r\n\r\n      // Update settings from form\r\n      const autoStart = (document.getElementById('gh-setting-auto-start') as HTMLInputElement)?.checked;\r\n      const loginDelay = parseInt((document.getElementById('gh-setting-login-delay') as HTMLInputElement)?.value) || 5;\r\n      const arenaEnabled = (document.getElementById('gh-setting-arena-enabled') as HTMLInputElement)?.checked;\r\n      const turmaEnabled = (document.getElementById('gh-setting-turma-enabled') as HTMLInputElement)?.checked;\r\n      const healEnabled = (document.getElementById('gh-setting-heal-enabled') as HTMLInputElement)?.checked;\r\n      const minHealth = parseInt((document.getElementById('gh-setting-min-health') as HTMLInputElement)?.value) || 50;\r\n      const questEnabled = (document.getElementById('gh-setting-quest-enabled') as HTMLInputElement)?.checked;\r\n      const marketEnabled = (document.getElementById('gh-setting-market-enabled') as HTMLInputElement)?.checked;\r\n      const forgeEnabled = (document.getElementById('gh-setting-forge-enabled') as HTMLInputElement)?.checked;\r\n      const repairEnabled = (document.getElementById('gh-setting-repair-enabled') as HTMLInputElement)?.checked;\r\n\r\n      // Update settings object\r\n      settings.general = settings.general || {};\r\n      settings.general.autoStart = autoStart;\r\n      settings.general.loginDelay = loginDelay;\r\n\r\n      settings.arena = settings.arena || {};\r\n      settings.arena.enabled = arenaEnabled;\r\n\r\n      settings.turma = settings.turma || {};\r\n      settings.turma.enabled = turmaEnabled;\r\n\r\n      settings.heal = settings.heal || {};\r\n      settings.heal.enabled = healEnabled;\r\n      settings.heal.minHealth = minHealth;\r\n\r\n      settings.quest = settings.quest || {};\r\n      settings.quest.enabled = questEnabled;\r\n\r\n      settings.market = settings.market || {};\r\n      settings.market.enabled = marketEnabled;\r\n\r\n      settings.forge = settings.forge || {};\r\n      settings.forge.enabled = forgeEnabled;\r\n\r\n      settings.repair = settings.repair || {};\r\n      settings.repair.enabled = repairEnabled;\r\n\r\n      // Save to storage\r\n      await chrome.storage.local.set({ 'gh_settings': settings });\r\n\r\n      // Notify main script of settings update\r\n      const event = new CustomEvent('ghSettingsUpdated', { detail: settings });\r\n      window.dispatchEvent(event);\r\n\r\n      this.showNotification({\r\n        title: 'Settings Saved',\r\n        message: 'Your settings have been saved successfully.'\r\n      });\r\n\r\n    } catch (error) {\r\n      console.error('Failed to save settings:', error);\r\n      this.showNotification({\r\n        title: 'Error',\r\n        message: 'Failed to save settings. Please try again.'\r\n      });\r\n    }\r\n  }\r\n\r\n  private openStatistics(): void {\r\n    // Open statistics modal\r\n    const event = new CustomEvent('ghOpenStatistics');\r\n    window.dispatchEvent(event);\r\n  }\r\n\r\n  private handleMessage(message: ExtensionMessage, _sender: any, _sendResponse: (response?: any) => void): void {\r\n    switch (message.type) {\r\n      case MessageType.NOTIFICATION:\r\n        this.showNotification(message.data);\r\n        break;\r\n      case MessageType.ERROR:\r\n        this.showError(message.data);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  private showNotification(data: any): void {\r\n    const container = document.getElementById('gh-notifications');\r\n    if (!container) return;\r\n\r\n    const notification = document.createElement('div');\r\n    notification.className = 'gh-notification gh-notification-info';\r\n    notification.innerHTML = `\r\n      <div class=\"gh-notification-content\">\r\n        <strong>${data.title || 'Notification'}</strong>\r\n        <p>${data.message}</p>\r\n      </div>\r\n      <button class=\"gh-notification-close\">×</button>\r\n    `;\r\n\r\n    container.appendChild(notification);\r\n\r\n    // Auto-remove after 5 seconds\r\n    setTimeout(() => {\r\n      notification.remove();\r\n    }, 5000);\r\n\r\n    // Add close button functionality\r\n    notification.querySelector('.gh-notification-close')?.addEventListener('click', () => {\r\n      notification.remove();\r\n    });\r\n  }\r\n\r\n  private showError(data: any): void {\r\n    console.error('Gladiatus Helper Bot Error:', data);\r\n    this.showNotification({\r\n      title: 'Error',\r\n      message: data.message || 'An error occurred'\r\n    });\r\n  }\r\n\r\n  private extractServerFromUrl(): string {\r\n    const match = window.location.href.match(/s(\\d+)-(\\w+)/);\r\n    return match ? `${match[1]}-${match[2]}` : '';\r\n  }\r\n\r\n  private extractPlayerIdFromUrl(): string {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    return urlParams.get('playerId') || '';\r\n  }\r\n\r\n  private extractLanguageFromUrl(): string {\r\n    const match = window.location.href.match(/s\\d+-(\\w+)/);\r\n    return match ? match[1] : 'en';\r\n  }\r\n}\r\n\r\n// Initialize content UI manager\r\nnew ContentUIManager();"], "names": [], "sourceRoot": ""}