import { CostumeSettings, TurmaSettings, ArenaSettings, ExpeditionSettings, DungeonSettings, EventSettings, UnderworldSettings, PackageSettings, ForgeAutomationSettings, RepairAutomationSettings, QuestManagementSettings, MarketOperationsSettings } from './settings';
import { ShopSettings, BankSettings, GuildSettings } from './game';
import { QuestRule, MarketRule, PauseSchedule, BankData, LicenseInfo } from './api';
export interface ExtensionMessage {
    type: MessageType;
    data?: any;
    isLobby?: boolean;
    shouldLogin?: boolean;
    loginDelay?: number;
    loginFailed?: boolean;
    server?: string;
    playerId?: string;
    language?: string;
}
export declare enum MessageType {
    LOGIN_REQUEST = "LOGIN_REQUEST",
    LOGIN_RESPONSE = "LOGIN_RESPONSE",
    LOGIN_FAILED = "LOGIN_FAILED",
    SCRIPT_STATUS = "SCRIPT_STATUS",
    SETTINGS_UPDATE = "SETTINGS_UPDATE",
    NOTIFICATION = "NOTIFICATION",
    ERROR = "ERROR"
}
export interface ExtensionStorage {
    settings: BotSettings;
    statistics: BotStatistics;
    licenseInfo: LicenseInfo;
    pauseSchedule: PauseSchedule[];
    questRules: QuestRule[];
    marketRules: MarketRule[];
    bankData: BankData;
}
export interface BotSettings {
    general: GeneralSettings;
    heal: HealSettings;
    hideGold: HideGoldSettings;
    packages: PackageSettings;
    costumes: CostumeSettings;
    turma: TurmaSettings;
    arena: ArenaSettings;
    expeditions: ExpeditionSettings;
    dungeon: DungeonSettings;
    event: EventSettings;
    underworld: UnderworldSettings;
    forge: ForgeAutomationSettings;
    repair: RepairAutomationSettings;
    quest: QuestManagementSettings;
    market: MarketOperationsSettings;
    shop: ShopSettings;
    bank: BankSettings;
    guild: GuildSettings;
}
export interface GeneralSettings {
    selectedBackpack: number;
    maxRandomDelay: number;
    loginDelay: number;
    highlightUnderworldItems: boolean;
    showShopBox: boolean;
    shopMinQuality: number;
    showBiddenBox: boolean;
    autoCollectGodOils: boolean;
    autoCollectDaily: boolean;
    autoLogin?: boolean;
    autoStart?: boolean;
}
export interface HealSettings {
    enabled: boolean;
    minHealth: number;
    healCervisia: boolean;
    healEggs: boolean;
    buyFoodNeeded: boolean;
    selectedEggs: number[];
    renewShop: RenewShopOption;
    autoStopAttacks: boolean;
    buyFood: boolean;
}
export declare enum RenewShopOption {
    NOT_ALLOW = "not_allow",
    CLOTHE_ONLY = "clothe_only",
    RUBY_OR_CLOTHE = "ruby_or_clothe"
}
export interface HideGoldSettings {
    primary: HideGoldFlow;
    backup: HideGoldFlow;
    minGoldBackup: number;
    soulBoundTo: string;
    filterBy: PackFilterType;
    allowMultiple: boolean;
    recheckDuration: number;
    pocketMoney: number;
    hideIn: HideGoldLocation;
    duration: PackDuration;
    skillsToTrain: string[];
    minItemPrice: number;
    minPack: number;
    maxPack: number;
    buyFromPlayers: string[];
}
export declare enum HideGoldLocation {
    GUILD = "guild",
    SHOP = "shop",
    AUCTION = "auction",
    TRAINING = "training",
    MARKET = "market",
    DONATE = "donate"
}
export declare enum PackDuration {
    TWO_HOURS = "2h",
    EIGHT_HOURS = "8h",
    TWENTY_FOUR_HOURS = "24h",
    FORTY_EIGHT_HOURS = "48h"
}
export declare enum PackFilterType {
    LOWEST_DURATION = "d",
    HIGHEST_DURATION = "dd",
    LOWEST_PRICE = "p",
    HIGHEST_PRICE = "pd"
}
export interface HideGoldFlow {
    enabled: boolean;
    location: HideGoldLocation;
    settings: any;
}
export interface BotStatistics {
    arena: ArenaStats;
    quest: QuestStats;
    expedition: ExpeditionStats;
}
export interface ArenaStats {
    wins: number;
    losses: number;
    goldEarned: number;
    experience: number;
    lastHourStats: HourlyStats;
    weekStats: WeeklyStats;
    startedFrom: Date;
}
export interface HourlyStats {
    wins: number;
    losses: number;
    goldEarned: number;
    experience: number;
}
export interface WeeklyStats {
    wins: number;
    losses: number;
    goldEarned: number;
    experience: number;
    totalGold: number;
}
export interface QuestStats {
    lastHourTotal: QuestHourlyStats;
    weekStats: QuestWeeklyStats;
}
export interface QuestHourlyStats {
    quests: number;
    experience: number;
    honor: number;
    food: number;
    items: number;
    goldEarned: number;
    godRewards: number;
}
export interface QuestWeeklyStats extends QuestHourlyStats {
    byQuestType: Record<string, QuestHourlyStats>;
}
export interface ExpeditionStats {
    wins: number;
    losses: number;
    items: number;
    fame: number;
}
//# sourceMappingURL=extension.d.ts.map