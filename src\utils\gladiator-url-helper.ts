// Gladiatus URL parsing and validation utilities

import { GladiatorUrlInfo, GladiatorQueries } from '../types';

export class GladiatorUrlHelper {
  private resolved: boolean = false;
  private urlInfo: GladiatorUrlInfo = {
    queries: {},
    resolved: false
  };

  /**
   * Check if URL is a Gladiatus domain
   */
  isGladiatus(url: string): boolean {
    return /gladiatus\.gameforge\.com/.test(url);
  }

  /**
   * Check if URL is a Gladiatus game page (not lobby)
   */
  isPlaying(url: string): boolean {
    return /https:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com/.test(url);
  }

  /**
   * Check if URL is the Gladiatus lobby
   */
  isLobby(url: string): boolean {
    return /lobby\.gladiatus\.gameforge\.com/.test(url) && url.indexOf('loading') < 0;
  }

  /**
   * Parse query string into object
   */
  resolveQueries(queryString: string): GladiatorQueries {
    const queries: GladiatorQueries = {};
    const params = queryString.split('&');

    for (let i = params.length - 1; i >= 0; i--) {
      const [key, value] = params[i].split('=');
      if (key && value) {
        queries[key] = decodeURIComponent(value);
      }
    }

    return queries;
  }

  /**
   * Resolve URL information from Gladiatus game URL
   */
  resolve(url: string, force: boolean = false): GladiatorUrlInfo | null {
    if (this.resolved && !force) {
      return this.urlInfo;
    }

    const match = url.match(
      /https?:\/\/s(\d+)-(\w+)\.gladiatus\.gameforge\.com\/game\/(?:index|main)\.php(?:\?(.*))?/i
    );

    if (!match) {
      return null;
    }

    this.urlInfo = {
      server: match[1],
      country: match[2],
      domain: `s${match[1]}-${match[2]}.gladiatus.gameforge.com`,
      queries: this.resolveQueries(match[3] || ''),
      resolved: true
    };

    this.resolved = true;
    return this.urlInfo;
  }

  /**
   * Build Gladiatus URL with parameters
   */
  buildUrl(params: Record<string, string | number>, page: string = 'index.php'): string {
    const queryParts: string[] = [];

    for (const key in params) {
      queryParts.push(`${key}=${encodeURIComponent(params[key])}`);
    }

    // Add security hash if available
    if (this.urlInfo.queries.sh) {
      queryParts.push(`sh=${this.urlInfo.queries.sh}`);
    }

    return `${page}?${queryParts.join('&')}`;
  }

  /**
   * Build AJAX URL with parameters
   */
  buildAjaxUrl(params: Record<string, string | number>): string {
    return this.buildUrl(params, 'ajax.php');
  }

  /**
   * Get current URL info
   */
  getUrlInfo(): GladiatorUrlInfo {
    return this.urlInfo;
  }

  /**
   * Reset resolved state
   */
  reset(): void {
    this.resolved = false;
    this.urlInfo = {
      queries: {},
      resolved: false
    };
  }
}