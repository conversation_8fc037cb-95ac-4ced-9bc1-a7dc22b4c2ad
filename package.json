{"name": "gladiatus-helper-bot-ts", "version": "2.2.10", "description": "Automate gladiatus with the Best Gladiatus Bot! - TypeScript Edition", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:dev": "webpack --mode development", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch"}, "devDependencies": {"@types/chrome": "^0.0.268", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "eslint": "^8.57.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "mini-css-extract-plugin": "^2.8.1", "rimraf": "^5.0.5", "ts-jest": "^29.4.1", "ts-loader": "^9.5.1", "typescript": "^5.4.2", "webpack": "^5.90.3", "webpack-cli": "^5.1.4"}, "dependencies": {"crypto-js": "^4.2.0"}}